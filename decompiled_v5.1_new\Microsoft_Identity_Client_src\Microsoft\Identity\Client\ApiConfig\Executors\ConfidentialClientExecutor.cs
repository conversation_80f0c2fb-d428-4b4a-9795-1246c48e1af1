using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Internal.Requests;

namespace Microsoft.Identity.Client.ApiConfig.Executors;

internal class ConfidentialClientExecutor : AbstractExecutor, IConfidentialClientApplicationExecutor
{
	private readonly ConfidentialClientApplication _confidentialClientApplication;

	public ConfidentialClientExecutor(IServiceBundle serviceBundle, ConfidentialClientApplication confidentialClientApplication)
		: base(serviceBundle)
	{
		ApplicationBase.GuardMobileFrameworks();
		_confidentialClientApplication = confidentialClientApplication;
	}

	public async Task<AuthenticationResult> ExecuteAsync(AcquireTokenCommonParameters commonParameters, AcquireTokenByAuthorizationCodeParameters authorizationCodeParameters, CancellationToken cancellationToken)
	{
		RequestContext requestContext = CreateRequestContextAndLogVersionInfo(commonParameters.CorrelationId, cancellationToken);
		AuthenticationRequestParameters authenticationRequestParameters = await _confidentialClientApplication.CreateRequestParametersAsync(commonParameters, requestContext, _confidentialClientApplication.UserTokenCacheInternal).ConfigureAwait(continueOnCapturedContext: false);
		authenticationRequestParameters.SendX5C = authorizationCodeParameters.SendX5C == true;
		return await new ConfidentialAuthCodeRequest(base.ServiceBundle, authenticationRequestParameters, authorizationCodeParameters).RunAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}

	public async Task<AuthenticationResult> ExecuteAsync(AcquireTokenCommonParameters commonParameters, AcquireTokenForClientParameters clientParameters, CancellationToken cancellationToken)
	{
		RequestContext requestContext = CreateRequestContextAndLogVersionInfo(commonParameters.CorrelationId, cancellationToken);
		AuthenticationRequestParameters authenticationRequestParameters = await _confidentialClientApplication.CreateRequestParametersAsync(commonParameters, requestContext, _confidentialClientApplication.AppTokenCacheInternal).ConfigureAwait(continueOnCapturedContext: false);
		authenticationRequestParameters.SendX5C = clientParameters.SendX5C == true;
		return await new ClientCredentialRequest(base.ServiceBundle, authenticationRequestParameters, clientParameters).RunAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}

	public async Task<AuthenticationResult> ExecuteAsync(AcquireTokenCommonParameters commonParameters, AcquireTokenOnBehalfOfParameters onBehalfOfParameters, CancellationToken cancellationToken)
	{
		RequestContext requestContext = CreateRequestContextAndLogVersionInfo(commonParameters.CorrelationId, cancellationToken);
		AuthenticationRequestParameters authenticationRequestParameters = await _confidentialClientApplication.CreateRequestParametersAsync(commonParameters, requestContext, _confidentialClientApplication.UserTokenCacheInternal).ConfigureAwait(continueOnCapturedContext: false);
		authenticationRequestParameters.SendX5C = onBehalfOfParameters.SendX5C == true;
		authenticationRequestParameters.UserAssertion = onBehalfOfParameters.UserAssertion;
		authenticationRequestParameters.LongRunningOboCacheKey = onBehalfOfParameters.LongRunningOboCacheKey;
		return await new OnBehalfOfRequest(base.ServiceBundle, authenticationRequestParameters, onBehalfOfParameters).RunAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}

	public async Task<Uri> ExecuteAsync(AcquireTokenCommonParameters commonParameters, GetAuthorizationRequestUrlParameters authorizationRequestUrlParameters, CancellationToken cancellationToken)
	{
		RequestContext requestContext = CreateRequestContextAndLogVersionInfo(commonParameters.CorrelationId, cancellationToken);
		AuthenticationRequestParameters requestParameters = await _confidentialClientApplication.CreateRequestParametersAsync(commonParameters, requestContext, _confidentialClientApplication.UserTokenCacheInternal).ConfigureAwait(continueOnCapturedContext: false);
		requestParameters.Account = authorizationRequestUrlParameters.Account;
		requestParameters.LoginHint = authorizationRequestUrlParameters.LoginHint;
		requestParameters.CcsRoutingHint = authorizationRequestUrlParameters.CcsRoutingHint;
		if (!string.IsNullOrWhiteSpace(authorizationRequestUrlParameters.RedirectUri))
		{
			requestParameters.RedirectUri = new Uri(authorizationRequestUrlParameters.RedirectUri);
		}
		await requestParameters.AuthorityManager.RunInstanceDiscoveryAndValidationAsync().ConfigureAwait(continueOnCapturedContext: false);
		AuthCodeRequestComponent authCodeRequestComponent = new AuthCodeRequestComponent(requestParameters, authorizationRequestUrlParameters.ToInteractiveParameters());
		if (authorizationRequestUrlParameters.CodeVerifier != null)
		{
			return await authCodeRequestComponent.GetAuthorizationUriWithPkceAsync(authorizationRequestUrlParameters.CodeVerifier, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		}
		return await authCodeRequestComponent.GetAuthorizationUriWithoutPkceAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}
}
