using System;
using System.Collections.Generic;
using System.Net;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.ManagedIdentity;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

internal class MsalServiceExceptionFactory
{
	private static readonly ISet<string> s_nonUiSubErrors = new HashSet<string>(new string[2] { "client_mismatch", "protection_policy_required" }, StringComparer.OrdinalIgnoreCase);

	internal static MsalServiceException FromHttpResponse(string errorCode, string errorMessage, HttpResponse httpResponse, Exception innerException = null)
	{
		MsalServiceException ex = null;
		OAuth2ResponseBase oAuth2ResponseBase = JsonHelper.TryToDeserializeFromJson<OAuth2ResponseBase>(httpResponse?.Body);
		if (IsInvalidGrant(oAuth2ResponseBase?.Error, oAuth2ResponseBase?.SubError) || IsInteractionRequired(oAuth2ResponseBase?.Error))
		{
			string text = null;
			text = ((!IsThrottled(oAuth2ResponseBase)) ? errorMessage : "Your app has been throttled by AAD due to too many requests. To avoid this, cache your tokens see https://aka.ms/msal-net-throttling.");
			if (oAuth2ResponseBase.Claims == null)
			{
				ex = new MsalUiRequiredException(errorCode, text, innerException);
			}
			else
			{
				text += " The returned error contains a claims challenge. For additional info on how to handle claims related to multifactor authentication, Conditional Access, and incremental consent, see https://aka.ms/msal-conditional-access-claims. If you are using the On-Behalf-Of flow, see https://aka.ms/msal-conditional-access-claims-obo for details.";
				ex = new MsalClaimsChallengeException(errorCode, text, innerException);
			}
		}
		if (string.Equals(oAuth2ResponseBase?.Error, "invalid_client", StringComparison.OrdinalIgnoreCase))
		{
			ex = new MsalServiceException("invalid_client", "A configuration issue is preventing authentication - check the error message from the server for details. You can modify the configuration in the application registration portal. See https://aka.ms/msal-net-invalid-client for details.  Original exception: " + oAuth2ResponseBase?.ErrorDescription, innerException);
		}
		if (ex == null)
		{
			ex = new MsalServiceException(errorCode, errorMessage, innerException);
		}
		SetHttpExceptionData(ex, httpResponse);
		ex.Claims = oAuth2ResponseBase?.Claims;
		ex.CorrelationId = oAuth2ResponseBase?.CorrelationId;
		ex.SubError = oAuth2ResponseBase?.SubError;
		ex.ErrorCodes = oAuth2ResponseBase?.ErrorCodes;
		return ex;
	}

	private static bool IsThrottled(OAuth2ResponseBase oAuth2Response)
	{
		if (oAuth2Response.ErrorDescription != null)
		{
			return oAuth2Response.ErrorDescription.StartsWith("AADSTS50196");
		}
		return false;
	}

	internal static MsalServiceException FromBrokerResponse(MsalTokenResponse msalTokenResponse, string errorMessage)
	{
		string error = msalTokenResponse.Error;
		string correlationId = msalTokenResponse.CorrelationId;
		string text = (string.IsNullOrEmpty(msalTokenResponse.SubError) ? "unknown_broker_error" : msalTokenResponse.SubError);
		HttpResponse httpResponse = msalTokenResponse.HttpResponse;
		MsalServiceException ex = null;
		if (IsAppProtectionPolicyRequired(error, text))
		{
			ex = new IntuneAppProtectionPolicyRequiredException(error, text)
			{
				Upn = msalTokenResponse.Upn,
				AuthorityUrl = msalTokenResponse.AuthorityUrl,
				TenantId = msalTokenResponse.TenantId,
				AccountUserId = msalTokenResponse.AccountUserId
			};
		}
		if (IsInvalidGrant(error, text) || IsInteractionRequired(error))
		{
			ex = new MsalUiRequiredException(error, errorMessage);
		}
		if (string.Equals(error, "invalid_client", StringComparison.OrdinalIgnoreCase))
		{
			ex = new MsalServiceException("invalid_client", "A configuration issue is preventing authentication - check the error message from the server for details. You can modify the configuration in the application registration portal. See https://aka.ms/msal-net-invalid-client for details.  Original exception: " + errorMessage);
		}
		if (ex == null)
		{
			ex = new MsalServiceException(error, errorMessage);
		}
		SetHttpExceptionData(ex, httpResponse);
		ex.CorrelationId = correlationId;
		ex.SubError = text;
		return ex;
	}

	internal static MsalServiceException FromImdsResponse(string errorCode, string errorMessage, HttpResponse httpResponse, Exception innerException = null)
	{
		MsalServiceException ex = new MsalServiceException(errorCode, errorMessage, innerException);
		SetHttpExceptionData(ex, httpResponse);
		return ex;
	}

	internal static MsalException CreateManagedIdentityException(string errorCode, string errorMessage, Exception innerException, ManagedIdentitySource managedIdentitySource, int? statusCode)
	{
		MsalException ex;
		if (!statusCode.HasValue)
		{
			ex = ((innerException == null) ? new MsalServiceException(errorCode, errorMessage) : new MsalServiceException(errorCode, errorMessage, innerException));
		}
		else
		{
			ex = new MsalServiceException(errorCode, errorMessage, statusCode.Value, innerException);
			bool flag;
			switch (statusCode)
			{
			case 404:
			case 408:
			case 429:
			case 500:
			case 503:
			case 504:
				flag = true;
				break;
			default:
				flag = false;
				break;
			}
			bool isRetryable = flag;
			ex.IsRetryable = isRetryable;
		}
		return DecorateExceptionWithManagedIdentitySource(ex, managedIdentitySource);
	}

	private static MsalException DecorateExceptionWithManagedIdentitySource(MsalException exception, ManagedIdentitySource managedIdentitySource)
	{
		Dictionary<string, string> additionalExceptionData = new Dictionary<string, string> { 
		{
			"ManagedIdentitySource",
			managedIdentitySource.ToString()
		} };
		exception.AdditionalExceptionData = additionalExceptionData;
		return exception;
	}

	internal static MsalThrottledServiceException FromThrottledAuthenticationResponse(HttpResponse httpResponse)
	{
		MsalServiceException ex = new MsalServiceException("request_throttled", "Your app has been throttled by AAD due to too many requests. To avoid this, cache your tokens see https://aka.ms/msal-net-throttling.");
		SetHttpExceptionData(ex, httpResponse);
		return new MsalThrottledServiceException(ex);
	}

	private static void SetHttpExceptionData(MsalServiceException ex, HttpResponse httpResponse)
	{
		ex.ResponseBody = httpResponse?.Body;
		ex.StatusCode = (int)(httpResponse?.StatusCode ?? ((HttpStatusCode)0));
		ex.Headers = httpResponse?.Headers;
	}

	private static bool IsInteractionRequired(string errorCode)
	{
		return string.Equals(errorCode, "interaction_required", StringComparison.OrdinalIgnoreCase);
	}

	private static bool IsInvalidGrant(string errorCode, string subErrorCode)
	{
		if (string.Equals(errorCode, "invalid_grant", StringComparison.OrdinalIgnoreCase))
		{
			return IsInvalidGrantSubError(subErrorCode);
		}
		return false;
	}

	private static bool IsAppProtectionPolicyRequired(string errorCode, string subErrorCode)
	{
		return false;
	}

	private static bool IsInvalidGrantSubError(string subError)
	{
		if (string.IsNullOrEmpty(subError))
		{
			return true;
		}
		return !s_nonUiSubErrors.Contains(subError);
	}
}
