using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.OAuth2;

namespace Microsoft.Identity.Client.Internal.Requests.Silent;

internal class CacheSilentStrategy : ISilentAuthRequestStrategy
{
	private readonly AcquireTokenSilentParameters _silentParameters;

	private const string TheOnlyFamilyId = "1";

	private readonly SilentRequest _silentRequest;

	private AuthenticationRequestParameters AuthenticationRequestParameters { get; }

	private ICacheSessionManager CacheManager => AuthenticationRequestParameters.CacheSessionManager;

	protected IServiceBundle ServiceBundle { get; }

	public CacheSilentStrategy(SilentRequest request, IServiceBundle serviceBundle, AuthenticationRequestParameters authenticationRequestParameters, AcquireTokenSilentParameters silentParameters)
	{
		AuthenticationRequestParameters = authenticationRequestParameters;
		_silentParameters = silentParameters;
		ServiceBundle = serviceBundle;
		_silentRequest = request;
	}

	public async Task<AuthenticationResult> ExecuteAsync(CancellationToken cancellationToken)
	{
		ILoggerAdapter logger = AuthenticationRequestParameters.RequestContext.Logger;
		MsalAccessTokenCacheItem cachedAccessTokenItem = null;
		CacheRefreshReason cacheInfoTelemetry = CacheRefreshReason.NotApplicable;
		ThrowIfCurrentBrokerAccount();
		AuthenticationResult authResult = null;
		if (!_silentParameters.ForceRefresh && string.IsNullOrEmpty(AuthenticationRequestParameters.Claims))
		{
			cachedAccessTokenItem = await CacheManager.FindAccessTokenAsync().ConfigureAwait(continueOnCapturedContext: false);
			if (cachedAccessTokenItem != null)
			{
				logger.Info("Returning access token found in cache. RefreshOn exists ? " + cachedAccessTokenItem.RefreshOn.HasValue);
				AuthenticationRequestParameters.RequestContext.ApiEvent.IsAccessTokenCacheHit = true;
				Metrics.IncrementTotalAccessTokensFromCache();
				authResult = await CreateAuthenticationResultAsync(cachedAccessTokenItem).ConfigureAwait(continueOnCapturedContext: false);
			}
			else if (AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo != CacheRefreshReason.Expired)
			{
				cacheInfoTelemetry = CacheRefreshReason.NoCachedAccessToken;
			}
		}
		else
		{
			cacheInfoTelemetry = CacheRefreshReason.ForceRefreshOrClaims;
			logger.Info("Skipped looking for an Access Token because ForceRefresh or Claims were set. ");
		}
		if (AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo == CacheRefreshReason.NotApplicable)
		{
			AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo = cacheInfoTelemetry;
		}
		try
		{
			if (cachedAccessTokenItem == null)
			{
				authResult = await RefreshRtOrFailAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			}
			else if (SilentRequestHelper.NeedsRefresh(cachedAccessTokenItem))
			{
				AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo = CacheRefreshReason.ProactivelyRefreshed;
				SilentRequestHelper.ProcessFetchInBackground(cachedAccessTokenItem, delegate
				{
					using CancellationTokenSource cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
					return RefreshRtOrFailAsync(cancellationTokenSource.Token);
				}, logger, ServiceBundle, AuthenticationRequestParameters.RequestContext.ApiEvent.ApiId);
			}
			return authResult;
		}
		catch (MsalServiceException ex)
		{
			logger.Warning($"Refreshing the RT failed. Is the exception retryable? {ex.IsRetryable}. Is there an AT in the cache that is usable? {cachedAccessTokenItem != null} ");
			if (cachedAccessTokenItem != null && ex.IsRetryable)
			{
				logger.Info("Returning existing access token. It is not expired, but should be refreshed. ");
				return await CreateAuthenticationResultAsync(cachedAccessTokenItem).ConfigureAwait(continueOnCapturedContext: false);
			}
			logger.Warning("Failed to refresh the RT and cannot use existing AT (expired or missing). ");
			throw;
		}
	}

	private void ThrowIfCurrentBrokerAccount()
	{
		if (PublicClientApplication.IsOperatingSystemAccount(AuthenticationRequestParameters.Account))
		{
			AuthenticationRequestParameters.RequestContext.Logger.Verbose(() => "OperatingSystemAccount is only supported by some brokers");
			throw new MsalUiRequiredException("current_broker_account", "Only some brokers (WAM) can log in the current OS account. ", null, UiRequiredExceptionClassification.AcquireTokenSilentFailed);
		}
	}

	private async Task<AuthenticationResult> RefreshRtOrFailAsync(CancellationToken cancellationToken)
	{
		MsalTokenResponse msalTokenResponse = await TryGetTokenUsingFociAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		if (msalTokenResponse == null)
		{
			msalTokenResponse = await SilentRequestHelper.RefreshAccessTokenAsync(await FindRefreshTokenOrFailAsync().ConfigureAwait(continueOnCapturedContext: false), _silentRequest, AuthenticationRequestParameters, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		}
		return await _silentRequest.CacheTokenResponseAndCreateAuthenticationResultAsync(msalTokenResponse).ConfigureAwait(continueOnCapturedContext: false);
	}

	private async Task<AuthenticationResult> CreateAuthenticationResultAsync(MsalAccessTokenCacheItem cachedAccessTokenItem)
	{
		MsalIdTokenCacheItem msalIdTokenItem = await CacheManager.GetIdTokenCacheItemAsync(cachedAccessTokenItem).ConfigureAwait(continueOnCapturedContext: false);
		Account account = await CacheManager.GetAccountAssociatedWithAccessTokenAsync(cachedAccessTokenItem).ConfigureAwait(continueOnCapturedContext: false);
		return new AuthenticationResult(cachedAccessTokenItem, msalIdTokenItem, AuthenticationRequestParameters.AuthenticationScheme, AuthenticationRequestParameters.RequestContext.CorrelationId, TokenSource.Cache, AuthenticationRequestParameters.RequestContext.ApiEvent, account, null, null);
	}

	private async Task<MsalTokenResponse> TryGetTokenUsingFociAsync(CancellationToken cancellationToken)
	{
		if (!ServiceBundle.PlatformProxy.GetFeatureFlags().IsFociEnabled)
		{
			return null;
		}
		ILoggerAdapter logger = AuthenticationRequestParameters.RequestContext.Logger;
		bool? flag = await CacheManager.IsAppFociMemberAsync("1").ConfigureAwait(continueOnCapturedContext: false);
		if (flag.HasValue && !flag.Value)
		{
			AuthenticationRequestParameters.RequestContext.Logger.Verbose(() => "[FOCI] App is not part of the family, skipping FOCI. ");
			return null;
		}
		logger.Verbose(() => "[FOCI] App is part of the family or unknown, looking for FRT. ");
		MsalRefreshTokenCacheItem familyRefreshToken = await CacheManager.FindFamilyRefreshTokenAsync("1").ConfigureAwait(continueOnCapturedContext: false);
		logger.Verbose(() => "[FOCI] FRT found? " + (familyRefreshToken != null));
		if (familyRefreshToken != null)
		{
			try
			{
				MsalTokenResponse result = await SilentRequestHelper.RefreshAccessTokenAsync(familyRefreshToken, _silentRequest, AuthenticationRequestParameters, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
				logger.Verbose(() => "[FOCI] FRT refresh succeeded. ");
				return result;
			}
			catch (MsalServiceException ex)
			{
				if ("invalid_grant".Equals(ex?.ErrorCode, StringComparison.OrdinalIgnoreCase) && "client_mismatch".Equals(ex?.SubError, StringComparison.OrdinalIgnoreCase))
				{
					logger.Error("[FOCI] FRT refresh failed - client mismatch. ");
					return null;
				}
				logger.Error("[FOCI] FRT refresh failed - other error. ");
				throw;
			}
		}
		return null;
	}

	private async Task<MsalRefreshTokenCacheItem> FindRefreshTokenOrFailAsync()
	{
		MsalRefreshTokenCacheItem obj = await CacheManager.FindRefreshTokenAsync().ConfigureAwait(continueOnCapturedContext: false);
		if (obj == null)
		{
			AuthenticationRequestParameters.RequestContext.Logger.Verbose(() => "No Refresh Token was found in the cache. ");
			throw new MsalUiRequiredException("no_tokens_found", "No Refresh Token found in the cache. ", null, UiRequiredExceptionClassification.AcquireTokenSilentFailed);
		}
		return obj;
	}
}
