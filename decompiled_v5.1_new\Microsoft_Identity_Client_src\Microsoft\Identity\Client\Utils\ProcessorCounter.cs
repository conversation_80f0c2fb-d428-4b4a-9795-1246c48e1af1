using System;

namespace Microsoft.Identity.Client.Utils;

internal static class ProcessorCounter
{
	private const int ProcessorCountRefreshIntervalMs = 30000;

	private static volatile int _processorCount;

	private static volatile int _lastProcessorCountRefreshTicks;

	internal static int ProcessorCount
	{
		get
		{
			int tickCount = Environment.TickCount;
			if (_processorCount == 0 || tickCount - _lastProcessorCountRefreshTicks >= 30000)
			{
				_processorCount = Environment.ProcessorCount;
				_lastProcessorCountRefreshTicks = tickCount;
			}
			return _processorCount;
		}
	}
}
