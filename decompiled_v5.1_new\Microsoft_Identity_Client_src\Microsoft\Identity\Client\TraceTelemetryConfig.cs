#define TRACE
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Text.Json.Nodes;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

[Obsolete("Telemetry is sent automatically by MSAL.NET. See https://aka.ms/msal-net-telemetry.", false)]
[EditorBrowsable(EditorBrowsableState.Never)]
public class TraceTelemetryConfig : ITelemetryConfig
{
	public TelemetryAudienceType AudienceType => TelemetryAudienceType.PreProduction;

	public string SessionId { get; }

	public Action<ITelemetryEventPayload> DispatchAction => delegate(ITelemetryEventPayload payload)
	{
		JsonObject jsonObject = new JsonObject();
		foreach (KeyValuePair<string, bool> boolValue in payload.BoolValues)
		{
			jsonObject[boolValue.Key] = boolValue.Value;
		}
		foreach (KeyValuePair<string, int> intValue in payload.IntValues)
		{
			jsonObject[intValue.Key] = intValue.Value;
		}
		foreach (KeyValuePair<string, long> int64Value in payload.Int64Values)
		{
			jsonObject[int64Value.Key] = int64Value.Value;
		}
		foreach (KeyValuePair<string, string> stringValue in payload.StringValues)
		{
			jsonObject[stringValue.Key] = stringValue.Value;
		}
		Trace.TraceInformation(JsonHelper.JsonObjectToString(jsonObject));
		Trace.Flush();
	};

	public IEnumerable<string> AllowedScopes => CollectionHelpers.GetEmptyReadOnlyList<string>();

	public TraceTelemetryConfig()
	{
		SessionId = Guid.NewGuid().ToString();
	}
}
