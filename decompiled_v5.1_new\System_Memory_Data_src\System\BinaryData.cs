using System.ComponentModel;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace System;

public class BinaryData
{
	private const int CopyToBufferSize = 81920;

	private readonly ReadOnlyMemory<byte> _bytes;

	public BinaryData(byte[] data)
	{
		if (data == null)
		{
			throw new ArgumentNullException("data");
		}
		_bytes = data;
	}

	public BinaryData(object? jsonSerializable, JsonSerializerOptions? options = null, Type? type = null)
	{
		_bytes = JsonSerializer.SerializeToUtf8Bytes(jsonSerializable, type ?? jsonSerializable?.GetType() ?? typeof(object), options);
	}

	public BinaryData(ReadOnlyMemory<byte> data)
	{
		_bytes = data;
	}

	public BinaryData(string data)
	{
		if (data == null)
		{
			throw new ArgumentNullException("data");
		}
		_bytes = Encoding.UTF8.GetBytes(data);
	}

	public static BinaryData FromBytes(ReadOnlyMemory<byte> data)
	{
		return new BinaryData(data);
	}

	public static BinaryData FromBytes(byte[] data)
	{
		return new BinaryData(data);
	}

	public static BinaryData FromString(string data)
	{
		return new BinaryData(data);
	}

	public static BinaryData FromStream(Stream stream)
	{
		if (stream == null)
		{
			throw new ArgumentNullException("stream");
		}
		return FromStreamAsync(stream, async: false).GetAwaiter().GetResult();
	}

	public static Task<BinaryData> FromStreamAsync(Stream stream, CancellationToken cancellationToken = default(CancellationToken))
	{
		if (stream == null)
		{
			throw new ArgumentNullException("stream");
		}
		return FromStreamAsync(stream, async: true, cancellationToken);
	}

	private static async Task<BinaryData> FromStreamAsync(Stream stream, bool async, CancellationToken cancellationToken = default(CancellationToken))
	{
		int capacity = 0;
		if (stream.CanSeek)
		{
			long num = stream.Length - stream.Position;
			if (num > int.MaxValue)
			{
				throw new ArgumentOutOfRangeException("stream", "Stream length must be less than Int32.MaxValue");
			}
			capacity = (int)num;
		}
		using MemoryStream memoryStream = (stream.CanSeek ? new MemoryStream(capacity) : new MemoryStream());
		if (async)
		{
			await stream.CopyToAsync(memoryStream, 81920, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		}
		else
		{
			stream.CopyTo(memoryStream);
		}
		return new BinaryData(memoryStream.GetBuffer().AsMemory(0, (int)memoryStream.Position));
	}

	public static BinaryData FromObjectAsJson<T>(T jsonSerializable, JsonSerializerOptions? options = null)
	{
		return new BinaryData(JsonSerializer.SerializeToUtf8Bytes(jsonSerializable, typeof(T), options));
	}

	public override string ToString()
	{
		if (MemoryMarshal.TryGetArray(_bytes, out var segment))
		{
			return Encoding.UTF8.GetString(segment.Array, segment.Offset, segment.Count);
		}
		return Encoding.UTF8.GetString(_bytes.ToArray());
	}

	public Stream ToStream()
	{
		return new System.IO.ReadOnlyMemoryStream(_bytes);
	}

	public ReadOnlyMemory<byte> ToMemory()
	{
		return _bytes;
	}

	public byte[] ToArray()
	{
		return _bytes.ToArray();
	}

	public T ToObjectFromJson<T>(JsonSerializerOptions? options = null)
	{
		return (T)JsonSerializer.Deserialize(_bytes.Span, typeof(T), options);
	}

	public static implicit operator ReadOnlyMemory<byte>(BinaryData? data)
	{
		return data?._bytes ?? default(ReadOnlyMemory<byte>);
	}

	public static implicit operator ReadOnlySpan<byte>(BinaryData? data)
	{
		return data?._bytes.Span ?? default(ReadOnlySpan<byte>);
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	public override bool Equals(object? obj)
	{
		return this == obj;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	public override int GetHashCode()
	{
		return base.GetHashCode();
	}
}
