using System;
using System.Collections.Concurrent;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.PlatformsCommon.Shared;

[Preserve(AllMembers = true)]
internal class CommonCryptographyManager : ICryptographyManager
{
	private static readonly ConcurrentDictionary<string, RSA> s_certificateToRsaMap = new ConcurrentDictionary<string, RSA>();

	private static readonly int s_maximumMapSize = 1000;

	protected ILoggerAdapter Logger { get; }

	public CommonCryptographyManager(ILoggerAdapter logger = null)
	{
		Logger = logger;
	}

	public string CreateBase64UrlEncodedSha256Hash(string input)
	{
		if (!string.IsNullOrEmpty(input))
		{
			return Base64UrlHelpers.Encode(CreateSha256HashBytes(input));
		}
		return null;
	}

	public string GenerateCodeVerifier()
	{
		byte[] array = new byte[96];
		using (RandomNumberGenerator randomNumberGenerator = RandomNumberGenerator.Create())
		{
			randomNumberGenerator.GetBytes(array);
		}
		return Base64UrlHelpers.Encode(array);
	}

	public string CreateSha256Hash(string input)
	{
		if (!string.IsNullOrEmpty(input))
		{
			return Convert.ToBase64String(CreateSha256HashBytes(input));
		}
		return null;
	}

	public byte[] CreateSha256HashBytes(string input)
	{
		using SHA256 sHA = SHA256.Create();
		return sHA.ComputeHash(Encoding.UTF8.GetBytes(input));
	}

	public virtual byte[] SignWithCertificate(string message, X509Certificate2 certificate, RSASignaturePadding signaturePadding)
	{
		if (!s_certificateToRsaMap.TryGetValue(certificate.Thumbprint, out var rsa))
		{
			if (s_certificateToRsaMap.Count >= s_maximumMapSize)
			{
				s_certificateToRsaMap.Clear();
			}
			rsa = certificate.GetRSAPrivateKey();
		}
		if (rsa == null)
		{
			throw new MsalClientException("certificate_not_rsa", MsalErrorMessage.CertMustBeRsa(certificate.PublicKey?.Oid?.FriendlyName));
		}
		try
		{
			return SignDataAndCacheProvider(message);
		}
		catch (Exception value)
		{
			Logger?.Warning($"Exception occurred when signing data with a certificate. {value}");
			rsa = certificate.GetRSAPrivateKey();
			return SignDataAndCacheProvider(message);
		}
		byte[] SignDataAndCacheProvider(string s)
		{
			byte[] result = rsa.SignData(Encoding.UTF8.GetBytes(s), HashAlgorithmName.SHA256, signaturePadding);
			s_certificateToRsaMap[certificate.Thumbprint] = rsa;
			return result;
		}
	}
}
