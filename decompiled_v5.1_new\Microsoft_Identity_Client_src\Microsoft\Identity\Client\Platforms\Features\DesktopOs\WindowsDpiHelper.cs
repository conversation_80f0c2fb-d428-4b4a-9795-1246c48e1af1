using System;
using System.Runtime.InteropServices;

namespace Microsoft.Identity.Client.Platforms.Features.DesktopOs;

internal static class WindowsDpiHelper
{
	public static int ZoomPercent { get; }

	static WindowsDpiHelper()
	{
		IntPtr dC = GetDC(IntPtr.Zero);
		double num;
		double num2;
		if (dC != IntPtr.Zero)
		{
			num = GetDeviceCaps(dC, 88);
			num2 = GetDeviceCaps(dC, 90);
			ReleaseDC(IntPtr.Zero, dC);
		}
		else
		{
			num = 96.0;
			num2 = 96.0;
		}
		int val = (int)(100.0 * (num / 96.0));
		int val2 = (int)(100.0 * (num2 / 96.0));
		ZoomPercent = Math.Min(val, val2);
	}

	[DllImport("User32.dll", CallingConvention = CallingConvention.StdCall, ExactSpelling = true)]
	internal static extern IntPtr GetDC(IntPtr hWnd);

	[DllImport("User32.dll", CallingConvention = CallingConvention.StdCall, ExactSpelling = true)]
	internal static extern int ReleaseDC(IntPtr hWnd, IntPtr hDC);

	[DllImport("Gdi32.dll", CallingConvention = CallingConvention.StdCall, ExactSpelling = true)]
	internal static extern int GetDeviceCaps(IntPtr hdc, int nIndex);

	[DllImport("User32.dll", ExactSpelling = true)]
	internal static extern bool IsProcessDPIAware();
}
