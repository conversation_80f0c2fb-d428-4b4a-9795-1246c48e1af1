using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Executors;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.Instance.Discovery;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Internal.Broker;
using Microsoft.Identity.Client.Internal.Requests;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

public abstract class ClientApplicationBase : ApplicationBase, IClientApplicationBase, IApplicationBase
{
	public IAppConfig AppConfig => base.ServiceBundle.Config;

	public ITokenCache UserTokenCache => UserTokenCacheInternal;

	internal ITokenCacheInternal UserTokenCacheInternal { get; }

	public string Authority => base.ServiceBundle.Config.Authority.AuthorityInfo.CanonicalAuthority?.ToString();

	internal AuthorityInfo AuthorityInfo => base.ServiceBundle.Config.Authority.AuthorityInfo;

	[EditorBrowsable(EditorBrowsableState.Never)]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[Obsolete("Use GetAccountsAsync instead (See https://aka.ms/msal-net-2-released)", true)]
	public IEnumerable<IUser> Users
	{
		get
		{
			throw new NotImplementedException();
		}
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use WithComponent on AbstractApplicationBuilder<T> to configure this instead.See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public string Component { get; set; }

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use ExtraQueryParameters on each call instead.See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public string SliceParameters { get; set; }

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Can be set on AbstractApplicationBuilder<T>.WithAuthority as needed.See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public bool ValidateAuthority { get; set; }

	[Obsolete("Should be set using AbstractApplicationBuilder<T>.WithRedirectUri and can be viewed with ClientApplicationBase.AppConfig.RedirectUri.See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public string RedirectUri { get; set; }

	[Obsolete("Use AppConfig.ClientId instead.See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public string ClientId => AppConfig.ClientId;

	internal ClientApplicationBase(ApplicationConfiguration config)
		: base(config)
	{
		UserTokenCacheInternal = config.UserTokenCacheInternalForTest ?? new TokenCache(base.ServiceBundle, isApplicationTokenCache: false, config.UserTokenLegacyCachePersistenceForTest);
	}

	public Task<IEnumerable<IAccount>> GetAccountsAsync()
	{
		return GetAccountsAsync(default(CancellationToken));
	}

	public Task<IEnumerable<IAccount>> GetAccountsAsync(CancellationToken cancellationToken = default(CancellationToken))
	{
		return GetAccountsInternalAsync(ApiEvent.ApiIds.GetAccounts, null, cancellationToken);
	}

	public Task<IEnumerable<IAccount>> GetAccountsAsync(string userFlow)
	{
		return GetAccountsAsync(userFlow, default(CancellationToken));
	}

	public async Task<IEnumerable<IAccount>> GetAccountsAsync(string userFlow, CancellationToken cancellationToken = default(CancellationToken))
	{
		if (string.IsNullOrWhiteSpace(userFlow))
		{
			throw new ArgumentException("userFlow should not be null or whitespace", "userFlow");
		}
		return (await GetAccountsInternalAsync(ApiEvent.ApiIds.GetAccountsByUserFlow, null, cancellationToken).ConfigureAwait(continueOnCapturedContext: false)).Where((IAccount acc) => acc.HomeAccountId.ObjectId.EndsWith(userFlow, StringComparison.OrdinalIgnoreCase));
	}

	public async Task<IAccount> GetAccountAsync(string accountId, CancellationToken cancellationToken = default(CancellationToken))
	{
		return (await GetAccountsInternalAsync(ApiEvent.ApiIds.GetAccountById, accountId, cancellationToken).ConfigureAwait(continueOnCapturedContext: false)).SingleOrDefault();
	}

	public async Task<IAccount> GetAccountAsync(string accountId)
	{
		if (!string.IsNullOrWhiteSpace(accountId))
		{
			return await GetAccountAsync(accountId, default(CancellationToken)).ConfigureAwait(continueOnCapturedContext: false);
		}
		return null;
	}

	public Task RemoveAsync(IAccount account)
	{
		return RemoveAsync(account, default(CancellationToken));
	}

	public async Task RemoveAsync(IAccount account, CancellationToken cancellationToken = default(CancellationToken))
	{
		Guid correlationId = Guid.NewGuid();
		RequestContext requestContext = CreateRequestContext(correlationId, cancellationToken);
		requestContext.ApiEvent = new ApiEvent(correlationId);
		requestContext.ApiEvent.ApiId = ApiEvent.ApiIds.RemoveAccount;
		Authority authority = await Microsoft.Identity.Client.Instance.Authority.CreateAuthorityForRequestAsync(requestContext, null).ConfigureAwait(continueOnCapturedContext: false);
		AuthenticationRequestParameters requestParameters = new AuthenticationRequestParameters(base.ServiceBundle, UserTokenCacheInternal, new AcquireTokenCommonParameters
		{
			ApiId = requestContext.ApiEvent.ApiId
		}, requestContext, authority);
		if (account != null && UserTokenCacheInternal != null)
		{
			await UserTokenCacheInternal.RemoveAccountAsync(account, requestParameters).ConfigureAwait(continueOnCapturedContext: false);
		}
		if (AppConfig.IsBrokerEnabled && base.ServiceBundle.PlatformProxy.CanBrokerSupportSilentAuth())
		{
			cancellationToken.ThrowIfCancellationRequested();
			IBroker broker = base.ServiceBundle.PlatformProxy.CreateBroker(base.ServiceBundle.Config, null);
			if (broker.IsBrokerInstalledAndInvokable(authority.AuthorityInfo.AuthorityType))
			{
				await broker.RemoveAccountAsync(base.ServiceBundle.Config, account).ConfigureAwait(continueOnCapturedContext: false);
			}
		}
	}

	private async Task<IEnumerable<IAccount>> GetAccountsInternalAsync(ApiEvent.ApiIds apiId, string homeAccountIdFilter, CancellationToken cancellationToken)
	{
		Guid correlationId = Guid.NewGuid();
		RequestContext requestContext = CreateRequestContext(correlationId, cancellationToken);
		requestContext.ApiEvent = new ApiEvent(correlationId);
		requestContext.ApiEvent.ApiId = apiId;
		Authority initialAuthority = await Microsoft.Identity.Client.Instance.Authority.CreateAuthorityForRequestAsync(requestContext, null).ConfigureAwait(continueOnCapturedContext: false);
		AuthenticationRequestParameters requestParams = new AuthenticationRequestParameters(base.ServiceBundle, UserTokenCacheInternal, new AcquireTokenCommonParameters
		{
			ApiId = apiId
		}, requestContext, initialAuthority, homeAccountIdFilter);
		CacheSessionManager cacheSessionManager = new CacheSessionManager(UserTokenCacheInternal, requestParams);
		IEnumerable<IAccount> accountsFromCache = await cacheSessionManager.GetAccountsAsync().ConfigureAwait(continueOnCapturedContext: false);
		IEnumerable<IAccount> accountsFromBroker = await GetAccountsFromBrokerAsync(homeAccountIdFilter, cacheSessionManager, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		if (accountsFromCache == null)
		{
			accountsFromCache = Enumerable.Empty<IAccount>();
		}
		if (accountsFromBroker == null)
		{
			accountsFromBroker = Enumerable.Empty<IAccount>();
		}
		base.ServiceBundle.ApplicationLogger.Info(() => $"Found {accountsFromCache.Count()} cache accounts and {accountsFromBroker.Count()} broker accounts");
		IEnumerable<IAccount> cacheAndBrokerAccounts = MergeAccounts(accountsFromCache, accountsFromBroker);
		base.ServiceBundle.ApplicationLogger.Info(() => $"Returning {cacheAndBrokerAccounts.Count()} accounts");
		return cacheAndBrokerAccounts;
	}

	private async Task<IEnumerable<IAccount>> GetAccountsFromBrokerAsync(string homeAccountIdFilter, ICacheSessionManager cacheSessionManager, CancellationToken cancellationToken)
	{
		if (AppConfig.IsBrokerEnabled && base.ServiceBundle.PlatformProxy.CanBrokerSupportSilentAuth())
		{
			IBroker broker = base.ServiceBundle.PlatformProxy.CreateBroker(base.ServiceBundle.Config, null);
			if (broker.IsBrokerInstalledAndInvokable(base.ServiceBundle.Config.Authority.AuthorityInfo.AuthorityType))
			{
				IEnumerable<IAccount> enumerable = (await broker.GetAccountsAsync(AppConfig.ClientId, AppConfig.RedirectUri, AuthorityInfo, cacheSessionManager, base.ServiceBundle.InstanceDiscoveryManager).ConfigureAwait(continueOnCapturedContext: false)) ?? Enumerable.Empty<IAccount>();
				if (!string.IsNullOrEmpty(homeAccountIdFilter))
				{
					enumerable = enumerable.Where((IAccount acc) => homeAccountIdFilter.Equals(acc.HomeAccountId.Identifier, StringComparison.OrdinalIgnoreCase));
				}
				return await FilterBrokerAccountsByEnvAsync(enumerable, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			}
		}
		return Enumerable.Empty<IAccount>();
	}

	private async Task<IEnumerable<IAccount>> FilterBrokerAccountsByEnvAsync(IEnumerable<IAccount> brokerAccounts, CancellationToken cancellationToken)
	{
		base.ServiceBundle.ApplicationLogger.Verbose(() => "Filtering broker accounts by environment. Before filtering: " + brokerAccounts.Count());
		ISet<string> existingEnvironmentsInCache = new HashSet<string>(brokerAccounts.Select((IAccount aci) => aci.Environment), StringComparer.OrdinalIgnoreCase);
		InstanceDiscoveryMetadataEntry instanceMetadata = await base.ServiceBundle.InstanceDiscoveryManager.GetMetadataEntryTryAvoidNetworkAsync(AuthorityInfo, existingEnvironmentsInCache, CreateRequestContext(Guid.NewGuid(), cancellationToken)).ConfigureAwait(continueOnCapturedContext: false);
		brokerAccounts = brokerAccounts.Where((IAccount acc) => instanceMetadata.Aliases.ContainsOrdinalIgnoreCase(acc.Environment));
		base.ServiceBundle.ApplicationLogger.Verbose(() => "After filtering: " + brokerAccounts.Count());
		return brokerAccounts;
	}

	private IEnumerable<IAccount> MergeAccounts(IEnumerable<IAccount> cacheAccounts, IEnumerable<IAccount> brokerAccounts)
	{
		List<IAccount> list = new List<IAccount>(cacheAccounts);
		foreach (IAccount account in brokerAccounts)
		{
			if (!list.Any((IAccount x) => x.HomeAccountId.Equals(account.HomeAccountId)))
			{
				list.Add(account);
				continue;
			}
			base.ServiceBundle.ApplicationLogger.InfoPii(() => "Account merge eliminated broker account with ID: " + account.HomeAccountId, () => "Account merge eliminated an account");
		}
		return list;
	}

	internal RequestContext CreateRequestContext(Guid correlationId, CancellationToken cancellationToken)
	{
		return new RequestContext(base.ServiceBundle, correlationId, cancellationToken);
	}

	public AcquireTokenSilentParameterBuilder AcquireTokenSilent(IEnumerable<string> scopes, IAccount account)
	{
		return AcquireTokenSilentParameterBuilder.Create(ClientExecutorFactory.CreateClientApplicationBaseExecutor(this), scopes, account);
	}

	public AcquireTokenSilentParameterBuilder AcquireTokenSilent(IEnumerable<string> scopes, string loginHint)
	{
		if (string.IsNullOrWhiteSpace(loginHint))
		{
			throw new ArgumentNullException("loginHint");
		}
		return AcquireTokenSilentParameterBuilder.Create(ClientExecutorFactory.CreateClientApplicationBaseExecutor(this), scopes, loginHint);
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use GetAccountAsync instead and pass IAccount.HomeAccountId.Identifier (See https://aka.ms/msal-net-2-released)", true)]
	public IUser GetUser(string identifier)
	{
		throw new NotImplementedException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use RemoveAccountAsync instead (See https://aka.ms/msal-net-2-released)", true)]
	public void Remove(IUser user)
	{
		throw new NotImplementedException();
	}

	[Obsolete("Use AcquireTokenSilent instead.See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public Task<AuthenticationResult> AcquireTokenSilentAsync(IEnumerable<string> scopes, IAccount account, string authority, bool forceRefresh)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[Obsolete("Use AcquireTokenSilent instead.See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public Task<AuthenticationResult> AcquireTokenSilentAsync(IEnumerable<string> scopes, IAccount account)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}
}
