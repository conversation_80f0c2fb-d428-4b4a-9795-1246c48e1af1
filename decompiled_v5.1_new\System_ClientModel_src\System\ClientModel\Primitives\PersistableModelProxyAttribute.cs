using System.Diagnostics.CodeAnalysis;

namespace System.ClientModel.Primitives;

[AttributeUsage(AttributeTargets.Class)]
public sealed class PersistableModelProxyAttribute : Attribute
{
	[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.NonPublicConstructors)]
	public Type ProxyType { get; }

	public PersistableModelProxyAttribute([DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.NonPublicConstructors)] Type proxyType)
	{
		ProxyType = proxyType;
	}
}
