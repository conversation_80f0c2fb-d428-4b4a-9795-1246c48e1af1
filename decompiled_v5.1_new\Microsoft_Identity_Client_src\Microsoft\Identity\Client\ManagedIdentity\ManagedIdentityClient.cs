using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.ManagedIdentity;

internal class ManagedIdentityClient
{
	private readonly AbstractManagedIdentity _identitySource;

	internal static Lazy<ManagedIdentitySource> s_managedIdentitySourceDetected = new Lazy<ManagedIdentitySource>(() => GetManagedIdentitySource());

	internal static void resetCachedSource()
	{
		s_managedIdentitySourceDetected = new Lazy<ManagedIdentitySource>(() => GetManagedIdentitySource());
	}

	public ManagedIdentityClient(RequestContext requestContext)
	{
		using (requestContext.Logger.LogMethodDuration(LogLevel.Verbose, ".ctor", "/_/src/client/Microsoft.Identity.Client/ManagedIdentity/ManagedIdentityClient.cs"))
		{
			_identitySource = SelectManagedIdentitySource(requestContext);
		}
	}

	internal Task<ManagedIdentityResponse> SendTokenRequestForManagedIdentityAsync(AcquireTokenForManagedIdentityParameters parameters, CancellationToken cancellationToken)
	{
		return _identitySource.AuthenticateAsync(parameters, cancellationToken);
	}

	private static AbstractManagedIdentity SelectManagedIdentitySource(RequestContext requestContext)
	{
		return s_managedIdentitySourceDetected.Value switch
		{
			ManagedIdentitySource.ServiceFabric => ServiceFabricManagedIdentitySource.Create(requestContext), 
			ManagedIdentitySource.AppService => AppServiceManagedIdentitySource.Create(requestContext), 
			ManagedIdentitySource.CloudShell => CloudShellManagedIdentitySource.Create(requestContext), 
			ManagedIdentitySource.AzureArc => AzureArcManagedIdentitySource.Create(requestContext), 
			_ => new ImdsManagedIdentitySource(requestContext), 
		};
	}

	private static ManagedIdentitySource GetManagedIdentitySource()
	{
		string identityEndpoint = EnvironmentVariables.IdentityEndpoint;
		string identityHeader = EnvironmentVariables.IdentityHeader;
		string identityServerThumbprint = EnvironmentVariables.IdentityServerThumbprint;
		_ = EnvironmentVariables.IdentityHeader;
		string msiEndpoint = EnvironmentVariables.MsiEndpoint;
		string imdsEndpoint = EnvironmentVariables.ImdsEndpoint;
		_ = EnvironmentVariables.PodIdentityEndpoint;
		if (!string.IsNullOrEmpty(identityEndpoint) && !string.IsNullOrEmpty(identityHeader))
		{
			if (!string.IsNullOrEmpty(identityServerThumbprint))
			{
				return ManagedIdentitySource.ServiceFabric;
			}
			return ManagedIdentitySource.AppService;
		}
		if (!string.IsNullOrEmpty(msiEndpoint))
		{
			return ManagedIdentitySource.CloudShell;
		}
		if (!string.IsNullOrEmpty(identityEndpoint) && !string.IsNullOrEmpty(imdsEndpoint))
		{
			return ManagedIdentitySource.AzureArc;
		}
		return ManagedIdentitySource.DefaultToImds;
	}
}
