<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AssemblyName>BiliveDanmakuAgent</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <PropertyGroup>
    <LangVersion>12.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup />
  <ItemGroup />
  <ItemGroup>
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\..\Relesez5.1\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="log4net">
      <HintPath>..\..\Relesez5.1\log4net.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipelines">
      <HintPath>C:\Program Files\dotnet\shared\Microsoft.AspNetCore.App\8.0.4\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="Nerdbank.Streams">
      <HintPath>..\..\Relesez5.1\Nerdbank.Streams.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>