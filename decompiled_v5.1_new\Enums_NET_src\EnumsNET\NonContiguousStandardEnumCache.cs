using System;
using System.Runtime.CompilerServices;
using EnumsNET.Numerics;

namespace EnumsNET;

internal sealed class NonContiguousStandardEnumCache<TUnderlying, TUnderlyingOperations> : StandardEnumCache<TUnderlying, TUnderlyingOperations> where TUnderlying : struct, IComparable<TUnderlying>, IEquatable<TUnderlying>, IConvertible where TUnderlyingOperations : struct, IUnderlyingOperations<TUnderlying>
{
	public NonContiguousStandardEnumCache(Type enumType, IEnumBridge<TUnderlying, TUnderlyingOperations> enumBridge, EnumMemberInternal<TUnderlying, TUnderlyingOperations>[] members, EnumMemberInternal<TUnderlying, TUnderlyingOperations>?[] buckets, TUnderlying allFlags, int distinctCount, object? customValidator)
		: base(enumType, enumBridge, members, buckets, allFlags, distinctCount, isContiguous: false, customValidator)
	{
	}

	public sealed override string AsString(ref byte value)
	{
		return AsString(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	public sealed override string AsString(object value)
	{
		return AsString(ToObject(value));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public new string AsString(TUnderlying value)
	{
		EnumMemberInternal<TUnderlying, TUnderlyingOperations> member = GetMember(value);
		if (member == null)
		{
			return value.ToString();
		}
		return member.Name;
	}

	public override bool TryFormat(ref byte value, Span<char> destination, out int charsWritten)
	{
		return TryFormat(UnsafeUtility.As<byte, TUnderlying>(ref value), destination, out charsWritten);
	}

	public override bool TryFormat(object value, Span<char> destination, out int charsWritten)
	{
		return TryFormat(ToObject(value), destination, out charsWritten);
	}

	public bool TryFormat(TUnderlying value, Span<char> destination, out int charsWritten)
	{
		EnumMemberInternal<TUnderlying, TUnderlyingOperations> member = GetMember(value);
		if (member != null)
		{
			return EnumCache.TryWriteNonNullableStringToSpan(member.Name, destination, out charsWritten);
		}
		return default(TUnderlyingOperations).TryFormat(value, destination, out charsWritten);
	}

	public override bool IsDefined(ref byte value)
	{
		return IsDefined(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	public override bool IsDefined(object value)
	{
		return IsDefined(ToObject(value));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public new bool IsDefined(TUnderlying value)
	{
		return GetMember(value) != null;
	}
}
