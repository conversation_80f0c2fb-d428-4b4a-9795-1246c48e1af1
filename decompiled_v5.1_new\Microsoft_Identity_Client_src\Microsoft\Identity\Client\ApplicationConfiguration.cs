using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;
using Microsoft.Identity.Client.AppConfig;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Extensibility;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.Instance.Discovery;
using Microsoft.Identity.Client.Internal.Broker;
using Microsoft.Identity.Client.Internal.ClientCredential;
using Microsoft.Identity.Client.Kerberos;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.UI;
using Microsoft.IdentityModel.Abstractions;

namespace Microsoft.Identity.Client;

internal sealed class ApplicationConfiguration : IAppConfig
{
	public const string DefaultClientName = "UnknownClient";

	public const string DefaultClientVersion = "0.0.0.0";

	private string _clientName = "UnknownClient";

	private string _clientVersion = "0.0.0.0";

	public Func<AppTokenProviderParameters, Task<AppTokenProviderResult>> AppTokenProvider;

	public string ClientName
	{
		get
		{
			return _clientName;
		}
		internal set
		{
			_clientName = (string.IsNullOrWhiteSpace(value) ? "UnknownClient" : value);
		}
	}

	public string ClientVersion
	{
		get
		{
			return _clientVersion;
		}
		internal set
		{
			_clientVersion = (string.IsNullOrWhiteSpace(value) ? "0.0.0.0" : value);
		}
	}

	public ITelemetryClient[] TelemetryClients { get; internal set; } = Array.Empty<ITelemetryClient>();

	public Func<object> ParentActivityOrWindowFunc { get; internal set; }

	public string IosKeychainSecurityGroup { get; internal set; }

	public bool IsBrokerEnabled { get; internal set; }

	public bool IsWebviewSsoPolicyEnabled { get; internal set; }

	public BrokerOptions BrokerOptions { get; set; }

	public Func<CoreUIParent, ApplicationConfiguration, ILoggerAdapter, IBroker> BrokerCreatorFunc { get; set; }

	public Func<IWebUIFactory> WebUiFactoryCreator { get; set; }

	public string KerberosServicePrincipalName { get; set; } = string.Empty;

	public KerberosTicketContainer TicketContainer { get; set; }

	[Obsolete("Telemetry is sent automatically by MSAL.NET. See https://aka.ms/msal-net-telemetry.")]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public ITelemetryConfig TelemetryConfig { get; internal set; }

	public IHttpManager HttpManager { get; internal set; }

	public IPlatformProxy PlatformProxy { get; internal set; }

	public CacheOptions AccessorOptions { get; set; }

	public Authority Authority { get; internal set; }

	public string ClientId { get; internal set; }

	public string RedirectUri { get; internal set; }

	public bool EnablePiiLogging { get; internal set; }

	public LogLevel LogLevel { get; internal set; } = LogLevel.Info;

	public bool IsDefaultPlatformLoggingEnabled { get; internal set; }

	public IMsalHttpClientFactory HttpClientFactory { get; internal set; }

	public bool IsExtendedTokenLifetimeEnabled { get; set; }

	public LogCallback LoggingCallback { get; internal set; }

	public IIdentityLogger IdentityLogger { get; internal set; }

	public string Component { get; internal set; }

	public IDictionary<string, string> ExtraQueryParameters { get; internal set; } = new Dictionary<string, string>();

	public bool UseRecommendedDefaultRedirectUri { get; internal set; }

	public bool ExperimentalFeaturesEnabled { get; set; }

	public IEnumerable<string> ClientCapabilities { get; set; }

	public bool SendX5C { get; internal set; }

	public bool LegacyCacheCompatibilityEnabled { get; internal set; } = true;

	public bool CacheSynchronizationEnabled { get; internal set; } = true;

	public bool MultiCloudSupportEnabled { get; set; }

	public bool RetryOnServerErrors { get; set; } = true;

	public ManagedIdentityId ManagedIdentityId { get; internal set; }

	public bool IsManagedIdentity { get; }

	public bool IsConfidentialClient { get; }

	public bool IsPublicClient
	{
		get
		{
			if (!IsConfidentialClient)
			{
				return !IsManagedIdentity;
			}
			return false;
		}
	}

	public IClientCredential ClientCredential { get; internal set; }

	public string ClientSecret
	{
		get
		{
			if (ClientCredential is SecretStringClientCredential secretStringClientCredential)
			{
				return secretStringClientCredential.Secret;
			}
			return null;
		}
	}

	public X509Certificate2 ClientCredentialCertificate
	{
		get
		{
			if (ClientCredential is CertificateAndClaimsClientCredential certificateAndClaimsClientCredential)
			{
				return certificateAndClaimsClientCredential.Certificate;
			}
			return null;
		}
	}

	public string AzureRegion { get; set; }

	public string TenantId { get; internal set; }

	public InstanceDiscoveryResponse CustomInstanceDiscoveryMetadata { get; set; }

	public Uri CustomInstanceDiscoveryMetadataUri { get; set; }

	public AadAuthorityAudience AadAuthorityAudience { get; set; }

	public AzureCloudInstance AzureCloudInstance { get; set; }

	public string Instance { get; set; }

	public bool ValidateAuthority { get; set; }

	public ILegacyCachePersistence UserTokenLegacyCachePersistenceForTest { get; set; }

	public ITokenCacheInternal UserTokenCacheInternalForTest { get; set; }

	public ITokenCacheInternal AppTokenCacheInternalForTest { get; set; }

	public IDeviceAuthManager DeviceAuthManagerForTest { get; set; }

	public bool IsInstanceDiscoveryEnabled { get; internal set; } = true;

	public ApplicationConfiguration(MsalClientType applicationType)
	{
		switch (applicationType)
		{
		case MsalClientType.ConfidentialClient:
			IsConfidentialClient = true;
			break;
		case MsalClientType.ManagedIdentityClient:
			IsManagedIdentity = true;
			break;
		}
	}
}
