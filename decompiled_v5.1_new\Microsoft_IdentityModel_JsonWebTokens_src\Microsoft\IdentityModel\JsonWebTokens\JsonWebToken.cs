using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using Microsoft.IdentityModel.Logging;
using Microsoft.IdentityModel.Tokens;

namespace Microsoft.IdentityModel.JsonWebTokens;

public class JsonWebToken : SecurityToken
{
	private char[] _hChars;

	private ClaimsIdentity _claimsIdentity;

	private bool _wasClaimsIdentitySet;

	private string _act;

	private string _alg;

	private IList<string> _audiences;

	private readonly object _audiencesLock = new object();

	private string _authenticationTag;

	private string _ciphertext;

	private string _cty;

	private string _enc;

	private string _encodedHeader;

	private string _encodedPayload;

	private string _encodedSignature;

	private string _encryptedKey;

	private DateTime? _iat;

	private string _id;

	private string _initializationVector;

	private string _iss;

	private string _kid;

	private string _sub;

	private string _typ;

	private DateTime? _validFrom;

	private DateTime? _validTo;

	private string _x5t;

	private string _zip;

	internal string ActualIssuer { get; set; }

	internal ClaimsIdentity ActorClaimsIdentity { get; set; }

	public string AuthenticationTag
	{
		get
		{
			if (_authenticationTag == null)
			{
				_authenticationTag = ((AuthenticationTagBytes == null) ? string.Empty : Encoding.UTF8.GetString(AuthenticationTagBytes));
			}
			return _authenticationTag;
		}
	}

	internal byte[] AuthenticationTagBytes { get; set; }

	public string Ciphertext
	{
		get
		{
			if (_ciphertext == null)
			{
				_ciphertext = ((CipherTextBytes == null) ? string.Empty : Encoding.UTF8.GetString(CipherTextBytes));
			}
			return _ciphertext;
		}
	}

	internal byte[] CipherTextBytes { get; set; }

	internal ClaimsIdentity ClaimsIdentity
	{
		get
		{
			if (!_wasClaimsIdentitySet)
			{
				_wasClaimsIdentitySet = true;
				string text = ActualIssuer ?? Issuer;
				foreach (Claim claim2 in Claims)
				{
					string type = claim2.Type;
					if (type == "http://schemas.xmlsoap.org/ws/2009/09/identity/claims/actor")
					{
						if (_claimsIdentity.Actor != null)
						{
							throw LogHelper.LogExceptionMessage(new InvalidOperationException(LogHelper.FormatInvariant("IDX14112: Only a single 'Actor' is supported. Found second claim of type: '{0}'", LogHelper.MarkAsNonPII("actort"), claim2.Value)));
						}
						try
						{
							new JsonWebToken(claim2.Value);
							_claimsIdentity.Actor = ActorClaimsIdentity;
						}
						catch
						{
						}
					}
					if (claim2.Properties.Count == 0)
					{
						_claimsIdentity.AddClaim(new Claim(type, claim2.Value, claim2.ValueType, text, text, _claimsIdentity));
						continue;
					}
					Claim claim = new Claim(type, claim2.Value, claim2.ValueType, text, text, _claimsIdentity);
					foreach (KeyValuePair<string, string> property in claim2.Properties)
					{
						claim.Properties[property.Key] = property.Value;
					}
					_claimsIdentity.AddClaim(claim);
				}
			}
			return _claimsIdentity;
		}
		set
		{
			_claimsIdentity = value;
		}
	}

	internal int Dot1 { get; set; }

	internal int Dot2 { get; set; }

	internal int Dot3 { get; set; }

	internal int Dot4 { get; set; }

	public string EncodedHeader
	{
		get
		{
			if (_encodedHeader == null)
			{
				if (EncodedToken != null)
				{
					_encodedHeader = EncodedToken.Substring(0, Dot1);
				}
				else
				{
					_encodedHeader = string.Empty;
				}
			}
			return _encodedHeader;
		}
	}

	public string EncryptedKey
	{
		get
		{
			if (_encryptedKey == null)
			{
				_encryptedKey = ((EncryptedKeyBytes == null) ? string.Empty : Encoding.UTF8.GetString(EncryptedKeyBytes));
			}
			return _encryptedKey;
		}
	}

	internal byte[] EncryptedKeyBytes { get; set; }

	public string EncodedPayload
	{
		get
		{
			if (_encodedPayload == null)
			{
				if (EncodedToken != null)
				{
					_encodedPayload = (IsEncrypted ? string.Empty : EncodedToken.Substring(Dot1 + 1, Dot2 - Dot1 - 1));
				}
				else
				{
					_encodedPayload = string.Empty;
				}
			}
			return _encodedPayload;
		}
	}

	public string EncodedSignature
	{
		get
		{
			if (_encodedSignature == null)
			{
				if (EncodedToken != null)
				{
					_encodedSignature = (IsEncrypted ? string.Empty : EncodedToken.Substring(Dot2 + 1, EncodedToken.Length - Dot2 - 1));
				}
				else
				{
					_encodedSignature = string.Empty;
				}
			}
			return _encodedSignature;
		}
	}

	public string EncodedToken { get; private set; }

	internal JsonClaimSet Header { get; set; }

	internal byte[] HeaderAsciiBytes { get; set; }

	internal byte[] InitializationVectorBytes { get; set; }

	public string InitializationVector
	{
		get
		{
			if (InitializationVectorBytes == null)
			{
				_initializationVector = ((InitializationVectorBytes == null) ? string.Empty : Encoding.UTF8.GetString(InitializationVectorBytes));
			}
			return _initializationVector;
		}
	}

	public JsonWebToken InnerToken { get; internal set; }

	public bool IsEncrypted => CipherTextBytes != null;

	public bool IsSigned { get; internal set; }

	internal JsonClaimSet Payload { get; set; }

	public override SecurityKey SecurityKey { get; }

	public override SecurityKey SigningKey { get; set; }

	internal byte[] MessageBytes { get; set; }

	internal int NumberOfDots { get; set; }

	public string Actor
	{
		get
		{
			if (_act == null)
			{
				_act = Payload.GetStringValue("actort");
			}
			return _act;
		}
	}

	public string Alg
	{
		get
		{
			if (_alg == null)
			{
				_alg = Header.GetStringValue("alg");
			}
			return _alg;
		}
	}

	public IEnumerable<string> Audiences
	{
		get
		{
			if (_audiences == null)
			{
				lock (_audiencesLock)
				{
					if (_audiences == null)
					{
						List<string> list = new List<string>();
						if (Payload.TryGetValue<JsonElement>("aud", out var value))
						{
							if (value.ValueKind == JsonValueKind.String)
							{
								list = new List<string> { value.GetString() };
							}
							if (value.ValueKind == JsonValueKind.Array)
							{
								foreach (JsonElement item in value.EnumerateArray())
								{
									list.Add(item.ToString());
								}
							}
						}
						_audiences = list;
					}
				}
			}
			return _audiences;
		}
	}

	public virtual IEnumerable<Claim> Claims => Payload.Claims(Issuer ?? "LOCAL AUTHORITY");

	public string Cty
	{
		get
		{
			if (_cty == null)
			{
				_cty = Header.GetStringValue("cty");
			}
			return _cty;
		}
	}

	public string Enc
	{
		get
		{
			if (_enc == null)
			{
				_enc = Header.GetStringValue("enc");
			}
			return _enc;
		}
	}

	public override string Id
	{
		get
		{
			if (_id == null)
			{
				_id = Payload.GetStringValue("jti");
			}
			return _id;
		}
	}

	public DateTime IssuedAt
	{
		get
		{
			if (!_iat.HasValue)
			{
				_iat = Payload.GetDateTime("iat");
			}
			return _iat.Value;
		}
	}

	public override string Issuer
	{
		get
		{
			if (_iss == null)
			{
				_iss = Payload.GetStringValue("iss");
			}
			return _iss;
		}
	}

	public string Kid
	{
		get
		{
			if (_kid == null)
			{
				_kid = Header.GetStringValue("kid");
			}
			return _kid;
		}
	}

	public string Subject
	{
		get
		{
			if (_sub == null)
			{
				_sub = Payload.GetStringValue("sub");
			}
			return _sub;
		}
	}

	public string Typ
	{
		get
		{
			if (_typ == null)
			{
				_typ = Header.GetStringValue("typ");
			}
			return _typ;
		}
	}

	public string X5t
	{
		get
		{
			if (_x5t == null)
			{
				_x5t = Header.GetStringValue("x5t");
			}
			return _x5t;
		}
	}

	public override DateTime ValidFrom
	{
		get
		{
			if (!_validFrom.HasValue)
			{
				_validFrom = Payload.GetDateTime("nbf");
			}
			return _validFrom.Value;
		}
	}

	public override DateTime ValidTo
	{
		get
		{
			if (!_validTo.HasValue)
			{
				_validTo = Payload.GetDateTime("exp");
			}
			return _validTo.Value;
		}
	}

	public string Zip
	{
		get
		{
			if (_zip == null)
			{
				_zip = Header.GetStringValue("zip");
			}
			return _zip;
		}
	}

	public JsonWebToken(string jwtEncodedString)
	{
		if (string.IsNullOrEmpty(jwtEncodedString))
		{
			throw LogHelper.LogExceptionMessage(new ArgumentNullException("jwtEncodedString"));
		}
		ReadToken(jwtEncodedString);
	}

	public JsonWebToken(string header, string payload)
	{
		if (string.IsNullOrEmpty(header))
		{
			throw LogHelper.LogArgumentNullException("header");
		}
		if (string.IsNullOrEmpty(payload))
		{
			throw LogHelper.LogArgumentNullException("payload");
		}
		try
		{
			Header = new JsonClaimSet(header);
		}
		catch (Exception innerException)
		{
			throw LogHelper.LogExceptionMessage(new ArgumentException(LogHelper.FormatInvariant("IDX14301: Unable to parse the header into a JSON object. \nHeader: '{0}'.", header), innerException));
		}
		try
		{
			Payload = new JsonClaimSet(payload);
		}
		catch (Exception innerException2)
		{
			throw LogHelper.LogExceptionMessage(new ArgumentException(LogHelper.FormatInvariant("IDX14302: Unable to parse the payload into a JSON object. \nPayload: '{0}'.", payload), innerException2));
		}
		_encodedHeader = Base64UrlEncoder.Encode(header);
		_encodedPayload = Base64UrlEncoder.Encode(payload);
		EncodedToken = _encodedHeader + "." + _encodedPayload + ".";
	}

	internal bool HasPayloadClaim(string claimName)
	{
		return Payload.HasClaim(claimName);
	}

	private void ReadToken(string encodedJson)
	{
		Dot1 = encodedJson.IndexOf('.');
		if (Dot1 == -1 || Dot1 == encodedJson.Length - 1)
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenMalformedException("IDX14100: JWT is not well formed, there are no dots (.).\nThe token needs to be in JWS or JWE Compact Serialization Format. (JWS): 'EncodedHeader.EndcodedPayload.EncodedSignature'. (JWE): 'EncodedProtectedHeader.EncodedEncryptedKey.EncodedInitializationVector.EncodedCiphertext.EncodedAuthenticationTag'."));
		}
		Dot2 = encodedJson.IndexOf('.', Dot1 + 1);
		if (Dot2 == -1)
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenMalformedException("IDX14120: JWT is not well formed, there is only one dot (.).\nThe token needs to be in JWS or JWE Compact Serialization Format. (JWS): 'EncodedHeader.EndcodedPayload.EncodedSignature'. (JWE): 'EncodedProtectedHeader.EncodedEncryptedKey.EncodedInitializationVector.EncodedCiphertext.EncodedAuthenticationTag'."));
		}
		if (Dot2 == encodedJson.Length - 1)
		{
			Dot3 = -1;
		}
		else
		{
			Dot3 = encodedJson.IndexOf('.', Dot2 + 1);
		}
		if (Dot3 == -1)
		{
			IsSigned = Dot2 + 1 != encodedJson.Length;
			JsonDocument jsonDocument = null;
			try
			{
				jsonDocument = JwtTokenUtilities.GetJsonDocumentFromBase64UrlEncodedString(encodedJson, 0, Dot1);
				Header = new JsonClaimSet(jsonDocument);
			}
			catch (Exception innerException)
			{
				throw LogHelper.LogExceptionMessage(new ArgumentException(LogHelper.FormatInvariant("IDX14102: Unable to decode the header '{0}' as Base64Url encoded string.", LogHelper.MarkAsUnsafeSecurityArtifact(encodedJson.Substring(0, Dot1), (object t) => t.ToString())), innerException));
			}
			finally
			{
				jsonDocument?.Dispose();
			}
			JsonDocument jsonDocument2 = null;
			try
			{
				jsonDocument2 = JwtTokenUtilities.GetJsonDocumentFromBase64UrlEncodedString(encodedJson, Dot1 + 1, Dot2 - Dot1 - 1);
				Payload = new JsonClaimSet(jsonDocument2);
			}
			catch (Exception innerException2)
			{
				throw LogHelper.LogExceptionMessage(new ArgumentException(LogHelper.FormatInvariant("IDX14101: Unable to decode the payload '{0}' as Base64Url encoded string.", LogHelper.MarkAsUnsafeSecurityArtifact(encodedJson.Substring(Dot1 + 1, Dot2 - Dot1 - 1), (object t) => t.ToString())), innerException2));
			}
			finally
			{
				jsonDocument2?.Dispose();
			}
		}
		else
		{
			Payload = new JsonClaimSet(JsonDocument.Parse("{}"));
			if (Dot3 == encodedJson.Length)
			{
				throw LogHelper.LogExceptionMessage(new ArgumentException("IDX14121: JWT is not a well formed JWE, there are there must be four dots (.).\nThe token needs to be in JWS or JWE Compact Serialization Format. (JWS): 'EncodedHeader.EndcodedPayload.EncodedSignature'. (JWE): 'EncodedProtectedHeader.EncodedEncryptedKey.EncodedInitializationVector.EncodedCiphertext.EncodedAuthenticationTag'."));
			}
			Dot4 = encodedJson.IndexOf('.', Dot3 + 1);
			if (Dot4 == -1)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenMalformedException("IDX14121: JWT is not a well formed JWE, there are there must be four dots (.).\nThe token needs to be in JWS or JWE Compact Serialization Format. (JWS): 'EncodedHeader.EndcodedPayload.EncodedSignature'. (JWE): 'EncodedProtectedHeader.EncodedEncryptedKey.EncodedInitializationVector.EncodedCiphertext.EncodedAuthenticationTag'."));
			}
			if (encodedJson.IndexOf('.', Dot4 + 1) != -1)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenMalformedException("IDX14122: JWT is not a well formed JWE, there are more than four dots (.) a JWE can have at most 4 dots.\nThe token needs to be in JWS or JWE Compact Serialization Format. (JWS): 'EncodedHeader.EndcodedPayload.EncodedSignature'. (JWE): 'EncodedProtectedHeader.EncodedEncryptedKey.EncodedInitializationVector.EncodedCiphertext.EncodedAuthenticationTag'."));
			}
			if (Dot4 == encodedJson.Length - 1)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenMalformedException("IDX14310: JWE authentication tag is missing."));
			}
			_hChars = encodedJson.ToCharArray(0, Dot1);
			if (_hChars.Length == 0)
			{
				throw LogHelper.LogExceptionMessage(new ArgumentException("IDX14307: JWE header is missing."));
			}
			HeaderAsciiBytes = Encoding.ASCII.GetBytes(_hChars);
			try
			{
				Header = new JsonClaimSet(Base64UrlEncoder.UnsafeDecode(_hChars));
			}
			catch (Exception innerException3)
			{
				throw LogHelper.LogExceptionMessage(new ArgumentException(LogHelper.FormatInvariant("IDX14102: Unable to decode the header '{0}' as Base64Url encoded string.", LogHelper.MarkAsUnsafeSecurityArtifact(encodedJson.Substring(0, Dot1), (object t) => t.ToString())), innerException3));
			}
			char[] array = encodedJson.ToCharArray(Dot1 + 1, Dot2 - Dot1 - 1);
			if (array.Length != 0)
			{
				EncryptedKeyBytes = Base64UrlEncoder.UnsafeDecode(array);
				_encryptedKey = encodedJson.Substring(Dot1 + 1, Dot2 - Dot1 - 1);
			}
			else
			{
				_encryptedKey = string.Empty;
			}
			char[] array2 = encodedJson.ToCharArray(Dot2 + 1, Dot3 - Dot2 - 1);
			if (array2.Length == 0)
			{
				throw LogHelper.LogExceptionMessage(new ArgumentException("IDX14308: JWE initialization vector is missing."));
			}
			try
			{
				InitializationVectorBytes = Base64UrlEncoder.UnsafeDecode(array2);
			}
			catch (Exception innerException4)
			{
				throw LogHelper.LogExceptionMessage(new ArgumentException("IDX14309: Unable to decode the initialization vector as Base64Url encoded string.", innerException4));
			}
			char[] array3 = encodedJson.ToCharArray(Dot4 + 1, encodedJson.Length - Dot4 - 1);
			if (array3.Length == 0)
			{
				throw LogHelper.LogExceptionMessage(new ArgumentException("IDX14310: JWE authentication tag is missing."));
			}
			try
			{
				AuthenticationTagBytes = Base64UrlEncoder.UnsafeDecode(array3);
			}
			catch (Exception innerException5)
			{
				throw LogHelper.LogExceptionMessage(new ArgumentException("IDX14311: Unable to decode the authentication tag as a Base64Url encoded string.", innerException5));
			}
			if (encodedJson.ToCharArray(Dot3 + 1, Dot4 - Dot3 - 1).Length == 0)
			{
				throw LogHelper.LogExceptionMessage(new ArgumentException("IDX14306: JWE Ciphertext cannot be an empty string."));
			}
			try
			{
				CipherTextBytes = Base64UrlEncoder.UnsafeDecode(encodedJson.ToCharArray(Dot3 + 1, Dot4 - Dot3 - 1));
			}
			catch (Exception innerException6)
			{
				throw LogHelper.LogExceptionMessage(new ArgumentException("IDX14312: Unable to decode the cipher text as a Base64Url encoded string.", innerException6));
			}
		}
		EncodedToken = encodedJson;
	}

	public override string UnsafeToString()
	{
		return EncodedToken;
	}

	internal override IEnumerable<Claim> CreateClaims(string issuer)
	{
		return Payload.CreateClaims(issuer);
	}

	public Claim GetClaim(string key)
	{
		return Payload.GetClaim(key, Issuer ?? "LOCAL AUTHORITY");
	}

	public T GetHeaderValue<T>(string key)
	{
		if (string.IsNullOrEmpty(key))
		{
			throw LogHelper.LogArgumentNullException("key");
		}
		return Header.GetValue<T>(key);
	}

	public T GetPayloadValue<T>(string key)
	{
		if (string.IsNullOrEmpty(key))
		{
			throw LogHelper.LogArgumentNullException("key");
		}
		if (typeof(T).Equals(typeof(Claim)))
		{
			return (T)(object)GetClaim(key);
		}
		return Payload.GetValue<T>(key);
	}

	public override string ToString()
	{
		int num = EncodedToken.LastIndexOf('.');
		if (num >= 0)
		{
			return EncodedToken.Substring(0, num);
		}
		return EncodedToken;
	}

	public bool TryGetClaim(string key, out Claim value)
	{
		return Payload.TryGetClaim(key, Issuer ?? "LOCAL AUTHORITY", out value);
	}

	public bool TryGetValue<T>(string key, out T value)
	{
		if (string.IsNullOrEmpty(key))
		{
			value = default(T);
			return false;
		}
		return Payload.TryGetValue<T>(key, out value);
	}

	public bool TryGetHeaderValue<T>(string key, out T value)
	{
		if (string.IsNullOrEmpty(key))
		{
			value = default(T);
			return false;
		}
		return Header.TryGetValue<T>(key, out value);
	}

	public bool TryGetPayloadValue<T>(string key, out T value)
	{
		if (string.IsNullOrEmpty(key))
		{
			value = default(T);
			return false;
		}
		if (typeof(T).Equals(typeof(Claim)))
		{
			Claim value2;
			bool result = TryGetClaim(key, out value2);
			value = (T)(object)value2;
			return result;
		}
		return Payload.TryGetValue<T>(key, out value);
	}
}
