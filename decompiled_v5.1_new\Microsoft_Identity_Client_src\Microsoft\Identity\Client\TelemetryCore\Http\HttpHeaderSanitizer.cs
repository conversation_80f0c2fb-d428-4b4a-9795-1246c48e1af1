using System.Text;

namespace Microsoft.Identity.Client.TelemetryCore.Http;

internal class HttpHeaderSanitizer
{
	private static readonly string[] s_headerEncodingTable = new string[32]
	{
		"%00", "%01", "%02", "%03", "%04", "%05", "%06", "%07", "%08", "%09",
		"%0a", "%0b", "%0c", "%0d", "%0e", "%0f", "%10", "%11", "%12", "%13",
		"%14", "%15", "%16", "%17", "%18", "%19", "%1a", "%1b", "%1c", "%1d",
		"%1e", "%1f"
	};

	public static string SanitizeHeader(string value)
	{
		string result = value;
		if (HeaderValueNeedsEncoding(value))
		{
			StringBuilder stringBuilder = new StringBuilder();
			foreach (char c in value)
			{
				if (c < ' ' && c != '\t')
				{
					stringBuilder.Append(s_headerEncodingTable[(uint)c]);
				}
				else if (c == '\u007f')
				{
					stringBuilder.Append("%7f");
				}
				else
				{
					stringBuilder.Append(c);
				}
			}
			result = stringBuilder.ToString();
		}
		return result;
	}

	private static bool HeaderValueNeedsEncoding(string value)
	{
		foreach (char c in value)
		{
			if ((c < ' ' && c != '\t') || c == '\u007f')
			{
				return true;
			}
		}
		return false;
	}
}
