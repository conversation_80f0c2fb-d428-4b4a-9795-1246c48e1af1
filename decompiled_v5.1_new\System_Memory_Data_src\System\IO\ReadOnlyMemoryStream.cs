using System.Threading;
using System.Threading.Tasks;

namespace System.IO;

internal sealed class ReadOnlyMemoryStream : Stream
{
	private ReadOnlyMemory<byte> _content;

	private bool _isOpen;

	private int _position;

	public override bool CanRead => _isOpen;

	public override bool CanSeek => _isOpen;

	public override bool CanWrite => false;

	public override long Length
	{
		get
		{
			ValidateNotClosed();
			return _content.Length;
		}
	}

	public override long Position
	{
		get
		{
			ValidateNotClosed();
			return _position;
		}
		set
		{
			ValidateNotClosed();
			if (value < 0 || value > int.MaxValue)
			{
				throw new ArgumentOutOfRangeException("value");
			}
			_position = (int)value;
		}
	}

	public ReadOnlyMemoryStream(ReadOnlyMemory<byte> content)
	{
		_content = content;
		_isOpen = true;
	}

	public override long Seek(long offset, SeekOrigin origin)
	{
		ValidateNotClosed();
		long num = origin switch
		{
			SeekOrigin.End => _content.Length + offset, 
			SeekOrigin.Current => _position + offset, 
			SeekOrigin.Begin => offset, 
			_ => throw new ArgumentOutOfRangeException("origin"), 
		};
		if (num > int.MaxValue)
		{
			throw new ArgumentOutOfRangeException("offset");
		}
		if (num < 0)
		{
			throw new IOException("An attempt was made to move the position before the beginning of the stream.");
		}
		_position = (int)num;
		return _position;
	}

	public override int ReadByte()
	{
		ValidateNotClosed();
		ReadOnlySpan<byte> span = _content.Span;
		if (_position >= span.Length)
		{
			return -1;
		}
		return span[_position++];
	}

	public override int Read(byte[] buffer, int offset, int count)
	{
		ValidateNotClosed();
		ValidateReadArrayArguments(buffer, offset, count);
		return ReadBuffer(new Span<byte>(buffer, offset, count));
	}

	private int ReadBuffer(Span<byte> buffer)
	{
		int num = _content.Length - _position;
		if (num <= 0 || buffer.Length == 0)
		{
			return 0;
		}
		ReadOnlySpan<byte> readOnlySpan;
		if (num <= buffer.Length)
		{
			readOnlySpan = _content.Span;
			readOnlySpan = readOnlySpan.Slice(_position);
			readOnlySpan.CopyTo(buffer);
			_position = _content.Length;
			return num;
		}
		readOnlySpan = _content.Span;
		readOnlySpan = readOnlySpan.Slice(_position, buffer.Length);
		readOnlySpan.CopyTo(buffer);
		_position += buffer.Length;
		return buffer.Length;
	}

	public override Task<int> ReadAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken)
	{
		ValidateNotClosed();
		ValidateReadArrayArguments(buffer, offset, count);
		if (!cancellationToken.IsCancellationRequested)
		{
			return Task.FromResult(ReadBuffer(new Span<byte>(buffer, offset, count)));
		}
		return Task.FromCanceled<int>(cancellationToken);
	}

	public override void Flush()
	{
	}

	public override Task FlushAsync(CancellationToken cancellationToken)
	{
		return Task.CompletedTask;
	}

	public override void SetLength(long value)
	{
		throw new NotSupportedException();
	}

	public override void Write(byte[] buffer, int offset, int count)
	{
		throw new NotSupportedException();
	}

	private static void ValidateReadArrayArguments(byte[] buffer, int offset, int count)
	{
		if (buffer == null)
		{
			throw new ArgumentNullException("buffer");
		}
		if (offset < 0)
		{
			throw new ArgumentOutOfRangeException("offset");
		}
		if (count < 0 || buffer.Length - offset < count)
		{
			throw new ArgumentOutOfRangeException("count");
		}
	}

	private void ValidateNotClosed()
	{
		if (!_isOpen)
		{
			throw new ObjectDisposedException(null, "Cannot access a closed Stream");
		}
	}

	protected override void Dispose(bool disposing)
	{
		try
		{
			if (disposing)
			{
				_isOpen = false;
				_content = default(ReadOnlyMemory<byte>);
			}
		}
		finally
		{
			base.Dispose(disposing);
		}
	}
}
