using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Internal.Requests;

namespace Microsoft.Identity.Client.ApiConfig.Executors;

internal class ManagedIdentityExecutor : AbstractExecutor, IManagedIdentityApplicationExecutor
{
	private readonly ManagedIdentityApplication _managedIdentityApplication;

	public ManagedIdentityExecutor(IServiceBundle serviceBundle, ManagedIdentityApplication managedIdentityApplication)
		: base(serviceBundle)
	{
		ApplicationBase.GuardMobileFrameworks();
		_managedIdentityApplication = managedIdentityApplication;
	}

	public async Task<AuthenticationResult> ExecuteAsync(AcquireTokenCommonParameters commonParameters, AcquireTokenForManagedIdentityParameters managedIdentityParameters, CancellationToken cancellationToken)
	{
		RequestContext requestContext = CreateRequestContextAndLogVersionInfo(commonParameters.CorrelationId, cancellationToken);
		AuthenticationRequestParameters authenticationRequestParameters = await _managedIdentityApplication.CreateRequestParametersAsync(commonParameters, requestContext, _managedIdentityApplication.AppTokenCacheInternal).ConfigureAwait(continueOnCapturedContext: false);
		return await new ManagedIdentityAuthRequest(base.ServiceBundle, authenticationRequestParameters, managedIdentityParameters).RunAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}
}
