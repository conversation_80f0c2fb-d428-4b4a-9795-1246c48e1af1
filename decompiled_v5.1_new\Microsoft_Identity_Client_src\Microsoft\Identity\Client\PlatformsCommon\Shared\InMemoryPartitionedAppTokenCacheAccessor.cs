using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.PlatformsCommon.Shared;

internal class InMemoryPartitionedAppTokenCacheAccessor : ITokenCacheAccessor
{
	internal readonly ConcurrentDictionary<string, ConcurrentDictionary<string, MsalAccessTokenCacheItem>> AccessTokenCacheDictionary;

	internal readonly ConcurrentDictionary<string, MsalAppMetadataCacheItem> AppMetadataDictionary;

	private static readonly ConcurrentDictionary<string, ConcurrentDictionary<string, MsalAccessTokenCacheItem>> s_accessTokenCacheDictionary = new ConcurrentDictionary<string, ConcurrentDictionary<string, MsalAccessTokenCacheItem>>();

	private static readonly ConcurrentDictionary<string, MsalAppMetadataCacheItem> s_appMetadataDictionary = new ConcurrentDictionary<string, MsalAppMetadataCacheItem>(1, 1);

	protected readonly ILoggerAdapter _logger;

	private readonly CacheOptions _tokenCacheAccessorOptions;

	public InMemoryPartitionedAppTokenCacheAccessor(ILoggerAdapter logger, CacheOptions tokenCacheAccessorOptions)
	{
		_logger = logger ?? throw new ArgumentNullException("logger");
		_tokenCacheAccessorOptions = tokenCacheAccessorOptions ?? new CacheOptions();
		if (_tokenCacheAccessorOptions.UseSharedCache)
		{
			AccessTokenCacheDictionary = s_accessTokenCacheDictionary;
			AppMetadataDictionary = s_appMetadataDictionary;
		}
		else
		{
			AccessTokenCacheDictionary = new ConcurrentDictionary<string, ConcurrentDictionary<string, MsalAccessTokenCacheItem>>();
			AppMetadataDictionary = new ConcurrentDictionary<string, MsalAppMetadataCacheItem>();
		}
	}

	public void SaveAccessToken(MsalAccessTokenCacheItem item)
	{
		string cacheKey = item.CacheKey;
		string clientCredentialKey = CacheKeyFactory.GetClientCredentialKey(item.ClientId, item.TenantId, item.KeyId);
		AccessTokenCacheDictionary.GetOrAdd(clientCredentialKey, new ConcurrentDictionary<string, MsalAccessTokenCacheItem>())[cacheKey] = item;
	}

	public void SaveRefreshToken(MsalRefreshTokenCacheItem item)
	{
		throw new MsalClientException("combined_user_app_cache_not_supported", "Using a combined flat storage, like a file, to store both app and user tokens is not supported. Use a partitioned token cache (for ex. distributed cache like Redis) or separate files for app and user token caches. See https://aka.ms/msal-net-token-cache-serialization .");
	}

	public void SaveIdToken(MsalIdTokenCacheItem item)
	{
		throw new MsalClientException("combined_user_app_cache_not_supported", "Using a combined flat storage, like a file, to store both app and user tokens is not supported. Use a partitioned token cache (for ex. distributed cache like Redis) or separate files for app and user token caches. See https://aka.ms/msal-net-token-cache-serialization .");
	}

	public void SaveAccount(MsalAccountCacheItem item)
	{
		throw new MsalClientException("combined_user_app_cache_not_supported", "Using a combined flat storage, like a file, to store both app and user tokens is not supported. Use a partitioned token cache (for ex. distributed cache like Redis) or separate files for app and user token caches. See https://aka.ms/msal-net-token-cache-serialization .");
	}

	public void SaveAppMetadata(MsalAppMetadataCacheItem item)
	{
		string cacheKey = item.CacheKey;
		AppMetadataDictionary[cacheKey] = item;
	}

	public MsalIdTokenCacheItem GetIdToken(MsalAccessTokenCacheItem accessTokenCacheItem)
	{
		throw new MsalClientException("combined_user_app_cache_not_supported", "Using a combined flat storage, like a file, to store both app and user tokens is not supported. Use a partitioned token cache (for ex. distributed cache like Redis) or separate files for app and user token caches. See https://aka.ms/msal-net-token-cache-serialization .");
	}

	public MsalAccountCacheItem GetAccount(MsalAccountCacheItem accountCacheItem)
	{
		throw new MsalClientException("combined_user_app_cache_not_supported", "Using a combined flat storage, like a file, to store both app and user tokens is not supported. Use a partitioned token cache (for ex. distributed cache like Redis) or separate files for app and user token caches. See https://aka.ms/msal-net-token-cache-serialization .");
	}

	public MsalAppMetadataCacheItem GetAppMetadata(MsalAppMetadataCacheItem appMetadataItem)
	{
		AppMetadataDictionary.TryGetValue(appMetadataItem.CacheKey, out var value);
		return value;
	}

	public void DeleteAccessToken(MsalAccessTokenCacheItem item)
	{
		string clientCredentialKey = CacheKeyFactory.GetClientCredentialKey(item.ClientId, item.TenantId, item.KeyId);
		AccessTokenCacheDictionary.TryGetValue(clientCredentialKey, out var value);
		if (value == null || !value.TryRemove(item.CacheKey, out var _))
		{
			_logger.InfoPii(() => "[Internal cache] Cannot delete access token because it was not found in the cache. Key " + item.CacheKey + ".", () => "[Internal cache] Cannot delete access token because it was not found in the cache.");
		}
	}

	public void DeleteRefreshToken(MsalRefreshTokenCacheItem item)
	{
		throw new MsalClientException("combined_user_app_cache_not_supported", "Using a combined flat storage, like a file, to store both app and user tokens is not supported. Use a partitioned token cache (for ex. distributed cache like Redis) or separate files for app and user token caches. See https://aka.ms/msal-net-token-cache-serialization .");
	}

	public void DeleteIdToken(MsalIdTokenCacheItem item)
	{
		throw new MsalClientException("combined_user_app_cache_not_supported", "Using a combined flat storage, like a file, to store both app and user tokens is not supported. Use a partitioned token cache (for ex. distributed cache like Redis) or separate files for app and user token caches. See https://aka.ms/msal-net-token-cache-serialization .");
	}

	public void DeleteAccount(MsalAccountCacheItem item)
	{
		throw new MsalClientException("combined_user_app_cache_not_supported", "Using a combined flat storage, like a file, to store both app and user tokens is not supported. Use a partitioned token cache (for ex. distributed cache like Redis) or separate files for app and user token caches. See https://aka.ms/msal-net-token-cache-serialization .");
	}

	public virtual List<MsalAccessTokenCacheItem> GetAllAccessTokens(string partitionKey = null, ILoggerAdapter requestlogger = null)
	{
		(requestlogger ?? _logger).Always($"[Internal cache] Total number of cache partitions found while getting access tokens: {AccessTokenCacheDictionary.Count}");
		if (string.IsNullOrEmpty(partitionKey))
		{
			return (from kv in AccessTokenCacheDictionary.SelectMany((KeyValuePair<string, ConcurrentDictionary<string, MsalAccessTokenCacheItem>> dict) => dict.Value)
				select kv.Value).ToList();
		}
		AccessTokenCacheDictionary.TryGetValue(partitionKey, out var value);
		return value?.Select((KeyValuePair<string, MsalAccessTokenCacheItem> kv) => kv.Value)?.ToList() ?? CollectionHelpers.GetEmptyList<MsalAccessTokenCacheItem>();
	}

	public virtual List<MsalRefreshTokenCacheItem> GetAllRefreshTokens(string partitionKey = null, ILoggerAdapter requestlogger = null)
	{
		return CollectionHelpers.GetEmptyList<MsalRefreshTokenCacheItem>();
	}

	public virtual List<MsalIdTokenCacheItem> GetAllIdTokens(string partitionKey = null, ILoggerAdapter requestlogger = null)
	{
		return CollectionHelpers.GetEmptyList<MsalIdTokenCacheItem>();
	}

	public virtual List<MsalAccountCacheItem> GetAllAccounts(string partitionKey = null, ILoggerAdapter requestlogger = null)
	{
		return CollectionHelpers.GetEmptyList<MsalAccountCacheItem>();
	}

	public List<MsalAppMetadataCacheItem> GetAllAppMetadata()
	{
		return AppMetadataDictionary.Select((KeyValuePair<string, MsalAppMetadataCacheItem> kv) => kv.Value).ToList();
	}

	public void SetiOSKeychainSecurityGroup(string keychainSecurityGroup)
	{
		throw new NotImplementedException();
	}

	public virtual void Clear(ILoggerAdapter requestlogger = null)
	{
		ILoggerAdapter logger = requestlogger ?? _logger;
		AccessTokenCacheDictionary.Clear();
		logger.Always("[Internal cache] Clearing app token cache accessor.");
	}

	public virtual bool HasAccessOrRefreshTokens()
	{
		return AccessTokenCacheDictionary.Any((KeyValuePair<string, ConcurrentDictionary<string, MsalAccessTokenCacheItem>> partition) => partition.Value.Any((KeyValuePair<string, MsalAccessTokenCacheItem> token) => !token.Value.IsExpiredWithBuffer()));
	}
}
