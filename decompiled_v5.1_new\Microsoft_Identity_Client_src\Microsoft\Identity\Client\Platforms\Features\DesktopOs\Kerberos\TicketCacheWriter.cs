using System;
using System.Runtime.InteropServices;

namespace Microsoft.Identity.Client.Platforms.Features.DesktopOs.Kerberos;

internal class TicketCacheWriter : IDisposable
{
	private const string _kerberosPackageName = "Kerberos";

	private const string _negotiatePackageName = "Negotiate";

	private readonly LsaSafeHandle _lsaHandle;

	private readonly int _selectedAuthPackage;

	private readonly int _negotiateAuthPackage;

	private bool _disposedValue;

	internal TicketCacheWriter(LsaSafeHandle lsaHandle, string packageName = "Kerberos")
	{
		_lsaHandle = lsaHandle;
		NativeMethods.LSA_STRING PackageName = new NativeMethods.LSA_STRING
		{
			Buffer = packageName,
			Length = (ushort)packageName.Length,
			MaximumLength = (ushort)packageName.Length
		};
		NativeMethods.LsaThrowIfError(NativeMethods.LsaLookupAuthenticationPackage(_lsaHandle, ref PackageName, out _selectedAuthPackage));
		NativeMethods.LSA_STRING PackageName2 = new NativeMethods.LSA_STRING
		{
			Buffer = "Negotiate",
			Length = (ushort)"Negotiate".Length,
			MaximumLength = (ushort)"Negotiate".Length
		};
		NativeMethods.LsaThrowIfError(NativeMethods.LsaLookupAuthenticationPackage(_lsaHandle, ref PackageName2, out _negotiateAuthPackage));
	}

	public static TicketCacheWriter Connect(string package = "Kerberos")
	{
		if (string.IsNullOrWhiteSpace(package))
		{
			package = "Kerberos";
		}
		NativeMethods.LsaThrowIfError(NativeMethods.LsaConnectUntrusted(out var LsaHandle));
		return new TicketCacheWriter(LsaHandle, package);
	}

	public unsafe void ImportCredential(byte[] ticketBytes, long luid = 0L)
	{
		if (ticketBytes == null)
		{
			throw new ArgumentNullException("ticketBytes");
		}
		NativeMethods.KERB_SUBMIT_TKT_REQUEST structure = new NativeMethods.KERB_SUBMIT_TKT_REQUEST
		{
			MessageType = NativeMethods.KERB_PROTOCOL_MESSAGE_TYPE.KerbSubmitTicketMessage,
			KerbCredSize = ticketBytes.Length,
			KerbCredOffset = Marshal.SizeOf(typeof(NativeMethods.KERB_SUBMIT_TKT_REQUEST)),
			LogonId = luid
		};
		int num = structure.KerbCredOffset + ticketBytes.Length;
		IntPtr intPtr = Marshal.AllocHGlobal(num);
		Marshal.StructureToPtr(structure, intPtr, fDeleteOld: false);
		Marshal.Copy(ticketBytes, 0, intPtr + structure.KerbCredOffset, ticketBytes.Length);
		LsaCallAuthenticationPackage(intPtr.ToPointer(), num);
	}

	private unsafe void LsaCallAuthenticationPackage(void* pBuffer, int bufferSize)
	{
		LsaBufferSafeHandle ProtocolReturnBuffer = null;
		try
		{
			NativeMethods.LsaThrowIfError(NativeMethods.LsaCallAuthenticationPackage(_lsaHandle, _selectedAuthPackage, pBuffer, bufferSize, out ProtocolReturnBuffer, out var _, out var ProtocolStatus));
			NativeMethods.LsaThrowIfError(ProtocolStatus);
		}
		finally
		{
			ProtocolReturnBuffer?.Dispose();
		}
	}

	protected virtual void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			_lsaHandle.Dispose();
			_disposedValue = true;
		}
	}

	~TicketCacheWriter()
	{
		Dispose(disposing: false);
	}

	public void Dispose()
	{
		Dispose(disposing: true);
		GC.SuppressFinalize(this);
	}
}
