namespace Microsoft.Identity.Client.Internal;

internal static class IdTokenClaim
{
	public const string Issuer = "iss";

	public const string ObjectId = "oid";

	public const string Subject = "sub";

	public const string TenantId = "tid";

	public const string Version = "ver";

	public const string PreferredUsername = "preferred_username";

	public const string Name = "name";

	public const string Email = "email";

	public const string HomeObjectId = "home_oid";

	public const string GivenName = "given_name";

	public const string FamilyName = "family_name";

	public const string Upn = "upn";
}
