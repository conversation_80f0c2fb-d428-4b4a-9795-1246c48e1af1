using System.Collections.Generic;
using System.Text.Json.Serialization;
using Microsoft.Identity.Client.Platforms.net6;

namespace Microsoft.Identity.Client.OAuth2;

[JsonObject]
[Preserve(AllMembers = true)]
internal class DeviceAuthHeader
{
	[JsonPropertyName("x5c")]
	public IList<string> X5c { get; set; }

	[JsonPropertyName("typ")]
	public string Type { get; set; }

	[JsonPropertyName("alg")]
	public string Alg { get; private set; }

	public DeviceAuthHeader(string base64EncodedCertificate)
	{
		Alg = "RS256";
		Type = "JWT";
		X5c = new List<string>();
		X5c.Add(base64EncodedCertificate);
	}
}
