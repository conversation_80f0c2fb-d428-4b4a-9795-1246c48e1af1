using System;
using System.Net.Http;
using Microsoft.Identity.Client.AuthScheme.PoP;

namespace Microsoft.Identity.Client.AppConfig;

public class PoPAuthenticationConfiguration
{
	public HttpMethod HttpMethod { get; set; }

	public string HttpHost { get; set; }

	public string HttpPath { get; set; }

	public IPoPCryptoProvider PopCryptoProvider { get; set; }

	public string Nonce { get; set; }

	public bool SignHttpRequest { get; set; } = true;

	public PoPAuthenticationConfiguration()
	{
		ApplicationBase.GuardMobileFrameworks();
	}

	public PoPAuthenticationConfiguration(HttpRequestMessage httpRequestMessage)
	{
		if (httpRequestMessage == null)
		{
			throw new ArgumentNullException("httpRequestMessage");
		}
		HttpMethod = httpRequestMessage.Method;
		HttpHost = httpRequestMessage.RequestUri.Authority;
		HttpPath = httpRequestMessage.RequestUri.AbsolutePath;
	}

	public PoPAuthenticationConfiguration(Uri requestUri)
	{
		if (requestUri == null)
		{
			throw new ArgumentNullException("requestUri");
		}
		HttpHost = requestUri.Authority;
		HttpPath = requestUri.AbsolutePath;
	}
}
