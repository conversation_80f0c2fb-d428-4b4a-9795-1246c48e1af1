using System;
using System.Text.Json.Nodes;
using Microsoft.Identity.Client.Cache.Keys;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Cache.Items;

internal class MsalRefreshTokenCacheItem : MsalCredentialCacheItemBase
{
	private Lazy<IiOSKey> iOSCacheKeyLazy;

	public string FamilyId { get; set; }

	internal string OboCacheKey { get; set; }

	public bool IsFRT => !string.IsNullOrEmpty(FamilyId);

	public string CacheKey { get; private set; }

	public IiOSKey iOSCacheKey => iOSCacheKeyLazy.Value;

	internal MsalRefreshTokenCacheItem()
	{
		base.CredentialType = "RefreshToken";
	}

	internal MsalRefreshTokenCacheItem(string preferredCacheEnv, string clientId, MsalTokenResponse response, string homeAccountId)
		: this(preferredCacheEnv, clientId, response.RefreshToken, response.ClientInfo, response.FamilyId, homeAccountId)
	{
	}

	internal MsalRefreshTokenCacheItem(string preferredCacheEnv, string clientId, string secret, string rawClientInfo, string familyId, string homeAccountId)
		: this()
	{
		base.ClientId = clientId;
		base.Environment = preferredCacheEnv;
		base.Secret = secret;
		base.RawClientInfo = rawClientInfo;
		FamilyId = familyId;
		base.HomeAccountId = homeAccountId;
		InitCacheKey();
	}

	internal void InitCacheKey()
	{
		string cacheKey;
		if (!string.IsNullOrWhiteSpace(FamilyId))
		{
			char value = '-';
			cacheKey = $"{base.HomeAccountId}{value}{base.Environment}{value}{"RefreshToken"}{value}{FamilyId}{value}{value}".ToLowerInvariant();
		}
		else
		{
			cacheKey = MsalCacheKeys.GetCredentialKey(base.HomeAccountId, base.Environment, "RefreshToken", base.ClientId, null, null);
		}
		CacheKey = cacheKey;
		iOSCacheKeyLazy = new Lazy<IiOSKey>(() => InitiOSKey());
	}

	internal string ToLogString(bool piiEnabled = false)
	{
		return MsalCacheKeys.GetCredentialKey(piiEnabled ? base.HomeAccountId : base.HomeAccountId?.GetHashCode().ToString(), base.Environment, "RefreshToken", base.ClientId, null, null);
	}

	private IiOSKey InitiOSKey()
	{
		string iOSService = GetiOSService();
		string iOSGeneric = GetiOSGeneric();
		string iOSAccount = MsalCacheKeys.GetiOSAccountKey(base.HomeAccountId, base.Environment);
		int iOSType = 2002;
		return new IosKey(iOSAccount, iOSService, iOSGeneric, iOSType);
	}

	private string GetiOSGeneric()
	{
		if (!string.IsNullOrWhiteSpace(FamilyId))
		{
			return $"{"RefreshToken"}{45}{FamilyId}{45}".ToLowerInvariant();
		}
		return MsalCacheKeys.GetiOSGenericKey("RefreshToken", base.ClientId, null);
	}

	public string GetiOSService()
	{
		if (!string.IsNullOrWhiteSpace(FamilyId))
		{
			return $"{"RefreshToken"}{45}{FamilyId}{45}{45}".ToLowerInvariant();
		}
		return MsalCacheKeys.GetiOSServiceKey("RefreshToken", base.ClientId, null, null);
	}

	internal static MsalRefreshTokenCacheItem FromJsonString(string json)
	{
		if (string.IsNullOrWhiteSpace(json))
		{
			return null;
		}
		return FromJObject(JsonHelper.ParseIntoJsonObject(json));
	}

	internal static MsalRefreshTokenCacheItem FromJObject(JsonObject j)
	{
		MsalRefreshTokenCacheItem msalRefreshTokenCacheItem = new MsalRefreshTokenCacheItem();
		msalRefreshTokenCacheItem.FamilyId = JsonHelper.ExtractExistingOrEmptyString(j, "family_id");
		msalRefreshTokenCacheItem.OboCacheKey = JsonHelper.ExtractExistingOrEmptyString(j, "user_assertion_hash");
		msalRefreshTokenCacheItem.PopulateFieldsFromJObject(j);
		msalRefreshTokenCacheItem.InitCacheKey();
		return msalRefreshTokenCacheItem;
	}

	internal override JsonObject ToJObject()
	{
		JsonObject jsonObject = base.ToJObject();
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "family_id", FamilyId);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "user_assertion_hash", OboCacheKey);
		return jsonObject;
	}

	internal string ToJsonString()
	{
		return ToJObject().ToString();
	}
}
