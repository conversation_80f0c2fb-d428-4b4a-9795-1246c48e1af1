using System;
using System.ComponentModel;

namespace Microsoft.Identity.Client;

[Obsolete("Use ConfidentialClientApplicationBuilder.WithCertificate or WithClientSecret instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
[EditorBrowsable(EditorBrowsableState.Never)]
public sealed class ClientCredential
{
	[EditorBrowsable(EditorBrowsableState.Never)]
	internal ClientAssertionCertificate Certificate
	{
		get
		{
			throw MigrationHelper.CreateMsalNet3BreakingChangesException();
		}
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	internal string Assertion
	{
		get
		{
			throw MigrationHelper.CreateMsalNet3BreakingChangesException();
		}
		set
		{
			throw MigrationHelper.CreateMsalNet3BreakingChangesException();
		}
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	internal long ValidTo
	{
		get
		{
			throw MigrationHelper.CreateMsalNet3BreakingChangesException();
		}
		set
		{
			throw MigrationHelper.CreateMsalNet3BreakingChangesException();
		}
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	internal bool ContainsX5C
	{
		get
		{
			throw MigrationHelper.CreateMsalNet3BreakingChangesException();
		}
		set
		{
			throw MigrationHelper.CreateMsalNet3BreakingChangesException();
		}
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	internal string Audience
	{
		get
		{
			throw MigrationHelper.CreateMsalNet3BreakingChangesException();
		}
		set
		{
			throw MigrationHelper.CreateMsalNet3BreakingChangesException();
		}
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	internal string Secret
	{
		get
		{
			throw MigrationHelper.CreateMsalNet3BreakingChangesException();
		}
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	public ClientCredential(ClientAssertionCertificate certificate)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	public ClientCredential(string secret)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}
}
