using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.Utils;

internal static class CoreHelpers
{
	internal static string ByteArrayToString(byte[] input)
	{
		if (input == null || input.Length == 0)
		{
			return null;
		}
		return Encoding.UTF8.GetString(input, 0, input.Length);
	}

	public static string UrlEncode(string message)
	{
		if (string.IsNullOrEmpty(message))
		{
			return message;
		}
		message = Uri.EscapeDataString(message);
		message = message.Replace("%20", "+");
		return message;
	}

	public static string UrlDecode(string message)
	{
		if (string.IsNullOrEmpty(message))
		{
			return message;
		}
		message = message.Replace("+", "%20");
		message = Uri.UnescapeDataString(message);
		return message;
	}

	public static void AddKeyValueString(StringBuilder messageBuilder, string key, string value)
	{
		AddKeyValueString(messageBuilder, key, value.ToCharArray());
	}

	public static string ToQueryParameter(this IDictionary<string, string> input)
	{
		StringBuilder stringBuilder = new StringBuilder();
		if (input.Count > 0)
		{
			foreach (string key in input.Keys)
			{
				stringBuilder.AppendFormat(CultureInfo.InvariantCulture, "{0}={1}&", key, UrlEncode(input[key]));
			}
			if (stringBuilder.Length > 0)
			{
				stringBuilder.Remove(stringBuilder.Length - 1, 1);
			}
		}
		return stringBuilder.ToString();
	}

	public static Dictionary<string, string> ParseKeyValueList(string input, char delimiter, bool urlDecode, bool lowercaseKeys, RequestContext requestContext)
	{
		Dictionary<string, string> dictionary = new Dictionary<string, string>();
		foreach (string item in SplitWithQuotes(input, delimiter))
		{
			IReadOnlyList<string> readOnlyList = SplitWithQuotes(item, '=');
			if (readOnlyList.Count == 2 && !string.IsNullOrWhiteSpace(readOnlyList[0]) && !string.IsNullOrWhiteSpace(readOnlyList[1]))
			{
				string text = readOnlyList[0];
				string text2 = readOnlyList[1];
				if (urlDecode)
				{
					text = UrlDecode(text);
					text2 = UrlDecode(text2);
				}
				if (lowercaseKeys)
				{
					text = text.Trim().ToLowerInvariant();
				}
				text2 = text2.Trim().Trim('"').Trim();
				if (dictionary.ContainsKey(text))
				{
					requestContext?.Logger.Warning(string.Format(CultureInfo.InvariantCulture, "Key/value pair list contains redundant key '{0}'.", text));
				}
				dictionary[text] = text2;
			}
		}
		return dictionary;
	}

	public static Dictionary<string, string> ParseKeyValueList(string input, char delimiter, bool urlDecode, RequestContext requestContext)
	{
		return ParseKeyValueList(input, delimiter, urlDecode, lowercaseKeys: true, requestContext);
	}

	internal static IReadOnlyList<string> SplitWithQuotes(string input, char delimiter)
	{
		if (string.IsNullOrWhiteSpace(input))
		{
			return Array.Empty<string>();
		}
		List<string> list = new List<string>();
		int num = 0;
		bool flag = false;
		string text;
		for (int i = 0; i < input.Length; i++)
		{
			if (input[i] == delimiter && !flag)
			{
				text = input.Substring(num, i - num);
				if (!string.IsNullOrWhiteSpace(text.Trim()))
				{
					list.Add(text);
				}
				num = i + 1;
			}
			else if (input[i] == '"')
			{
				flag = !flag;
			}
		}
		text = input.Substring(num);
		if (!string.IsNullOrWhiteSpace(text.Trim()))
		{
			list.Add(text);
		}
		return list;
	}

	private static void AddKeyValueString(StringBuilder messageBuilder, string key, char[] value)
	{
		string arg = ((messageBuilder.Length == 0) ? string.Empty : "&");
		messageBuilder.AppendFormat(CultureInfo.InvariantCulture, "{0}{1}=", arg, key);
		messageBuilder.Append(value);
	}

	internal static string GetCcsClientInfoHint(string userObjectId, string userTenantID)
	{
		if (!string.IsNullOrEmpty(userObjectId) && !string.IsNullOrEmpty(userTenantID))
		{
			return "oid:" + userObjectId + "@" + userTenantID;
		}
		return string.Empty;
	}

	internal static string GetCcsUpnHint(string upn)
	{
		if (!string.IsNullOrEmpty(upn))
		{
			return "upn:" + upn;
		}
		return string.Empty;
	}
}
