using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Http;

namespace Microsoft.Identity.Client.Internal.Requests;

internal class ConfidentialAuthCodeRequest : RequestBase
{
	private readonly AcquireTokenByAuthorizationCodeParameters _authorizationCodeParameters;

	public ConfidentialAuthCodeRequest(IServiceBundle serviceBundle, AuthenticationRequestParameters authenticationRequestParameters, AcquireTokenByAuthorizationCodeParameters authorizationCodeParameters)
		: base(serviceBundle, authenticationRequestParameters, authorizationCodeParameters)
	{
		_authorizationCodeParameters = authorizationCodeParameters;
		RedirectUriHelper.Validate(authenticationRequestParameters.RedirectUri);
	}

	protected override async Task<AuthenticationResult> ExecuteAsync(CancellationToken cancellationToken)
	{
		await ResolveAuthorityAsync().ConfigureAwait(continueOnCapturedContext: false);
		return await CacheTokenResponseAndCreateAuthenticationResultAsync(await SendTokenRequestAsync(GetBodyParameters(), cancellationToken).ConfigureAwait(continueOnCapturedContext: false)).ConfigureAwait(continueOnCapturedContext: false);
	}

	private Dictionary<string, string> GetBodyParameters()
	{
		Dictionary<string, string> dictionary = new Dictionary<string, string>
		{
			["client_info"] = "1",
			["grant_type"] = "authorization_code",
			["code"] = _authorizationCodeParameters.AuthorizationCode,
			["redirect_uri"] = base.AuthenticationRequestParameters.RedirectUri.OriginalString
		};
		if (!string.IsNullOrEmpty(_authorizationCodeParameters.PkceCodeVerifier))
		{
			dictionary["code_verifier"] = _authorizationCodeParameters.PkceCodeVerifier;
		}
		if (_authorizationCodeParameters.SpaCode)
		{
			dictionary["return_spa_code"] = "1";
		}
		return dictionary;
	}
}
