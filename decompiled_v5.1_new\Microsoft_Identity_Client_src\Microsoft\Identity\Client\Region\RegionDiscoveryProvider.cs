using System;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Instance.Discovery;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;

namespace Microsoft.Identity.Client.Region;

internal class RegionDiscoveryProvider : IRegionDiscoveryProvider
{
	private readonly IRegionManager _regionManager;

	public const string PublicEnvForRegional = "login.microsoft.com";

	public RegionDiscoveryProvider(IHttpManager httpManager, bool clearCache)
	{
		_regionManager = new RegionManager(httpManager, 2000, clearCache);
	}

	public async Task<InstanceDiscoveryMetadataEntry> GetMetadataAsync(Uri authority, RequestContext requestContext)
	{
		string text = null;
		ApiEvent apiEvent = requestContext.ApiEvent;
		if (apiEvent != null && apiEvent.ApiId == ApiEvent.ApiIds.AcquireTokenForClient)
		{
			text = await _regionManager.GetAzureRegionAsync(requestContext).ConfigureAwait(continueOnCapturedContext: false);
		}
		if (string.IsNullOrEmpty(text))
		{
			requestContext.Logger.Info("[Region discovery] Not using a regional authority. ");
			return null;
		}
		if (authority.Host.StartsWith(text + "."))
		{
			return CreateEntry(requestContext.ServiceBundle.Config.Authority.AuthorityInfo.Host, authority.Host);
		}
		string regionalizedEnvironment = GetRegionalizedEnvironment(authority, text, requestContext);
		return CreateEntry(authority.Host, regionalizedEnvironment);
	}

	private static InstanceDiscoveryMetadataEntry CreateEntry(string originalEnv, string regionalEnv)
	{
		InstanceDiscoveryMetadataEntry instanceDiscoveryMetadataEntry = new InstanceDiscoveryMetadataEntry();
		instanceDiscoveryMetadataEntry.Aliases = new string[2] { regionalEnv, originalEnv };
		instanceDiscoveryMetadataEntry.PreferredCache = originalEnv;
		instanceDiscoveryMetadataEntry.PreferredNetwork = regionalEnv;
		return instanceDiscoveryMetadataEntry;
	}

	private static string GetRegionalizedEnvironment(Uri authority, string region, RequestContext requestContext)
	{
		string host = authority.Host;
		if (KnownMetadataProvider.IsPublicEnvironment(host))
		{
			requestContext.Logger.Info(() => $"[Region discovery] Regionalized Environment is : {region}.{"login.microsoft.com"}. ");
			return region + ".login.microsoft.com";
		}
		if (KnownMetadataProvider.TryGetKnownEnviromentPreferredNetwork(host, out var preferredNetworkEnvironment))
		{
			host = preferredNetworkEnvironment;
		}
		requestContext.Logger.Info(() => $"[Region discovery] Regionalized Environment is : {region}.{host}. ");
		return region + "." + host;
	}
}
