using System.Text.Json.Serialization;
using Microsoft.Identity.Client.Platforms.net6;

namespace Microsoft.Identity.Client.ManagedIdentity;

[JsonObject]
[Preserve(AllMembers = true)]
internal class ManagedIdentityResponse
{
	[JsonPropertyName("access_token")]
	public string AccessToken { get; set; }

	[JsonPropertyName("expires_on")]
	public string ExpiresOn { get; set; }

	[JsonPropertyName("resource")]
	public string Resource { get; set; }

	[JsonPropertyName("token_type")]
	public string TokenType { get; set; }

	[JsonPropertyName("client_id")]
	public string ClientId { get; set; }
}
