namespace Microsoft.Identity.Client.Platforms.Features.DesktopOs.Kerberos;

internal enum SecStatus : uint
{
	SEC_E_OK = 0u,
	SEC_E_ERROR = 2147483648u,
	SEC_E_INSUFFICIENT_MEMORY = 2148074240u,
	SEC_E_INVALID_HANDLE = 2148074241u,
	SEC_E_TARGET_UNKNOWN = 2148074243u,
	SEC_E_UNSUPPORTED_FUNCTION = 2148074242u,
	SEC_E_INTERNAL_ERROR = 2148074244u,
	SEC_E_SECPKG_NOT_FOUND = 2148074245u,
	SEC_E_INVALID_TOKEN = 2148074248u,
	SEC_E_QOP_NOT_SUPPORTED = 2148074250u,
	SEC_E_LOGON_DENIED = 2148074252u,
	SEC_E_UNKNOWN_CREDENTIALS = 2148074253u,
	SEC_E_NO_CREDENTIALS = 2148074254u,
	SEC_E_MESSAGE_ALTERED = 2148074255u,
	SEC_E_OUT_OF_SEQUENCE = 2148074256u,
	SEC_E_NO_AUTHENTICATING_AUTHORITY = 2148074257u,
	SEC_E_CONTEXT_EXPIRED = 2148074263u,
	SEC_E_INCOMPLETE_MESSAGE = 2148074264u,
	SEC_E_BUFFER_TOO_SMALL = 2148074273u,
	SEC_E_WRONG_PRINCIPAL = 2148074274u,
	SEC_E_CRYPTO_SYSTEM_INVALID = 2148074295u,
	SEC_I_CONTINUE_NEEDED = 590610u,
	SEC_I_CONTEXT_EXPIRED = 590615u,
	SEC_I_INCOMPLETE_CREDENTIALS = 590624u,
	SEC_I_RENEGOTIATE = 590625u
}
