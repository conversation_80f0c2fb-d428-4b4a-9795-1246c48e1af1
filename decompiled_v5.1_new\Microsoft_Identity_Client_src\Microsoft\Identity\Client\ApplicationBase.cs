using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Internal.Requests;

namespace Microsoft.Identity.Client;

public abstract class ApplicationBase : IApplicationBase
{
	internal const string DefaultAuthority = "https://login.microsoftonline.com/common/";

	internal IServiceBundle ServiceBundle { get; }

	internal ApplicationBase(ApplicationConfiguration config)
	{
		ServiceBundle = Microsoft.Identity.Client.Internal.ServiceBundle.Create(config);
	}

	internal virtual async Task<AuthenticationRequestParameters> CreateRequestParametersAsync(AcquireTokenCommonParameters commonParameters, RequestContext requestContext, ITokenCacheInternal cache)
	{
		Authority initialAuthority = await Authority.CreateAuthorityForRequestAsync(requestContext, commonParameters.AuthorityOverride).ConfigureAwait(continueOnCapturedContext: false);
		return new AuthenticationRequestParameters(ServiceBundle, cache, commonParameters, requestContext, initialAuthority);
	}

	internal static void GuardMobileFrameworks()
	{
	}
}
