using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal.Requests;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;

namespace Microsoft.Identity.Client.Internal;

internal static class SilentRequestHelper
{
	internal const string MamEnrollmentIdKey = "microsoft_enrollment_id";

	internal const string ProactiveRefreshServiceError = "Proactive token refresh failed with MsalServiceException.";

	internal const string ProactiveRefreshGeneralError = "Proactive token refresh failed with exception.";

	internal const string ProactiveRefreshCancellationError = "Proactive token refresh was canceled.";

	private static Random s_random = new Random();

	internal static async Task<MsalTokenResponse> RefreshAccessTokenAsync(MsalRefreshTokenCacheItem msalRefreshTokenItem, RequestBase request, AuthenticationRequestParameters authenticationRequestParameters, CancellationToken cancellationToken)
	{
		authenticationRequestParameters.RequestContext.Logger.Verbose(() => "Refreshing access token...");
		await authenticationRequestParameters.AuthorityManager.RunInstanceDiscoveryAndValidationAsync().ConfigureAwait(continueOnCapturedContext: false);
		Dictionary<string, string> bodyParameters = GetBodyParameters(msalRefreshTokenItem.Secret);
		MsalTokenResponse msalTokenResponse = await request.SendTokenRequestAsync(bodyParameters, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		if (msalTokenResponse.RefreshToken == null)
		{
			msalTokenResponse.RefreshToken = msalRefreshTokenItem.Secret;
			authenticationRequestParameters.RequestContext.Logger.Warning("Refresh token was missing from the token refresh response, so the refresh token in the request is returned instead. ");
		}
		return msalTokenResponse;
	}

	private static Dictionary<string, string> GetBodyParameters(string refreshTokenSecret)
	{
		return new Dictionary<string, string>
		{
			["client_info"] = "1",
			["grant_type"] = "refresh_token",
			["refresh_token"] = refreshTokenSecret
		};
	}

	internal static bool NeedsRefresh(MsalAccessTokenCacheItem oldAccessToken)
	{
		DateTimeOffset? refreshOnWithJitter;
		return NeedsRefresh(oldAccessToken, out refreshOnWithJitter);
	}

	internal static bool NeedsRefresh(MsalAccessTokenCacheItem oldAccessToken, out DateTimeOffset? refreshOnWithJitter)
	{
		refreshOnWithJitter = GetRefreshOnWithJitter(oldAccessToken);
		if (refreshOnWithJitter.HasValue && refreshOnWithJitter.Value < DateTimeOffset.UtcNow)
		{
			return true;
		}
		return false;
	}

	internal static void ProcessFetchInBackground(MsalAccessTokenCacheItem oldAccessToken, Func<Task<AuthenticationResult>> fetchAction, ILoggerAdapter logger, IServiceBundle serviceBundle, ApiEvent.ApiIds apiId)
	{
		Task.Run(async delegate
		{
			try
			{
				await fetchAction().ConfigureAwait(continueOnCapturedContext: false);
				serviceBundle.PlatformProxy.OtelInstrumentation.IncrementSuccessCounter(serviceBundle.PlatformProxy.GetProductName(), apiId, TokenSource.IdentityProvider, CacheRefreshReason.ProactivelyRefreshed, CacheLevel.None, logger);
			}
			catch (MsalServiceException ex)
			{
				string prefix = $"{"Proactive token refresh failed with MsalServiceException."} Is exception retryable? {ex.IsRetryable}";
				if (ex.StatusCode == 400)
				{
					logger.ErrorPiiWithPrefix(ex, prefix);
				}
				else
				{
					logger.ErrorPiiWithPrefix(ex, prefix);
				}
				serviceBundle.PlatformProxy.OtelInstrumentation.LogFailureMetrics(serviceBundle.PlatformProxy.GetProductName(), ex.ErrorCode, apiId, CacheRefreshReason.ProactivelyRefreshed);
			}
			catch (OperationCanceledException ex2)
			{
				logger.WarningPiiWithPrefix(ex2, "Proactive token refresh was canceled.");
				serviceBundle.PlatformProxy.OtelInstrumentation.LogFailureMetrics(serviceBundle.PlatformProxy.GetProductName(), ex2.GetType().Name, apiId, CacheRefreshReason.ProactivelyRefreshed);
			}
			catch (Exception ex3)
			{
				logger.ErrorPiiWithPrefix(ex3, "Proactive token refresh failed with exception.");
				serviceBundle.PlatformProxy.OtelInstrumentation.LogFailureMetrics(serviceBundle.PlatformProxy.GetProductName(), ex3.GetType().Name, apiId, CacheRefreshReason.ProactivelyRefreshed);
			}
		});
	}

	private static DateTimeOffset? GetRefreshOnWithJitter(MsalAccessTokenCacheItem msalAccessTokenCacheItem)
	{
		if (msalAccessTokenCacheItem.RefreshOn.HasValue)
		{
			int num = s_random.Next(-300, 300);
			return msalAccessTokenCacheItem.RefreshOn.Value + TimeSpan.FromSeconds(num);
		}
		return null;
	}
}
