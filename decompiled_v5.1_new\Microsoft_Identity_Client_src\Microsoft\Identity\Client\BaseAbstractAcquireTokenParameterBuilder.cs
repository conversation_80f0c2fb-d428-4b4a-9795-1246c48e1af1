using System;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;

namespace Microsoft.Identity.Client;

public abstract class BaseAbstractAcquireTokenParameterBuilder<T> where T : BaseAbstractAcquireTokenParameterBuilder<T>
{
	internal IServiceBundle ServiceBundle { get; }

	internal AcquireTokenCommonParameters CommonParameters { get; } = new AcquireTokenCommonParameters();

	protected BaseAbstractAcquireTokenParameterBuilder()
	{
	}

	internal BaseAbstractAcquireTokenParameterBuilder(IServiceBundle serviceBundle)
	{
		ServiceBundle = serviceBundle;
	}

	public abstract Task<AuthenticationResult> ExecuteAsync(CancellationToken cancellationToken);

	internal abstract ApiEvent.ApiIds CalculateApiEventId();

	public Task<AuthenticationResult> ExecuteAsync()
	{
		return ExecuteAsync(CancellationToken.None);
	}

	public T WithCorrelationId(Guid correlationId)
	{
		CommonParameters.UserProvidedCorrelationId = correlationId;
		CommonParameters.UseCorrelationIdFromUser = true;
		return (T)this;
	}

	protected virtual void Validate()
	{
	}

	internal void ValidateAndCalculateApiId()
	{
		Validate();
		CommonParameters.ApiId = CalculateApiEventId();
		CommonParameters.CorrelationId = (CommonParameters.UseCorrelationIdFromUser ? CommonParameters.UserProvidedCorrelationId : Guid.NewGuid());
	}

	internal void ValidateUseOfExperimentalFeature([CallerMemberName] string memberName = "")
	{
		if (!ServiceBundle.Config.ExperimentalFeaturesEnabled)
		{
			throw new MsalClientException("experimental_feature", MsalErrorMessage.ExperimentalFeature(memberName));
		}
	}
}
