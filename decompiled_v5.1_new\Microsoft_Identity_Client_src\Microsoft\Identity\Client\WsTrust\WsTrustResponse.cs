using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;
using System.Xml.Linq;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.WsTrust;

internal class WsTrustResponse
{
	public const string Saml1Assertion = "urn:oasis:names:tc:SAML:1.0:assertion";

	public string Token { get; private set; }

	public string TokenType { get; private set; }

	public static WsTrustResponse CreateFromResponse(string response, WsTrustVersion version)
	{
		return CreateFromResponseDocument(XDocument.Parse(response, LoadOptions.PreserveWhitespace), version);
	}

	public static string ReadErrorResponse(XDocument responseDocument)
	{
		string text = null;
		XElement xElement = responseDocument.Descendants(XmlNamespace.SoapEnvelope + "Body").FirstOrDefault();
		if (xElement != null)
		{
			XElement xElement2 = xElement.Elements(XmlNamespace.SoapEnvelope + "Fault").FirstOrDefault();
			if (xElement2 != null)
			{
				text = GetFaultMessage(xElement2);
			}
		}
		if (string.IsNullOrWhiteSpace(text))
		{
			text = responseDocument.ToString();
		}
		return text;
	}

	private static string GetFaultMessage(XElement fault)
	{
		XElement xElement = fault.Elements(XmlNamespace.SoapEnvelope + "Reason").FirstOrDefault();
		if (xElement != null)
		{
			XElement xElement2 = xElement.Elements(XmlNamespace.SoapEnvelope + "Text").FirstOrDefault();
			if (xElement2 != null)
			{
				using (XmlReader xmlReader = xElement2.CreateReader())
				{
					xmlReader.MoveToContent();
					return xmlReader.ReadInnerXml();
				}
			}
		}
		return null;
	}

	internal static WsTrustResponse CreateFromResponseDocument(XDocument responseDocument, WsTrustVersion version)
	{
		Dictionary<string, string> dictionary = new Dictionary<string, string>();
		XNamespace xNamespace = XmlNamespace.Trust;
		if (version == WsTrustVersion.WsTrust2005)
		{
			xNamespace = XmlNamespace.Trust2005;
		}
		bool flag = true;
		if (version == WsTrustVersion.WsTrust13 && responseDocument.Descendants(xNamespace + "RequestSecurityTokenResponseCollection").FirstOrDefault() == null)
		{
			flag = false;
		}
		if (!flag)
		{
			return null;
		}
		foreach (XElement item in responseDocument.Descendants(xNamespace + "RequestSecurityTokenResponse"))
		{
			XElement xElement = item.Elements(xNamespace + "TokenType").FirstOrDefault();
			if (xElement == null)
			{
				continue;
			}
			XElement xElement2 = item.Elements(xNamespace + "RequestedSecurityToken").FirstOrDefault();
			if (xElement2 == null)
			{
				continue;
			}
			StringBuilder stringBuilder = new StringBuilder();
			foreach (XNode item2 in xElement2.Nodes())
			{
				stringBuilder.Append(item2.ToString(SaveOptions.DisableFormatting));
			}
			dictionary.Add(xElement.Value, stringBuilder.ToString());
		}
		if (dictionary.Count == 0)
		{
			return null;
		}
		string text = (dictionary.ContainsKey("urn:oasis:names:tc:SAML:1.0:assertion") ? "urn:oasis:names:tc:SAML:1.0:assertion" : dictionary.Keys.First());
		return new WsTrustResponse
		{
			TokenType = text,
			Token = dictionary[text]
		};
	}
}
