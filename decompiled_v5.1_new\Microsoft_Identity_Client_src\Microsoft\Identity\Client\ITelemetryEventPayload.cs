using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace Microsoft.Identity.Client;

[Obsolete("Telemetry is sent automatically by MSAL.NET. See https://aka.ms/msal-net-telemetry.", false)]
[EditorBrowsable(EditorBrowsableState.Never)]
public interface ITelemetryEventPayload
{
	string Name { get; }

	IReadOnlyDictionary<string, bool> BoolValues { get; }

	IReadOnlyDictionary<string, long> Int64Values { get; }

	IReadOnlyDictionary<string, int> IntValues { get; }

	IReadOnlyDictionary<string, string> StringValues { get; }

	string ToJsonString();
}
