using System.Collections.Generic;
using System.Text.Json.Serialization;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.Platforms.net6;

namespace Microsoft.Identity.Client.Instance.Validation;

[JsonObject]
[Preserve(AllMembers = true)]
internal class AdfsWebFingerResponse : OAuth2ResponseBase
{
	[JsonPropertyName("subject")]
	public string Subject { get; set; }

	[JsonPropertyName("links")]
	public List<LinksList> Links { get; set; }
}
