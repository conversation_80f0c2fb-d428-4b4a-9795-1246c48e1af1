using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace EnumsNET;

public abstract class EnumMember : IComparable<EnumMember>, IEquatable<EnumMember>, IComparable, IFormattable, IConvertible
{
	private protected readonly EnumMemberInternal Member;

	public object Value => GetValue();

	public string Name => Member.Name;

	public AttributeCollection Attributes => Member.Attributes;

	private protected EnumMember(EnumMemberInternal member)
	{
		Member = member;
	}

	public sealed override string ToString()
	{
		return Member.Name;
	}

	public string AsString()
	{
		return Member.Name;
	}

	public string AsString(string? format)
	{
		if (!string.IsNullOrEmpty(format))
		{
			return Member.AsString(format);
		}
		return Member.Name;
	}

	public string? AsString(EnumFormat format)
	{
		return Member.AsString(format);
	}

	public string? AsString(EnumFormat format0, EnumFormat format1)
	{
		return Member.AsString(ValueCollection.Create(format0, format1));
	}

	public string? AsString(EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return Member.AsString(ValueCollection.Create(format0, format1, format2));
	}

	public string? AsString(params EnumFormat[]? formats)
	{
		if (formats == null || formats.Length == 0)
		{
			return Member.Name;
		}
		return Member.AsString(ValueCollection.Create(formats));
	}

	public bool TryFormat(Span<char> destination, out int charsWritten)
	{
		return EnumCache.TryWriteNonNullableStringToSpan(Member.Name, destination, out charsWritten);
	}

	public bool TryFormat(Span<char> destination, out int charsWritten, ReadOnlySpan<char> format)
	{
		if (format.Length != 0)
		{
			return Member.TryFormat(destination, out charsWritten, format);
		}
		return TryFormat(destination, out charsWritten);
	}

	public bool TryFormat(Span<char> destination, out int charsWritten, params EnumFormat[]? formats)
	{
		if (formats == null || formats.Length == 0)
		{
			return TryFormat(destination, out charsWritten);
		}
		return Member.TryFormat(destination, out charsWritten, ValueCollection.Create(formats));
	}

	public object GetUnderlyingValue()
	{
		return Member.GetValue();
	}

	[CLSCompliant(false)]
	public sbyte ToSByte()
	{
		return Member.ToSByte();
	}

	public byte ToByte()
	{
		return Member.ToByte();
	}

	public short ToInt16()
	{
		return Member.ToInt16();
	}

	[CLSCompliant(false)]
	public ushort ToUInt16()
	{
		return Member.ToUInt16();
	}

	public int ToInt32()
	{
		return Member.ToInt32();
	}

	[CLSCompliant(false)]
	public uint ToUInt32()
	{
		return Member.ToUInt32();
	}

	public long ToInt64()
	{
		return Member.ToInt64();
	}

	[CLSCompliant(false)]
	public ulong ToUInt64()
	{
		return Member.ToUInt64();
	}

	public sealed override int GetHashCode()
	{
		return Member.GetHashCode();
	}

	public bool Equals(EnumMember? other)
	{
		return this == other;
	}

	public sealed override bool Equals(object? other)
	{
		return this == other;
	}

	private protected abstract object GetValue();

	internal IValuesContainer GetFlags()
	{
		return Member.GetFlags();
	}

	internal IReadOnlyList<EnumMember> GetFlagMembers()
	{
		return Member.GetFlagMembers();
	}

	internal bool IsValidFlagCombination()
	{
		return Member.IsValidFlagCombination();
	}

	internal int GetFlagCount()
	{
		return Member.GetFlagCount();
	}

	internal bool HasAnyFlags()
	{
		return Member.HasAnyFlags();
	}

	internal bool HasAllFlags()
	{
		return Member.HasAllFlags();
	}

	string IFormattable.ToString(string? format, IFormatProvider? formatProvider)
	{
		return AsString(format);
	}

	TypeCode IConvertible.GetTypeCode()
	{
		return Member.GetTypeCode();
	}

	bool IConvertible.ToBoolean(IFormatProvider? provider)
	{
		return Member.ToBoolean(provider);
	}

	char IConvertible.ToChar(IFormatProvider? provider)
	{
		return Member.ToChar(provider);
	}

	sbyte IConvertible.ToSByte(IFormatProvider? provider)
	{
		return Member.ToSByte();
	}

	byte IConvertible.ToByte(IFormatProvider? provider)
	{
		return Member.ToByte();
	}

	short IConvertible.ToInt16(IFormatProvider? provider)
	{
		return Member.ToInt16();
	}

	ushort IConvertible.ToUInt16(IFormatProvider? provider)
	{
		return Member.ToUInt16();
	}

	int IConvertible.ToInt32(IFormatProvider? provider)
	{
		return Member.ToInt32();
	}

	uint IConvertible.ToUInt32(IFormatProvider? provider)
	{
		return Member.ToUInt32();
	}

	long IConvertible.ToInt64(IFormatProvider? provider)
	{
		return Member.ToInt64();
	}

	ulong IConvertible.ToUInt64(IFormatProvider? provider)
	{
		return Member.ToUInt64();
	}

	float IConvertible.ToSingle(IFormatProvider? provider)
	{
		return Member.ToSingle(provider);
	}

	double IConvertible.ToDouble(IFormatProvider? provider)
	{
		return Member.ToDouble(provider);
	}

	decimal IConvertible.ToDecimal(IFormatProvider? provider)
	{
		return Member.ToDecimal(provider);
	}

	DateTime IConvertible.ToDateTime(IFormatProvider? provider)
	{
		return Member.ToDateTime(provider);
	}

	string IConvertible.ToString(IFormatProvider? provider)
	{
		return Member.Name;
	}

	object IConvertible.ToType(Type conversionType, IFormatProvider? provider)
	{
		return Member.ToType(conversionType, provider);
	}

	int IComparable.CompareTo(object? obj)
	{
		return ((IComparable<EnumMember>)this).CompareTo(obj as EnumMember);
	}

	int IComparable<EnumMember>.CompareTo(EnumMember? other)
	{
		return 0;
	}
}
public sealed class EnumMember<TEnum> : EnumMember, IComparable<EnumMember<TEnum>>, IComparable<EnumMember>, IEquatable<EnumMember<TEnum>>
{
	public new TEnum Value
	{
		get
		{
			TEnum source = default(TEnum);
			Member.GetValue(ref UnsafeUtility.As<TEnum, byte>(ref source));
			return source;
		}
	}

	internal EnumMember(EnumMemberInternal member)
		: base(member)
	{
	}

	public bool Equals(EnumMember<TEnum>? other)
	{
		return this == other;
	}

	private protected override object GetValue()
	{
		return Value;
	}

	int IComparable<EnumMember>.CompareTo(EnumMember? other)
	{
		return ((IComparable<EnumMember<TEnum>>)this).CompareTo(other as EnumMember<TEnum>);
	}

	int IComparable<EnumMember<TEnum>>.CompareTo(EnumMember<TEnum>? other)
	{
		return Member.CompareTo(other?.Member);
	}
}
