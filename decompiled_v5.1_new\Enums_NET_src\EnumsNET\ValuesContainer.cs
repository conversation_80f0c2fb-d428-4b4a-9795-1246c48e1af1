using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Threading;
using EnumsNET.Numerics;
using EnumsNET.Utilities;

namespace EnumsNET;

internal sealed class ValuesContainer<TEnum, TUnderlying, TUnderlyingOperations> : IReadOnlyList<TEnum>, IEnumerable<TEnum>, IEnumerable, IReadOnlyCollection<TEnum>, IValuesContainer where TEnum : struct, Enum where TUnderlying : struct, IComparable<TUnderlying>, IEquatable<TUnderlying>, IConvertible where TUnderlyingOperations : struct, IUnderlyingOperations<TUnderlying>
{
	private readonly IEnumerable<EnumMemberInternal<TUnderlying, TUnderlyingOperations>> _members;

	private TEnum[]? _valuesArray;

	private IReadOnlyList<object>? _nonGenericValuesContainer;

	public int Count { get; }

	public TEnum this[int index] => (_valuesArray ?? (_valuesArray = ArrayHelper.ToArray(this, Count)))[index];

	public ValuesContainer(IEnumerable<EnumMemberInternal<TUnderlying, TUnderlyingOperations>> members, int count, bool cached)
	{
		_members = members;
		Count = count;
		if (cached)
		{
			_valuesArray = ArrayHelper.ToArray(this, count);
		}
	}

	public IEnumerator<TEnum> GetEnumerator()
	{
		if (_valuesArray == null)
		{
			return Enumerate();
		}
		return ((IEnumerable<TEnum>)_valuesArray).GetEnumerator();
	}

	private IEnumerator<TEnum> Enumerate()
	{
		foreach (EnumMemberInternal<TUnderlying, TUnderlyingOperations> member in _members)
		{
			TUnderlying source = member.Value;
			yield return UnsafeUtility.As<TUnderlying, TEnum>(ref source);
		}
	}

	public IReadOnlyList<object> GetNonGenericContainer()
	{
		IReadOnlyList<object> nonGenericValuesContainer = _nonGenericValuesContainer;
		return nonGenericValuesContainer ?? Interlocked.CompareExchange(ref _nonGenericValuesContainer, nonGenericValuesContainer = new NonGenericValuesContainer<TEnum, TUnderlying, TUnderlyingOperations>(this), null) ?? nonGenericValuesContainer;
	}

	IEnumerator IEnumerable.GetEnumerator()
	{
		return GetEnumerator();
	}
}
