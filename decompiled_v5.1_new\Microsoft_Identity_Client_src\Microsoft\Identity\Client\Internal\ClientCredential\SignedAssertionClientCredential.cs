using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.TelemetryCore;

namespace Microsoft.Identity.Client.Internal.ClientCredential;

internal class SignedAssertionClientCredential : IClientCredential
{
	private readonly string _signedAssertion;

	public AssertionType AssertionType => AssertionType.ClientAssertion;

	public SignedAssertionClientCredential(string signedAssertion)
	{
		_signedAssertion = signedAssertion;
	}

	public Task AddConfidentialClientParametersAsync(OAuth2Client oAuth2Client, ILoggerAdapter logger, ICryptographyManager cryptographyManager, string clientId, string tokenEndpoint, bool sendX5C, bool useSha2, CancellationToken cancellationToken)
	{
		oAuth2Client.AddBodyParameter("client_assertion_type", "urn:ietf:params:oauth:client-assertion-type:jwt-bearer");
		oAuth2Client.AddBodyParameter("client_assertion", _signedAssertion);
		return Task.CompletedTask;
	}
}
