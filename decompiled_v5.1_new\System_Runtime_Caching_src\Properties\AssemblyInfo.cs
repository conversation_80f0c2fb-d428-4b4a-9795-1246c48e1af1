using System;
using System.Diagnostics;
using System.Reflection;
using System.Resources;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Security;
using System.Security.Permissions;

[assembly: AssemblyMetadata("Serviceable", "True")]
[assembly: AssemblyMetadata("PreferInbox", "True")]
[assembly: AssemblyDefaultAlias("System.Runtime.Caching")]
[assembly: NeutralResourcesLanguage("en-US")]
[assembly: CLSCompliant(true)]
[assembly: AssemblyMetadata("IsTrimmable", "True")]
[assembly: DefaultDllImportSearchPaths(DllImportSearchPath.System32 | DllImportSearchPath.AssemblyDirectory)]
[assembly: AssemblyCompany("Microsoft Corporation")]
[assembly: AssemblyCopyright("© Microsoft Corporation. All rights reserved.")]
[assembly: AssemblyDescription("Provides classes to use caching facilities.\r\n\r\nCommonly Used Types:\r\nSystem.Runtime.Caching.CacheEntryChangeMonitor\r\nSystem.Runtime.Caching.CacheEntryRemovedArguments\r\nSystem.Runtime.Caching.CacheEntryUpdateArguments\r\nSystem.Runtime.Caching.CacheItem\r\nSystem.Runtime.Caching.CacheItemPolicy\r\nSystem.Runtime.Caching.ChangeMonitor\r\nSystem.Runtime.Caching.FileChangeMonitor\r\nSystem.Runtime.Caching.HostFileChangeMonitor\r\nSystem.Runtime.Caching.MemoryCache\r\nSystem.Runtime.Caching.ObjectCache")]
[assembly: AssemblyFileVersion("8.0.23.53103")]
[assembly: AssemblyInformationalVersion("8.0.0+5535e31a712343a63f5d7d796cd874e563e5ac14")]
[assembly: AssemblyProduct("Microsoft® .NET")]
[assembly: AssemblyTitle("System.Runtime.Caching")]
[assembly: AssemblyMetadata("RepositoryUrl", "https://github.com/dotnet/runtime")]
[assembly: AssemblyVersion("*******")]
[module: RefSafetyRules(11)]
