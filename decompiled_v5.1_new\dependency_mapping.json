{"dll_to_package_mapping": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll": "Microsoft.AspNetCore.Mvc.NewtonsoftJson", "Newtonsoft.Json.dll": "Newtonsoft.Json", "Quartz.dll": "Quartz", "Microsoft.Data.SqlClient.dll": "Microsoft.Data.SqlClient", "NPOI.Core.dll": "NPOI", "NPOI.OOXML.dll": "NPOI", "NPOI.OpenXml4Net.dll": "NPOI", "NPOI.OpenXmlFormats.dll": "NPOI", "SixLabors.ImageSharp.dll": "SixLabors.ImageSharp", "SixLabors.Fonts.dll": "SixLabors.Fonts", "SixLabors.ImageSharp.Drawing.dll": "SixLabors.ImageSharp.Drawing", "System.Memory.Data.dll": "System.Memory.Data", "System.ClientModel.dll": "System.ClientModel", "Azure.Core.dll": "Azure.Core", "Microsoft.Bcl.AsyncInterfaces.dll": "Microsoft.Bcl.AsyncInterfaces", "Microsoft.Identity.Client.dll": "Microsoft.Identity.Client", "Microsoft.Identity.Client.Extensions.Msal.dll": "Microsoft.Identity.Client.Extensions.Msal", "System.Security.Cryptography.ProtectedData.dll": "System.Security.Cryptography.ProtectedData", "log4net.dll": "log4net", "Nerdbank.Streams.dll": "Nerdbank.Streams", "Acornima.dll": "Acornima", "System.Configuration.ConfigurationManager.dll": "System.Configuration.ConfigurationManager", "Microsoft.IdentityModel.Tokens.dll": "Microsoft.IdentityModel.Tokens", "Microsoft.IdentityModel.Logging.dll": "Microsoft.IdentityModel.Logging", "Microsoft.IdentityModel.Protocols.dll": "Microsoft.IdentityModel.Protocols", "System.IdentityModel.Tokens.Jwt.dll": "System.IdentityModel.Tokens.Jwt", "Microsoft.IdentityModel.Abstractions.dll": "Microsoft.IdentityModel.Abstractions", "Microsoft.VisualStudio.Validation.dll": "Microsoft.VisualStudio.Validation", "Microsoft.VisualStudio.Threading.dll": "Microsoft.VisualStudio.Threading", "ICSharpCode.SharpZipLib.dll": "SharpZipLib", "BouncyCastle.Cryptography.dll": "BouncyCastle.Cryptography", "Enums.NET.dll": "Enums.NET", "ExtendedNumerics.BigDecimal.dll": "ExtendedNumerics.BigDecimal", "MathNet.Numerics.dll": "MathNet.Numerics", "Microsoft.IO.RecyclableMemoryStream.dll": "Microsoft.IO.RecyclableMemoryStream", "Microsoft.IdentityModel.JsonWebTokens.dll": "Microsoft.IdentityModel.JsonWebTokens", "Newtonsoft.Json.Bson.dll": "Newtonsoft.Json.Bson", "System.Runtime.Caching.dll": "System.Runtime.Caching"}, "package_versions": {"Microsoft.AspNetCore.Mvc.NewtonsoftJson": "6.0.0", "Newtonsoft.Json": "13.0.3", "Quartz": "3.6.0", "Microsoft.Data.SqlClient": "5.1.0", "NPOI": "2.6.0", "SixLabors.ImageSharp": "2.1.0", "SixLabors.Fonts": "1.0.0", "SixLabors.ImageSharp.Drawing": "1.0.0", "System.Memory.Data": "6.0.0", "System.ClientModel": "1.0.0", "Azure.Core": "1.35.0", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.Identity.Client": "4.56.0", "Microsoft.Identity.Client.Extensions.Msal": "2.25.0", "System.Security.Cryptography.ProtectedData": "6.0.0", "log4net": "2.0.15", "Nerdbank.Streams": "2.10.0", "Acornima": "1.17.0", "System.Configuration.ConfigurationManager": "6.0.0", "Microsoft.IdentityModel.Tokens": "6.24.0", "Microsoft.IdentityModel.Logging": "6.24.0", "Microsoft.IdentityModel.Protocols": "6.24.0", "System.IdentityModel.Tokens.Jwt": "6.24.0", "Microsoft.IdentityModel.Abstractions": "6.24.0", "Microsoft.VisualStudio.Validation": "17.6.11", "Microsoft.VisualStudio.Threading": "17.6.40", "SharpZipLib": "1.4.2", "BouncyCastle.Cryptography": "2.2.1", "Enums.NET": "4.0.1", "ExtendedNumerics.BigDecimal": "2025.1001.2.129", "MathNet.Numerics": "5.0.0", "Microsoft.IO.RecyclableMemoryStream": "3.0.0", "Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Newtonsoft.Json.Bson": "1.0.2", "System.Runtime.Caching": "6.0.0"}}