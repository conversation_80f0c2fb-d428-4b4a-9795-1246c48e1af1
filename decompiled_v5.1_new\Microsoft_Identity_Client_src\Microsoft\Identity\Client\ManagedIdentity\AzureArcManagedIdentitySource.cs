using System;
using System.Globalization;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.PlatformsCommon.Shared;

namespace Microsoft.Identity.Client.ManagedIdentity;

internal class AzureArcManagedIdentitySource : AbstractManagedIdentity
{
	private const string ArcApiVersion = "2019-11-01";

	private const string AzureArc = "Azure Arc";

	private readonly Uri _endpoint;

	public static AbstractManagedIdentity Create(RequestContext requestContext)
	{
		string identityEndpoint = EnvironmentVariables.IdentityEndpoint;
		requestContext.Logger.Info(() => "[Managed Identity] Azure Arc managed identity is available.");
		if (!Uri.TryCreate(identityEndpoint, UriKind.Absolute, out Uri endpointUri))
		{
			string errorMessage = string.Format(CultureInfo.InvariantCulture, "[Managed Identity] The environment variable {0} contains an invalid Uri {1} in {2} managed identity source.", "IDENTITY_ENDPOINT", identityEndpoint, "Azure Arc");
			throw MsalServiceExceptionFactory.CreateManagedIdentityException("invalid_managed_identity_endpoint", errorMessage, null, ManagedIdentitySource.AzureArc, null);
		}
		requestContext.Logger.Verbose(() => "[Managed Identity] Creating Azure Arc managed identity. Endpoint URI: " + endpointUri);
		return new AzureArcManagedIdentitySource(endpointUri, requestContext);
	}

	private AzureArcManagedIdentitySource(Uri endpoint, RequestContext requestContext)
		: base(requestContext, ManagedIdentitySource.AzureArc)
	{
		_endpoint = endpoint;
		if (requestContext.ServiceBundle.Config.ManagedIdentityId.IsUserAssigned)
		{
			string errorMessage = string.Format(CultureInfo.InvariantCulture, "[Managed Identity] User assigned identity is not supported by the {0} Managed Identity. To authenticate with the system assigned identity omit the client id in ManagedIdentityApplicationBuilder.Create().", "Azure Arc");
			throw MsalServiceExceptionFactory.CreateManagedIdentityException("user_assigned_managed_identity_not_supported", errorMessage, null, ManagedIdentitySource.AzureArc, null);
		}
	}

	protected override ManagedIdentityRequest CreateRequest(string resource)
	{
		ManagedIdentityRequest managedIdentityRequest = new ManagedIdentityRequest(HttpMethod.Get, _endpoint);
		managedIdentityRequest.Headers.Add("Metadata", "true");
		managedIdentityRequest.QueryParameters["api-version"] = "2019-11-01";
		managedIdentityRequest.QueryParameters["resource"] = resource;
		return managedIdentityRequest;
	}

	protected override async Task<ManagedIdentityResponse> HandleResponseAsync(AcquireTokenForManagedIdentityParameters parameters, HttpResponse response, CancellationToken cancellationToken)
	{
		_requestContext.Logger.Verbose(() => $"[Managed Identity] Response received. Status code: {response.StatusCode}");
		if (response.StatusCode == HttpStatusCode.Unauthorized)
		{
			if (!response.HeadersAsDictionary.TryGetValue("WWW-Authenticate", out var value))
			{
				_requestContext.Logger.Error("[Managed Identity] WWW-Authenticate header is expected but not found.");
				throw MsalServiceExceptionFactory.CreateManagedIdentityException("managed_identity_request_failed", "[Managed Identity] Did not receive expected WWW-Authenticate header in the response from Azure Arc Managed Identity Endpoint.", null, ManagedIdentitySource.AzureArc, null);
			}
			string[] array = value.Split(new char[1] { '=' }, StringSplitOptions.RemoveEmptyEntries);
			ValidateSplitChallenge(array);
			string value2 = "Basic " + File.ReadAllText(array[1]);
			ManagedIdentityRequest managedIdentityRequest = CreateRequest(parameters.Resource);
			_requestContext.Logger.Verbose(() => "[Managed Identity] Adding authorization header to the request.");
			managedIdentityRequest.Headers.Add("Authorization", value2);
			response = await _requestContext.ServiceBundle.HttpManager.SendGetAsync(managedIdentityRequest.ComputeUri(), managedIdentityRequest.Headers, _requestContext.Logger, retry: true, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			return await base.HandleResponseAsync(parameters, response, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		}
		return await base.HandleResponseAsync(parameters, response, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}

	private void ValidateSplitChallenge(string[] splitChallenge)
	{
		if (splitChallenge.Length != 2)
		{
			throw CreateManagedIdentityException("managed_identity_request_failed", "[Managed Identity] The WWW-Authenticate header in the response from Azure Arc Managed Identity Endpoint did not match the expected format.");
		}
		_requestContext.Logger.Verbose(() => "[Managed Identity] Challenge is valid. FilePath: " + splitChallenge[1]);
		if (!IsValidPath(splitChallenge[1]))
		{
			throw CreateManagedIdentityException("managed_identity_request_failed", "[Managed Identity] The file on the file path in the WWW-Authenticate header is not secure.");
		}
		_requestContext.Logger.Verbose(() => "[Managed Identity] File path is valid. Path: " + splitChallenge[1]);
		long length = new FileInfo(splitChallenge[1]).Length;
		if (!File.Exists(splitChallenge[1]) || length > 4096)
		{
			_requestContext.Logger.Error($"[Managed Identity] File does not exist or is greater than 4096 bytes. File exists: {File.Exists(splitChallenge[1])}. Length of file: {length}");
			throw CreateManagedIdentityException("managed_identity_request_failed", "[Managed Identity] The file on the file path in the WWW-Authenticate header is not secure.");
		}
		_requestContext.Logger.Verbose(() => "[Managed Identity] File exists and is less than 4096 bytes.");
	}

	private MsalException CreateManagedIdentityException(string errorCode, string errorMessage)
	{
		return MsalServiceExceptionFactory.CreateManagedIdentityException(errorCode, errorMessage, null, ManagedIdentitySource.AzureArc, null);
	}

	private bool IsValidPath(string path)
	{
		string value;
		if (DesktopOsHelper.IsWindows())
		{
			value = Environment.ExpandEnvironmentVariables("%ProgramData%\\AzureConnectedMachineAgent\\Tokens\\") + Path.GetFileNameWithoutExtension(path) + ".key";
		}
		else
		{
			if (!DesktopOsHelper.IsLinux())
			{
				throw CreateManagedIdentityException("managed_identity_request_failed", "[Managed Identity] The platform is not supported by Azure Arc. Azure Arc only supports Windows and Linux.");
			}
			value = "/var/opt/azcmagent/tokens/" + Path.GetFileNameWithoutExtension(path) + ".key";
		}
		return path.Equals(value);
	}
}
