using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

public class AuthenticationInfoParameters
{
	private const string AuthenticationInfoKey = "Authentication-Info";

	public string NextNonce { get; private set; }

	public string this[string key] => RawParameters[key];

	internal IDictionary<string, string> RawParameters { get; private set; }

	public static AuthenticationInfoParameters CreateFromResponseHeaders(HttpResponseHeaders httpResponseHeaders)
	{
		AuthenticationInfoParameters authenticationInfoParameters = new AuthenticationInfoParameters();
		try
		{
			IEnumerable<string> value = httpResponseHeaders.SingleOrDefault<KeyValuePair<string, IEnumerable<string>>>((KeyValuePair<string, IEnumerable<string>> header) => header.Key == "Authentication-Info").Value;
			if (value != null)
			{
				string text = value.FirstOrDefault();
				string[] array = text.Split(new char[1] { ' ' }, 2);
				IDictionary<string, string> dictionary;
				if (array.Length != 2)
				{
					dictionary = new Dictionary<string, string>();
					dictionary.Add(new KeyValuePair<string, string>("Authentication-Info", text));
				}
				else
				{
					dictionary = (from v in CoreHelpers.SplitWithQuotes(array[1], ',')
						select AuthenticationHeaderParser.CreateKeyValuePair(v.Trim(), "Authentication-Info")).ToDictionary<KeyValuePair<string, string>, string, string>((KeyValuePair<string, string> pair) => pair.Key, (KeyValuePair<string, string> pair) => pair.Value, StringComparer.OrdinalIgnoreCase);
					if (dictionary.TryGetValue("nextnonce", out var value2))
					{
						authenticationInfoParameters.NextNonce = value2;
					}
				}
				authenticationInfoParameters.RawParameters = dictionary;
			}
			return authenticationInfoParameters;
		}
		catch (Exception ex) when (!(ex is MsalClientException))
		{
			throw new MsalClientException("unable_to_parse_authentication_header", $"{"MSAL is unable to parse the authentication header returned from the resource endpoint. This can be a result of a malformed header returned in either the WWW-Authenticate or the Authentication-Info collections acquired from the provided endpoint."}Response Headers: {httpResponseHeaders} See inner exception for details.", ex);
		}
	}
}
