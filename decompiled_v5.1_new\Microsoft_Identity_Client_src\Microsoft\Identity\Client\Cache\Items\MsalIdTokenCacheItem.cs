using System;
using System.Text.Json.Nodes;
using Microsoft.Identity.Client.Cache.Keys;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Cache.Items;

internal class MsalIdTokenCacheItem : MsalCredentialCacheItemBase
{
	private readonly Lazy<IdToken> idTokenLazy;

	private Lazy<IiOSKey> iOSCacheKeyLazy;

	internal string TenantId { get; set; }

	internal IdToken IdToken => idTokenLazy.Value;

	public string CacheKey { get; private set; }

	public IiOSKey iOSCacheKey => iOSCacheKeyLazy.Value;

	internal MsalIdTokenCacheItem()
	{
		base.CredentialType = "IdToken";
		idTokenLazy = new Lazy<IdToken>(() => IdToken.Parse(base.Secret));
	}

	internal MsalIdTokenCacheItem(string preferredCacheEnv, string clientId, MsalTokenResponse response, string tenantId, string homeAccountId)
		: this(preferredCacheEnv, clientId, response.IdToken, response.ClientInfo, homeAccountId, tenantId)
	{
	}

	internal MsalIdTokenCacheItem(string preferredCacheEnv, string clientId, string secret, string rawClientInfo, string homeAccountId, string tenantId)
		: this()
	{
		base.Environment = preferredCacheEnv;
		TenantId = tenantId;
		base.ClientId = clientId;
		base.Secret = secret;
		base.RawClientInfo = rawClientInfo;
		base.HomeAccountId = homeAccountId;
		InitCacheKey();
	}

	internal void InitCacheKey()
	{
		CacheKey = MsalCacheKeys.GetCredentialKey(base.HomeAccountId, base.Environment, "IdToken", base.ClientId, TenantId, null);
		iOSCacheKeyLazy = new Lazy<IiOSKey>(InitiOSKey);
	}

	private IiOSKey InitiOSKey()
	{
		string iOSAccount = MsalCacheKeys.GetiOSAccountKey(base.HomeAccountId, base.Environment);
		string iOSGeneric = MsalCacheKeys.GetiOSGenericKey("IdToken", base.ClientId, TenantId);
		string iOSService = MsalCacheKeys.GetiOSServiceKey("IdToken", base.ClientId, TenantId, null);
		int iOSType = 2003;
		return new IosKey(iOSAccount, iOSService, iOSGeneric, iOSType);
	}

	internal static MsalIdTokenCacheItem FromJsonString(string json)
	{
		if (string.IsNullOrWhiteSpace(json))
		{
			return null;
		}
		return FromJObject(JsonHelper.ParseIntoJsonObject(json));
	}

	internal static MsalIdTokenCacheItem FromJObject(JsonObject j)
	{
		MsalIdTokenCacheItem msalIdTokenCacheItem = new MsalIdTokenCacheItem();
		msalIdTokenCacheItem.TenantId = JsonHelper.ExtractExistingOrEmptyString(j, "realm");
		msalIdTokenCacheItem.PopulateFieldsFromJObject(j);
		msalIdTokenCacheItem.InitCacheKey();
		return msalIdTokenCacheItem;
	}

	internal override JsonObject ToJObject()
	{
		JsonObject jsonObject = base.ToJObject();
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "realm", TenantId);
		return jsonObject;
	}

	internal string ToJsonString()
	{
		return ToJObject().ToString();
	}

	internal string GetUsername()
	{
		object obj = IdToken?.PreferredUsername;
		if (obj == null)
		{
			IdToken idToken = IdToken;
			if (idToken == null)
			{
				return null;
			}
			obj = idToken.Upn;
		}
		return (string)obj;
	}
}
