#define TRACE
using System;
using System.Buffers;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.IO.Pipelines;
using System.Linq;
using System.Runtime.ExceptionServices;
using System.Runtime.Serialization;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using MessagePack;
using Microsoft;
using Microsoft.VisualStudio.Threading;

namespace Nerdbank.Streams;

public class MultiplexingStream : IDisposableObservable, IDisposable, System.IAsyncDisposable
{
	[DebuggerDisplay("{DebuggerDisplay,nq}")]
	public class Channel : IDisposableObservable, IDisposable, IDuplexPipe
	{
		private struct PipeWriterRental : IDisposable
		{
			private readonly AsyncSemaphore.Releaser releaser;

			internal PipeWriter Writer { get; }

			internal PipeWriterRental(PipeWriter writer, AsyncSemaphore.Releaser releaser)
			{
				Writer = writer;
				this.releaser = releaser;
			}

			public void Dispose()
			{
				releaser.Dispose();
			}
		}

		[DataContract]
		internal class OfferParameters
		{
			[DataMember]
			internal string Name { get; }

			[DataMember]
			internal long? RemoteWindowSize { get; }

			internal OfferParameters(string name, long? remoteWindowSize)
			{
				Name = name ?? throw new ArgumentNullException("name");
				RemoteWindowSize = remoteWindowSize;
			}
		}

		[DataContract]
		internal class AcceptanceParameters
		{
			[DataMember]
			internal long? RemoteWindowSize { get; }

			internal AcceptanceParameters(long? remoteWindowSize)
			{
				RemoteWindowSize = remoteWindowSize;
			}
		}

		private class WindowPipeReader : PipeReader
		{
			private readonly Channel owner;

			private readonly PipeReader inner;

			private ReadResult lastReadResult;

			private long bytesProcessed;

			private SequencePosition lastExaminedPosition;

			internal WindowPipeReader(Channel owner, PipeReader inner)
			{
				this.owner = owner;
				this.inner = inner;
			}

			public override void AdvanceTo(SequencePosition consumed)
			{
				long bytesExamined = Consumed(consumed, consumed);
				inner.AdvanceTo(consumed);
				owner.LocalContentExamined(bytesExamined);
			}

			public override void AdvanceTo(SequencePosition consumed, SequencePosition examined)
			{
				long bytesExamined = Consumed(consumed, examined);
				inner.AdvanceTo(consumed, examined);
				owner.LocalContentExamined(bytesExamined);
			}

			public override void CancelPendingRead()
			{
				inner.CancelPendingRead();
			}

			public override void Complete(Exception? exception = null)
			{
				inner.Complete(exception);
			}

			public override async ValueTask<ReadResult> ReadAsync(CancellationToken cancellationToken = default(CancellationToken))
			{
				return lastReadResult = await inner.ReadAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			}

			public override bool TryRead(out ReadResult readResult)
			{
				bool result = inner.TryRead(out readResult);
				lastReadResult = readResult;
				return result;
			}

			public override ValueTask CompleteAsync(Exception? exception = null)
			{
				return inner.CompleteAsync(exception);
			}

			[Obsolete]
			public override void OnWriterCompleted(Action<Exception?, object?> callback, object? state)
			{
				inner.OnWriterCompleted(callback, state);
			}

			private long Consumed(SequencePosition consumed, SequencePosition examined)
			{
				SequencePosition start = lastExaminedPosition;
				if (start.Equals(default(SequencePosition)))
				{
					start = lastReadResult.Buffer.Start;
				}
				long num = ((start.Equals(lastReadResult.Buffer.Start) && lastReadResult.Buffer.End.Equals(examined)) ? lastReadResult.Buffer.Length : lastReadResult.Buffer.Slice(start, examined).Length);
				bytesProcessed += num;
				long result = 0L;
				if (bytesProcessed >= 20480 || bytesProcessed == owner.localWindowSize)
				{
					result = bytesProcessed;
					bytesProcessed = 0L;
				}
				lastExaminedPosition = (consumed.Equals(examined) ? default(SequencePosition) : examined);
				return result;
			}
		}

		private readonly TaskCompletionSource<AcceptanceParameters> acceptanceSource = new TaskCompletionSource<AcceptanceParameters>(TaskCreationOptions.RunContinuationsAsynchronously);

		private readonly TaskCompletionSource<object?> completionSource = new TaskCompletionSource<object>();

		private readonly CancellationTokenSource disposalTokenSource = new CancellationTokenSource();

		private readonly TaskCompletionSource<object?>? optionsAppliedTaskSource;

		private readonly AsyncManualResetEvent mxStreamIOWriterCompleted = new AsyncManualResetEvent();

		private readonly AsyncManualResetEvent remoteWindowHasCapacity = new AsyncManualResetEvent(initialState: true);

		private readonly QualifiedChannelId channelId;

		private readonly AsyncSemaphore mxStreamIOWriterSemaphore = new AsyncSemaphore(1);

		private long remoteWindowFilled;

		private long? remoteWindowSize;

		private long? localWindowSize;

		private bool isDisposed;

		private Exception? faultingException;

		private PipeReader? mxStreamIOReader;

		private Task? mxStreamIOReaderCompleted;

		private PipeWriter? mxStreamIOWriter;

		private IDuplexPipe? channelIO;

		private IDuplexPipe? existingPipe;

		private bool? existingPipeGiven;

		[Obsolete("Use QualifiedId instead.")]
		public int Id => checked((int)channelId.Id);

		public QualifiedChannelId QualifiedId => channelId;

		public TraceSource? TraceSource { get; private set; }

		public bool IsDisposed
		{
			get
			{
				if (!isDisposed)
				{
					return Completion.IsCompleted;
				}
				return true;
			}
		}

		public PipeReader Input
		{
			get
			{
				Assumes.True(existingPipeGiven.HasValue, "this.existingPipeGiven.HasValue");
				Assumes.NotNull(channelIO);
				if (!existingPipeGiven.Value)
				{
					return channelIO.Input;
				}
				throw new NotSupportedException(Strings.NotSupportedWhenExistingPipeSpecified);
			}
		}

		public PipeWriter Output
		{
			get
			{
				Assumes.True(existingPipeGiven.HasValue, "this.existingPipeGiven.HasValue");
				Assumes.NotNull(channelIO);
				if (!existingPipeGiven.Value)
				{
					return channelIO.Output;
				}
				throw new NotSupportedException(Strings.NotSupportedWhenExistingPipeSpecified);
			}
		}

		public Task Acceptance => acceptanceSource.Task;

		public Task Completion => completionSource.Task;

		public MultiplexingStream MultiplexingStream { get; }

		internal CancellationToken DisposalToken => disposalTokenSource.Token;

		internal OfferParameters OfferParams { get; }

		internal string Name => OfferParams.Name;

		internal bool IsAccepted => Acceptance.Status == TaskStatus.RanToCompletion;

		internal bool IsRejectedOrCanceled => Acceptance.Status == TaskStatus.Canceled;

		internal bool IsRemotelyTerminated { get; set; }

		internal Task OptionsApplied => optionsAppliedTaskSource?.Task ?? Task.CompletedTask;

		private string DebuggerDisplay => QualifiedId.DebuggerDisplay + " " + (Name ?? "(anonymous)");

		private object SyncObject => acceptanceSource;

		private long RemoteWindowRemaining
		{
			get
			{
				lock (SyncObject)
				{
					Assumes.True(remoteWindowSize > 0, "this.remoteWindowSize > 0");
					return remoteWindowSize.Value - remoteWindowFilled;
				}
			}
		}

		private bool BackpressureSupportEnabled => MultiplexingStream.protocolMajorVersion > 1;

		internal Channel(MultiplexingStream multiplexingStream, QualifiedChannelId channelId, OfferParameters offerParameters, ChannelOptions? channelOptions = null)
		{
			Requires.NotNull(multiplexingStream, "multiplexingStream");
			Requires.NotNull(offerParameters, "offerParameters");
			MultiplexingStream = multiplexingStream;
			this.channelId = channelId;
			OfferParams = offerParameters;
			switch (channelId.Source)
			{
			case ChannelSource.Local:
				localWindowSize = offerParameters.RemoteWindowSize;
				break;
			case ChannelSource.Remote:
				remoteWindowSize = offerParameters.RemoteWindowSize;
				break;
			case ChannelSource.Seeded:
				remoteWindowSize = offerParameters.RemoteWindowSize;
				localWindowSize = offerParameters.RemoteWindowSize;
				break;
			default:
				throw new NotSupportedException();
			}
			if (channelOptions == null)
			{
				optionsAppliedTaskSource = new TaskCompletionSource<object>();
			}
			else
			{
				ApplyChannelOptions(channelOptions);
			}
		}

		public void Dispose()
		{
			Dispose(null);
		}

		internal void Dispose(Exception? disposeException)
		{
			lock (SyncObject)
			{
				if (isDisposed)
				{
					return;
				}
				isDisposed = true;
			}
			acceptanceSource.TrySetCanceled();
			optionsAppliedTaskSource?.TrySetCanceled();
			PipeWriter pipeWriter;
			lock (SyncObject)
			{
				pipeWriter = mxStreamIOWriter;
			}
			if (pipeWriter != null)
			{
				pipeWriter.CancelPendingFlush();
				mxStreamIOWriterSemaphore.EnterAsync().ContinueWith(delegate(Task<AsyncSemaphore.Releaser> releaser, object? state)
				{
					try
					{
						Channel channel = (Channel)state;
						PipeWriter pipeWriter2;
						lock (channel.SyncObject)
						{
							pipeWriter2 = channel.mxStreamIOWriter;
						}
						pipeWriter2?.Complete();
						channel.mxStreamIOWriterCompleted.Set();
					}
					finally
					{
						releaser.Result.Dispose();
					}
				}, this, CancellationToken.None, TaskContinuationOptions.OnlyOnRanToCompletion, TaskScheduler.Default);
			}
			if (mxStreamIOReader != null)
			{
				mxStreamIOReader?.CancelPendingRead();
				PipeReader pipeReader = null;
				lock (SyncObject)
				{
					if (!(mxStreamIOReader is UnownedPipeReader))
					{
						pipeReader = mxStreamIOReader;
						mxStreamIOReader = null;
					}
				}
				pipeReader?.Complete();
			}
			if (disposeException != null)
			{
				completionSource.TrySetException(disposeException);
			}
			else
			{
				completionSource.TrySetResult(null);
			}
			remoteWindowHasCapacity.Set();
			disposalTokenSource.Cancel();
			MultiplexingStream.OnChannelDisposed(this, disposeException);
		}

		internal async Task OnChannelTerminatedAsync(Exception? remoteError = null)
		{
			lock (SyncObject)
			{
				if (isDisposed)
				{
					return;
				}
			}
			try
			{
				using PipeWriterRental writerRental = await GetReceivedMessagePipeWriterAsync().ConfigureAwait(continueOnCapturedContext: false);
				await writerRental.Writer.CompleteAsync().ConfigureAwait(continueOnCapturedContext: false);
			}
			catch (ObjectDisposedException)
			{
			}
			DisposeSelfOnFailure(async delegate
			{
				await OptionsApplied.ConfigureAwait(continueOnCapturedContext: false);
				IsRemotelyTerminated = true;
				Dispose(remoteError);
			});
		}

		internal async ValueTask OnContentAsync(ReadOnlySequence<byte> payload, CancellationToken cancellationToken)
		{
			_ = 2;
			try
			{
				PipeWriterRental writerRental = await GetReceivedMessagePipeWriterAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
				if (!mxStreamIOWriterCompleted.IsSet)
				{
					ReadOnlySequence<byte>.Enumerator enumerator = payload.GetEnumerator();
					while (enumerator.MoveNext())
					{
						ReadOnlyMemory<byte> current = enumerator.Current;
						Memory<byte> memory = writerRental.Writer.GetMemory(current.Length);
						current.CopyTo(memory);
						writerRental.Writer.Advance(current.Length);
					}
					if (!payload.IsEmpty && MultiplexingStream.TraceSource.Switch.ShouldTrace(TraceEventType.Verbose))
					{
						MultiplexingStream.TraceSource.TraceData(TraceEventType.Verbose, 18, payload);
					}
					ValueTask<FlushResult> flushResult = writerRental.Writer.FlushAsync(cancellationToken);
					if (!BackpressureSupportEnabled)
					{
						await flushResult.ConfigureAwait(continueOnCapturedContext: false);
					}
					else if (!flushResult.IsCompleted)
					{
						Assumes.True(existingPipeGiven == true, "this.existingPipeGiven == true");
						Fault(new InvalidOperationException(Strings.ExistingPipeOutputHasPauseThresholdSetTooLow));
					}
					if (flushResult.IsCanceled)
					{
						Assumes.True(IsDisposed, "this.IsDisposed");
						await writerRental.Writer.CompleteAsync().ConfigureAwait(continueOnCapturedContext: false);
					}
				}
			}
			catch (ObjectDisposedException) when (IsDisposed)
			{
			}
			catch (Exception exception)
			{
				Fault(exception);
			}
		}

		internal void OnContentWritingCompleted()
		{
			DisposeSelfOnFailure(async delegate
			{
				if (!IsDisposed)
				{
					try
					{
						using PipeWriterRental writerRental = await GetReceivedMessagePipeWriterAsync().ConfigureAwait(continueOnCapturedContext: false);
						await writerRental.Writer.CompleteAsync().ConfigureAwait(continueOnCapturedContext: false);
					}
					catch (ObjectDisposedException)
					{
						if (mxStreamIOWriter != null)
						{
							using (await mxStreamIOWriterSemaphore.EnterAsync().ConfigureAwait(continueOnCapturedContext: false))
							{
								await mxStreamIOWriter.CompleteAsync().ConfigureAwait(continueOnCapturedContext: false);
							}
						}
					}
				}
				else if (mxStreamIOWriter != null)
				{
					using (await mxStreamIOWriterSemaphore.EnterAsync().ConfigureAwait(continueOnCapturedContext: false))
					{
						await mxStreamIOWriter.CompleteAsync().ConfigureAwait(continueOnCapturedContext: false);
					}
				}
				mxStreamIOWriterCompleted.Set();
			});
		}

		internal bool TryAcceptOffer(ChannelOptions channelOptions)
		{
			lock (SyncObject)
			{
				long valueOrDefault = localWindowSize.GetValueOrDefault();
				if (!localWindowSize.HasValue)
				{
					long? channelReceivingWindowSize = channelOptions.ChannelReceivingWindowSize;
					long num;
					if (channelReceivingWindowSize.HasValue)
					{
						long valueOrDefault2 = channelReceivingWindowSize.GetValueOrDefault();
						num = Math.Max(valueOrDefault2, MultiplexingStream.DefaultChannelReceivingWindowSize);
					}
					else
					{
						num = MultiplexingStream.DefaultChannelReceivingWindowSize;
					}
					valueOrDefault = num;
					localWindowSize = valueOrDefault;
				}
			}
			AcceptanceParameters acceptanceParameters = new AcceptanceParameters(localWindowSize.Value);
			if (acceptanceSource.TrySetResult(acceptanceParameters))
			{
				if (QualifiedId.Source != ChannelSource.Seeded)
				{
					ReadOnlySequence<byte> payload = MultiplexingStream.formatter.Serialize(acceptanceParameters);
					MultiplexingStream.SendFrame(new FrameHeader
					{
						Code = ControlCode.OfferAccepted,
						ChannelId = QualifiedId
					}, payload, CancellationToken.None);
				}
				try
				{
					ApplyChannelOptions(channelOptions);
					return true;
				}
				catch (ObjectDisposedException)
				{
				}
			}
			return false;
		}

		internal bool OnAccepted(AcceptanceParameters acceptanceParameters)
		{
			lock (SyncObject)
			{
				if (acceptanceSource.TrySetResult(acceptanceParameters))
				{
					remoteWindowSize = acceptanceParameters.RemoteWindowSize;
					return true;
				}
				return false;
			}
		}

		internal void OnContentProcessed(long bytesProcessed)
		{
			Requires.Range(bytesProcessed >= 0, "bytesProcessed", "A non-negative number is required.");
			lock (SyncObject)
			{
				Assumes.True(bytesProcessed <= remoteWindowFilled, "bytesProcessed <= this.remoteWindowFilled");
				remoteWindowFilled -= bytesProcessed;
				if (remoteWindowFilled < remoteWindowSize)
				{
					remoteWindowHasCapacity.Set();
				}
			}
		}

		private async ValueTask<PipeWriterRental> GetReceivedMessagePipeWriterAsync(CancellationToken cancellationToken = default(CancellationToken))
		{
			using AsyncSemaphore.Releaser releaser = await mxStreamIOWriterSemaphore.EnterAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			try
			{
				lock (SyncObject)
				{
					Verify.NotDisposed(this);
					PipeWriter pipeWriter = mxStreamIOWriter;
					if (pipeWriter == null)
					{
						InitializeOwnPipes();
						pipeWriter = mxStreamIOWriter;
					}
					return new PipeWriterRental(pipeWriter, releaser);
				}
			}
			catch
			{
				releaser.Dispose();
				throw;
			}
		}

		private void ApplyChannelOptions(ChannelOptions channelOptions)
		{
			Requires.NotNull(channelOptions, "channelOptions");
			Assumes.Null(TraceSource);
			try
			{
				TraceSource = channelOptions.TraceSource ?? MultiplexingStream.DefaultChannelTraceSourceFactory?.Invoke(QualifiedId, Name) ?? new TraceSource($"{"MultiplexingStream"}.{"Channel"} {QualifiedId} ({Name})", SourceLevels.Critical);
				IDuplexPipe duplexPipe = null;
				IDuplexPipe duplexPipe2 = null;
				lock (SyncObject)
				{
					Verify.NotDisposed(this);
					InitializeOwnPipes();
					if (channelOptions.ExistingPipe != null)
					{
						Assumes.NotNull(channelIO);
						existingPipe = channelOptions.ExistingPipe;
						existingPipeGiven = true;
						duplexPipe = channelOptions.ExistingPipe;
						duplexPipe2 = channelIO;
					}
					else
					{
						existingPipeGiven = false;
					}
				}
				if (duplexPipe != null && duplexPipe2 != null)
				{
					DisposeSelfOnFailure(duplexPipe2.Input.LinkToAsync(duplexPipe.Output));
					DisposeSelfOnFailure(duplexPipe.Input.LinkToAsync(duplexPipe2.Output, DisposalToken));
				}
				mxStreamIOReaderCompleted = ProcessOutboundTransmissionsAsync();
				DisposeSelfOnFailure(mxStreamIOReaderCompleted);
				DisposeSelfOnFailure(AutoCloseOnPipesClosureAsync());
			}
			catch (Exception exception)
			{
				optionsAppliedTaskSource?.TrySetException(exception);
				throw;
			}
			finally
			{
				optionsAppliedTaskSource?.TrySetResult(null);
			}
		}

		private void InitializeOwnPipes()
		{
			lock (SyncObject)
			{
				Verify.NotDisposed(this);
				if (mxStreamIOReader == null)
				{
					long? num = localWindowSize;
					if (!num.HasValue)
					{
						localWindowSize = MultiplexingStream.DefaultChannelReceivingWindowSize;
					}
					Pipe pipe = new Pipe();
					Pipe pipe2 = (BackpressureSupportEnabled ? new Pipe(new PipeOptions(null, null, null, localWindowSize.Value + 1, -1L)) : new Pipe());
					mxStreamIOReader = pipe.Reader;
					mxStreamIOWriter = pipe2.Writer;
					channelIO = new DuplexPipe(BackpressureSupportEnabled ? new WindowPipeReader(this, pipe2.Reader) : pipe2.Reader, pipe.Writer);
				}
			}
		}

		private async Task ProcessOutboundTransmissionsAsync()
		{
			PipeReader mxStreamIOReader;
			lock (SyncObject)
			{
				mxStreamIOReader = this.mxStreamIOReader;
				Assumes.NotNull(mxStreamIOReader);
				this.mxStreamIOReader = new UnownedPipeReader(mxStreamIOReader);
			}
			try
			{
				await Acceptance.ConfigureAwait(continueOnCapturedContext: false);
				while (!Completion.IsCompleted)
				{
					if (!remoteWindowHasCapacity.IsSet && TraceSource.Switch.ShouldTrace(TraceEventType.Verbose))
					{
						TraceSource.TraceEvent(TraceEventType.Verbose, 0, "Remote window is full. Waiting for remote party to process data before sending more.");
					}
					await remoteWindowHasCapacity.WaitAsync().ConfigureAwait(continueOnCapturedContext: false);
					if (IsRemotelyTerminated)
					{
						if (TraceSource.Switch.ShouldTrace(TraceEventType.Verbose))
						{
							TraceSource.TraceEvent(TraceEventType.Verbose, 0, "Transmission on channel {0} \"{1}\" terminated the remote party terminated the channel.", QualifiedId, Name);
						}
						break;
					}
					ReadResult result = await mxStreamIOReader.ReadAsync().ConfigureAwait(continueOnCapturedContext: false);
					if (result.IsCanceled)
					{
						if (TraceSource.Switch.ShouldTrace(TraceEventType.Verbose))
						{
							TraceSource.TraceEvent(TraceEventType.Verbose, 0, "Transmission terminated because the read was canceled.");
						}
						break;
					}
					long num = Math.Min(result.Buffer.Length, 20480L);
					if (BackpressureSupportEnabled)
					{
						num = Math.Min(RemoteWindowRemaining, num);
					}
					ReadOnlySequence<byte> bufferToRelay = result.Buffer.Slice(0L, num);
					OnTransmittingBytes(bufferToRelay.Length);
					bool isCompleted = result.IsCompleted && result.Buffer.Length == bufferToRelay.Length;
					if (TraceSource.Switch.ShouldTrace(TraceEventType.Verbose))
					{
						TraceSource.TraceEvent(TraceEventType.Verbose, 0, "{0} of {1} bytes will be transmitted.", bufferToRelay.Length, result.Buffer.Length);
					}
					if (bufferToRelay.Length > 0)
					{
						FrameHeader header = new FrameHeader
						{
							Code = ControlCode.Content,
							ChannelId = QualifiedId
						};
						await MultiplexingStream.SendFrameAsync(header, bufferToRelay, CancellationToken.None).ConfigureAwait(continueOnCapturedContext: false);
					}
					mxStreamIOReader.AdvanceTo(bufferToRelay.End);
					result.ScrubAfterAdvanceTo();
					if (isCompleted)
					{
						if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
						{
							TraceSource.TraceEvent(TraceEventType.Information, 0, "Transmission terminated because the writer completed.");
						}
						break;
					}
				}
				await mxStreamIOReader.CompleteAsync(faultingException).ConfigureAwait(continueOnCapturedContext: false);
			}
			catch (Exception ex)
			{
				Exception ex2 = ex;
				await mxStreamIOReader.CompleteAsync(ex2).ConfigureAwait(continueOnCapturedContext: false);
				lock (SyncObject)
				{
					if (!IsDisposed && faultingException == null)
					{
						faultingException = ex2;
					}
				}
				if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
				{
					TraceSource.TraceEvent(TraceEventType.Error, 0, "Rethrowing caught exception in ProcessOutboundTransmissionsAsync: {0}", ex2.Message);
				}
				ExceptionDispatchInfo.Capture((ex as Exception) ?? throw ex).Throw();
			}
			finally
			{
				MultiplexingStream.OnChannelWritingCompleted(this);
				lock (SyncObject)
				{
					this.mxStreamIOReader = mxStreamIOReader;
				}
			}
		}

		private void OnTransmittingBytes(long transmittedBytes)
		{
			if (!BackpressureSupportEnabled)
			{
				return;
			}
			Requires.Range(transmittedBytes >= 0, "transmittedBytes", "A non-negative number is required.");
			lock (SyncObject)
			{
				Requires.Range(remoteWindowFilled + transmittedBytes <= remoteWindowSize, "transmittedBytes", "The value exceeds the space remaining in the window size.");
				remoteWindowFilled += transmittedBytes;
				if (remoteWindowFilled == remoteWindowSize)
				{
					remoteWindowHasCapacity.Reset();
				}
			}
		}

		private void LocalContentExamined(long bytesExamined)
		{
			Requires.Range(bytesExamined >= 0, "bytesExamined");
			if (bytesExamined != 0L && !IsDisposed)
			{
				if (TraceSource.Switch.ShouldTrace(TraceEventType.Verbose))
				{
					TraceSource.TraceEvent(TraceEventType.Verbose, 0, "Acknowledging processing of {0} bytes.", bytesExamined);
				}
				MultiplexingStream.SendFrame(new FrameHeader
				{
					Code = ControlCode.ContentProcessed,
					ChannelId = QualifiedId
				}, MultiplexingStream.formatter.SerializeContentProcessed(bytesExamined), CancellationToken.None);
			}
		}

		private async Task AutoCloseOnPipesClosureAsync()
		{
			Assumes.NotNull(mxStreamIOReaderCompleted);
			await Task.WhenAll(mxStreamIOWriterCompleted.WaitAsync(), mxStreamIOReaderCompleted).ConfigureAwait(continueOnCapturedContext: false);
			if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
			{
				TraceSource.TraceEvent(TraceEventType.Information, 5, "Channel {0} \"{1}\" self-closing because both reader and writer are complete.", QualifiedId, Name);
			}
			Dispose(null);
		}

		private void Fault(Exception exception)
		{
			mxStreamIOReader?.CancelPendingRead();
			lock (SyncObject)
			{
				if (isDisposed)
				{
					return;
				}
				if (faultingException == null)
				{
					faultingException = exception;
				}
			}
			TraceSource? traceSource = TraceSource;
			if (traceSource != null && traceSource.Switch.ShouldTrace(TraceEventType.Error))
			{
				TraceSource.TraceEvent(TraceEventType.Error, 23, "Channel faulted with exception: {0}", faultingException);
				if (exception != faultingException)
				{
					TraceSource.TraceEvent(TraceEventType.Error, 23, "A subsequent fault exception was reported: {0}", exception);
				}
			}
			Dispose(faultingException);
		}

		private void DisposeSelfOnFailure(Func<Task> asyncFunc)
		{
			DisposeSelfOnFailure(asyncFunc());
		}

		private void DisposeSelfOnFailure(Task task)
		{
			Requires.NotNull(task, "task");
			if (task.IsCompleted)
			{
				if (task.IsFaulted)
				{
					Fault(task.Exception.InnerException ?? task.Exception);
				}
			}
			else
			{
				task.ContinueWith(delegate(Task t, object? s)
				{
					((Channel)s).Fault(t.Exception.InnerException ?? t.Exception);
				}, this, CancellationToken.None, TaskContinuationOptions.OnlyOnFaulted, TaskScheduler.Default).Forget();
			}
		}
	}

	public class ChannelOfferEventArgs : EventArgs
	{
		[Obsolete("Use QualifiedId instead.")]
		public int Id => checked((int)QualifiedId.Id);

		public QualifiedChannelId QualifiedId { get; }

		public string Name { get; }

		public bool IsAccepted { get; }

		internal ChannelOfferEventArgs(QualifiedChannelId id, string name, bool isAccepted)
		{
			QualifiedId = id;
			Name = name;
			IsAccepted = isAccepted;
		}
	}

	public class ChannelOptions
	{
		private IDuplexPipe? existingPipe;

		public TraceSource? TraceSource { get; set; }

		public IDuplexPipe? ExistingPipe
		{
			get
			{
				return existingPipe;
			}
			set
			{
				if (value != null && value.Input == null && value.Output == null)
				{
					throw new ArgumentException("At least a reader or writer must be specified.");
				}
				IDuplexPipe? duplexPipe2;
				if (value != null && !(value is DuplexPipe))
				{
					IDuplexPipe duplexPipe = new DuplexPipe(value.Input, value.Output);
					duplexPipe2 = duplexPipe;
				}
				else
				{
					duplexPipe2 = value;
				}
				existingPipe = duplexPipe2;
			}
		}

		[Obsolete("This value is ignored.")]
		public PipeOptions? InputPipeOptions { get; set; }

		public long? ChannelReceivingWindowSize { get; set; }
	}

	internal enum ControlCode : byte
	{
		Offer,
		OfferAccepted,
		Content,
		ContentWritingCompleted,
		ChannelTerminated,
		ContentProcessed
	}

	private enum TraceEventId
	{
		HandshakeSuccessful = 1,
		HandshakeFailed,
		FatalError,
		UnexpectedChannelAccept,
		ChannelAutoClosing,
		StreamDisposed,
		AcceptChannelWaiting,
		AcceptChannelAlreadyOffered,
		AcceptChannelCanceled,
		ChannelOfferReceived,
		ChannelDisposed,
		OfferChannelCanceled,
		FrameSent,
		FrameSendSkipped,
		FrameReceived,
		FrameNotReceived,
		FrameSentPayload,
		FrameReceivedPayload,
		ContentDiscardedOnDisposedChannel,
		WaitingForNextFrame,
		UnexpectedContentProcessed,
		HandshakeStarted,
		ChannelFatalError
	}

	internal abstract class Formatter : System.IAsyncDisposable
	{
		protected bool? IsOddEndpoint { get; set; }

		protected PipeWriter PipeWriter { get; }

		protected bool IsDisposed { get; private set; }

		protected Formatter(PipeWriter writer)
		{
			PipeWriter = writer;
		}

		public virtual ValueTask DisposeAsync()
		{
			if (IsDisposed)
			{
				return default(ValueTask);
			}
			IsDisposed = true;
			return PipeWriter.CompleteAsync();
		}

		internal abstract object? WriteHandshake();

		internal abstract Task<(bool? IsOdd, Version ProtocolVersion)> ReadHandshakeAsync(object? writeHandshakeResult, Options options, CancellationToken cancellationToken);

		internal abstract void WriteFrame(FrameHeader header, ReadOnlySequence<byte> payload);

		internal abstract Task<(FrameHeader Header, ReadOnlySequence<byte> Payload)?> ReadFrameAsync(CancellationToken cancellationToken);

		internal ValueTask<FlushResult> FlushAsync(CancellationToken cancellationToken)
		{
			return PipeWriter.FlushAsync(cancellationToken);
		}

		internal abstract ReadOnlySequence<byte> Serialize(Channel.OfferParameters offerParameters);

		internal abstract Channel.OfferParameters DeserializeOfferParameters(ReadOnlySequence<byte> payload);

		internal abstract ReadOnlySequence<byte> Serialize(Channel.AcceptanceParameters acceptanceParameters);

		internal abstract Channel.AcceptanceParameters DeserializeAcceptanceParameters(ReadOnlySequence<byte> payload);

		internal abstract long DeserializeContentProcessed(ReadOnlySequence<byte> payload);

		internal abstract ReadOnlySequence<byte> SerializeContentProcessed(long bytesProcessed);

		protected static bool IsOdd(ReadOnlySpan<byte> localRandomBuffer, ReadOnlySpan<byte> remoteRandomBuffer)
		{
			bool? flag = null;
			for (int i = 0; i < localRandomBuffer.Length; i++)
			{
				byte b = localRandomBuffer[i];
				byte b2 = remoteRandomBuffer[i];
				if (b > b2)
				{
					flag = true;
					break;
				}
				if (b < b2)
				{
					flag = false;
					break;
				}
			}
			if (!flag.HasValue)
			{
				throw new MultiplexingProtocolException("Unable to determine even/odd party.");
			}
			return flag.Value;
		}

		protected FrameHeader CreateFrameHeader(ControlCode code, ulong? channelId, ChannelSource? channelSource)
		{
			QualifiedChannelId? channelId2 = null;
			if (channelId.HasValue)
			{
				if (!channelSource.HasValue)
				{
					Assumes.True(IsOddEndpoint.HasValue, "this.IsOddEndpoint.HasValue");
					bool flag = channelId.Value % 2 == 1;
					channelSource = ((flag != IsOddEndpoint.Value) ? ChannelSource.Local : ChannelSource.Remote);
				}
				channelId2 = new QualifiedChannelId(channelId.Value, channelSource.Value);
			}
			return new FrameHeader
			{
				Code = code,
				ChannelId = channelId2
			};
		}
	}

	internal class V1Formatter : Formatter
	{
		private static readonly byte[] ProtocolMagicNumber = new byte[4] { 47, 223, 29, 80 };

		private static readonly Version ProtocolVersion = new Version(1, 0);

		private readonly Stream readingStream;

		private readonly AsyncSemaphore readingSemaphore = new AsyncSemaphore(1);

		private readonly Memory<byte> headerBuffer = new byte[HeaderLength];

		private readonly Memory<byte> payloadBuffer = new byte[20480];

		private static int HeaderLength => 7;

		internal V1Formatter(PipeWriter writer, Stream readingStream)
			: base(writer)
		{
			this.readingStream = readingStream;
		}

		public override async ValueTask DisposeAsync()
		{
			if (!base.IsDisposed)
			{
				using (await readingSemaphore.EnterAsync().ConfigureAwait(continueOnCapturedContext: false))
				{
					readingStream.Dispose();
				}
				await base.DisposeAsync().ConfigureAwait(continueOnCapturedContext: false);
			}
		}

		internal override object WriteHandshake()
		{
			byte[] array = Guid.NewGuid().ToByteArray();
			Span<byte> span = base.PipeWriter.GetSpan(ProtocolMagicNumber.Length + array.Length);
			ProtocolMagicNumber.CopyTo(span);
			array.CopyTo(span.Slice(ProtocolMagicNumber.Length));
			base.PipeWriter.Advance(ProtocolMagicNumber.Length + array.Length);
			return array;
		}

		internal override async Task<(bool? IsOdd, Version ProtocolVersion)> ReadHandshakeAsync(object? writeHandshakeResult, Options options, CancellationToken cancellationToken)
		{
			byte[] randomSendBuffer = (writeHandshakeResult as byte[]) ?? throw new ArgumentException("This should be the result of a prior call to WriteHandshake", "writeHandshakeResult");
			byte[] handshakeBytes = new byte[ProtocolMagicNumber.Length + 16];
			using (await readingSemaphore.EnterAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false))
			{
				await readingStream.ReadBlockOrThrowAsync(handshakeBytes, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			}
			for (int i = 0; i < ProtocolMagicNumber.Length; i++)
			{
				if (handshakeBytes[i] != ProtocolMagicNumber[i])
				{
					throw new MultiplexingProtocolException("Handshake magic number mismatch.");
				}
			}
			base.IsOddEndpoint = Formatter.IsOdd(randomSendBuffer, handshakeBytes.AsSpan(ProtocolMagicNumber.Length));
			return (IsOdd: base.IsOddEndpoint, ProtocolVersion: ProtocolVersion);
		}

		internal override void WriteFrame(FrameHeader header, ReadOnlySequence<byte> payload)
		{
			Verify.NotDisposed(!base.IsDisposed, this);
			Span<byte> destination = base.PipeWriter.GetSpan(checked(HeaderLength + (int)payload.Length));
			destination[0] = (byte)header.Code;
			Utilities.Write(destination.Slice(1, 4), checked((int)(header.ChannelId?.Id ?? 0)));
			Utilities.Write(destination.Slice(5, 2), (ushort)payload.Length);
			destination = destination.Slice(HeaderLength);
			ReadOnlySequence<byte>.Enumerator enumerator = payload.GetEnumerator();
			while (enumerator.MoveNext())
			{
				ReadOnlyMemory<byte> current = enumerator.Current;
				current.Span.CopyTo(destination);
				destination = destination.Slice(current.Length);
			}
			base.PipeWriter.Advance(HeaderLength + (int)payload.Length);
		}

		internal override async Task<(FrameHeader, ReadOnlySequence<byte>)?> ReadFrameAsync(CancellationToken cancellationToken)
		{
			using (await readingSemaphore.EnterAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false))
			{
				Verify.NotDisposed(!base.IsDisposed, this);
				if (!(await ReadToFillAsync(readingStream, headerBuffer, throwOnEmpty: false, cancellationToken).ConfigureAwait(continueOnCapturedContext: false)))
				{
					return null;
				}
				FrameHeader header = CreateFrameHeader((ControlCode)headerBuffer.Span[0], checked((ulong)Utilities.ReadInt(headerBuffer.Span.Slice(1, 4))), null);
				int length = Utilities.ReadInt(headerBuffer.Span.Slice(5, 2));
				Memory<byte> payloadBuffer = this.payloadBuffer.Slice(0, length);
				await ReadToFillAsync(readingStream, payloadBuffer, throwOnEmpty: true, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
				return (header, new ReadOnlySequence<byte>(payloadBuffer));
			}
		}

		internal override long DeserializeContentProcessed(ReadOnlySequence<byte> payload)
		{
			return Utilities.ReadInt(payload.IsSingleSegment ? payload.First.Span : ((ReadOnlySpan<byte>)BuffersExtensions.ToArray(in payload)));
		}

		internal unsafe override ReadOnlySequence<byte> Serialize(Channel.OfferParameters offerParameters)
		{
			Sequence<byte> sequence = new Sequence<byte>();
			Span<byte> span = sequence.GetSpan(ControlFrameEncoding.GetMaxByteCount(offerParameters.Name.Length));
			fixed (byte* bytes = span)
			{
				fixed (char* name = offerParameters.Name)
				{
					int bytes2 = ControlFrameEncoding.GetBytes(name, offerParameters.Name.Length, bytes, span.Length);
					sequence.Advance(bytes2);
				}
			}
			return sequence;
		}

		internal unsafe override Channel.OfferParameters DeserializeOfferParameters(ReadOnlySequence<byte> payload)
		{
			ReadOnlySpan<byte> readOnlySpan = (payload.IsSingleSegment ? payload.First.Span : ((ReadOnlySpan<byte>)BuffersExtensions.ToArray(in payload)));
			fixed (byte* ptr = readOnlySpan)
			{
				return new Channel.OfferParameters((ptr != null) ? ControlFrameEncoding.GetString(ptr, readOnlySpan.Length) : string.Empty, null);
			}
		}

		internal override ReadOnlySequence<byte> Serialize(Channel.AcceptanceParameters acceptanceParameters)
		{
			return default(ReadOnlySequence<byte>);
		}

		internal override Channel.AcceptanceParameters DeserializeAcceptanceParameters(ReadOnlySequence<byte> payload)
		{
			return new Channel.AcceptanceParameters(null);
		}

		internal override ReadOnlySequence<byte> SerializeContentProcessed(long bytesProcessed)
		{
			throw new NotSupportedException();
		}
	}

	internal class V2Formatter : Formatter
	{
		private static readonly Version ProtocolVersion = new Version(2, 0);

		private readonly MessagePackStreamReader reader;

		private readonly AsyncSemaphore readingSemaphore = new AsyncSemaphore(1);

		internal V2Formatter(PipeWriter writer, Stream readingStream)
			: base(writer)
		{
			reader = new MessagePackStreamReader(readingStream, leaveOpen: false);
		}

		public override async ValueTask DisposeAsync()
		{
			if (!base.IsDisposed)
			{
				using (await readingSemaphore.EnterAsync().ConfigureAwait(continueOnCapturedContext: false))
				{
					reader.Dispose();
				}
				await base.DisposeAsync().ConfigureAwait(continueOnCapturedContext: false);
			}
		}

		internal override object? WriteHandshake()
		{
			MessagePackWriter messagePackWriter = new MessagePackWriter(base.PipeWriter);
			messagePackWriter.WriteArrayHeader(2);
			messagePackWriter.WriteArrayHeader(2);
			messagePackWriter.Write(ProtocolVersion.Major);
			messagePackWriter.Write(ProtocolVersion.Minor);
			byte[] array = Guid.NewGuid().ToByteArray();
			messagePackWriter.Write(array);
			messagePackWriter.Flush();
			return array;
		}

		internal override async Task<(bool? IsOdd, Version ProtocolVersion)> ReadHandshakeAsync(object? writeHandshakeResult, Options options, CancellationToken cancellationToken)
		{
			using (await readingSemaphore.EnterAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false))
			{
				byte[] randomSendBuffer = (writeHandshakeResult as byte[]) ?? throw new ArgumentException("This should be the result of a prior call to WriteHandshake", "writeHandshakeResult");
				ReadOnlySequence<byte>? readOnlySequence = await reader.ReadAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
				if (!readOnlySequence.HasValue)
				{
					throw new EndOfStreamException();
				}
				(bool, Version) tuple = DeserializeHandshake(randomSendBuffer, readOnlySequence.Value, options);
				base.IsOddEndpoint = tuple.Item1;
				(bool, Version) tuple2 = tuple;
				return (IsOdd: tuple2.Item1, ProtocolVersion: tuple2.Item2);
			}
		}

		internal override void WriteFrame(FrameHeader header, ReadOnlySequence<byte> payload)
		{
			Verify.NotDisposed(!base.IsDisposed, this);
			MessagePackWriter messagePackWriter = new MessagePackWriter(base.PipeWriter);
			int num = ((!payload.IsEmpty) ? 3 : ((!header.ChannelId.HasValue) ? 1 : 2));
			messagePackWriter.WriteArrayHeader(num);
			messagePackWriter.Write((int)header.Code);
			if (num > 1)
			{
				if (header.ChannelId.HasValue)
				{
					messagePackWriter.Write(header.ChannelId.Value.Id);
				}
				else
				{
					messagePackWriter.WriteNil();
				}
				if (num > 2)
				{
					messagePackWriter.Write(in payload);
				}
			}
			messagePackWriter.Flush();
		}

		internal override async Task<(FrameHeader Header, ReadOnlySequence<byte> Payload)?> ReadFrameAsync(CancellationToken cancellationToken)
		{
			using (await readingSemaphore.EnterAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false))
			{
				Verify.NotDisposed(!base.IsDisposed, this);
				ReadOnlySequence<byte>? readOnlySequence = await reader.ReadAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
				if (!readOnlySequence.HasValue)
				{
					return null;
				}
				return DeserializeFrame(readOnlySequence.Value);
			}
		}

		internal ReadOnlySequence<byte> SerializeException(Exception? exception)
		{
			if (exception == null)
			{
				return ReadOnlySequence<byte>.Empty;
			}
			Sequence<byte> sequence = new Sequence<byte>();
			MessagePackWriter messagePackWriter = new MessagePackWriter(sequence);
			messagePackWriter.WriteArrayHeader(1);
			messagePackWriter.Write(exception.GetType().Name + ": " + exception.Message);
			messagePackWriter.Flush();
			return sequence;
		}

		internal Exception? DeserializeException(ReadOnlySequence<byte> payload)
		{
			if (payload.IsEmpty)
			{
				return null;
			}
			MessagePackReader messagePackReader = new MessagePackReader(in payload);
			if (messagePackReader.ReadArrayHeader() == 0)
			{
				return null;
			}
			string text = messagePackReader.ReadString();
			return new MultiplexingProtocolException("Received error from remote side: " + text);
		}

		internal override ReadOnlySequence<byte> SerializeContentProcessed(long bytesProcessed)
		{
			Sequence<byte> sequence = new Sequence<byte>();
			MessagePackWriter messagePackWriter = new MessagePackWriter(sequence);
			messagePackWriter.WriteArrayHeader(1);
			messagePackWriter.Write(bytesProcessed);
			messagePackWriter.Flush();
			return sequence;
		}

		internal override long DeserializeContentProcessed(ReadOnlySequence<byte> payload)
		{
			MessagePackReader messagePackReader = new MessagePackReader(in payload);
			messagePackReader.ReadArrayHeader();
			return messagePackReader.ReadInt64();
		}

		internal override ReadOnlySequence<byte> Serialize(Channel.OfferParameters offerParameters)
		{
			Sequence<byte> sequence = new Sequence<byte>();
			MessagePackWriter messagePackWriter = new MessagePackWriter(sequence);
			messagePackWriter.WriteArrayHeader((!offerParameters.RemoteWindowSize.HasValue) ? 1 : 2);
			messagePackWriter.Write(offerParameters.Name);
			long? remoteWindowSize = offerParameters.RemoteWindowSize;
			if (remoteWindowSize.HasValue)
			{
				long valueOrDefault = remoteWindowSize.GetValueOrDefault();
				messagePackWriter.Write(valueOrDefault);
			}
			messagePackWriter.Flush();
			return sequence;
		}

		internal override Channel.OfferParameters DeserializeOfferParameters(ReadOnlySequence<byte> payload)
		{
			MessagePackReader messagePackReader = new MessagePackReader(in payload);
			int num = messagePackReader.ReadArrayHeader();
			if (num == 0)
			{
				throw new MultiplexingProtocolException("Insufficient elements in offer parameter payload.");
			}
			string name = messagePackReader.ReadString();
			long? remoteWindowSize = null;
			if (num > 1)
			{
				remoteWindowSize = messagePackReader.ReadInt64();
			}
			return new Channel.OfferParameters(name, remoteWindowSize);
		}

		internal override ReadOnlySequence<byte> Serialize(Channel.AcceptanceParameters acceptanceParameters)
		{
			Sequence<byte> sequence = new Sequence<byte>();
			MessagePackWriter messagePackWriter = new MessagePackWriter(sequence);
			long? remoteWindowSize = acceptanceParameters.RemoteWindowSize;
			if (remoteWindowSize.HasValue)
			{
				long valueOrDefault = remoteWindowSize.GetValueOrDefault();
				messagePackWriter.WriteArrayHeader(1);
				messagePackWriter.Write(valueOrDefault);
			}
			else
			{
				messagePackWriter.WriteArrayHeader(0);
			}
			messagePackWriter.Flush();
			return sequence;
		}

		internal override Channel.AcceptanceParameters DeserializeAcceptanceParameters(ReadOnlySequence<byte> payload)
		{
			MessagePackReader messagePackReader = new MessagePackReader(in payload);
			int num = messagePackReader.ReadArrayHeader();
			long? remoteWindowSize = null;
			if (num > 0)
			{
				remoteWindowSize = messagePackReader.ReadInt64();
			}
			return new Channel.AcceptanceParameters(remoteWindowSize);
		}

		protected virtual (FrameHeader Header, ReadOnlySequence<byte> Payload) DeserializeFrame(ReadOnlySequence<byte> frameSequence)
		{
			MessagePackReader messagePackReader = new MessagePackReader(in frameSequence);
			int num = messagePackReader.ReadArrayHeader();
			if (num < 1)
			{
				throw new MultiplexingProtocolException("Not enough elements in frame header.");
			}
			ControlCode code = (ControlCode)messagePackReader.ReadInt32();
			ulong? channelId = null;
			if (num > 1)
			{
				if (messagePackReader.IsNil)
				{
					messagePackReader.ReadNil();
				}
				else
				{
					channelId = messagePackReader.ReadUInt64();
				}
				if (num > 2)
				{
					ReadOnlySequence<byte> valueOrDefault = messagePackReader.ReadBytes().GetValueOrDefault();
					return (Header: CreateFrameHeader(code, channelId, null), Payload: valueOrDefault);
				}
			}
			return (Header: CreateFrameHeader(code, channelId, null), Payload: default(ReadOnlySequence<byte>));
		}

		private static void Discard(ref MessagePackReader reader, int elementsToDiscard)
		{
			for (int i = 0; i < elementsToDiscard; i++)
			{
				reader.Skip();
			}
		}

		private static (bool IsOdd, Version ProtocolVersion) DeserializeHandshake(ReadOnlySpan<byte> localRandomNumber, ReadOnlySequence<byte> handshakeSequence, Options options)
		{
			MessagePackReader messagePackReader = new MessagePackReader(in handshakeSequence);
			int num = messagePackReader.ReadArrayHeader();
			if (num < 2)
			{
				throw new MultiplexingProtocolException("Unexpected handshake.");
			}
			int num2 = messagePackReader.ReadArrayHeader();
			if (num2 < 2)
			{
				throw new MultiplexingProtocolException("Too few elements in handshake.");
			}
			int major = messagePackReader.ReadInt32();
			int minor = messagePackReader.ReadInt32();
			Version version = new Version(major, minor);
			Discard(ref messagePackReader, num2 - 2);
			if (version.Major != ProtocolVersion.Major)
			{
				throw new MultiplexingProtocolException($"Incompatible version. Local version: {ProtocolVersion}. Remote version: {version}.");
			}
			ReadOnlySequence<byte>? readOnlySequence = messagePackReader.ReadBytes();
			byte[] array = (readOnlySequence.HasValue ? readOnlySequence.GetValueOrDefault().ToArray<byte>() : null);
			if (array == null)
			{
				throw new MultiplexingProtocolException("Missing random number.");
			}
			bool item = Formatter.IsOdd(localRandomNumber, array);
			Discard(ref messagePackReader, num - 2);
			return (IsOdd: item, ProtocolVersion: version);
		}
	}

	internal class V3Formatter : V2Formatter
	{
		private static readonly Version ProtocolVersion = new Version(3, 0);

		private static readonly Task<(bool?, Version)> ReadHandshakeResult = Task.FromResult<(bool?, Version)>((null, ProtocolVersion));

		internal V3Formatter(PipeWriter writer, Stream readingStream)
			: base(writer, readingStream)
		{
		}

		internal override object? WriteHandshake()
		{
			return null;
		}

		internal override Task<(bool? IsOdd, Version ProtocolVersion)> ReadHandshakeAsync(object? writeHandshakeResult, Options options, CancellationToken cancellationToken)
		{
			return ReadHandshakeResult;
		}

		internal override void WriteFrame(FrameHeader header, ReadOnlySequence<byte> payload)
		{
			Verify.NotDisposed(!base.IsDisposed, this);
			MessagePackWriter messagePackWriter = new MessagePackWriter(base.PipeWriter);
			int num = ((!payload.IsEmpty) ? 4 : ((!header.ChannelId.HasValue) ? 1 : 3));
			messagePackWriter.WriteArrayHeader(num);
			messagePackWriter.Write((int)header.Code);
			if (num > 1)
			{
				QualifiedChannelId qualifiedChannelId = header.ChannelId ?? throw new NotSupportedException("A frame may not contain payload without a channel ID.");
				messagePackWriter.Write(qualifiedChannelId.Id);
				messagePackWriter.Write((sbyte)qualifiedChannelId.Source);
				if (!payload.IsEmpty)
				{
					messagePackWriter.Write(in payload);
				}
			}
			messagePackWriter.Flush();
		}

		protected override (FrameHeader Header, ReadOnlySequence<byte> Payload) DeserializeFrame(ReadOnlySequence<byte> frameSequence)
		{
			MessagePackReader messagePackReader = new MessagePackReader(in frameSequence);
			int num = messagePackReader.ReadArrayHeader();
			FrameHeader item = default(FrameHeader);
			if (num < 1)
			{
				throw new MultiplexingProtocolException("Not enough elements in frame header.");
			}
			item.Code = (ControlCode)messagePackReader.ReadInt32();
			if (num > 1)
			{
				if (!messagePackReader.TryReadNil())
				{
					if (num < 3)
					{
						throw new MultiplexingProtocolException("Not enough elements in frame header.");
					}
					item.ChannelId = new QualifiedChannelId(messagePackReader.ReadUInt64(), (ChannelSource)messagePackReader.ReadSByte());
				}
				if (num > 3)
				{
					ReadOnlySequence<byte> valueOrDefault = messagePackReader.ReadBytes().GetValueOrDefault();
					return (Header: item, Payload: valueOrDefault);
				}
			}
			return (Header: item, Payload: default(ReadOnlySequence<byte>));
		}
	}

	[DebuggerDisplay("{DebuggerDisplay,nq}")]
	internal struct FrameHeader
	{
		internal ControlCode Code { get; set; }

		internal QualifiedChannelId? ChannelId { get; set; }

		internal QualifiedChannelId RequiredChannelId => ChannelId ?? throw new MultiplexingProtocolException("Expected ChannelId not present in frame header.");

		private string DebuggerDisplay => $"{Code} {ChannelId?.DebuggerDisplay}";

		internal void FlipChannelPerspective()
		{
			if (ChannelId.HasValue)
			{
				ChannelId = new QualifiedChannelId(ChannelId.Value.Id, (ChannelSource)(0 - ChannelId.Value.Source));
			}
		}
	}

	public class Options
	{
		private static readonly long RecommendedDefaultChannelReceivingWindowSize = 102400L;

		private TraceSource traceSource = new TraceSource("MultiplexingStream", SourceLevels.Critical);

		private int protocolMajorVersion = 1;

		private long defaultChannelReceivingWindowSize = RecommendedDefaultChannelReceivingWindowSize;

		private Func<int, string, TraceSource?>? defaultChannelTraceSourceFactory;

		private Func<QualifiedChannelId, string, TraceSource?>? defaultChannelTraceSourceFactoryWithQualifier;

		private bool startSuspended;

		private bool faultOpenChannelsOnStreamDisposal;

		public TraceSource TraceSource
		{
			get
			{
				return traceSource;
			}
			set
			{
				Requires.NotNull(value, "value");
				ThrowIfFrozen();
				traceSource = value;
			}
		}

		public long DefaultChannelReceivingWindowSize
		{
			get
			{
				return defaultChannelReceivingWindowSize;
			}
			set
			{
				Requires.Range(value > 0, "value");
				ThrowIfFrozen();
				defaultChannelReceivingWindowSize = value;
			}
		}

		public int ProtocolMajorVersion
		{
			get
			{
				return protocolMajorVersion;
			}
			set
			{
				Requires.Range(value > 0, "value");
				ThrowIfFrozen();
				protocolMajorVersion = value;
			}
		}

		[Obsolete("Use DefaultChannelTraceSourceFactoryWithQualifier instead.")]
		public Func<int, string, TraceSource?>? DefaultChannelTraceSourceFactory
		{
			get
			{
				return defaultChannelTraceSourceFactory;
			}
			set
			{
				ThrowIfFrozen();
				defaultChannelTraceSourceFactory = value;
			}
		}

		public Func<QualifiedChannelId, string, TraceSource?>? DefaultChannelTraceSourceFactoryWithQualifier
		{
			get
			{
				return defaultChannelTraceSourceFactoryWithQualifier;
			}
			set
			{
				ThrowIfFrozen();
				defaultChannelTraceSourceFactoryWithQualifier = value;
			}
		}

		public bool StartSuspended
		{
			get
			{
				return startSuspended;
			}
			set
			{
				ThrowIfFrozen();
				startSuspended = value;
			}
		}

		public IList<ChannelOptions> SeededChannels { get; private set; }

		public bool FaultOpenChannelsOnStreamDisposal
		{
			get
			{
				return faultOpenChannelsOnStreamDisposal;
			}
			set
			{
				ThrowIfFrozen();
				faultOpenChannelsOnStreamDisposal = value;
			}
		}

		public bool IsFrozen { get; private set; }

		public Options()
		{
			SeededChannels = new List<ChannelOptions>();
		}

		public Options(Options copyFrom)
		{
			Requires.NotNull(copyFrom, "copyFrom");
			defaultChannelReceivingWindowSize = copyFrom.defaultChannelReceivingWindowSize;
			traceSource = copyFrom.traceSource;
			protocolMajorVersion = copyFrom.protocolMajorVersion;
			defaultChannelTraceSourceFactory = copyFrom.defaultChannelTraceSourceFactory;
			defaultChannelTraceSourceFactoryWithQualifier = copyFrom.defaultChannelTraceSourceFactoryWithQualifier;
			startSuspended = copyFrom.startSuspended;
			faultOpenChannelsOnStreamDisposal = copyFrom.faultOpenChannelsOnStreamDisposal;
			SeededChannels = copyFrom.SeededChannels.ToList();
		}

		private Options(Options copyFrom, bool frozen)
			: this(copyFrom)
		{
			IsFrozen = frozen;
			if (frozen)
			{
				SeededChannels = new ReadOnlyCollection<ChannelOptions>(SeededChannels);
			}
		}

		public Options GetFrozenCopy()
		{
			if (!IsFrozen)
			{
				return new Options(this, frozen: true);
			}
			return this;
		}

		private void ThrowIfFrozen()
		{
			if (IsFrozen)
			{
				Verify.FailOperation(Strings.Frozen);
			}
		}
	}

	public enum ChannelSource : sbyte
	{
		Local = 1,
		Remote = -1,
		Seeded = 0
	}

	[DebuggerDisplay("{DebuggerDisplay,nq}")]
	public struct QualifiedChannelId : IEquatable<QualifiedChannelId>
	{
		public ulong Id { get; }

		public ChannelSource Source { get; }

		internal string DebuggerDisplay => ToString();

		public QualifiedChannelId(ulong id, ChannelSource source)
		{
			Id = id;
			Source = source;
		}

		public bool Equals(QualifiedChannelId other)
		{
			if (Id == other.Id)
			{
				return Source == other.Source;
			}
			return false;
		}

		public override bool Equals(object? obj)
		{
			if (obj is QualifiedChannelId other)
			{
				return Equals(other);
			}
			return false;
		}

		public override int GetHashCode()
		{
			return (int)Id | ((int)Source << 29);
		}

		public override string ToString()
		{
			return $"{Id} ({Source})";
		}
	}

	private const int ControlChannelId = 0;

	private const int FramePayloadMaxLength = 20480;

	private static readonly Encoding ControlFrameEncoding = Encoding.UTF8;

	private static readonly ChannelOptions DefaultChannelOptions = new ChannelOptions();

	private readonly bool? isOdd;

	private readonly Formatter formatter;

	private readonly object syncObject = new object();

	private readonly Dictionary<string, Queue<Channel>> channelsOfferedByThemByName = new Dictionary<string, Queue<Channel>>(StringComparer.Ordinal);

	private readonly Dictionary<string, Queue<TaskCompletionSource<Channel>>> acceptingChannels = new Dictionary<string, Queue<TaskCompletionSource<Channel>>>(StringComparer.Ordinal);

	private readonly Dictionary<QualifiedChannelId, Channel> openChannels = new Dictionary<QualifiedChannelId, Channel>();

	private readonly HashSet<QualifiedChannelId> channelsPendingTermination = new HashSet<QualifiedChannelId>();

	private readonly SemaphoreSlim sendingSemaphore = new SemaphoreSlim(1);

	private readonly TaskCompletionSource<object?> completionSource = new TaskCompletionSource<object>();

	private readonly CancellationTokenSource disposalTokenSource = new CancellationTokenSource();

	private readonly int protocolMajorVersion;

	private readonly bool faultOpenChannelsOnStreamDisposal;

	private long lastOfferedChannelId;

	private bool listeningStarted;

	public Task Completion => completionSource.Task;

	public TraceSource TraceSource { get; }

	public long DefaultChannelReceivingWindowSize { get; }

	bool IDisposableObservable.IsDisposed => Completion.IsCompleted;

	internal CancellationToken DisposalToken => disposalTokenSource.Token;

	private Func<QualifiedChannelId, string, TraceSource?>? DefaultChannelTraceSourceFactory { get; }

	public event EventHandler<ChannelOfferEventArgs>? ChannelOffered;

	private MultiplexingStream(Formatter formatter, bool? isOdd, Options options)
	{
		this.formatter = formatter;
		this.isOdd = isOdd;
		if (isOdd.HasValue)
		{
			lastOfferedChannelId = (isOdd.Value ? (-1) : 0);
		}
		TraceSource = options.TraceSource;
		faultOpenChannelsOnStreamDisposal = options.FaultOpenChannelsOnStreamDisposal;
		object obj = options.DefaultChannelTraceSourceFactoryWithQualifier;
		if (obj == null)
		{
			Func<int, string, TraceSource?> fac = options.DefaultChannelTraceSourceFactory;
			obj = ((fac != null) ? ((Func<QualifiedChannelId, string, TraceSource>)((QualifiedChannelId id, string name) => fac(checked((int)id.Id), name))) : null);
		}
		DefaultChannelTraceSourceFactory = (Func<QualifiedChannelId, string, TraceSource?>?)obj;
		DefaultChannelReceivingWindowSize = options.DefaultChannelReceivingWindowSize;
		protocolMajorVersion = options.ProtocolMajorVersion;
		for (int num = 0; num < options.SeededChannels.Count; num++)
		{
			QualifiedChannelId qualifiedChannelId = new QualifiedChannelId((ulong)num, ChannelSource.Seeded);
			openChannels.Add(qualifiedChannelId, new Channel(this, qualifiedChannelId, new Channel.OfferParameters(string.Empty, options.SeededChannels[num]?.ChannelReceivingWindowSize ?? DefaultChannelReceivingWindowSize)));
		}
		lastOfferedChannelId += options.SeededChannels.Count;
		if (!options.StartSuspended)
		{
			StartListening();
		}
	}

	public static MultiplexingStream Create(Stream stream, Options? options = null)
	{
		Requires.NotNull(stream, "stream");
		Requires.Argument(stream.CanRead, "stream", "Stream must be readable.");
		Requires.Argument(stream.CanWrite, "stream", "Stream must be writable.");
		options = options ?? new Options
		{
			ProtocolMajorVersion = 3
		};
		PipeWriter writer = stream.UsePipeWriter();
		if (options.ProtocolMajorVersion == 3)
		{
			V3Formatter v3Formatter = new V3Formatter(writer, stream);
			return new MultiplexingStream(v3Formatter, null, options);
		}
		throw new NotSupportedException($"Protocol major version {options.ProtocolMajorVersion} is not supported.");
	}

	public static Task<MultiplexingStream> CreateAsync(Stream stream, CancellationToken cancellationToken = default(CancellationToken))
	{
		return CreateAsync(stream, null, cancellationToken);
	}

	public static async Task<MultiplexingStream> CreateAsync(Stream stream, Options? options, CancellationToken cancellationToken = default(CancellationToken))
	{
		Requires.NotNull(stream, "stream");
		Requires.Argument(stream.CanRead, "stream", "Stream must be readable.");
		Requires.Argument(stream.CanWrite, "stream", "Stream must be writable.");
		options = options ?? new Options();
		if (options.ProtocolMajorVersion < 3 && options.SeededChannels.Count > 0)
		{
			throw new NotSupportedException(Strings.SeededChannelsRequireV3Protocol);
		}
		PipeWriter writer = stream.UsePipeWriter(null, CancellationToken.None);
		Formatter formatter = options.ProtocolMajorVersion switch
		{
			1 => new V1Formatter(writer, stream), 
			2 => new V2Formatter(writer, stream), 
			3 => new V3Formatter(writer, stream), 
			_ => throw new NotSupportedException($"Protocol major version {options.ProtocolMajorVersion} is not supported."), 
		};
		try
		{
			if (options.TraceSource.Switch.ShouldTrace(TraceEventType.Information))
			{
				options.TraceSource.TraceEvent(TraceEventType.Information, 22, $"Multiplexing protocol handshake beginning with major version {options.ProtocolMajorVersion}.");
			}
			object handshakeData = formatter.WriteHandshake();
			await formatter.FlushAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			(bool?, Version) tuple = await formatter.ReadHandshakeAsync(handshakeData, options, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			if (options.TraceSource.Switch.ShouldTrace(TraceEventType.Information))
			{
				options.TraceSource.TraceEvent(TraceEventType.Information, 1, "Multiplexing protocol established successfully.");
			}
			return new MultiplexingStream(formatter, tuple.Item1, options);
		}
		catch (Exception ex)
		{
			if (options.TraceSource.Switch.ShouldTrace(TraceEventType.Critical))
			{
				options.TraceSource.TraceEvent(TraceEventType.Critical, 2, ex.Message);
			}
			throw;
		}
	}

	public void StartListening()
	{
		Verify.NotDisposed(this);
		if (listeningStarted)
		{
			throw new InvalidOperationException(Strings.ListeningHasAlreadyStarted);
		}
		DisposeSelfOnFailure(ReadStreamAsync());
		listeningStarted = true;
	}

	public Channel CreateChannel(ChannelOptions? options = null)
	{
		Verify.NotDisposed(this);
		ThrowIfNotListening();
		Channel.OfferParameters offerParameters = new Channel.OfferParameters(string.Empty, options?.ChannelReceivingWindowSize ?? DefaultChannelReceivingWindowSize);
		ReadOnlySequence<byte> payload = formatter.Serialize(offerParameters);
		QualifiedChannelId qualifiedChannelId = new QualifiedChannelId(GetUnusedChannelId(), ChannelSource.Local);
		Channel channel = new Channel(this, qualifiedChannelId, offerParameters, options ?? DefaultChannelOptions);
		lock (syncObject)
		{
			openChannels.Add(qualifiedChannelId, channel);
		}
		FrameHeader header = new FrameHeader
		{
			Code = ControlCode.Offer,
			ChannelId = qualifiedChannelId
		};
		SendFrame(header, payload, DisposalToken);
		return channel;
	}

	public Channel AcceptChannel(int id, ChannelOptions? options = null)
	{
		return AcceptChannel((ulong)id, options);
	}

	public Channel AcceptChannel(ulong id, ChannelOptions? options = null)
	{
		Verify.NotDisposed(this);
		ThrowIfNotListening();
		options = options ?? DefaultChannelOptions;
		Channel value;
		lock (syncObject)
		{
			if (openChannels.TryGetValue(new QualifiedChannelId(id, ChannelSource.Remote), out value))
			{
				if (value.Name != null && channelsOfferedByThemByName.TryGetValue(value.Name, out Queue<Channel> value2))
				{
					value2.RemoveMidQueue(value);
				}
			}
			else if (!openChannels.TryGetValue(new QualifiedChannelId(id, ChannelSource.Seeded), out value))
			{
				throw new InvalidOperationException(Strings.NoChannelFoundById);
			}
		}
		AcceptChannelOrThrow(value, options);
		return value;
	}

	public void RejectChannel(int id)
	{
		RejectChannel((ulong)id);
	}

	public void RejectChannel(ulong id)
	{
		Verify.NotDisposed(this);
		ThrowIfNotListening();
		Channel value;
		lock (syncObject)
		{
			if (!openChannels.TryGetValue(new QualifiedChannelId(id, ChannelSource.Remote), out value))
			{
				if (openChannels.ContainsKey(new QualifiedChannelId(id, ChannelSource.Seeded)))
				{
					throw new InvalidOperationException(Strings.NotAllowedOnSeededChannel);
				}
				throw new InvalidOperationException(Strings.NoChannelFoundById);
			}
			if (value.Name != null && channelsOfferedByThemByName.TryGetValue(value.Name, out Queue<Channel> value2))
			{
				value2.RemoveMidQueue(value);
			}
		}
		value.Dispose();
	}

	public Task<Channel> OfferChannelAsync(string name, CancellationToken cancellationToken)
	{
		return OfferChannelAsync(name, null, cancellationToken);
	}

	public async Task<Channel> OfferChannelAsync(string name, ChannelOptions? options = null, CancellationToken cancellationToken = default(CancellationToken))
	{
		Requires.NotNull(name, "name");
		cancellationToken.ThrowIfCancellationRequested();
		Verify.NotDisposed(this);
		ThrowIfNotListening();
		Channel.OfferParameters offerParameters = new Channel.OfferParameters(name, options?.ChannelReceivingWindowSize ?? DefaultChannelReceivingWindowSize);
		ReadOnlySequence<byte> payload = formatter.Serialize(offerParameters);
		QualifiedChannelId qualifiedChannelId = new QualifiedChannelId(GetUnusedChannelId(), ChannelSource.Local);
		Channel channel = new Channel(this, qualifiedChannelId, offerParameters, options ?? DefaultChannelOptions);
		lock (syncObject)
		{
			openChannels.Add(qualifiedChannelId, channel);
		}
		FrameHeader header = new FrameHeader
		{
			Code = ControlCode.Offer,
			ChannelId = qualifiedChannelId
		};
		using (cancellationToken.Register(OfferChannelCanceled, channel))
		{
			await SendFrameAsync(header, payload, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			await channel.Acceptance.ConfigureAwait(continueOnCapturedContext: false);
			return channel;
		}
	}

	public Task<Channel> AcceptChannelAsync(string name, CancellationToken cancellationToken)
	{
		return AcceptChannelAsync(name, null, cancellationToken);
	}

	public async Task<Channel> AcceptChannelAsync(string name, ChannelOptions? options = null, CancellationToken cancellationToken = default(CancellationToken))
	{
		Requires.NotNull(name, "name");
		Verify.NotDisposed(this);
		ThrowIfNotListening();
		options = options ?? DefaultChannelOptions;
		TaskCompletionSource<Channel> taskCompletionSource;
		while (true)
		{
			Channel channel = null;
			taskCompletionSource = null;
			lock (syncObject)
			{
				if (channelsOfferedByThemByName.TryGetValue(name, out Queue<Channel> value))
				{
					while (channel == null && value.Count > 0)
					{
						channel = value.Dequeue();
						if (channel.Acceptance.IsCompleted)
						{
							channel = null;
						}
						else if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
						{
							TraceSource.TraceEvent(TraceEventType.Information, 8, "Accepting channel {1} \"{0}\" which is already offered by the other side.", name, channel.QualifiedId);
						}
					}
				}
				if (channel == null)
				{
					if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
					{
						TraceSource.TraceEvent(TraceEventType.Information, 7, "Waiting to accept channel \"{0}\", when offered by the other side.", name);
					}
					if (!acceptingChannels.TryGetValue(name, out Queue<TaskCompletionSource<Channel>> value2))
					{
						acceptingChannels.Add(name, value2 = new Queue<TaskCompletionSource<Channel>>());
					}
					taskCompletionSource = new TaskCompletionSource<Channel>(options);
					value2.Enqueue(taskCompletionSource);
				}
			}
			if (channel == null)
			{
				break;
			}
			if (TryAcceptChannel(channel, options))
			{
				return channel;
			}
		}
		using (cancellationToken.Register(AcceptChannelCanceled, Tuple.Create(taskCompletionSource, name), useSynchronizationContext: false))
		{
			Channel channel = await taskCompletionSource.Task.ConfigureAwait(continueOnCapturedContext: false);
			await channel.OptionsApplied.ConfigureAwait(continueOnCapturedContext: false);
			return channel;
		}
	}

	[Obsolete("Use DisposeAsync instead.")]
	public void Dispose()
	{
		DisposeAsync().AsTask().GetAwaiter().GetResult();
	}

	public async ValueTask DisposeAsync()
	{
		if (disposalTokenSource.IsCancellationRequested)
		{
			return;
		}
		disposalTokenSource.Cancel();
		try
		{
			if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
			{
				TraceSource.TraceEvent(TraceEventType.Information, 6, "Disposing.");
			}
			await sendingSemaphore.WaitAsync().ConfigureAwait(continueOnCapturedContext: false);
			try
			{
				await formatter.DisposeAsync().ConfigureAwait(continueOnCapturedContext: false);
			}
			finally
			{
				sendingSemaphore.Release();
			}
			lock (syncObject)
			{
				foreach (KeyValuePair<QualifiedChannelId, Channel> openChannel in openChannels)
				{
					openChannel.Value.Dispose(faultOpenChannelsOnStreamDisposal ? new ObjectDisposedException("MultiplexingStream") : null);
				}
				foreach (KeyValuePair<string, Queue<TaskCompletionSource<Channel>>> acceptingChannel in acceptingChannels)
				{
					foreach (TaskCompletionSource<Channel> item in acceptingChannel.Value)
					{
						item.TrySetCanceled();
					}
				}
				openChannels.Clear();
				channelsOfferedByThemByName.Clear();
				acceptingChannels.Clear();
			}
			completionSource.TrySetResult(null);
		}
		catch (Exception exception)
		{
			completionSource.TrySetException(exception);
		}
	}

	protected virtual void OnChannelOffered(ChannelOfferEventArgs args)
	{
		this.ChannelOffered?.Invoke(this, args);
	}

	private static async ValueTask<bool> ReadToFillAsync(Stream stream, Memory<byte> buffer, bool throwOnEmpty, CancellationToken cancellationToken)
	{
		Requires.NotNull(stream, "stream");
		int bytesRead;
		int num;
		for (bytesRead = 0; bytesRead < buffer.Length; bytesRead += num)
		{
			num = await stream.ReadAsync(buffer.Slice(bytesRead), cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			if (num == 0)
			{
				break;
			}
		}
		if (bytesRead < buffer.Length && (bytesRead > 0 || throwOnEmpty))
		{
			throw new EndOfStreamException();
		}
		return bytesRead == buffer.Length;
	}

	private static async Task ReadAndDiscardAsync(Stream stream, int length, CancellationToken cancellationToken)
	{
		byte[] rented = ArrayPool<byte>.Shared.Rent(Math.Min(4096, length));
		try
		{
			int num;
			for (int bytesRead = 0; bytesRead < length; bytesRead += num)
			{
				Memory<byte> buffer = rented.AsMemory(0, Math.Min(rented.Length, length - bytesRead));
				num = await stream.ReadAsync(buffer, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
				if (num == 0)
				{
					throw new EndOfStreamException();
				}
			}
		}
		finally
		{
			ArrayPool<byte>.Shared.Return(rented);
		}
	}

	private static ReadOnlyMemory<T> AsMemory<T>(ReadOnlySequence<T> sequence, Memory<T> backupBuffer)
	{
		if (sequence.IsSingleSegment)
		{
			return sequence.First;
		}
		sequence.CopyTo(backupBuffer.Span);
		return backupBuffer.Slice(0, (int)sequence.Length);
	}

	private async Task ReadStreamAsync()
	{
		try
		{
			while (!DisposalToken.IsCancellationRequested)
			{
				if (TraceSource.Switch.ShouldTrace(TraceEventType.Verbose))
				{
					TraceSource.TraceEvent(TraceEventType.Verbose, 20, "Waiting for next frame");
				}
				(FrameHeader, ReadOnlySequence<byte>)? tuple = await formatter.ReadFrameAsync(DisposalToken).ConfigureAwait(continueOnCapturedContext: false);
				if (!tuple.HasValue)
				{
					if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
					{
						TraceSource.TraceEvent(TraceEventType.Information, 16, "Clean end of stream.");
					}
					break;
				}
				FrameHeader item = tuple.Value.Item1;
				item.FlipChannelPerspective();
				if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
				{
					TraceSource.TraceEvent(TraceEventType.Information, 15, "Received {0} frame for channel {1} with {2} bytes of content.", item.Code, item.ChannelId, tuple.Value.Item2.Length);
				}
				switch (item.Code)
				{
				case ControlCode.Offer:
					OnOffer(item.RequiredChannelId, tuple.Value.Item2);
					break;
				case ControlCode.OfferAccepted:
					OnOfferAccepted(item, tuple.Value.Item2);
					break;
				case ControlCode.Content:
					await OnContentAsync(item, tuple.Value.Item2, DisposalToken).ConfigureAwait(continueOnCapturedContext: false);
					break;
				case ControlCode.ContentProcessed:
					OnContentProcessed(item, tuple.Value.Item2);
					break;
				case ControlCode.ContentWritingCompleted:
					OnContentWritingCompleted(item.RequiredChannelId);
					break;
				case ControlCode.ChannelTerminated:
					await OnChannelTerminatedAsync(item.RequiredChannelId, tuple.Value.Item2).ConfigureAwait(continueOnCapturedContext: false);
					break;
				}
			}
		}
		catch (EndOfStreamException)
		{
			if (TraceSource.Switch.ShouldTrace(TraceEventType.Error))
			{
				TraceSource.TraceEvent(TraceEventType.Error, 3, "End of stream in the middle of a frame.");
			}
		}
		catch (Exception ex2)
		{
			if (ex2 is OperationCanceledException && DisposalToken.IsCancellationRequested)
			{
				if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
				{
					TraceSource.TraceEvent(TraceEventType.Information, 11, "MultiplexingStream.ReadStreamAsync shutting down due to cancellation and disposal.");
				}
			}
			else if (TraceSource.Switch.ShouldTrace(TraceEventType.Error))
			{
				TraceSource.TraceEvent(TraceEventType.Error, 3, "Exception thrown in MultiplexingStream.ReadStreamAsync leading to stream shutdown: {0}", ex2);
			}
			throw;
		}
		finally
		{
			if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
			{
				TraceSource.TraceEvent(TraceEventType.Information, 0, "MultiplexingStream.ReadStreamAsync is shutting down all channels before exiting.");
			}
			lock (syncObject)
			{
				foreach (KeyValuePair<QualifiedChannelId, Channel> openChannel in openChannels)
				{
					openChannel.Value.OnContentWritingCompleted();
				}
			}
		}
		await DisposeAsync().ConfigureAwait(continueOnCapturedContext: false);
	}

	private async Task OnChannelTerminatedAsync(QualifiedChannelId channelId, ReadOnlySequence<byte> payload)
	{
		Channel value;
		lock (syncObject)
		{
			if (openChannels.TryGetValue(channelId, out value))
			{
				openChannels.Remove(channelId);
				channelsPendingTermination.Remove(channelId);
				if (value.Name != null && channelsOfferedByThemByName.TryGetValue(value.Name, out Queue<Channel> value2))
				{
					value2.RemoveMidQueue(value);
				}
			}
		}
		if (value != null)
		{
			Exception ex = (formatter as V2Formatter)?.DeserializeException(payload);
			if (ex != null && TraceSource.Switch.ShouldTrace(TraceEventType.Error))
			{
				TraceSource.TraceEvent(TraceEventType.Error, 23, "Received {2} for channel {0} with exception: {1}", channelId, ex.Message, ControlCode.ChannelTerminated);
			}
			await value.OnChannelTerminatedAsync(ex).ConfigureAwait(continueOnCapturedContext: false);
		}
	}

	private void OnContentWritingCompleted(QualifiedChannelId channelId)
	{
		Channel channel;
		lock (syncObject)
		{
			channel = openChannels[channelId];
		}
		if (channelId.Source == ChannelSource.Local && !channel.IsAccepted)
		{
			throw new MultiplexingProtocolException($"Remote party indicated they're done writing to channel {channelId} before accepting it.");
		}
		channel.OnContentWritingCompleted();
	}

	private async ValueTask OnContentAsync(FrameHeader header, ReadOnlySequence<byte> payload, CancellationToken cancellationToken)
	{
		QualifiedChannelId requiredChannelId = header.RequiredChannelId;
		Channel channel;
		lock (syncObject)
		{
			channel = openChannels[requiredChannelId];
		}
		if (requiredChannelId.Source == ChannelSource.Local && !channel.IsAccepted)
		{
			throw new MultiplexingProtocolException($"Remote party sent content for channel {requiredChannelId} before accepting it.");
		}
		if (!payload.IsEmpty)
		{
			await channel.OnContentAsync(payload, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		}
	}

	private void OnOfferAccepted(FrameHeader header, ReadOnlySequence<byte> payloadBuffer)
	{
		Channel.AcceptanceParameters acceptanceParameters = formatter.DeserializeAcceptanceParameters(payloadBuffer);
		QualifiedChannelId requiredChannelId = header.RequiredChannelId;
		Channel value;
		lock (syncObject)
		{
			if (!openChannels.TryGetValue(requiredChannelId, out value))
			{
				QualifiedChannelId qualifiedChannelId = requiredChannelId;
				throw new MultiplexingProtocolException("Offer accepted for unknown or forgotten channel ID " + qualifiedChannelId.ToString());
			}
		}
		if (!value.OnAccepted(acceptanceParameters) && TraceSource.Switch.ShouldTrace(TraceEventType.Warning))
		{
			TraceSource.TraceEvent(TraceEventType.Warning, 4, "Ignoring OfferAccepted message for channel {0} that we already canceled our offer for.", value.QualifiedId);
		}
	}

	private void OnContentProcessed(FrameHeader header, ReadOnlySequence<byte> payloadBuffer)
	{
		Channel value;
		lock (syncObject)
		{
			openChannels.TryGetValue(header.RequiredChannelId, out value);
		}
		if (value == null)
		{
			if (TraceSource.Switch.ShouldTrace(TraceEventType.Warning))
			{
				TraceSource.TraceEvent(TraceEventType.Warning, 21, "Ignoring ContentProcessed message for channel {0} that does not exist.", header.ChannelId);
			}
		}
		else
		{
			long bytesProcessed = formatter.DeserializeContentProcessed(payloadBuffer);
			value.OnContentProcessed(bytesProcessed);
		}
	}

	private void OnOffer(QualifiedChannelId channelId, ReadOnlySequence<byte> payloadBuffer)
	{
		Channel.OfferParameters offerParameters = formatter.DeserializeOfferParameters(payloadBuffer);
		Channel channel = new Channel(this, channelId, offerParameters);
		bool flag = false;
		ChannelOptions channelOptions = DefaultChannelOptions;
		lock (syncObject)
		{
			if (acceptingChannels.TryGetValue(offerParameters.Name, out Queue<TaskCompletionSource<Channel>> value))
			{
				while (value.Count > 0)
				{
					TaskCompletionSource<Channel> taskCompletionSource = value.Dequeue();
					if (taskCompletionSource.TrySetResult(channel))
					{
						if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
						{
							TraceSource.TraceEvent(TraceEventType.Information, 10, "Remote party offers channel {1} \"{0}\" which matches up with a pending AcceptChannelAsync", offerParameters.Name, channelId);
						}
						flag = true;
						channelOptions = (ChannelOptions)taskCompletionSource.Task.AsyncState;
						Assumes.NotNull(channelOptions);
						break;
					}
				}
			}
			if (!flag)
			{
				if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
				{
					TraceSource.TraceEvent(TraceEventType.Information, 10, "Remote party offers channel {1} \"{0}\" which has no pending AcceptChannelAsync", offerParameters.Name, channelId);
				}
				if (!channelsOfferedByThemByName.TryGetValue(offerParameters.Name, out Queue<Channel> value2))
				{
					channelsOfferedByThemByName.Add(offerParameters.Name, value2 = new Queue<Channel>());
				}
				value2.Enqueue(channel);
			}
			openChannels.Add(channelId, channel);
		}
		if (flag)
		{
			AcceptChannelOrThrow(channel, channelOptions);
		}
		ChannelOfferEventArgs args = new ChannelOfferEventArgs(channelId, channel.Name, flag);
		OnChannelOffered(args);
	}

	private bool TryAcceptChannel(Channel channel, ChannelOptions options)
	{
		Requires.NotNull(channel, "channel");
		Requires.NotNull(options, "options");
		if (channel.TryAcceptOffer(options))
		{
			return true;
		}
		return false;
	}

	private void AcceptChannelOrThrow(Channel channel, ChannelOptions options)
	{
		Requires.NotNull(channel, "channel");
		Requires.NotNull(options, "options");
		if (!TryAcceptChannel(channel, options))
		{
			if (channel.IsAccepted)
			{
				throw new InvalidOperationException("Channel is already accepted.");
			}
			if (channel.IsRejectedOrCanceled)
			{
				throw new InvalidOperationException("Channel is no longer available for acceptance.");
			}
			throw new InvalidOperationException("Channel could not be accepted.");
		}
	}

	private void OnChannelDisposed(Channel channel, Exception? exception = null)
	{
		Requires.NotNull(channel, "channel");
		if (!Completion.IsCompleted && !DisposalToken.IsCancellationRequested)
		{
			FrameHeader header = new FrameHeader
			{
				Code = ControlCode.ChannelTerminated,
				ChannelId = channel.QualifiedId
			};
			ReadOnlySequence<byte> payload = (formatter as V2Formatter)?.SerializeException(exception) ?? default(ReadOnlySequence<byte>);
			if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
			{
				TraceSource.TraceEvent(TraceEventType.Information, 11, "Local channel {0} \"{1}\" stream disposed.", channel.QualifiedId, channel.Name);
			}
			SendFrame(header, payload, DisposalToken);
		}
	}

	private void OnChannelWritingCompleted(Channel channel)
	{
		Requires.NotNull(channel, "channel");
		lock (syncObject)
		{
			if (!channelsPendingTermination.Contains(channel.QualifiedId) && openChannels.ContainsKey(channel.QualifiedId))
			{
				SendFrame(ControlCode.ContentWritingCompleted, channel.QualifiedId);
			}
		}
	}

	private void SendFrame(ControlCode code, QualifiedChannelId channelId)
	{
		FrameHeader header = new FrameHeader
		{
			Code = code,
			ChannelId = channelId
		};
		SendFrame(header, default(ReadOnlySequence<byte>), CancellationToken.None);
	}

	private void SendFrame(FrameHeader header, ReadOnlySequence<byte> payload, CancellationToken cancellationToken)
	{
		if (!Completion.IsCompleted)
		{
			DisposeSelfOnFailure(SendFrameAsync(header, payload, cancellationToken));
		}
	}

	private async Task SendFrameAsync(FrameHeader header, ReadOnlySequence<byte> payload, CancellationToken cancellationToken)
	{
		Assumes.True(payload.Length <= 20480, "payload", "Frame content exceeds max limit.");
		Verify.NotDisposed(this);
		await sendingSemaphore.WaitAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		try
		{
			Verify.NotDisposed(this);
			QualifiedChannelId requiredChannelId = header.RequiredChannelId;
			lock (syncObject)
			{
				if (header.Code == ControlCode.ChannelTerminated)
				{
					if (openChannels.ContainsKey(requiredChannelId))
					{
						Assumes.True(channelsPendingTermination.Add(requiredChannelId), "Sending ChannelTerminated more than once for channel {0}.", header.ChannelId);
					}
				}
				else
				{
					if (!openChannels.ContainsKey(requiredChannelId))
					{
						return;
					}
					if (channelsPendingTermination.Contains(requiredChannelId))
					{
						ControlCode code = header.Code;
						if ((code == ControlCode.ContentWritingCompleted || code == ControlCode.ContentProcessed) ? true : false)
						{
							TraceSource.TraceEvent(TraceEventType.Information, 14, "Skipping {0} frame for channel {1} because we're about to terminate it.", header.Code, header.ChannelId);
							return;
						}
						Assumes.Fail($"Sending {header.Code} frame for channel {header.ChannelId}, which we've already sent termination for.");
					}
				}
			}
			if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
			{
				TraceSource.TraceEvent(TraceEventType.Information, 13, "Sending {0} frame for channel {1}, carrying {2} bytes of content.", header.Code, header.ChannelId, (int)payload.Length);
			}
			if (!payload.IsEmpty && TraceSource.Switch.ShouldTrace(TraceEventType.Verbose))
			{
				TraceSource.TraceData(TraceEventType.Verbose, 17, payload);
			}
			formatter.WriteFrame(header, payload);
			await formatter.FlushAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		}
		finally
		{
			sendingSemaphore.Release();
		}
	}

	private ulong GetUnusedChannelId()
	{
		return (ulong)Interlocked.Add(ref lastOfferedChannelId, (!isOdd.HasValue) ? 1 : 2);
	}

	private void OfferChannelCanceled(object state)
	{
		Requires.NotNull(state, "state");
		Channel channel = (Channel)state;
		if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
		{
			TraceSource.TraceEvent(TraceEventType.Information, 12, "Offer of channel {1} (\"{0}\") canceled.", channel.Name, channel.QualifiedId);
		}
		channel.Dispose();
	}

	private void AcceptChannelCanceled(object state)
	{
		Requires.NotNull(state, "state");
		var (taskCompletionSource2, text2) = (Tuple<TaskCompletionSource<Channel>, string>)state;
		if (taskCompletionSource2.TrySetCanceled())
		{
			if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
			{
				TraceSource.TraceEvent(TraceEventType.Information, 9, "Cancelling AcceptChannelAsync for \"{0}\"", text2);
			}
			lock (syncObject)
			{
				if (acceptingChannels.TryGetValue(text2, out Queue<TaskCompletionSource<Channel>> value))
				{
					Assumes.True(value.RemoveMidQueue(taskCompletionSource2), "queue.RemoveMidQueue(channelSource)");
				}
				return;
			}
		}
		if (TraceSource.Switch.ShouldTrace(TraceEventType.Information))
		{
			TraceSource.TraceEvent(TraceEventType.Information, 9, "Cancelling AcceptChannelAsync for \"{0}\" attempted but failed.", text2);
		}
	}

	private void DisposeSelfOnFailure(Task task)
	{
		Requires.NotNull(task, "task");
		if (task.IsCompleted)
		{
			if (task.IsFaulted)
			{
				Fault(task.Exception.InnerException ?? task.Exception);
			}
		}
		else
		{
			task.ContinueWith(delegate(Task t, object? s)
			{
				((MultiplexingStream)s).Fault(t.Exception.InnerException ?? t.Exception);
			}, this, CancellationToken.None, TaskContinuationOptions.OnlyOnFaulted, TaskScheduler.Default).Forget();
		}
	}

	private void Fault(Exception exception)
	{
		if (!(exception is ObjectDisposedException) || !DisposalToken.IsCancellationRequested)
		{
			if (TraceSource.Switch.ShouldTrace(TraceEventType.Critical))
			{
				TraceSource.TraceEvent(TraceEventType.Critical, 3, "Disposing self due to exception: {0}", exception);
			}
			completionSource.TrySetException(exception);
			DisposeAsync().Forget();
		}
	}

	private void ThrowIfNotListening()
	{
		if (!listeningStarted)
		{
			throw new InvalidOperationException(Strings.ListeningHasNotStarted);
		}
	}
}
