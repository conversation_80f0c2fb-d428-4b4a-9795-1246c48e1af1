using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.WsTrust;

internal class MexDocument
{
	private class MexPolicy
	{
		public WsTrustVersion Version { get; set; }

		public string Id { get; set; }

		public UserAuthType AuthType { get; set; }

		public Uri Url { get; set; }
	}

	private const string WsTrustSoapTransport = "http://schemas.xmlsoap.org/soap/http";

	private readonly Dictionary<string, MexPolicy> _policies = new Dictionary<string, MexPolicy>();

	private readonly Dictionary<string, MexPolicy> _bindings = new Dictionary<string, MexPolicy>();

	public MexDocument(string responseBody)
	{
		XDocument xDocument = XDocument.Parse(responseBody, LoadOptions.None);
		ReadPolicies(xDocument);
		ReadPolicyBindings(xDocument);
		SetPolicyEndpointAddresses(xDocument);
	}

	public WsTrustEndpoint GetWsTrustUsernamePasswordEndpoint()
	{
		return GetWsTrustEndpoint(UserAuthType.UsernamePassword);
	}

	public WsTrustEndpoint GetWsTrustWindowsTransportEndpoint()
	{
		return GetWsTrustEndpoint(UserAuthType.IntegratedAuth);
	}

	private WsTrustEndpoint GetWsTrustEndpoint(UserAuthType userAuthType)
	{
		MexPolicy mexPolicy = SelectPolicy(userAuthType);
		if (mexPolicy == null)
		{
			return null;
		}
		return new WsTrustEndpoint(mexPolicy.Url, mexPolicy.Version);
	}

	private MexPolicy SelectPolicy(UserAuthType userAuthType)
	{
		return _policies.Values.FirstOrDefault((MexPolicy p) => p.Url != null && p.AuthType == userAuthType && p.Version == WsTrustVersion.WsTrust13) ?? _policies.Values.FirstOrDefault((MexPolicy p) => p.Url != null && p.AuthType == userAuthType);
	}

	private void ReadPolicies(XContainer mexDocument)
	{
		foreach (XElement item in FindElements(mexDocument, XmlNamespace.Wsp, "Policy"))
		{
			XElement xElement = item.Elements(XmlNamespace.Wsp + "ExactlyOne").FirstOrDefault();
			if (xElement == null)
			{
				continue;
			}
			foreach (XElement item2 in xElement.Descendants(XmlNamespace.Wsp + "All"))
			{
				XNamespace sp = XmlNamespace.Sp;
				XElement xElement2 = item2.Elements(XmlNamespace.Http + "NegotiateAuthentication").FirstOrDefault();
				if (xElement2 != null)
				{
					AddPolicy(item, UserAuthType.IntegratedAuth);
				}
				xElement2 = item2.Elements(sp + "SignedEncryptedSupportingTokens").FirstOrDefault();
				if (xElement2 == null && (xElement2 = item2.Elements(XmlNamespace.Sp2005 + "SignedSupportingTokens").FirstOrDefault()) == null)
				{
					continue;
				}
				sp = XmlNamespace.Sp2005;
				XElement xElement3 = xElement2.Elements(XmlNamespace.Wsp + "Policy").FirstOrDefault();
				if (xElement3 == null)
				{
					continue;
				}
				XElement xElement4 = xElement3.Elements(sp + "UsernameToken").FirstOrDefault();
				if (xElement4 != null)
				{
					XElement xElement5 = xElement4.Elements(XmlNamespace.Wsp + "Policy").FirstOrDefault();
					if (xElement5 != null && xElement5.Elements(sp + "WssUsernameToken10").FirstOrDefault() != null)
					{
						AddPolicy(item, UserAuthType.UsernamePassword);
					}
				}
			}
		}
	}

	private void ReadPolicyBindings(XContainer mexDocument)
	{
		foreach (XElement item in FindElements(mexDocument, XmlNamespace.Wsdl, "binding"))
		{
			foreach (XElement item2 in item.Elements(XmlNamespace.Wsp + "PolicyReference"))
			{
				XAttribute xAttribute = item2.Attribute("URI");
				if (xAttribute == null || !_policies.ContainsKey(xAttribute.Value))
				{
					continue;
				}
				XAttribute xAttribute2 = item.Attribute("name");
				if (xAttribute2 == null)
				{
					continue;
				}
				XElement xElement = item.Elements(XmlNamespace.Wsdl + "operation").FirstOrDefault();
				if (xElement == null)
				{
					continue;
				}
				XElement xElement2 = xElement.Elements(XmlNamespace.Soap12 + "operation").FirstOrDefault();
				if (xElement2 == null)
				{
					continue;
				}
				XAttribute xAttribute3 = xElement2.Attribute("soapAction");
				if (xAttribute3 == null || (string.Compare(XmlNamespace.Issue.ToString(), xAttribute3.Value, StringComparison.OrdinalIgnoreCase) != 0 && string.Compare(XmlNamespace.Issue2005.ToString(), xAttribute3.Value, StringComparison.OrdinalIgnoreCase) != 0))
				{
					continue;
				}
				bool flag = string.Compare(XmlNamespace.Issue2005.ToString(), xAttribute3.Value, StringComparison.OrdinalIgnoreCase) == 0;
				_policies[xAttribute.Value].Version = (flag ? WsTrustVersion.WsTrust2005 : WsTrustVersion.WsTrust13);
				XElement xElement3 = item.Elements(XmlNamespace.Soap12 + "binding").FirstOrDefault();
				if (xElement3 != null)
				{
					XAttribute xAttribute4 = xElement3.Attribute("transport");
					if (xAttribute4 != null && string.Compare("http://schemas.xmlsoap.org/soap/http", xAttribute4.Value, StringComparison.OrdinalIgnoreCase) == 0)
					{
						_bindings.Add(xAttribute2.Value, _policies[xAttribute.Value]);
					}
				}
			}
		}
	}

	private void SetPolicyEndpointAddresses(XContainer mexDocument)
	{
		foreach (XElement item in FindElements(mexDocument, XmlNamespace.Wsdl, "service").First().Elements(XmlNamespace.Wsdl + "port"))
		{
			XAttribute xAttribute = item.Attribute("binding");
			if (xAttribute == null)
			{
				continue;
			}
			string[] array = xAttribute.Value.Split(new char[1] { ':' }, 2);
			if (array.Length < 2 || !_bindings.ContainsKey(array[1]))
			{
				continue;
			}
			XElement xElement = item.Elements(XmlNamespace.Wsa10 + "EndpointReference").FirstOrDefault();
			if (xElement != null)
			{
				XElement xElement2 = xElement.Elements(XmlNamespace.Wsa10 + "Address").FirstOrDefault();
				if (xElement2 != null && Uri.IsWellFormedUriString(xElement2.Value, UriKind.Absolute))
				{
					_bindings[array[1]].Url = new Uri(xElement2.Value);
				}
			}
		}
	}

	private static IEnumerable<XElement> FindElements(XContainer mexDocument, XNamespace xNamespace, string element)
	{
		IEnumerable<XElement> enumerable = mexDocument.Elements()?.First()?.Elements(xNamespace + element);
		if (enumerable == null)
		{
			throw new MsalClientException("parsing_ws_metadata_exchange_failed", "Parsing WS metadata exchange failed.  Could not find XML data.");
		}
		if (!enumerable.Any())
		{
			enumerable = mexDocument.Elements().DescendantsAndSelf().Elements(xNamespace + element);
			if (!enumerable.Any())
			{
				throw new MsalClientException("parsing_ws_metadata_exchange_failed", "Parsing WS metadata exchange failed.  Could not find element " + element + ".");
			}
		}
		return enumerable;
	}

	private void AddPolicy(XElement policy, UserAuthType policyAuthType)
	{
		if ((policy.Descendants(XmlNamespace.Sp + "TransportBinding").FirstOrDefault() ?? policy.Descendants(XmlNamespace.Sp2005 + "TransportBinding").FirstOrDefault()) != null)
		{
			XAttribute xAttribute = policy.Attribute(XmlNamespace.Wsu + "Id");
			if (xAttribute != null)
			{
				_policies.Add("#" + xAttribute.Value, new MexPolicy
				{
					Id = xAttribute.Value,
					AuthType = policyAuthType
				});
			}
		}
	}
}
