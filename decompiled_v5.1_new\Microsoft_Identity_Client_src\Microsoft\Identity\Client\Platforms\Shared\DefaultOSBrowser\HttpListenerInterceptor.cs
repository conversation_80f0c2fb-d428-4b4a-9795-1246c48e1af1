using System;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Platforms.Shared.Desktop.OsBrowser;

namespace Microsoft.Identity.Client.Platforms.Shared.DefaultOSBrowser;

internal class HttpListenerInterceptor : IUriInterceptor
{
	private ILoggerAdapter _logger;

	public Action TestBeforeTopLevelCall { get; set; }

	public Action<string> TestBeforeStart { get; set; }

	public Action TestBeforeGetContext { get; set; }

	public HttpListenerInterceptor(ILoggerAdapter logger)
	{
		_logger = logger;
	}

	public async Task<Uri> ListenToSingleRequestAndRespondAsync(int port, string path, Func<Uri, MessageAndHttpCode> responseProducer, CancellationToken cancellationToken)
	{
		TestBeforeTopLevelCall?.Invoke();
		cancellationToken.ThrowIfCancellationRequested();
		HttpListener httpListener = null;
		string urlToListenTo = string.Empty;
		try
		{
			path = ((!string.IsNullOrEmpty(path)) ? (path.StartsWith("/") ? path : ("/" + path)) : "/");
			urlToListenTo = "http://localhost:" + port + path;
			if (!urlToListenTo.EndsWith("/"))
			{
				urlToListenTo += "/";
			}
			httpListener = new HttpListener();
			httpListener.Prefixes.Add(urlToListenTo);
			TestBeforeStart?.Invoke(urlToListenTo);
			httpListener.Start();
			_logger.Info(() => "Listening for authorization code on " + urlToListenTo);
			using (cancellationToken.Register(delegate
			{
				_logger.Warning("HttpListener stopped because cancellation was requested.");
				TryStopListening(httpListener);
			}))
			{
				TestBeforeGetContext?.Invoke();
				HttpListenerContext httpListenerContext = await httpListener.GetContextAsync().ConfigureAwait(continueOnCapturedContext: false);
				cancellationToken.ThrowIfCancellationRequested();
				Respond(responseProducer, httpListenerContext);
				_logger.Verbose(() => "HttpListner received a message on " + urlToListenTo);
				return httpListenerContext.Request.Url;
			}
		}
		catch (Exception ex) when (ex is HttpListenerException || ex is ObjectDisposedException)
		{
			_logger.Info(() => "HttpListenerException - cancellation requested? " + cancellationToken.IsCancellationRequested);
			cancellationToken.ThrowIfCancellationRequested();
			if (ex is HttpListenerException)
			{
				throw new MsalClientException("http_listener_error", "An HttpListenerException occurred while listening on " + urlToListenTo + " for the system browser to complete the login. Possible cause and mitigation: the app is unable to listen on the specified URL; run 'netsh http add iplisten 127.0.0.1' from the Admin command prompt.", ex);
			}
			throw;
		}
		finally
		{
			TryStopListening(httpListener);
		}
	}

	private static void TryStopListening(HttpListener httpListener)
	{
		try
		{
			httpListener?.Abort();
		}
		catch
		{
		}
	}

	private void Respond(Func<Uri, MessageAndHttpCode> responseProducer, HttpListenerContext context)
	{
		MessageAndHttpCode messageAndCode = responseProducer(context.Request.Url);
		_logger.Info(() => "Processing a response message to the browser. HttpStatus:" + messageAndCode.HttpCode);
		switch (messageAndCode.HttpCode)
		{
		case HttpStatusCode.Found:
			context.Response.StatusCode = 302;
			context.Response.RedirectLocation = messageAndCode.Message;
			break;
		case HttpStatusCode.OK:
		{
			byte[] bytes = Encoding.UTF8.GetBytes(messageAndCode.Message);
			context.Response.ContentLength64 = bytes.Length;
			context.Response.OutputStream.Write(bytes, 0, bytes.Length);
			break;
		}
		default:
			throw new NotImplementedException("HttpCode not supported" + messageAndCode.HttpCode);
		}
		context.Response.OutputStream.Close();
	}
}
