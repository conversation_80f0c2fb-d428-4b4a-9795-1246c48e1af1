using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.PlatformsCommon.Factories;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

public class AuthenticationHeaderParser
{
	private static readonly Lazy<IMsalHttpClientFactory> _httpClientFactory = new Lazy<IMsalHttpClientFactory>(() => PlatformProxyFactory.CreatePlatformProxy(null).CreateDefaultHttpClientFactory());

	public IReadOnlyList<WwwAuthenticateParameters> WwwAuthenticateParameters { get; private set; }

	public AuthenticationInfoParameters AuthenticationInfoParameters { get; private set; }

	public string PopNonce { get; private set; }

	public static Task<AuthenticationHeaderParser> ParseAuthenticationHeadersAsync(string resourceUri, CancellationToken cancellationToken = default(CancellationToken))
	{
		return ParseAuthenticationHeadersAsync(resourceUri, GetHttpClient(), cancellationToken);
	}

	public static async Task<AuthenticationHeaderParser> ParseAuthenticationHeadersAsync(string resourceUri, HttpClient httpClient, CancellationToken cancellationToken = default(CancellationToken))
	{
		if (httpClient == null)
		{
			throw new ArgumentNullException("httpClient");
		}
		if (string.IsNullOrWhiteSpace(resourceUri))
		{
			throw new ArgumentNullException("resourceUri");
		}
		return ParseAuthenticationHeaders((await httpClient.GetAsync(resourceUri, cancellationToken).ConfigureAwait(continueOnCapturedContext: false)).Headers);
	}

	public static AuthenticationHeaderParser ParseAuthenticationHeaders(HttpResponseHeaders httpResponseHeaders)
	{
		AuthenticationHeaderParser authenticationHeaderParser = new AuthenticationHeaderParser();
		AuthenticationInfoParameters authenticationInfoParameters = new AuthenticationInfoParameters();
		string text = null;
		if (httpResponseHeaders.WwwAuthenticate.Count != 0)
		{
			IReadOnlyList<WwwAuthenticateParameters> readOnlyList = Microsoft.Identity.Client.WwwAuthenticateParameters.CreateFromAuthenticationHeaders(httpResponseHeaders);
			text = readOnlyList.SingleOrDefault((WwwAuthenticateParameters parameter) => string.Equals(parameter.AuthenticationScheme, "PoP", StringComparison.Ordinal))?.Nonce;
			authenticationHeaderParser.WwwAuthenticateParameters = readOnlyList;
		}
		else
		{
			authenticationHeaderParser.WwwAuthenticateParameters = new List<WwwAuthenticateParameters>();
			authenticationInfoParameters = (authenticationHeaderParser.AuthenticationInfoParameters = AuthenticationInfoParameters.CreateFromResponseHeaders(httpResponseHeaders));
		}
		authenticationHeaderParser.PopNonce = text ?? authenticationInfoParameters.NextNonce;
		return authenticationHeaderParser;
	}

	internal static HttpClient GetHttpClient()
	{
		return _httpClientFactory.Value.GetHttpClient();
	}

	internal static KeyValuePair<string, string> CreateKeyValuePair(string paramValue, string authScheme)
	{
		string[] array = (from s in CoreHelpers.SplitWithQuotes(paramValue, '=')
			select s.Trim().Trim('"')).ToArray();
		if (array.Length < 2)
		{
			return new KeyValuePair<string, string>(authScheme, paramValue);
		}
		return new KeyValuePair<string, string>(array[0], array[1]);
	}
}
