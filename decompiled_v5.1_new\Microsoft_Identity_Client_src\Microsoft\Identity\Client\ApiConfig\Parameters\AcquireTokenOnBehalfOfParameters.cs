using System.Text;
using Microsoft.Identity.Client.Core;

namespace Microsoft.Identity.Client.ApiConfig.Parameters;

internal class AcquireTokenOnBehalfOfParameters : AbstractAcquireTokenConfidentialClientParameters, IAcquireTokenParameters
{
	public UserAssertion UserAssertion { get; set; }

	public string LongRunningOboCacheKey { get; set; }

	public bool SearchInCacheForLongRunningObo { get; set; }

	public bool ForceRefresh { get; set; }

	public void LogParameters(ILoggerAdapter logger)
	{
		if (logger.IsLoggingEnabled(LogLevel.Info))
		{
			StringBuilder stringBuilder = new StringBuilder($"=== OnBehalfOfParameters ===\r\nSendX5C: {base.SendX5C}\r\nForceRefresh: {ForceRefresh}\r\nUserAssertion set: {UserAssertion != null}\r\nSearchInCacheForLongRunningObo: {SearchInCacheForLongRunningObo}\r\nLongRunningOboCacheKey set: {!string.IsNullOrWhiteSpace(LongRunningOboCacheKey)}");
			if (UserAssertion != null && !string.IsNullOrWhiteSpace(LongRunningOboCacheKey))
			{
				stringBuilder.AppendLine("InitiateLongRunningProcessInWebApi called: True");
			}
			else if (UserAssertion == null && !string.IsNullOrWhiteSpace(LongRunningOboCacheKey))
			{
				stringBuilder.AppendLine("AcquireTokenInLongRunningProcess called: True");
			}
			logger.Info(stringBuilder.ToString());
		}
	}
}
