using System;
using System.Globalization;
using System.Net.Http.Headers;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

public class MsalServiceException : MsalException
{
	private const string ClaimsKey = "claims";

	private const string ResponseBodyKey = "response_body";

	private const string CorrelationIdKey = "correlation_id";

	private const string SubErrorKey = "sub_error";

	private int _statusCode;

	private string _responseBody;

	private HttpResponseHeaders _headers;

	public int StatusCode
	{
		get
		{
			return _statusCode;
		}
		internal set
		{
			_statusCode = value;
			UpdateIsRetryable();
		}
	}

	[JsonInclude]
	public string Claims { get; internal set; }

	public string ResponseBody
	{
		get
		{
			return _responseBody;
		}
		set
		{
			_responseBody = value;
			UpdateIsRetryable();
		}
	}

	public HttpResponseHeaders Headers
	{
		get
		{
			return _headers;
		}
		set
		{
			_headers = value;
			UpdateIsRetryable();
		}
	}

	internal string SubError { get; set; }

	internal string[] ErrorCodes { get; set; }

	public MsalServiceException(string errorCode, string errorMessage)
		: base(errorCode, errorMessage)
	{
		if (string.IsNullOrWhiteSpace(errorMessage))
		{
			throw new ArgumentNullException("errorMessage");
		}
		UpdateIsRetryable();
	}

	public MsalServiceException(string errorCode, string errorMessage, int statusCode)
		: this(errorCode, errorMessage)
	{
		StatusCode = statusCode;
	}

	public MsalServiceException(string errorCode, string errorMessage, Exception innerException)
		: base(errorCode, errorMessage, innerException)
	{
		UpdateIsRetryable();
	}

	public MsalServiceException(string errorCode, string errorMessage, int statusCode, Exception innerException)
		: base(errorCode, errorMessage, innerException)
	{
		StatusCode = statusCode;
		UpdateIsRetryable();
	}

	public MsalServiceException(string errorCode, string errorMessage, int statusCode, string claims, Exception innerException)
		: this(errorCode, errorMessage, statusCode, innerException)
	{
		Claims = claims;
	}

	protected virtual void UpdateIsRetryable()
	{
		base.IsRetryable = (StatusCode >= 500 && StatusCode < 600) || StatusCode == 429 || StatusCode == 408 || string.Equals(base.ErrorCode, "request_timeout", StringComparison.OrdinalIgnoreCase) || string.Equals(base.ErrorCode, "temporarily_unavailable", StringComparison.OrdinalIgnoreCase);
	}

	public override string ToString()
	{
		return base.ToString() + string.Format(CultureInfo.InvariantCulture, "\n\tStatusCode: {0} \n\tResponseBody: {1} \n\tHeaders: {2}", StatusCode, ResponseBody, Headers);
	}

	internal override void PopulateJson(JsonObject jObject)
	{
		base.PopulateJson(jObject);
		jObject["claims"] = Claims;
		jObject["response_body"] = ResponseBody;
		jObject["correlation_id"] = base.CorrelationId;
		jObject["sub_error"] = SubError;
	}

	internal override void PopulateObjectFromJson(JsonObject jObject)
	{
		base.PopulateObjectFromJson(jObject);
		Claims = JsonHelper.GetExistingOrEmptyString(jObject, "claims");
		ResponseBody = JsonHelper.GetExistingOrEmptyString(jObject, "response_body");
		base.CorrelationId = JsonHelper.GetExistingOrEmptyString(jObject, "correlation_id");
		SubError = JsonHelper.GetExistingOrEmptyString(jObject, "sub_error");
	}
}
