using System;
using System.Collections.Generic;
using Microsoft.IdentityModel.Tokens;

namespace Microsoft.IdentityModel.JsonWebTokens;

internal class JwtTokenDecryptionParameters
{
	public byte[] CipherTextBytes { get; set; }

	public byte[] HeaderAsciiBytes { get; set; }

	public byte[] InitializationVectorBytes { get; set; }

	public byte[] AuthenticationTagBytes { get; set; }

	public string Alg { get; set; }

	public string AuthenticationTag { get; set; }

	public string Ciphertext { get; set; }

	public Func<byte[], string, int, string> DecompressionFunction { get; set; }

	public string Enc { get; set; }

	public string EncodedHeader { get; set; }

	public string EncodedToken { get; set; }

	public string InitializationVector { get; set; }

	public IEnumerable<SecurityKey> Keys { get; set; }

	public int MaximumDeflateSize { get; set; } = 256000;

	public string Zip { get; set; }
}
