using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Identity.Client.AuthScheme.PoP;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Platforms.Features.DesktopOs;
using Microsoft.Identity.Client.Platforms.Shared.NetStdCore;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.PlatformsCommon.Shared;
using Microsoft.Identity.Client.UI;

namespace Microsoft.Identity.Client.Platforms.netcore;

internal class NetCorePlatformProxy : AbstractPlatformProxy
{
	public override bool BrokerSupportsWamAccounts => true;

	public NetCorePlatformProxy(ILoggerAdapter logger)
		: base(logger)
	{
	}

	public override Task<string> GetUserPrincipalNameAsync()
	{
		return Task.FromResult(GetUserPrincipalName(8));
	}

	private string GetUserPrincipalName(int nameFormat)
	{
		if (DesktopOsHelper.IsWindows())
		{
			uint userNameSize = 0u;
			WindowsNativeMethods.GetUserNameEx(nameFormat, null, ref userNameSize);
			if (userNameSize == 0)
			{
				throw new MsalClientException("get_user_name_failed", "Failed to get user name. ", new Win32Exception(Marshal.GetLastWin32Error()));
			}
			StringBuilder stringBuilder = new StringBuilder((int)userNameSize);
			if (!WindowsNativeMethods.GetUserNameEx(nameFormat, stringBuilder, ref userNameSize))
			{
				throw new MsalClientException("get_user_name_failed", "Failed to get user name. ", new Win32Exception(Marshal.GetLastWin32Error()));
			}
			return stringBuilder.ToString();
		}
		throw new PlatformNotSupportedException("MSAL cannot determine the username (UPN) of the currently logged in user.For Integrated Windows Authentication and Username/Password flows, please use .WithUsername() before calling ExecuteAsync(). For more details see https://aka.ms/msal-net-iwa");
	}

	protected override string InternalGetProcessorArchitecture()
	{
		if (!DesktopOsHelper.IsWindows())
		{
			return null;
		}
		return WindowsNativeMethods.GetProcessorArchitecture();
	}

	protected override string InternalGetOperatingSystem()
	{
		if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
		{
			return RuntimeInformation.OSDescription;
		}
		if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
		{
			return "MacOS";
		}
		if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
		{
			return "Linux";
		}
		return RuntimeInformation.OSDescription;
	}

	protected override string InternalGetDeviceModel()
	{
		return null;
	}

	public override string GetDefaultRedirectUri(string clientId, bool useRecommendedRedirectUri = false)
	{
		if (useRecommendedRedirectUri)
		{
			return "http://localhost";
		}
		return "urn:ietf:wg:oauth:2.0:oob";
	}

	protected override string InternalGetProductName()
	{
		return "MSAL.NetCore";
	}

	protected override string InternalGetCallingApplicationName()
	{
		return Assembly.GetEntryAssembly()?.GetName()?.Name?.ToString(CultureInfo.InvariantCulture);
	}

	protected override string InternalGetCallingApplicationVersion()
	{
		return Assembly.GetEntryAssembly()?.GetName()?.Version?.ToString();
	}

	protected override string InternalGetDeviceId()
	{
		return Environment.MachineName;
	}

	public override ILegacyCachePersistence CreateLegacyCachePersistence()
	{
		return new InMemoryLegacyCachePersistance();
	}

	protected override IWebUIFactory CreateWebUiFactory()
	{
		return new NetCoreWebUIFactory();
	}

	protected override ICryptographyManager InternalGetCryptographyManager()
	{
		return new CommonCryptographyManager(base.Logger);
	}

	protected override IPlatformLogger InternalGetPlatformLogger()
	{
		return new EventSourcePlatformLogger();
	}

	protected override IFeatureFlags CreateFeatureFlags()
	{
		return new NetCoreFeatureFlags();
	}

	public override Task StartDefaultOsBrowserAsync(string url, bool isBrokerConfigured)
	{
		if (DesktopOsHelper.IsWindows())
		{
			try
			{
				Process.Start(new ProcessStartInfo
				{
					FileName = url,
					UseShellExecute = true
				});
			}
			catch
			{
				url = url.Replace("&", "^&");
				Process.Start(new ProcessStartInfo("cmd", "/c start msedge " + url)
				{
					CreateNoWindow = true
				});
			}
		}
		else if (DesktopOsHelper.IsLinux())
		{
			if (!string.IsNullOrWhiteSpace(Environment.GetEnvironmentVariable("SUDO_USER")))
			{
				throw new MsalClientException("linux_xdg_open_failed", "Unable to open a web page using xdg-open, gnome-open, kfmclient or wslview tools in sudo mode. Please run the process as non-sudo user.");
			}
			try
			{
				bool flag = false;
				string[] openToolsLinux = GetOpenToolsLinux(isBrokerConfigured);
				foreach (string executable in openToolsLinux)
				{
					if (TryGetExecutablePath(executable, out var path))
					{
						OpenLinuxBrowser(path, url);
						flag = true;
						break;
					}
				}
				if (!flag)
				{
					throw new MsalClientException("linux_xdg_open_failed", "Unable to open a web page using xdg-open, gnome-open, kfmclient or wslview tools. See inner exception for details. Possible causes for this error are: tools are not installed or they cannot open a URL. Make sure you can open a web page by invoking from a terminal: xdg-open https://www.bing.com ");
				}
			}
			catch (Exception innerException)
			{
				throw new MsalClientException("linux_xdg_open_failed", "Unable to open a web page using xdg-open, gnome-open, kfmclient or wslview tools. See inner exception for details. Possible causes for this error are: tools are not installed or they cannot open a URL. Make sure you can open a web page by invoking from a terminal: xdg-open https://www.bing.com ", innerException);
			}
		}
		else
		{
			if (!DesktopOsHelper.IsMac())
			{
				throw new PlatformNotSupportedException(RuntimeInformation.OSDescription);
			}
			Process.Start("/usr/bin/open", url);
		}
		return Task.FromResult(0);
	}

	private void OpenLinuxBrowser(string openToolPath, string url)
	{
		Process.Start(new ProcessStartInfo(openToolPath, url)
		{
			RedirectStandardOutput = true,
			RedirectStandardError = true
		});
	}

	private string[] GetOpenToolsLinux(bool isBrokerConfigured)
	{
		if (!isBrokerConfigured)
		{
			return new string[5] { "xdg-open", "gnome-open", "kfmclient", "microsoft-edge", "wslview" };
		}
		return new string[5] { "microsoft-edge", "xdg-open", "gnome-open", "kfmclient", "wslview" };
	}

	public override IPoPCryptoProvider GetDefaultPoPCryptoProvider()
	{
		return PoPProviderFactory.GetOrCreateProvider();
	}

	private bool TryGetExecutablePath(string executable, out string path)
	{
		string environmentVariable = Environment.GetEnvironmentVariable("PATH");
		if (environmentVariable != null)
		{
			string[] array = environmentVariable.Split(':');
			foreach (string path2 in array)
			{
				path = Path.Combine(path2, executable);
				if (File.Exists(path))
				{
					return true;
				}
			}
		}
		path = null;
		return false;
	}

	public override IDeviceAuthManager CreateDeviceAuthManager()
	{
		return new DeviceAuthManager(base.CryptographyManager);
	}
}
