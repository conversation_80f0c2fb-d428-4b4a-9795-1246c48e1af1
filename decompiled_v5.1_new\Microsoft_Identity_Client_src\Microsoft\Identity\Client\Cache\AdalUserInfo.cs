using System;
using System.Text.Json.Serialization;
using Microsoft.Identity.Client.Platforms.net6;

namespace Microsoft.Identity.Client.Cache;

[JsonObject]
[Preserve(AllMembers = true)]
internal sealed class AdalUserInfo
{
	[JsonInclude]
	public string UniqueId { get; internal set; }

	[JsonInclude]
	public string DisplayableId { get; internal set; }

	[JsonInclude]
	public string GivenName { get; internal set; }

	[JsonInclude]
	public string FamilyName { get; internal set; }

	[JsonInclude]
	public DateTimeOffset? PasswordExpiresOn { get; internal set; }

	[JsonInclude]
	public Uri PasswordChangeUrl { get; internal set; }

	[JsonInclude]
	public string IdentityProvider { get; internal set; }

	public AdalUserInfo()
	{
	}

	public AdalUserInfo(AdalUserInfo other)
	{
		if (other != null)
		{
			UniqueId = other.UniqueId;
			DisplayableId = other.DisplayableId;
			GivenName = other.GivenName;
			FamilyName = other.FamilyName;
			IdentityProvider = other.IdentityProvider;
			PasswordChangeUrl = other.PasswordChangeUrl;
			PasswordExpiresOn = other.PasswordExpiresOn;
		}
	}
}
