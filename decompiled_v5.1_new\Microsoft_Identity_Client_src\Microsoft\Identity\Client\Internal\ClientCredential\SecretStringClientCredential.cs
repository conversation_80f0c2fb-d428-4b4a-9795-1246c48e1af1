using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.TelemetryCore;

namespace Microsoft.Identity.Client.Internal.ClientCredential;

internal class SecretStringClientCredential : IClientCredential
{
	internal string Secret { get; }

	public AssertionType AssertionType => AssertionType.Secret;

	public SecretStringClientCredential(string secret)
	{
		Secret = secret;
	}

	public Task AddConfidentialClientParametersAsync(OAuth2Client oAuth2Client, ILoggerAdapter logger, ICryptographyManager cryptographyManager, string clientId, string tokenEndpoint, bool sendX5C, bool useSha2, CancellationToken cancellationToken)
	{
		oAuth2Client.AddBodyParameter("client_secret", Secret);
		return Task.CompletedTask;
	}
}
