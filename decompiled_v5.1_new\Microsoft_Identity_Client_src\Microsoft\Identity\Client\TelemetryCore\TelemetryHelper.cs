using System;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;

namespace Microsoft.Identity.Client.TelemetryCore;

internal sealed class TelemetryHelper : IDisposable
{
	private readonly ApiEvent _eventToEnd;

	private readonly IHttpTelemetryManager _httpTelemetryManager;

	private bool _disposedValue;

	public TelemetryHelper(IHttpTelemetryManager httpTelemetryManager, ApiEvent eventBase)
	{
		_httpTelemetryManager = httpTelemetryManager;
		_eventToEnd = eventBase;
	}

	private void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				_httpTelemetryManager?.RecordStoppedEvent(_eventToEnd);
			}
			_disposedValue = true;
		}
	}

	public void Dispose()
	{
		Dispose(disposing: true);
	}
}
