using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Advanced;
using Microsoft.Identity.Client.ApiConfig.Executors;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

public sealed class AcquireTokenOnBehalfOfParameterBuilder : AbstractConfidentialClientAcquireTokenParameterBuilder<AcquireTokenOnBehalfOfParameterBuilder>
{
	internal AcquireTokenOnBehalfOfParameters Parameters { get; } = new AcquireTokenOnBehalfOfParameters();

	internal AcquireTokenOnBehalfOfParameterBuilder(IConfidentialClientApplicationExecutor confidentialClientApplicationExecutor)
		: base(confidentialClientApplicationExecutor)
	{
	}

	internal static AcquireTokenOnBehalfOfParameterBuilder Create(IConfidentialClientApplicationExecutor confidentialClientApplicationExecutor, IEnumerable<string> scopes, UserAssertion userAssertion)
	{
		return new AcquireTokenOnBehalfOfParameterBuilder(confidentialClientApplicationExecutor).WithScopes(scopes).WithUserAssertion(userAssertion);
	}

	internal static AcquireTokenOnBehalfOfParameterBuilder Create(IConfidentialClientApplicationExecutor confidentialClientApplicationExecutor, IEnumerable<string> scopes, UserAssertion userAssertion, string cacheKey)
	{
		return new AcquireTokenOnBehalfOfParameterBuilder(confidentialClientApplicationExecutor).WithScopes(scopes).WithUserAssertion(userAssertion).WithCacheKey(cacheKey);
	}

	internal static AcquireTokenOnBehalfOfParameterBuilder Create(IConfidentialClientApplicationExecutor confidentialClientApplicationExecutor, IEnumerable<string> scopes, string cacheKey)
	{
		return new AcquireTokenOnBehalfOfParameterBuilder(confidentialClientApplicationExecutor).WithScopes(scopes).WithCacheKey(cacheKey);
	}

	private AcquireTokenOnBehalfOfParameterBuilder WithUserAssertion(UserAssertion userAssertion)
	{
		Parameters.UserAssertion = userAssertion;
		return this;
	}

	private AcquireTokenOnBehalfOfParameterBuilder WithCacheKey(string cacheKey)
	{
		Parameters.LongRunningOboCacheKey = cacheKey ?? throw new ArgumentNullException("cacheKey");
		return this;
	}

	public AcquireTokenOnBehalfOfParameterBuilder WithSendX5C(bool withSendX5C)
	{
		Parameters.SendX5C = withSendX5C;
		return this;
	}

	public AcquireTokenOnBehalfOfParameterBuilder WithForceRefresh(bool forceRefresh)
	{
		Parameters.ForceRefresh = forceRefresh;
		return this;
	}

	public AcquireTokenOnBehalfOfParameterBuilder WithCcsRoutingHint(string userObjectIdentifier, string tenantIdentifier)
	{
		if (string.IsNullOrEmpty(userObjectIdentifier) || string.IsNullOrEmpty(tenantIdentifier))
		{
			return this;
		}
		Dictionary<string, string> extraHttpHeaders = new Dictionary<string, string> { 
		{
			"x-anchormailbox",
			CoreHelpers.GetCcsClientInfoHint(userObjectIdentifier, tenantIdentifier)
		} };
		this.WithExtraHttpHeaders(extraHttpHeaders);
		return this;
	}

	public AcquireTokenOnBehalfOfParameterBuilder WithCcsRoutingHint(string userName)
	{
		if (string.IsNullOrEmpty(userName))
		{
			return this;
		}
		Dictionary<string, string> extraHttpHeaders = new Dictionary<string, string> { 
		{
			"x-anchormailbox",
			CoreHelpers.GetCcsUpnHint(userName)
		} };
		this.WithExtraHttpHeaders(extraHttpHeaders);
		return this;
	}

	internal override Task<AuthenticationResult> ExecuteInternalAsync(CancellationToken cancellationToken)
	{
		return base.ConfidentialClientApplicationExecutor.ExecuteAsync(base.CommonParameters, Parameters, cancellationToken);
	}

	protected override void Validate()
	{
		base.Validate();
		if (!Parameters.SendX5C.HasValue)
		{
			Parameters.SendX5C = base.ServiceBundle.Config?.SendX5C ?? false;
		}
	}

	internal override ApiEvent.ApiIds CalculateApiEventId()
	{
		if (string.IsNullOrEmpty(Parameters.LongRunningOboCacheKey))
		{
			return ApiEvent.ApiIds.AcquireTokenOnBehalfOf;
		}
		if (Parameters.UserAssertion != null)
		{
			return ApiEvent.ApiIds.InitiateLongRunningObo;
		}
		return ApiEvent.ApiIds.AcquireTokenInLongRunningObo;
	}
}
