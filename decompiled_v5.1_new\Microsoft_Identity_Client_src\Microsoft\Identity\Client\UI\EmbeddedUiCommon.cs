using System;

namespace Microsoft.Identity.Client.UI;

internal static class EmbeddedU<PERSON>Common
{
	public static bool IsAllowedIeOrEdgeAuthorizationRedirect(Uri uri)
	{
		if (!uri.Scheme.Equals("https", StringComparison.OrdinalIgnoreCase) && !uri.AbsoluteUri.Equals("about:blank", StringComparison.OrdinalIgnoreCase) && !uri.Scheme.Equals("javascript", StringComparison.OrdinalIgnoreCase))
		{
			return uri.Scheme.Equals("res", StringComparison.OrdinalIgnoreCase);
		}
		return true;
	}
}
