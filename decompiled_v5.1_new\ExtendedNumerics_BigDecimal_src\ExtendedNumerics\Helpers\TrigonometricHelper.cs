using System;
using System.Numerics;

namespace ExtendedNumerics.Helpers;

public static class TrigonometricHelper
{
	public static BigDecimal HalfPi = BigDecimal.Pi / 2.0;

	public static BigDecimal TwicePi = BigDecimal.Pi * 2.0;

	internal static BigDecimal GetPrecisionTarget(int precision)
	{
		int num = precision + 1;
		return new BigDecimal(BigInteger.One, -num);
	}

	internal static int ModOddHalfPi(BigDecimal radians)
	{
		if (BigDecimal.Normalize(BigDecimal.Mod(radians, HalfPi)).IsZero())
		{
			return (int)BigDecimal.Normalize(BigDecimal.Mod(BigDecimal.Normalize(BigDecimal.Divide(radians, HalfPi)), new BigDecimal(2)));
		}
		return 0;
	}

	internal static BigDecimal TaylorSeriesSum(BigDecimal radians, BigDecimal sumStart, BigInteger counterStart, BigInteger jump, BigInteger multiplier, bool factorialDenominator, int precision)
	{
		BigDecimal precisionTarget = GetPrecisionTarget(precision);
		BigDecimal bigDecimal = sumStart;
		BigInteger bigInteger = counterStart;
		BigDecimal bigDecimal2 = -1;
		BigDecimal value = 1;
		BigDecimal bigDecimal3 = 1;
		int num = 0;
		while (num <= precision * 2)
		{
			BigDecimal bigDecimal4 = bigInteger;
			if (factorialDenominator)
			{
				bigDecimal4 = new BigDecimal(BigIntegerHelper.FastFactorial.Factorial(bigInteger));
			}
			BigDecimal bigDecimal5 = BigDecimal.One / bigDecimal4;
			BigDecimal bigDecimal6 = BigDecimal.Pow(radians, bigInteger);
			bigDecimal += bigDecimal5 * bigDecimal6 * bigDecimal3;
			if (bigDecimal2 != -1)
			{
				value = bigDecimal2 - bigDecimal;
			}
			bigInteger += jump;
			num++;
			bigDecimal3 *= (BigDecimal)multiplier;
			bigDecimal2 = bigDecimal;
			if (!(BigDecimal.Abs(value) > precisionTarget))
			{
				break;
			}
		}
		return bigDecimal;
	}

	internal static BigDecimal WrapInput(BigDecimal radians)
	{
		int sign = radians.Sign;
		radians = BigDecimal.Abs(radians);
		if (radians == 0)
		{
			return 0;
		}
		if (radians == HalfPi)
		{
			return sign;
		}
		if (radians > HalfPi)
		{
			int exponent = (int)BigDecimal.Round(radians / BigDecimal.Pi, MidpointRounding.ToEven);
			BigDecimal bigDecimal = BigInteger.Pow(BigInteger.MinusOne, exponent);
			return (BigDecimal.Mod(radians + HalfPi, BigDecimal.Pi) - HalfPi) * bigDecimal;
		}
		return radians;
	}

	public static BigDecimal Modulus(this BigDecimal value, BigDecimal mod)
	{
		BigDecimal left = BigDecimal.Round(BigDecimal.Divide(value, mod));
		return BigDecimal.Subtract(value, BigDecimal.Multiply(left, mod));
	}
}
