using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Nodes;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Internal;

internal static class ClaimsHelper
{
	private const string AccessTokenClaim = "access_token";

	private const string XmsClientCapability = "xms_cc";

	internal static string GetMergedClaimsAndClientCapabilities(string claims, IEnumerable<string> clientCapabilities)
	{
		if (clientCapabilities != null && clientCapabilities.Any())
		{
			JsonObject capabilitiesJson = CreateClientCapabilitiesRequestJson(clientCapabilities);
			return JsonHelper.JsonObjectToString(MergeClaimsIntoCapabilityJson(claims, capabilitiesJson));
		}
		return claims;
	}

	internal static JsonObject MergeClaimsIntoCapabilityJson(string claims, JsonObject capabilitiesJson)
	{
		if (!string.IsNullOrEmpty(claims))
		{
			JsonObject newContent;
			try
			{
				newContent = JsonHelper.ParseIntoJsonObject(claims);
			}
			catch (JsonException innerException)
			{
				throw new MsalClientException("invalid_json_claims_format", MsalErrorMessage.InvalidJsonClaimsFormat(claims), innerException);
			}
			capabilitiesJson = JsonHelper.Merge(capabilitiesJson, newContent);
		}
		return capabilitiesJson;
	}

	private static JsonObject CreateClientCapabilitiesRequestJson(IEnumerable<string> clientCapabilities)
	{
		JsonObject jsonObject = new JsonObject();
		JsonObject jsonObject2 = new JsonObject();
		JsonObject jsonObject3 = new JsonObject();
		JsonNode[] items = clientCapabilities.Select((string c) => JsonValue.Create(c)).ToArray();
		jsonObject3["values"] = new JsonArray(items);
		jsonObject2["xms_cc"] = jsonObject3;
		jsonObject["access_token"] = jsonObject2;
		return jsonObject;
	}
}
