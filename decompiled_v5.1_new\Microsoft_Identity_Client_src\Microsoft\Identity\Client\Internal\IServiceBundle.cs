using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Instance.Discovery;
using Microsoft.Identity.Client.OAuth2.Throttling;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.TelemetryCore;
using Microsoft.Identity.Client.WsTrust;

namespace Microsoft.Identity.Client.Internal;

internal interface IServiceBundle
{
	ApplicationConfiguration Config { get; }

	ILoggerAdapter ApplicationLogger { get; }

	IHttpManager HttpManager { get; }

	IInstanceDiscoveryManager InstanceDiscoveryManager { get; }

	IPlatformProxy PlatformProxy { get; }

	IWsTrustWebRequestManager WsTrustWebRequestManager { get; }

	IDeviceAuthManager DeviceAuthManager { get; }

	IThrottlingProvider ThrottlingManager { get; }

	IHttpTelemetryManager HttpTelemetryManager { get; }

	void SetPlatformProxyForTest(IPlatformProxy platformProxy);
}
