using System.Collections.Generic;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Extensibility;
using Microsoft.Identity.Client.UI;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.ApiConfig.Parameters;

internal class AcquireTokenInteractiveParameters : IAcquireTokenParameters
{
	public Prompt Prompt { get; set; } = Prompt.NotSpecified;

	public CoreUIParent UiParent { get; } = new CoreUIParent();

	public IEnumerable<string> ExtraScopesToConsent { get; set; } = CollectionHelpers.GetEmptyReadOnlyList<string>();

	public WebViewPreference UseEmbeddedWebView { get; set; }

	public string LoginHint { get; set; }

	public IAccount Account { get; set; }

	public ICustomWebUi CustomWebUi { get; set; }

	public string CodeVerifier { get; set; }

	public void LogParameters(ILoggerAdapter logger)
	{
		if (logger.IsLoggingEnabled(LogLevel.Info))
		{
			UiParent.SystemWebViewOptions?.LogParameters(logger);
			logger.Info($"=== InteractiveParameters Data ===\r\nLoginHint provided: {!string.IsNullOrEmpty(LoginHint)}\r\nUser provided: {Account != null}\r\nUseEmbeddedWebView: {UseEmbeddedWebView}\r\nExtraScopesToConsent: {string.Join(";", ExtraScopesToConsent ?? CollectionHelpers.GetEmptyReadOnlyList<string>())}\r\nPrompt: {Prompt.PromptValue}\r\nHasCustomWebUi: {CustomWebUi != null}");
		}
	}
}
