using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.Identity.Client.Platforms.net6;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.UI;

[JsonObject]
[Preserve(AllMembers = true)]
internal class AuthorizationResult
{
	public AuthorizationStatus Status { get; private set; }

	public string Code { get; set; }

	public string Error { get; set; }

	public string ErrorDescription { get; set; }

	public string CloudInstanceHost { get; set; }

	public string ClientInfo { get; set; }

	public string State { get; set; }

	public static AuthorizationResult FromUri(string webAuthenticationResult)
	{
		if (string.IsNullOrWhiteSpace(webAuthenticationResult))
		{
			return FromStatus(AuthorizationStatus.UnknownError, "authentication_failed", "The authorization server returned an invalid response. ");
		}
		string query = new Uri(webAuthenticationResult).Query;
		if (string.IsNullOrWhiteSpace(query))
		{
			return FromStatus(AuthorizationStatus.UnknownError, "authentication_failed", "The authorization server returned an invalid response. ");
		}
		return FromParsedValues(CoreHelpers.ParseKeyValueList(query.Substring(1), '&', urlDecode: true, null), webAuthenticationResult);
	}

	public static AuthorizationResult FromPostData(byte[] postData)
	{
		if (postData == null)
		{
			return FromStatus(AuthorizationStatus.UnknownError, "authentication_failed", "The authorization server returned an invalid response. ");
		}
		return FromParsedValues(CoreHelpers.ParseKeyValueList(Encoding.Default.GetString(postData).TrimEnd('\0'), '&', urlDecode: true, null));
	}

	private static AuthorizationResult FromParsedValues(Dictionary<string, string> parameters, string url = null)
	{
		if (parameters.TryGetValue("error", out var value))
		{
			if (parameters.TryGetValue("error_subcode", out var value2) && "cancel".Equals(value2, StringComparison.OrdinalIgnoreCase))
			{
				return FromStatus(AuthorizationStatus.UserCancel);
			}
			string value3;
			return FromStatus(AuthorizationStatus.ProtocolError, value, parameters.TryGetValue("error_description", out value3) ? value3 : null);
		}
		AuthorizationResult authorizationResult = new AuthorizationResult
		{
			Status = AuthorizationStatus.Success
		};
		if (parameters.TryGetValue("state", out var value4))
		{
			authorizationResult.State = value4;
		}
		if (parameters.TryGetValue("cloud_instance_host_name", out var value5))
		{
			authorizationResult.CloudInstanceHost = value5;
		}
		if (parameters.TryGetValue("client_info", out var value6))
		{
			authorizationResult.ClientInfo = value6;
		}
		if (parameters.TryGetValue("code", out var value7))
		{
			authorizationResult.Code = value7;
		}
		else
		{
			if (string.IsNullOrEmpty(url) || !url.StartsWith("msauth://", StringComparison.OrdinalIgnoreCase))
			{
				return FromStatus(AuthorizationStatus.UnknownError, "authentication_failed", "The authorization server returned an invalid response. ");
			}
			authorizationResult.Code = url;
		}
		return authorizationResult;
	}

	internal static AuthorizationResult FromStatus(AuthorizationStatus status)
	{
		if (status == AuthorizationStatus.Success)
		{
			throw new InvalidOperationException("Use the FromUri builder");
		}
		AuthorizationResult authorizationResult = new AuthorizationResult
		{
			Status = status
		};
		switch (status)
		{
		case AuthorizationStatus.UserCancel:
			authorizationResult.Error = "authentication_canceled";
			authorizationResult.ErrorDescription = "User canceled authentication. ";
			break;
		case AuthorizationStatus.UnknownError:
			authorizationResult.Error = "unknown_error";
			authorizationResult.ErrorDescription = "Unknown error";
			break;
		}
		return authorizationResult;
	}

	public static AuthorizationResult FromStatus(AuthorizationStatus status, string error, string errorDescription)
	{
		return new AuthorizationResult
		{
			Status = status,
			Error = error,
			ErrorDescription = errorDescription
		};
	}
}
