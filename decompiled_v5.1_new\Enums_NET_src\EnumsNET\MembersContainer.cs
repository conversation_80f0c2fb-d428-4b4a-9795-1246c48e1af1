using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using EnumsNET.Utilities;

namespace EnumsNET;

internal sealed class MembersContainer<TEnum> : IReadOnlyList<EnumMember<TEnum>>, IEnumerable<EnumMember<TEnum>>, IEnumerable, IReadOnlyCollection<EnumMember<TEnum>> where TEnum : struct, Enum
{
	private readonly IEnumerable<EnumMemberInternal> _members;

	private EnumMember<TEnum>[]? _membersArray;

	public int Count { get; }

	public EnumMember<TEnum> this[int index] => (_membersArray ?? (_membersArray = ArrayHelper.ToArray(this, Count)))[index];

	public MembersContainer(IEnumerable<EnumMemberInternal> members, int count, bool cached)
	{
		_members = members;
		Count = count;
		if (cached)
		{
			_membersArray = ArrayHelper.ToArray(this, count);
		}
	}

	public IEnumerator<EnumMember<TEnum>> GetEnumerator()
	{
		if (_membersArray == null)
		{
			return Enumerate();
		}
		return ((IEnumerable<EnumMember<TEnum>>)_membersArray).GetEnumerator();
	}

	private IEnumerator<EnumMember<TEnum>> Enumerate()
	{
		foreach (EnumMemberInternal member in _members)
		{
			yield return UnsafeUtility.As<EnumMember<TEnum>>(member.EnumMember);
		}
	}

	IEnumerator IEnumerable.GetEnumerator()
	{
		return GetEnumerator();
	}
}
