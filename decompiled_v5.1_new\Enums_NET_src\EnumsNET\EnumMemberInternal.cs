using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using System.Threading;
using EnumsNET.Numerics;

namespace EnumsNET;

internal abstract class EnumMemberInternal : IComparable<EnumMemberInternal>
{
	public readonly string Name;

	public readonly AttributeCollection Attributes;

	private EnumMember? _enumMember;

	public EnumMember EnumMember
	{
		get
		{
			EnumMember enumMember = _enumMember;
			return enumMember ?? Interlocked.CompareExchange(ref _enumMember, enumMember = CreateEnumMember(), null) ?? enumMember;
		}
	}

	private protected abstract EnumMember CreateEnumMember();

	protected EnumMemberInternal(string name, AttributeCollection attributes)
	{
		Name = name;
		Attributes = attributes;
	}

	public abstract void GetValue(ref byte result);

	public abstract object GetValue();

	public abstract string AsString(string format);

	public abstract string? AsString(EnumFormat format);

	public abstract string? AsString(ValueCollection<EnumFormat> formats);

	public abstract bool TryFormat(Span<char> destination, out int charsWritten, ReadOnlySpan<char> format);

	public abstract bool TryFormat(Span<char> destination, out int charsWritten, ValueCollection<EnumFormat> formats);

	public abstract byte ToByte();

	public abstract short ToInt16();

	public abstract int ToInt32();

	public abstract long ToInt64();

	public abstract sbyte ToSByte();

	public abstract ushort ToUInt16();

	public abstract uint ToUInt32();

	public abstract ulong ToUInt64();

	public abstract bool IsValidFlagCombination();

	public abstract bool HasAnyFlags();

	public abstract bool HasAllFlags();

	public abstract int GetFlagCount();

	public abstract IValuesContainer GetFlags();

	public abstract IReadOnlyList<EnumMember> GetFlagMembers();

	public abstract int CompareTo(EnumMemberInternal? other);

	public abstract TypeCode GetTypeCode();

	public abstract bool ToBoolean(IFormatProvider? provider);

	public abstract char ToChar(IFormatProvider? provider);

	public abstract float ToSingle(IFormatProvider? provider);

	public abstract double ToDouble(IFormatProvider? provider);

	public abstract decimal ToDecimal(IFormatProvider? provider);

	public abstract DateTime ToDateTime(IFormatProvider? provider);

	public abstract object ToType(Type conversionType, IFormatProvider? provider);
}
internal sealed class EnumMemberInternal<TUnderlying, TUnderlyingOperations> : EnumMemberInternal, IEquatable<EnumMemberInternal<TUnderlying, TUnderlyingOperations>> where TUnderlying : struct, IComparable<TUnderlying>, IEquatable<TUnderlying>, IConvertible where TUnderlyingOperations : struct, IUnderlyingOperations<TUnderlying>
{
	internal EnumCache<TUnderlying, TUnderlyingOperations> EnumCache;

	public readonly TUnderlying Value;

	internal EnumMemberInternal<TUnderlying, TUnderlyingOperations>? Next;

	private protected override EnumMember CreateEnumMember()
	{
		return EnumCache.EnumBridge.CreateEnumMember(this);
	}

	public EnumMemberInternal(TUnderlying value, string name, AttributeCollection attributes)
		: base(name, attributes)
	{
		Value = value;
	}

	public override void GetValue(ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = Value;
	}

	public override object GetValue()
	{
		return Value;
	}

	public override string AsString(string format)
	{
		return EnumCache.AsStringInternal(Value, this, format);
	}

	public override string? AsString(EnumFormat format)
	{
		bool isInitialized = true;
		EnumMemberInternal<TUnderlying, TUnderlyingOperations> member = this;
		return EnumCache.AsStringInternal(Value, ref isInitialized, ref member, format);
	}

	public override string? AsString(ValueCollection<EnumFormat> formats)
	{
		return EnumCache.AsStringInternal(Value, this, formats);
	}

	public override bool TryFormat(Span<char> destination, out int charsWritten, ReadOnlySpan<char> format)
	{
		return EnumCache.TryFormatInternal(Value, this, destination, out charsWritten, format);
	}

	public override bool TryFormat(Span<char> destination, out int charsWritten, ValueCollection<EnumFormat> formats)
	{
		return EnumCache.TryFormatInternal(Value, this, destination, out charsWritten, formats);
	}

	public override int GetHashCode()
	{
		return Value.GetHashCode();
	}

	public override bool IsValidFlagCombination()
	{
		return EnumCache.IsValidFlagCombination(Value);
	}

	public override int GetFlagCount()
	{
		return EnumCache.GetFlagCount(Value);
	}

	public override bool HasAnyFlags()
	{
		return EnumCache.HasAnyFlags(Value);
	}

	public override bool HasAllFlags()
	{
		return EnumCache.HasAllFlags(Value);
	}

	public override IValuesContainer GetFlags()
	{
		return EnumCache.GetFlags(Value);
	}

	public override IReadOnlyList<EnumMember> GetFlagMembers()
	{
		return EnumCache.GetFlagMembers(Value);
	}

	public override int CompareTo(EnumMemberInternal? other)
	{
		if (other == null)
		{
			return 1;
		}
		return Value.CompareTo(UnsafeUtility.As<EnumMemberInternal<TUnderlying, TUnderlyingOperations>>(other).Value);
	}

	public bool Equals(EnumMemberInternal<TUnderlying, TUnderlyingOperations>? other)
	{
		if (other != null)
		{
			return Value.Equals(other.Value);
		}
		return false;
	}

	public override sbyte ToSByte()
	{
		return Value.ToSByte(null);
	}

	public override byte ToByte()
	{
		return Value.ToByte(null);
	}

	public override short ToInt16()
	{
		return Value.ToInt16(null);
	}

	public override ushort ToUInt16()
	{
		return Value.ToUInt16(null);
	}

	public override int ToInt32()
	{
		return Value.ToInt32(null);
	}

	public override uint ToUInt32()
	{
		return Value.ToUInt32(null);
	}

	public override long ToInt64()
	{
		return Value.ToInt64(null);
	}

	public override ulong ToUInt64()
	{
		return Value.ToUInt64(null);
	}

	public override TypeCode GetTypeCode()
	{
		return Value.GetTypeCode();
	}

	public override bool ToBoolean(IFormatProvider? provider)
	{
		return Value.ToBoolean(provider);
	}

	public override char ToChar(IFormatProvider? provider)
	{
		return Value.ToChar(provider);
	}

	public override float ToSingle(IFormatProvider? provider)
	{
		return Value.ToSingle(provider);
	}

	public override double ToDouble(IFormatProvider? provider)
	{
		return Value.ToDouble(provider);
	}

	public override decimal ToDecimal(IFormatProvider? provider)
	{
		return Value.ToDecimal(provider);
	}

	public override DateTime ToDateTime(IFormatProvider? provider)
	{
		return Value.ToDateTime(provider);
	}

	public override object ToType(Type conversionType, IFormatProvider? provider)
	{
		return Value.ToType(conversionType, provider);
	}
}
