using System;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.AuthScheme.PoP;

internal static class PoPProviderFactory
{
	private static InMemoryCryptoProvider s_currentProvider;

	private static DateTime s_providerExpiration;

	private static object s_lock = new object();

	public static TimeSpan KeyRotationInterval { get; } = TimeSpan.FromHours(8.0);

	internal static ITimeService TimeService { get; set; } = new TimeService();

	public static InMemoryCryptoProvider GetOrCreateProvider()
	{
		lock (s_lock)
		{
			DateTime utcNow = TimeService.GetUtcNow();
			if (s_currentProvider != null && s_providerExpiration > utcNow)
			{
				return s_currentProvider;
			}
			s_currentProvider = new InMemoryCryptoProvider();
			s_providerExpiration = TimeService.GetUtcNow() + KeyRotationInterval;
			return s_currentProvider;
		}
	}

	public static void Reset()
	{
		s_currentProvider = null;
		TimeService = new TimeService();
	}
}
