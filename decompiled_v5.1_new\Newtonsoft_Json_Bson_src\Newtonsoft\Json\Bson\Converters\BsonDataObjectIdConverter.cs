using System;
using System.Globalization;
using Newtonsoft.Json.Bson.Utilities;

namespace Newtonsoft.Json.Bson.Converters;

public class BsonDataObjectIdConverter : JsonConverter
{
	public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
	{
		BsonDataObjectId bsonDataObjectId = (BsonDataObjectId)value;
		if (writer is BsonDataWriter bsonDataWriter)
		{
			bsonDataWriter.WriteObjectId(bsonDataObjectId.Value);
		}
		else
		{
			writer.WriteValue(bsonDataObjectId.Value);
		}
	}

	public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
	{
		if (reader.TokenType != JsonToken.Bytes)
		{
			throw new JsonSerializationException("Expected Bytes but got {0}.".FormatWith(CultureInfo.InvariantCulture, reader.TokenType));
		}
		return new BsonDataObjectId((byte[])reader.Value);
	}

	public override bool CanConvert(Type objectType)
	{
		return objectType == typeof(BsonDataObjectId);
	}
}
