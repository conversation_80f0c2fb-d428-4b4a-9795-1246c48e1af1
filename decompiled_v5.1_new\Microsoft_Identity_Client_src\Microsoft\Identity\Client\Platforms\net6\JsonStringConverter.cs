using System;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Microsoft.Identity.Client.Platforms.net6;

internal class JsonStringConverter : JsonConverter<string>
{
	public override string Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
	{
		if (reader.TokenType == JsonTokenType.Number)
		{
			if (reader.TryGetInt64(out var value))
			{
				return value.ToString();
			}
			if (reader.TryGetInt32(out var value2))
			{
				return value2.ToString();
			}
			if (reader.TryGetDouble(out var value3))
			{
				return value3.ToString();
			}
		}
		else if (reader.TokenType == JsonTokenType.String)
		{
			return reader.GetString();
		}
		throw new JsonException();
	}

	public override void Write(Utf8JsonWriter writer, string value, JsonSerializerOptions options)
	{
		writer.WriteStringValue(value);
	}
}
