#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复项目文件目标框架脚本
将所有项目统一为 net8.0 目标框架
"""

import os
import glob
import re

def fix_target_framework(file_path):
    """修复单个项目文件的目标框架"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换目标框架
        content = re.sub(r'<TargetFramework>netcoreapp8\.0</TargetFramework>', 
                        '<TargetFramework>net8.0</TargetFramework>', content)
        content = re.sub(r'<TargetFramework>netcoreapp6\.0</TargetFramework>', 
                        '<TargetFramework>net8.0</TargetFramework>', content)
        content = re.sub(r'<TargetFramework>net6\.0</TargetFramework>', 
                        '<TargetFramework>net8.0</TargetFramework>', content)
        
        # 如果有修改，保存文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        return False
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False

def main():
    print("开始批量修复项目文件目标框架...")
    
    # 查找所有 .csproj 文件
    pattern = "**/*.csproj"
    project_files = glob.glob(pattern, recursive=True)
    
    # 过滤掉 _Fixed 和 _Updated 文件
    project_files = [f for f in project_files if '_Fixed' not in f and '_Updated' not in f]
    
    total_files = len(project_files)
    modified_files = 0
    
    print(f"找到 {total_files} 个项目文件")
    
    for i, file_path in enumerate(project_files, 1):
        file_name = os.path.basename(file_path)
        print(f"[{i}/{total_files}] 处理: {file_name}")
        
        if fix_target_framework(file_path):
            print(f"  ✓ 已修复")
            modified_files += 1
        else:
            print(f"  - 无需修改")
    
    print(f"\n修复完成!")
    print(f"总计处理: {total_files} 个文件")
    print(f"成功修改: {modified_files} 个文件")
    
    # 验证结果
    print("\n验证修复结果...")
    net8_count = 0
    other_count = 0
    
    for file_path in project_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if '<TargetFramework>net8.0</TargetFramework>' in content:
                net8_count += 1
            else:
                other_count += 1
                print(f"警告: {os.path.basename(file_path)} 仍使用其他目标框架")
        except:
            pass
    
    print(f"验证结果:")
    print(f"  net8.0: {net8_count} 个项目")
    print(f"  其他框架: {other_count} 个项目")
    
    if other_count == 0:
        print("\n🎉 所有项目已成功统一为 net8.0 目标框架!")
    else:
        print("\n⚠️  仍有项目需要手动检查")

if __name__ == "__main__":
    main()