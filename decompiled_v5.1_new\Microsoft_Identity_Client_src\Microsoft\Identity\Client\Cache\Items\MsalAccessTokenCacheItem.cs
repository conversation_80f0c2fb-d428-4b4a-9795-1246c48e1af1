using System;
using System.Collections.Generic;
using System.Text.Json.Nodes;
using Microsoft.Identity.Client.AuthScheme;
using Microsoft.Identity.Client.Cache.Keys;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Cache.Items;

internal class MsalAccessTokenCacheItem : MsalCredentialCacheItemBase
{
	private string[] _extraKeyParts;

	private string _credentialDescriptor;

	private Lazy<IiOSKey> iOSCacheKeyLazy;

	internal string TenantId { get; private set; }

	internal string OboCacheKey { get; set; }

	internal string KeyId { get; }

	internal string TokenType { get; }

	internal HashSet<string> ScopeSet { get; }

	internal string ScopeString { get; }

	internal DateTimeOffset ExpiresOn { get; private set; }

	internal DateTimeOffset ExtendedExpiresOn { get; private set; }

	internal DateTimeOffset? RefreshOn { get; private set; }

	internal DateTimeOffset CachedAt { get; private set; }

	public bool IsExtendedLifeTimeToken { get; set; }

	internal string CacheKey { get; private set; }

	public IiOSKey iOSCacheKey => iOSCacheKeyLazy.Value;

	internal MsalAccessTokenCacheItem(string preferredCacheEnv, string clientId, MsalTokenResponse response, string tenantId, string homeAccountId, string keyId = null, string oboCacheKey = null)
		: this(ScopeHelper.OrderScopesAlphabetically(response.Scope), DateTimeOffset.UtcNow, DateTimeHelpers.DateTimeOffsetFromDuration(response.ExpiresIn), DateTimeHelpers.DateTimeOffsetFromDuration(response.ExtendedExpiresIn), DateTimeHelpers.DateTimeOffsetFromDuration(response.RefreshIn), tenantId, keyId, response.TokenType)
	{
		base.Environment = preferredCacheEnv;
		base.ClientId = clientId;
		base.Secret = response.AccessToken;
		base.RawClientInfo = response.ClientInfo;
		base.HomeAccountId = homeAccountId;
		OboCacheKey = oboCacheKey;
		InitCacheKey();
	}

	internal MsalAccessTokenCacheItem(string preferredCacheEnv, string clientId, string scopes, string tenantId, string secret, DateTimeOffset cachedAt, DateTimeOffset expiresOn, DateTimeOffset extendedExpiresOn, string rawClientInfo, string homeAccountId, string keyId = null, DateTimeOffset? refreshOn = null, string tokenType = "Bearer", string oboCacheKey = null)
		: this(scopes, cachedAt, expiresOn, extendedExpiresOn, refreshOn, tenantId, keyId, tokenType)
	{
		base.Environment = preferredCacheEnv;
		base.ClientId = clientId;
		base.Secret = secret;
		base.RawClientInfo = rawClientInfo;
		base.HomeAccountId = homeAccountId;
		OboCacheKey = oboCacheKey;
		InitCacheKey();
	}

	private MsalAccessTokenCacheItem(string scopes, DateTimeOffset cachedAt, DateTimeOffset expiresOn, DateTimeOffset extendedExpiresOn, DateTimeOffset? refreshOn, string tenantId, string keyId, string tokenType)
	{
		base.CredentialType = "AccessToken";
		ScopeString = scopes;
		ScopeSet = ScopeHelper.ConvertStringToScopeSet(ScopeString);
		ExpiresOn = expiresOn;
		ExtendedExpiresOn = extendedExpiresOn;
		RefreshOn = refreshOn;
		TenantId = tenantId ?? "";
		KeyId = keyId;
		TokenType = tokenType;
		CachedAt = cachedAt;
	}

	internal MsalAccessTokenCacheItem WithExpiresOn(DateTimeOffset expiresOn)
	{
		return new MsalAccessTokenCacheItem(base.Environment, base.ClientId, ScopeString, TenantId, base.Secret, CachedAt, expiresOn, ExtendedExpiresOn, base.RawClientInfo, base.HomeAccountId, KeyId, RefreshOn, TokenType, OboCacheKey);
	}

	internal void InitCacheKey()
	{
		_extraKeyParts = null;
		_credentialDescriptor = "AccessToken";
		if (AuthSchemeHelper.StoreTokenTypeInCacheKey(TokenType))
		{
			_extraKeyParts = new string[1] { TokenType };
			_credentialDescriptor = "AccessToken_With_AuthScheme";
		}
		CacheKey = MsalCacheKeys.GetCredentialKey(base.HomeAccountId, base.Environment, _credentialDescriptor, base.ClientId, TenantId, ScopeString, _extraKeyParts);
		iOSCacheKeyLazy = new Lazy<IiOSKey>(InitiOSKey);
	}

	internal string ToLogString(bool piiEnabled = false)
	{
		return MsalCacheKeys.GetCredentialKey(piiEnabled ? base.HomeAccountId : base.HomeAccountId?.GetHashCode().ToString(), base.Environment, _credentialDescriptor, base.ClientId, TenantId, ScopeString, _extraKeyParts);
	}

	private IiOSKey InitiOSKey()
	{
		string iOSAccount = MsalCacheKeys.GetiOSAccountKey(base.HomeAccountId, base.Environment);
		string iOSService = MsalCacheKeys.GetiOSServiceKey(_credentialDescriptor, base.ClientId, TenantId, ScopeString, _extraKeyParts);
		string iOSGeneric = MsalCacheKeys.GetiOSGenericKey(_credentialDescriptor, base.ClientId, TenantId);
		int iOSType = 2001;
		return new IosKey(iOSAccount, iOSService, iOSGeneric, iOSType);
	}

	internal static MsalAccessTokenCacheItem FromJsonString(string json)
	{
		if (string.IsNullOrWhiteSpace(json))
		{
			return null;
		}
		return FromJObject(JsonHelper.ParseIntoJsonObject(json));
	}

	internal static MsalAccessTokenCacheItem FromJObject(JsonObject j)
	{
		long num = JsonHelper.ExtractParsedIntOrZero(j, "cached_at");
		long num2 = JsonHelper.ExtractParsedIntOrZero(j, "expires_on");
		long num3 = JsonHelper.ExtractParsedIntOrZero(j, "refresh_on");
		long num4 = JsonHelper.ExtractParsedIntOrZero(j, "ext_expires_on");
		long num5 = JsonHelper.ExtractParsedIntOrZero(j, "extended_expires_on");
		if (num5 == 0L && num4 > 0)
		{
			num5 = num4;
		}
		string tenantId = JsonHelper.ExtractExistingOrEmptyString(j, "realm");
		string oboCacheKey = JsonHelper.ExtractExistingOrDefault<string>(j, "user_assertion_hash");
		string keyId = JsonHelper.ExtractExistingOrDefault<string>(j, "kid");
		string tokenType = JsonHelper.ExtractExistingOrDefault<string>(j, "token_type") ?? "Bearer";
		MsalAccessTokenCacheItem obj = new MsalAccessTokenCacheItem(JsonHelper.ExtractExistingOrEmptyString(j, "target"), expiresOn: DateTimeHelpers.UnixTimestampToDateTime(num2), extendedExpiresOn: DateTimeHelpers.UnixTimestampToDateTime(num5), refreshOn: DateTimeHelpers.UnixTimestampToDateTimeOrNull(num3), cachedAt: DateTimeHelpers.UnixTimestampToDateTime(num), tenantId: tenantId, keyId: keyId, tokenType: tokenType);
		obj.OboCacheKey = oboCacheKey;
		obj.PopulateFieldsFromJObject(j);
		obj.InitCacheKey();
		return obj;
	}

	internal override JsonObject ToJObject()
	{
		JsonObject jsonObject = base.ToJObject();
		string text = DateTimeHelpers.DateTimeToUnixTimestamp(ExtendedExpiresOn);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "realm", TenantId);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "target", ScopeString);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "user_assertion_hash", OboCacheKey);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "cached_at", DateTimeHelpers.DateTimeToUnixTimestamp(CachedAt));
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "expires_on", DateTimeHelpers.DateTimeToUnixTimestamp(ExpiresOn));
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "extended_expires_on", text);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "kid", KeyId);
		MsalItemWithAdditionalFields.SetItemIfValueNotNullOrDefault(jsonObject, "token_type", TokenType, "Bearer");
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "refresh_on", RefreshOn.HasValue ? DateTimeHelpers.DateTimeToUnixTimestamp(RefreshOn.Value) : null);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "ext_expires_on", text);
		return jsonObject;
	}

	internal string ToJsonString()
	{
		return ToJObject().ToString();
	}

	internal MsalIdTokenCacheItem GetIdTokenItem()
	{
		return new MsalIdTokenCacheItem(base.Environment, base.ClientId, base.Secret, base.RawClientInfo, base.HomeAccountId, TenantId);
	}

	internal bool IsExpiredWithBuffer()
	{
		return ExpiresOn < DateTime.UtcNow + Constants.AccessTokenExpirationBuffer;
	}
}
