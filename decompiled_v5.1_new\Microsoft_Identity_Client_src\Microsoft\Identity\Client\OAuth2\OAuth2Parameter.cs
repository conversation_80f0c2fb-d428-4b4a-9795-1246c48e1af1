namespace Microsoft.Identity.Client.OAuth2;

internal static class OAuth2Parameter
{
	public const string ResponseType = "response_type";

	public const string GrantType = "grant_type";

	public const string ClientId = "client_id";

	public const string ClientSecret = "client_secret";

	public const string ClientAssertion = "client_assertion";

	public const string ClientAssertionType = "client_assertion_type";

	public const string RefreshToken = "refresh_token";

	public const string RedirectUri = "redirect_uri";

	public const string Resource = "resource";

	public const string Code = "code";

	public const string DeviceCode = "device_code";

	public const string Scope = "scope";

	public const string Assertion = "assertion";

	public const string RequestedTokenUse = "requested_token_use";

	public const string Username = "username";

	public const string Password = "password";

	public const string LoginHint = "login_hint";

	public const string CorrelationId = "client-request-id";

	public const string State = "state";

	public const string CodeChallengeMethod = "code_challenge_method";

	public const string CodeChallenge = "code_challenge";

	public const string PkceCodeVerifier = "code_verifier";

	public const string LoginReq = "login_req";

	public const string DomainReq = "domain_req";

	public const string Prompt = "prompt";

	public const string ClientInfo = "client_info";

	public const string Claims = "claims";

	public const string TokenType = "token_type";

	public const string RequestConfirmation = "req_cnf";

	public const string SpaCode = "return_spa_code";
}
