using System.Threading;

namespace Microsoft.Identity.Client;

public class Metrics
{
	private static long _totalAccessTokensFromIdP;

	private static long _totalAccessTokensFromCache;

	private static long _totalAccessTokensFromBroker;

	private static long _totalDurationInMs;

	public static long TotalAccessTokensFromIdP
	{
		get
		{
			return _totalAccessTokensFromIdP;
		}
		internal set
		{
			_totalAccessTokensFromIdP = value;
		}
	}

	public static long TotalAccessTokensFromCache
	{
		get
		{
			return _totalAccessTokensFromCache;
		}
		internal set
		{
			_totalAccessTokensFromCache = value;
		}
	}

	public static long TotalAccessTokensFromBroker
	{
		get
		{
			return _totalAccessTokensFromBroker;
		}
		internal set
		{
			_totalAccessTokensFromBroker = value;
		}
	}

	public static long TotalDurationInMs
	{
		get
		{
			return _totalDurationInMs;
		}
		internal set
		{
			_totalDurationInMs = value;
		}
	}

	private Metrics()
	{
	}

	internal static void IncrementTotalAccessTokensFromIdP()
	{
		Interlocked.Increment(ref _totalAccessTokensFromIdP);
	}

	internal static void IncrementTotalAccessTokensFromCache()
	{
		Interlocked.Increment(ref _totalAccessTokensFromCache);
	}

	internal static void IncrementTotalAccessTokensFromBroker()
	{
		Interlocked.Increment(ref _totalAccessTokensFromBroker);
	}

	internal static void IncrementTotalDurationInMs(long requestDurationInMs)
	{
		Interlocked.Add(ref _totalDurationInMs, requestDurationInMs);
	}
}
