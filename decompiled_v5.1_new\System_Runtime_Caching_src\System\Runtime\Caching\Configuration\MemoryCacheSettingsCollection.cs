using System.Configuration;
using System.Runtime.Versioning;

namespace System.Runtime.Caching.Configuration;

[UnsupportedOSPlatform("browser")]
[ConfigurationCollection(typeof(MemoryCacheElement), CollectionType = ConfigurationElementCollectionType.AddRemoveClearMap)]
internal sealed class MemoryCacheSettingsCollection : ConfigurationElementCollection
{
	private static readonly ConfigurationPropertyCollection s_properties = new ConfigurationPropertyCollection();

	protected override ConfigurationPropertyCollection Properties => s_properties;

	public MemoryCacheElement this[int index]
	{
		get
		{
			return (MemoryCacheElement)BaseGet(index);
		}
		set
		{
			if (BaseGet(index) != null)
			{
				BaseRemoveAt(index);
			}
			base.BaseAdd(index, value);
		}
	}

	public new MemoryCacheElement this[string key] => (MemoryCacheElement)BaseGet(key);

	public override ConfigurationElementCollectionType CollectionType => ConfigurationElementCollectionType.AddRemoveClearMapAlternate;

	public int IndexOf(MemoryCacheElement cache)
	{
		return BaseIndexOf(cache);
	}

	public void Add(MemoryCacheElement cache)
	{
		BaseAdd(cache);
	}

	public void Remove(MemoryCacheElement cache)
	{
		BaseRemove(cache.Name);
	}

	public void RemoveAt(int index)
	{
		BaseRemoveAt(index);
	}

	public void Clear()
	{
		BaseClear();
	}

	protected override ConfigurationElement CreateNewElement()
	{
		return new MemoryCacheElement();
	}

	protected override ConfigurationElement CreateNewElement(string elementName)
	{
		return new MemoryCacheElement(elementName);
	}

	protected override object GetElementKey(ConfigurationElement element)
	{
		return ((MemoryCacheElement)element).Name;
	}
}
