using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Advanced;
using Microsoft.Identity.Client.ApiConfig.Executors;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

public sealed class AcquireTokenByAuthorizationCodeParameterBuilder : AbstractConfidentialClientAcquireTokenParameterBuilder<AcquireTokenByAuthorizationCodeParameterBuilder>
{
	private AcquireTokenByAuthorizationCodeParameters Parameters { get; } = new AcquireTokenByAuthorizationCodeParameters();

	internal AcquireTokenByAuthorizationCodeParameterBuilder(IConfidentialClientApplicationExecutor confidentialClientApplicationExecutor)
		: base(confidentialClientApplicationExecutor)
	{
		ApplicationBase.GuardMobileFrameworks();
	}

	internal static AcquireTokenByAuthorizationCodeParameterBuilder Create(IConfidentialClientApplicationExecutor confidentialClientApplicationExecutor, IEnumerable<string> scopes, string authorizationCode)
	{
		ApplicationBase.GuardMobileFrameworks();
		return new AcquireTokenByAuthorizationCodeParameterBuilder(confidentialClientApplicationExecutor).WithScopes(scopes).WithAuthorizationCode(authorizationCode);
	}

	private AcquireTokenByAuthorizationCodeParameterBuilder WithAuthorizationCode(string authorizationCode)
	{
		Parameters.AuthorizationCode = authorizationCode;
		return this;
	}

	internal override ApiEvent.ApiIds CalculateApiEventId()
	{
		return ApiEvent.ApiIds.AcquireTokenByAuthorizationCode;
	}

	protected override void Validate()
	{
		base.Validate();
		if (string.IsNullOrWhiteSpace(Parameters.AuthorizationCode))
		{
			throw new ArgumentException("AuthorizationCode can not be null or whitespace", "AuthorizationCode");
		}
		if (!Parameters.SendX5C.HasValue)
		{
			Parameters.SendX5C = base.ServiceBundle.Config.SendX5C;
		}
	}

	internal override Task<AuthenticationResult> ExecuteInternalAsync(CancellationToken cancellationToken)
	{
		return base.ConfidentialClientApplicationExecutor.ExecuteAsync(base.CommonParameters, Parameters, cancellationToken);
	}

	public AcquireTokenByAuthorizationCodeParameterBuilder WithSendX5C(bool withSendX5C)
	{
		Parameters.SendX5C = withSendX5C;
		return this;
	}

	public AcquireTokenByAuthorizationCodeParameterBuilder WithPkceCodeVerifier(string pkceCodeVerifier)
	{
		Parameters.PkceCodeVerifier = pkceCodeVerifier;
		return this;
	}

	public AcquireTokenByAuthorizationCodeParameterBuilder WithCcsRoutingHint(string userObjectIdentifier, string tenantIdentifier)
	{
		if (string.IsNullOrEmpty(userObjectIdentifier) || string.IsNullOrEmpty(tenantIdentifier))
		{
			return this;
		}
		Dictionary<string, string> extraHttpHeaders = new Dictionary<string, string> { 
		{
			"x-anchormailbox",
			CoreHelpers.GetCcsClientInfoHint(userObjectIdentifier, tenantIdentifier)
		} };
		this.WithExtraHttpHeaders(extraHttpHeaders);
		return this;
	}

	public AcquireTokenByAuthorizationCodeParameterBuilder WithCcsRoutingHint(string userName)
	{
		if (string.IsNullOrEmpty(userName))
		{
			return this;
		}
		Dictionary<string, string> extraHttpHeaders = new Dictionary<string, string> { 
		{
			"x-anchormailbox",
			CoreHelpers.GetCcsUpnHint(userName)
		} };
		this.WithExtraHttpHeaders(extraHttpHeaders);
		return this;
	}

	public AcquireTokenByAuthorizationCodeParameterBuilder WithSpaAuthorizationCode(bool requestSpaAuthorizationCode = true)
	{
		Parameters.SpaCode = requestSpaAuthorizationCode;
		return this;
	}
}
