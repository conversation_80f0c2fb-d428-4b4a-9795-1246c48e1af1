using System;
using System.ComponentModel;
using Microsoft.Identity.Client.ManagedIdentity;

namespace Microsoft.Identity.Client;

[Obsolete("MsalManagedIdentityException is deprecated and will be removed in a future release. Catch MsalServiceException instead.", true)]
[EditorBrowsable(EditorBrowsableState.Never)]
public class MsalManagedIdentityException : MsalServiceException
{
	public new ManagedIdentitySource ManagedIdentitySource { get; }

	public MsalManagedIdentityException(string errorCode, string errorMessage, ManagedIdentitySource source)
		: this(errorCode, errorMessage, null, source)
	{
	}

	public MsalManagedIdentityException(string errorCode, string errorMessage, ManagedIdentitySource source, int statusCode)
		: this(errorCode, errorMessage, null, source, statusCode)
	{
	}

	public MsalManagedIdentityException(string errorCode, string errorMessage, Exception innerException, ManagedIdentitySource source, int statusCode)
		: this(errorCode, errorMessage, innerException, source)
	{
		base.StatusCode = statusCode;
	}

	public MsalManagedIdentityException(string errorCode, string errorMessage, Exception innerException, ManagedIdentitySource source)
		: base(errorCode, errorMessage, innerException)
	{
		ManagedIdentitySource = source;
	}

	protected override void UpdateIsRetryable()
	{
		switch (base.StatusCode)
		{
		case 404:
		case 408:
		case 429:
		case 500:
		case 503:
		case 504:
			base.IsRetryable = true;
			break;
		default:
			base.IsRetryable = false;
			break;
		}
	}
}
