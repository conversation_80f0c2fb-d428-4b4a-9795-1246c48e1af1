using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Executors;

namespace Microsoft.Identity.Client;

public abstract class AbstractManagedIdentityAcquireTokenParameterBuilder<T> : BaseAbstractAcquireTokenParameterBuilder<T> where T : BaseAbstractAcquireTokenParameterBuilder<T>
{
	internal IManagedIdentityApplicationExecutor ManagedIdentityApplicationExecutor { get; }

	protected AbstractManagedIdentityAcquireTokenParameterBuilder()
	{
	}

	internal AbstractManagedIdentityAcquireTokenParameterBuilder(IManagedIdentityApplicationExecutor managedIdentityApplicationExecutor)
		: base(managedIdentityApplicationExecutor.ServiceBundle)
	{
		ApplicationBase.GuardMobileFrameworks();
		ManagedIdentityApplicationExecutor = managedIdentityApplicationExecutor;
	}

	internal abstract Task<AuthenticationResult> ExecuteInternalAsync(CancellationToken cancellationToken);

	public override Task<AuthenticationResult> ExecuteAsync(CancellationToken cancellationToken)
	{
		ApplicationBase.GuardMobileFrameworks();
		ValidateAndCalculateApiId();
		return ExecuteInternalAsync(cancellationToken);
	}
}
