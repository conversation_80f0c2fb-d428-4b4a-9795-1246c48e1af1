using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Instance.Discovery;
using Microsoft.Identity.Client.Internal.Requests;
using Microsoft.Identity.Client.OAuth2;

namespace Microsoft.Identity.Client.Internal.Broker;

internal interface IBroker
{
	bool IsPopSupported { get; }

	bool IsBrokerInstalledAndInvokable(AuthorityType authorityType);

	Task<MsalTokenResponse> AcquireTokenInteractiveAsync(AuthenticationRequestParameters authenticationRequestParameters, AcquireTokenInteractiveParameters acquireTokenInteractiveParameters);

	Task<MsalTokenResponse> AcquireTokenSilentAsync(AuthenticationRequestParameters authenticationRequestParameters, AcquireTokenSilentParameters acquireTokenSilentParameters);

	Task<MsalTokenResponse> AcquireTokenSilentDefaultUserAsync(AuthenticationRequestParameters authenticationRequestParameters, AcquireTokenSilentParameters acquireTokenSilentParameters);

	Task<MsalTokenResponse> AcquireTokenByUsernamePasswordAsync(AuthenticationRequestParameters authenticationRequestParameters, AcquireTokenByUsernamePasswordParameters acquireTokenByUsernamePasswordParameters);

	IReadOnlyDictionary<string, string> GetSsoPolicyHeaders();

	void HandleInstallUrl(string appLink);

	Task<IReadOnlyList<IAccount>> GetAccountsAsync(string clientId, string redirectUri, AuthorityInfo authorityInfo, ICacheSessionManager cacheSessionManager, IInstanceDiscoveryManager instanceDiscoveryManager);

	Task RemoveAccountAsync(ApplicationConfiguration appConfig, IAccount account);
}
