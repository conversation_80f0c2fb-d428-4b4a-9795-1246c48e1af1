using System;
using System.Globalization;
using System.Text;
using Microsoft.Identity.Client.Platforms.Features.DesktopOs.Kerberos;
using Microsoft.Identity.Client.PlatformsCommon.Shared;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Kerberos;

public static class KerberosSupplementalTicketManager
{
	private const int DefaultLogonId = 0;

	private const string KerberosClaimType = "xms_as_rep";

	private const string IdTokenAsRepTemplate = "{{\"id_token\": {{ \"xms_as_rep\":{{\"essential\":\"false\",\"value\":\"{0}\"}} }} }}";

	private const string AccessTokenAsRepTemplate = "{{\"access_token\": {{ \"xms_as_rep\":{{\"essential\":\"false\",\"value\":\"{0}\"}} }} }}";

	public static KerberosSupplementalTicket FromIdToken(string idToken)
	{
		if (string.IsNullOrEmpty(idToken) || idToken.Length < 128)
		{
			return null;
		}
		string[] array = idToken.Split('.');
		if (array.Length != 3)
		{
			return null;
		}
		byte[] bytes = Base64UrlHelpers.DecodeBytes(array[1]);
		string text = Encoding.UTF8.GetString(bytes);
		if (string.IsNullOrEmpty(text))
		{
			return null;
		}
		if (!JsonHelper.TryGetValue(JsonHelper.ParseIntoJsonObject(text), "xms_as_rep", out var value))
		{
			return null;
		}
		return JsonHelper.DeserializeFromJson<KerberosSupplementalTicket>(JsonHelper.GetValue<string>(value));
	}

	public static void SaveToWindowsTicketCache(KerberosSupplementalTicket ticket)
	{
		SaveToWindowsTicketCache(ticket, 0L);
	}

	public static void SaveToWindowsTicketCache(KerberosSupplementalTicket ticket, long logonId)
	{
		if (!DesktopOsHelper.IsWindows())
		{
			throw new PlatformNotSupportedException("Ticket Cache interface is not supported on this OS. It is supported on Windows only.");
		}
		if (ticket == null || string.IsNullOrEmpty(ticket.KerberosMessageBuffer))
		{
			throw new ArgumentException("Kerberos Ticket information is not valid");
		}
		using TicketCacheWriter ticketCacheWriter = TicketCacheWriter.Connect();
		byte[] ticketBytes = Convert.FromBase64String(ticket.KerberosMessageBuffer);
		ticketCacheWriter.ImportCredential(ticketBytes, logonId);
	}

	public static byte[] GetKerberosTicketFromWindowsTicketCache(string servicePrincipalName)
	{
		return GetKerberosTicketFromWindowsTicketCache(servicePrincipalName, 0L);
	}

	public static byte[] GetKerberosTicketFromWindowsTicketCache(string servicePrincipalName, long logonId)
	{
		if (!DesktopOsHelper.IsWindows())
		{
			throw new PlatformNotSupportedException("Ticket Cache interface is not supported on this OS. It is supported on Windows only.");
		}
		using TicketCacheReader ticketCacheReader = new TicketCacheReader(servicePrincipalName, logonId);
		return ticketCacheReader.RequestToken();
	}

	public static byte[] GetKrbCred(KerberosSupplementalTicket ticket)
	{
		if (!string.IsNullOrEmpty(ticket.KerberosMessageBuffer))
		{
			return Convert.FromBase64String(ticket.KerberosMessageBuffer);
		}
		return null;
	}

	internal static string GetKerberosTicketClaim(string servicePrincipalName, KerberosTicketContainer ticketContainer)
	{
		if (string.IsNullOrEmpty(servicePrincipalName))
		{
			return string.Empty;
		}
		if (ticketContainer == KerberosTicketContainer.IdToken)
		{
			return string.Format(CultureInfo.InvariantCulture, "{{\"id_token\": {{ \"xms_as_rep\":{{\"essential\":\"false\",\"value\":\"{0}\"}} }} }}", servicePrincipalName);
		}
		return string.Format(CultureInfo.InvariantCulture, "{{\"access_token\": {{ \"xms_as_rep\":{{\"essential\":\"false\",\"value\":\"{0}\"}} }} }}", servicePrincipalName);
	}
}
