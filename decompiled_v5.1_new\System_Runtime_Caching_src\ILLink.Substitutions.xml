﻿<linker>
  <assembly fullname="System.Runtime.Caching" feature="System.Resources.UseSystemResourceKeys" featurevalue="true">
    <!-- System.Resources.UseSystemResourceKeys removes resource strings and instead uses the resource key as the exception message -->
    <resource name="FxResources.System.Runtime.Caching.SR.resources" action="remove" />
    <type fullname="System.SR">
      <method signature="System.Boolean UsingResourceKeys()" body="stub" value="true" />
    </type>
  </assembly>
</linker>