using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.Net;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Extensibility;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Instance.Discovery;
using Microsoft.Identity.Client.Instance.Oidc;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.OAuth2;

internal class OAuth2Client
{
	private readonly Dictionary<string, string> _headers;

	private readonly Dictionary<string, string> _queryParameters = new Dictionary<string, string>();

	private readonly IDictionary<string, string> _bodyParameters = new Dictionary<string, string>();

	private readonly IHttpManager _httpManager;

	public OAuth2Client(ILoggerAdapter logger, IHttpManager httpManager)
	{
		_headers = new Dictionary<string, string>(MsalIdHelper.GetMsalIdParameters(logger));
		_httpManager = httpManager ?? throw new ArgumentNullException("httpManager");
	}

	public void AddQueryParameter(string key, string value)
	{
		if (!string.IsNullOrWhiteSpace(key) && !string.IsNullOrWhiteSpace(value))
		{
			_queryParameters[key] = value;
		}
	}

	public void AddBodyParameter(string key, string value)
	{
		if (!string.IsNullOrWhiteSpace(key) && !string.IsNullOrWhiteSpace(value))
		{
			_bodyParameters[key] = value;
		}
	}

	internal void AddHeader(string key, string value)
	{
		_headers[key] = value;
	}

	internal IReadOnlyDictionary<string, string> GetBodyParameters()
	{
		return new ReadOnlyDictionary<string, string>(_bodyParameters);
	}

	public Task<InstanceDiscoveryResponse> DiscoverAadInstanceAsync(Uri endpoint, RequestContext requestContext)
	{
		return ExecuteRequestAsync<InstanceDiscoveryResponse>(endpoint, HttpMethod.Get, requestContext);
	}

	public Task<OidcMetadata> DiscoverOidcMetadataAsync(Uri endpoint, RequestContext requestContext)
	{
		return ExecuteRequestAsync<OidcMetadata>(endpoint, HttpMethod.Get, requestContext);
	}

	internal Task<MsalTokenResponse> GetTokenAsync(Uri endPoint, RequestContext requestContext, bool addCommonHeaders, Func<OnBeforeTokenRequestData, Task> onBeforePostRequestHandler)
	{
		return ExecuteRequestAsync<MsalTokenResponse>(endPoint, HttpMethod.Post, requestContext, expectErrorsOn200OK: false, addCommonHeaders, onBeforePostRequestHandler);
	}

	internal async Task<T> ExecuteRequestAsync<T>(Uri endPoint, HttpMethod method, RequestContext requestContext, bool expectErrorsOn200OK = false, bool addCommonHeaders = true, Func<OnBeforeTokenRequestData, Task> onBeforePostRequestData = null)
	{
		if (addCommonHeaders)
		{
			AddCommonHeaders(requestContext);
		}
		Uri endpointUri = AddExtraQueryParams(endPoint);
		HttpResponse httpResponse;
		using (requestContext.Logger.LogBlockDuration($"[Oauth2Client] Sending {method} request "))
		{
			_ = 2;
			try
			{
				if (method == HttpMethod.Post)
				{
					if (onBeforePostRequestData != null)
					{
						OnBeforeTokenRequestData requestData = new OnBeforeTokenRequestData(_bodyParameters, _headers, endpointUri, requestContext.UserCancellationToken);
						await onBeforePostRequestData(requestData).ConfigureAwait(continueOnCapturedContext: false);
						endpointUri = requestData.RequestUri;
					}
					httpResponse = await _httpManager.SendPostAsync(endpointUri, _headers, _bodyParameters, requestContext.Logger, requestContext.UserCancellationToken).ConfigureAwait(continueOnCapturedContext: false);
				}
				else
				{
					httpResponse = await _httpManager.SendGetAsync(endpointUri, _headers, requestContext.Logger, retry: true, requestContext.UserCancellationToken).ConfigureAwait(continueOnCapturedContext: false);
				}
			}
			catch (Exception ex)
			{
				if (ex is TaskCanceledException && requestContext.UserCancellationToken.IsCancellationRequested)
				{
					throw;
				}
				requestContext.Logger.ErrorPii(string.Format("=== Token Acquisition ({0}) failed:\n\tAuthority: {1}\n\tClientId: {2}.", requestContext.ApiEvent?.ApiIdString, endpointUri.Scheme + "://" + endpointUri.Host + endpointUri.AbsolutePath, requestContext.ServiceBundle.Config.ClientId), string.Format("=== Token Acquisition ({0}) failed.\n\tHost: {1}.", requestContext.ApiEvent?.ApiIdString, endpointUri.Scheme + "://" + endpointUri.Host));
				requestContext.Logger.ErrorPii(ex);
				throw;
			}
		}
		if (requestContext.ApiEvent != null)
		{
			requestContext.ApiEvent.DurationInHttpInMs += _httpManager.LastRequestDurationInMs;
		}
		if (httpResponse.StatusCode != HttpStatusCode.OK || expectErrorsOn200OK)
		{
			requestContext.Logger.Verbose(() => "[Oauth2Client] Processing error response ");
			try
			{
				if (!string.IsNullOrWhiteSpace(httpResponse.Body))
				{
					MsalTokenResponse msalTokenResponse = JsonHelper.DeserializeFromJson<MsalTokenResponse>(httpResponse.Body);
					if (httpResponse.StatusCode == HttpStatusCode.OK && expectErrorsOn200OK && !string.IsNullOrEmpty(msalTokenResponse?.Error))
					{
						ThrowServerException(httpResponse, requestContext);
					}
				}
			}
			catch (JsonException)
			{
			}
		}
		return CreateResponse<T>(httpResponse, requestContext);
	}

	internal void AddBodyParameter(KeyValuePair<string, string> kvp)
	{
		_bodyParameters.Add(kvp);
	}

	private void AddCommonHeaders(RequestContext requestContext)
	{
		_headers.Add("client-request-id", requestContext.CorrelationId.ToString());
		_headers.Add("return-client-request-id", "true");
		if (!string.IsNullOrWhiteSpace(requestContext.Logger.ClientName))
		{
			_headers.Add("x-app-name", requestContext.Logger.ClientName);
		}
		if (!string.IsNullOrWhiteSpace(requestContext.Logger.ClientVersion))
		{
			_headers.Add("x-app-ver", requestContext.Logger.ClientVersion);
		}
	}

	public static T CreateResponse<T>(HttpResponse response, RequestContext requestContext)
	{
		if (response.StatusCode != HttpStatusCode.OK)
		{
			ThrowServerException(response, requestContext);
		}
		VerifyCorrelationIdHeaderInResponse(response.HeadersAsDictionary, requestContext);
		using (requestContext.Logger.LogBlockDuration("[OAuth2Client] Deserializing response"))
		{
			return JsonHelper.DeserializeFromJson<T>(response.Body);
		}
	}

	private static void ThrowServerException(HttpResponse response, RequestContext requestContext)
	{
		bool shouldLogAsError = true;
		string text = string.Format(CultureInfo.InvariantCulture, "HttpStatusCode: {0}: {1}", (int)response.StatusCode, response.StatusCode.ToString());
		requestContext.Logger.Info(text);
		MsalServiceException ex;
		try
		{
			ex = ExtractErrorsFromTheResponse(response, ref shouldLogAsError);
		}
		catch (JsonException)
		{
			ex = MsalServiceExceptionFactory.FromHttpResponse("non_parsable_oauth_error", "An error response was returned by the OAuth2 server, but it could not be parsed. Please inspect the exception properties for details. ", response);
		}
		catch (Exception innerException)
		{
			ex = MsalServiceExceptionFactory.FromHttpResponse("unknown_error", response.Body, response, innerException);
		}
		if (ex == null)
		{
			ex = MsalServiceExceptionFactory.FromHttpResponse((response.StatusCode == HttpStatusCode.NotFound) ? "not_found" : "http_status_not_200", text, response);
		}
		if (shouldLogAsError)
		{
			requestContext.Logger.ErrorPii($"=== Token Acquisition ({requestContext.ApiEvent?.ApiIdString}) failed:\n\tAuthority: {requestContext.ServiceBundle.Config.Authority.AuthorityInfo.CanonicalAuthority}\n\tClientId: {requestContext.ServiceBundle.Config.ClientId}.", $"=== Token Acquisition ({requestContext.ApiEvent?.ApiIdString}) failed.\n\tHost: {requestContext.ServiceBundle.Config.Authority.AuthorityInfo.Host}.");
			requestContext.Logger.ErrorPii(ex);
		}
		else
		{
			requestContext.Logger.InfoPii(ex);
		}
		throw ex;
	}

	private static MsalServiceException ExtractErrorsFromTheResponse(HttpResponse response, ref bool shouldLogAsError)
	{
		if (string.IsNullOrWhiteSpace(response.Body))
		{
			return null;
		}
		MsalTokenResponse msalTokenResponse;
		try
		{
			msalTokenResponse = JsonHelper.DeserializeFromJson<MsalTokenResponse>(response.Body);
		}
		catch (JsonException)
		{
			if (response.StatusCode == HttpStatusCode.TooManyRequests)
			{
				return MsalServiceExceptionFactory.FromThrottledAuthenticationResponse(response);
			}
			throw;
		}
		if (msalTokenResponse == null || msalTokenResponse.Error == null)
		{
			return null;
		}
		if (string.Compare(msalTokenResponse.Error, "authorization_pending", StringComparison.OrdinalIgnoreCase) == 0)
		{
			shouldLogAsError = false;
		}
		return MsalServiceExceptionFactory.FromHttpResponse(msalTokenResponse.Error, msalTokenResponse.ErrorDescription, response);
	}

	private Uri AddExtraQueryParams(Uri endPoint)
	{
		UriBuilder uriBuilder = new UriBuilder(endPoint);
		string queryParams = _queryParameters.ToQueryParameter();
		uriBuilder.AppendQueryParameters(queryParams);
		return uriBuilder.Uri;
	}

	private static void VerifyCorrelationIdHeaderInResponse(IDictionary<string, string> headers, RequestContext requestContext)
	{
		foreach (string key in headers.Keys)
		{
			string text = key.Trim();
			if (string.Compare(text, "client-request-id", StringComparison.OrdinalIgnoreCase) == 0)
			{
				string text2 = headers[text].Trim();
				if (string.Compare(text2, requestContext.CorrelationId.ToString(), StringComparison.OrdinalIgnoreCase) != 0)
				{
					requestContext.Logger.WarningPii(string.Format(CultureInfo.InvariantCulture, "Returned correlation id '{0}' does not match the sent correlation id '{1}'", text2, requestContext.CorrelationId), "Returned correlation id does not match the sent correlation id");
				}
				break;
			}
		}
	}
}
