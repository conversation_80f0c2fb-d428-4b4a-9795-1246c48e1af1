using Microsoft.Identity.Client.Platforms.net6;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Cache;

[JsonObject]
[Preserve(AllMembers = true)]
internal class AdalResultWrapper
{
	public AdalResult Result { get; set; }

	public string RawClientInfo { get; set; }

	public string RefreshToken { get; set; }

	internal bool IsMultipleResourceRefreshToken
	{
		get
		{
			if (!string.IsNullOrWhiteSpace(RefreshToken))
			{
				return !string.IsNullOrWhiteSpace(ResourceInResponse);
			}
			return false;
		}
	}

	internal string ResourceInResponse { get; set; }

	public string UserAssertionHash { get; set; }

	public static AdalResultWrapper Deserialize(string serializedObject)
	{
		return JsonHelper.DeserializeFromJson<AdalResultWrapper>(serializedObject);
	}

	public string Serialize()
	{
		return JsonHelper.SerializeToJson(this);
	}

	internal AdalResultWrapper Clone()
	{
		return Deserialize(Serialize());
	}
}
