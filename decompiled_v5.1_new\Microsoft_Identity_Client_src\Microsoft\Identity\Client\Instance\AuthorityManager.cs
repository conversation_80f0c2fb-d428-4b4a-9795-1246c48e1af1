using System.Threading.Tasks;
using Microsoft.Identity.Client.Instance.Discovery;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Instance;

internal class AuthorityManager
{
	private static readonly ConcurrentHashSet<string> s_validatedEnvironments = new ConcurrentHashSet<string>();

	private readonly RequestContext _requestContext;

	private readonly Authority _initialAuthority;

	private Authority _currentAuthority;

	private bool _instanceDiscoveryAndValidationExecuted;

	private InstanceDiscoveryMetadataEntry _metadata;

	public Authority OriginalAuthority => _initialAuthority;

	public Authority Authority => _currentAuthority;

	public AuthorityManager(RequestContext requestContext, Authority initialAuthority)
	{
		_requestContext = requestContext;
		_initialAuthority = initialAuthority;
		_currentAuthority = initialAuthority;
	}

	public async Task<InstanceDiscoveryMetadataEntry> GetInstanceDiscoveryEntryAsync()
	{
		await RunInstanceDiscoveryAndValidationAsync().ConfigureAwait(continueOnCapturedContext: false);
		return _metadata;
	}

	public async Task RunInstanceDiscoveryAndValidationAsync()
	{
		if (!_instanceDiscoveryAndValidationExecuted)
		{
			_metadata = await _requestContext.ServiceBundle.InstanceDiscoveryManager.GetMetadataEntryAsync(_initialAuthority.AuthorityInfo, _requestContext).ConfigureAwait(continueOnCapturedContext: false);
			_currentAuthority = Authority.CreateAuthorityWithEnvironment(_initialAuthority.AuthorityInfo, _metadata.PreferredNetwork);
			if (_initialAuthority.AuthorityInfo.ValidateAuthority && _requestContext.ServiceBundle.Config.IsInstanceDiscoveryEnabled)
			{
				await ValidateAuthorityAsync(_initialAuthority).ConfigureAwait(continueOnCapturedContext: false);
			}
			_instanceDiscoveryAndValidationExecuted = true;
		}
	}

	public static void ClearValidationCache()
	{
		s_validatedEnvironments.Clear();
	}

	private async Task ValidateAuthorityAsync(Authority authority)
	{
		if (!s_validatedEnvironments.Contains(authority.AuthorityInfo.Host))
		{
			await AuthorityInfo.AuthorityInfoHelper.CreateAuthorityValidator(authority.AuthorityInfo, _requestContext).ValidateAuthorityAsync(authority.AuthorityInfo).ConfigureAwait(continueOnCapturedContext: false);
			s_validatedEnvironments.Add(authority.AuthorityInfo.Host);
		}
	}
}
