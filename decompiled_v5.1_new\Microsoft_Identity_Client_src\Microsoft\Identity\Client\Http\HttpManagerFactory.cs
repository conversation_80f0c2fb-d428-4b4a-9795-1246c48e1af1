namespace Microsoft.Identity.Client.Http;

internal sealed class HttpManagerFactory
{
	public static IHttpManager GetHttpManager(IMsalHttpClientFactory httpClientFactory, bool withRetry, bool isManagedIdentity)
	{
		if (!withRetry)
		{
			return new HttpManager(httpClientFactory);
		}
		if (!isManagedIdentity)
		{
			return new HttpManagerWithRetry(httpClientFactory);
		}
		return new HttpManagerManagedIdentity(httpClientFactory);
	}
}
