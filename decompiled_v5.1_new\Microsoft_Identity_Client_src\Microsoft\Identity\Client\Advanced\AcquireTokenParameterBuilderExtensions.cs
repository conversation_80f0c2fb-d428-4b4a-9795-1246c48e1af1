using System.Collections.Generic;

namespace Microsoft.Identity.Client.Advanced;

public static class AcquireTokenParameterBuilderExtensions
{
	public static T WithExtraHttpHeaders<T>(this AbstractAcquireTokenParameterBuilder<T> builder, IDictionary<string, string> extraHttpHeaders) where T : AbstractAcquireTokenParameterBuilder<T>
	{
		builder.CommonParameters.ExtraHttpHeaders = extraHttpHeaders;
		return (T)builder;
	}
}
