using System;
using System.Collections.Generic;
using EnumsNET.Numerics;

namespace EnumsNET;

internal interface IEnumBridge
{
	EnumComparer CreateEnumComparer(EnumCache enumCache);

	EnumMember CreateEnumMember(EnumMemberInternal member);

	IReadOnlyList<EnumMember> CreateMembersContainer(IEnumerable<EnumMemberInternal> members, int count, bool cached);
}
internal interface IEnumBridge<TUnderlying, TUnderlyingOperations> : IEnumBridge where TUnderlying : struct, IComparable<TUnderlying>, IEquatable<TUnderlying>, IConvertible where TUnderlyingOperations : struct, IUnderlyingOperations<TUnderlying>
{
	bool CustomValidate(object customValidator, TUnderlying value);

	TUnderlying? IsEnum(object value);

	object ToObjectUnchecked(TUnderlying value);

	IValuesContainer CreateValuesContainer(IEnumerable<EnumMemberInternal<TUnderlying, TUnderlyingOperations>> members, int count, bool cached);
}
