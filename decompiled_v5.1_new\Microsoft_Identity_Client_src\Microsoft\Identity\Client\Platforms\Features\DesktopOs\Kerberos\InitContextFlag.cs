using System;

namespace Microsoft.Identity.Client.Platforms.Features.DesktopOs.Kerberos;

[Flags]
internal enum InitContextFlag
{
	Zero = 0,
	Delegate = 1,
	MutualAuth = 2,
	ReplayDetect = 4,
	SequenceDetect = 8,
	Confidentiality = 0x10,
	UseSessionKey = 0x20,
	AllocateMemory = 0x100,
	Connection = 0x800,
	InitExtendedError = 0x4000,
	InitStream = 0x8000,
	InitIntegrity = 0x10000,
	InitManualCredValidation = 0x80000,
	InitUseSuppliedCreds = 0x80,
	InitIdentify = 0x20000,
	ProxyBindings = 0x4000000,
	AllowMissingBindings = 0x10000000,
	UnverifiedTargetName = 0x20000000
}
