using System;
using System.Globalization;

namespace Newtonsoft.Json.Bson.Utilities;

internal static class MiscellaneousUtils
{
	public static ArgumentOutOfRangeException CreateArgumentOutOfRangeException(string paramName, object actualValue, string message)
	{
		string message2 = message + Environment.NewLine + "Actual value was {0}.".FormatWith(CultureInfo.InvariantCulture, actualValue);
		return new ArgumentOutOfRangeException(paramName, message2);
	}
}
