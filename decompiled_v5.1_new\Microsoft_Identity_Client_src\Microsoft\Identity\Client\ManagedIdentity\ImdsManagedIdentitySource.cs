using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.AppConfig;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.ManagedIdentity;

internal class ImdsManagedIdentitySource : AbstractManagedIdentity
{
	private static readonly Uri s_imdsEndpoint = new Uri("http://169.254.169.254/metadata/identity/oauth2/token");

	private const string ImdsTokenPath = "/metadata/identity/oauth2/token";

	private const string ImdsApiVersion = "2018-02-01";

	private const string DefaultMessage = "[Managed Identity] Service request failed.";

	internal const string IdentityUnavailableError = "[Managed Identity] Authentication unavailable. Either the requested identity has not been assigned to this resource, or other errors could be present. Ensure the identity is correctly assigned and check the inner exception for more details. For more information, visit https://aka.ms/msal-managed-identity.";

	internal const string GatewayError = "[Managed Identity] Authentication unavailable. The request failed due to a gateway error.";

	private readonly Uri _imdsEndpoint;

	internal ImdsManagedIdentitySource(RequestContext requestContext)
		: base(requestContext, ManagedIdentitySource.Imds)
	{
		requestContext.Logger.Info(() => "[Managed Identity] Defaulting to IMDS endpoint for managed identity.");
		if (!string.IsNullOrEmpty(EnvironmentVariables.PodIdentityEndpoint))
		{
			requestContext.Logger.Verbose(() => "[Managed Identity] Environment variable AZURE_POD_IDENTITY_AUTHORITY_HOST for IMDS returned endpoint: " + EnvironmentVariables.PodIdentityEndpoint);
			UriBuilder uriBuilder = new UriBuilder(EnvironmentVariables.PodIdentityEndpoint)
			{
				Path = "/metadata/identity/oauth2/token"
			};
			_imdsEndpoint = uriBuilder.Uri;
		}
		else
		{
			requestContext.Logger.Verbose(() => "[Managed Identity] Unable to find AZURE_POD_IDENTITY_AUTHORITY_HOST environment variable for IMDS, using the default endpoint.");
			_imdsEndpoint = s_imdsEndpoint;
		}
		requestContext.Logger.Verbose(() => "[Managed Identity] Creating IMDS managed identity source. Endpoint URI: " + _imdsEndpoint);
	}

	protected override ManagedIdentityRequest CreateRequest(string resource)
	{
		ManagedIdentityRequest managedIdentityRequest = new ManagedIdentityRequest(HttpMethod.Get, _imdsEndpoint);
		managedIdentityRequest.Headers.Add("Metadata", "true");
		managedIdentityRequest.QueryParameters["api-version"] = "2018-02-01";
		managedIdentityRequest.QueryParameters["resource"] = resource;
		switch (_requestContext.ServiceBundle.Config.ManagedIdentityId.IdType)
		{
		case ManagedIdentityIdType.ClientId:
			_requestContext.Logger.Info("[Managed Identity] Adding user assigned client id to the request.");
			managedIdentityRequest.QueryParameters["client_id"] = _requestContext.ServiceBundle.Config.ManagedIdentityId.UserAssignedId;
			break;
		case ManagedIdentityIdType.ResourceId:
			_requestContext.Logger.Info("[Managed Identity] Adding user assigned resource id to the request.");
			managedIdentityRequest.QueryParameters["mi_res_id"] = _requestContext.ServiceBundle.Config.ManagedIdentityId.UserAssignedId;
			break;
		case ManagedIdentityIdType.ObjectId:
			_requestContext.Logger.Info("[Managed Identity] Adding user assigned object id to the request.");
			managedIdentityRequest.QueryParameters["object_id"] = _requestContext.ServiceBundle.Config.ManagedIdentityId.UserAssignedId;
			break;
		}
		return managedIdentityRequest;
	}

	protected override async Task<ManagedIdentityResponse> HandleResponseAsync(AcquireTokenForManagedIdentityParameters parameters, HttpResponse response, CancellationToken cancellationToken)
	{
		string text = response.StatusCode switch
		{
			HttpStatusCode.BadRequest => "[Managed Identity] Authentication unavailable. Either the requested identity has not been assigned to this resource, or other errors could be present. Ensure the identity is correctly assigned and check the inner exception for more details. For more information, visit https://aka.ms/msal-managed-identity.", 
			HttpStatusCode.BadGateway => "[Managed Identity] Authentication unavailable. The request failed due to a gateway error.", 
			HttpStatusCode.GatewayTimeout => "[Managed Identity] Authentication unavailable. The request failed due to a gateway error.", 
			_ => null, 
		};
		if (text != null)
		{
			string text2 = CreateRequestFailedMessage(response, text);
			string messageFromErrorResponse = GetMessageFromErrorResponse(response);
			text2 = text2 + Environment.NewLine + messageFromErrorResponse;
			_requestContext.Logger.Error($"Error message: {text2} Http status code: {response.StatusCode}");
			throw MsalServiceExceptionFactory.CreateManagedIdentityException("managed_identity_request_failed", text2, null, ManagedIdentitySource.Imds, null);
		}
		return await base.HandleResponseAsync(parameters, response, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}

	internal static string CreateRequestFailedMessage(HttpResponse response, string message)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.AppendLine(message ?? "[Managed Identity] Service request failed.").Append("Status: ").Append(response.StatusCode.ToString());
		if (response.Body != null)
		{
			stringBuilder.AppendLine().AppendLine("Content:").AppendLine(response.Body);
		}
		stringBuilder.AppendLine().AppendLine("Headers:");
		foreach (KeyValuePair<string, string> item in response.HeadersAsDictionary)
		{
			StringBuilder stringBuilder2 = stringBuilder;
			StringBuilder.AppendInterpolatedStringHandler handler = new StringBuilder.AppendInterpolatedStringHandler(2, 2, stringBuilder2);
			handler.AppendFormatted(item.Key);
			handler.AppendLiteral(": ");
			handler.AppendFormatted(item.Value);
			stringBuilder2.AppendLine(ref handler);
		}
		return stringBuilder.ToString();
	}
}
