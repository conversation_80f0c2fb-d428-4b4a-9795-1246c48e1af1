using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Internal;

internal class JsonWebToken
{
	private const int MaxTokenLength = 65536;

	public const long JwtToAadLifetimeInSeconds = 600L;

	private readonly IDictionary<string, string> _claimsToSign;

	private readonly ICryptographyManager _cryptographyManager;

	private readonly string _clientId;

	private readonly string _audience;

	private readonly bool _appendDefaultClaims;

	public JsonWebToken(ICryptographyManager cryptographyManager, string clientId, string audience)
	{
		_cryptographyManager = cryptographyManager;
		_clientId = clientId;
		_audience = audience;
	}

	public JsonWebToken(ICryptographyManager cryptographyManager, string clientId, string audience, IDictionary<string, string> claimsToSign, bool appendDefaultClaims = false)
		: this(cryptographyManager, clientId, audience)
	{
		_claimsToSign = claimsToSign;
		_appendDefaultClaims = appendDefaultClaims;
	}

	private string CreateJsonPayload()
	{
		long num = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
		long value = num + 600;
		if (_claimsToSign == null || _claimsToSign.Count == 0)
		{
			return $"{{\"aud\":\"{_audience}\",\"iss\":\"{_clientId}\",\"sub\":\"{_clientId}\",\"nbf\":\"{num}\",\"exp\":\"{value}\",\"jti\":\"{Guid.NewGuid()}\"}}";
		}
		StringBuilder stringBuilder = new StringBuilder();
		if (_appendDefaultClaims)
		{
			string value2 = $"{{\"aud\":\"{_audience}\",\"iss\":\"{_clientId}\",\"sub\":\"{_clientId}\",\"nbf\":\"{num}\",\"exp\":\"{value}\",\"jti\":\"{Guid.NewGuid()}\",";
			stringBuilder.Append(value2);
		}
		else
		{
			stringBuilder.Append('{');
		}
		int num2 = 0;
		foreach (KeyValuePair<string, string> item in _claimsToSign)
		{
			StringBuilder stringBuilder2 = stringBuilder;
			StringBuilder.AppendInterpolatedStringHandler handler = new StringBuilder.AppendInterpolatedStringHandler(5, 2, stringBuilder2);
			handler.AppendLiteral("\"");
			handler.AppendFormatted(item.Key);
			handler.AppendLiteral("\":\"");
			handler.AppendFormatted(item.Value);
			handler.AppendLiteral("\"");
			stringBuilder2.Append(ref handler);
			if (num2 != _claimsToSign.Count - 1)
			{
				num2++;
				stringBuilder.Append(',');
			}
		}
		stringBuilder.Append('}');
		return stringBuilder.ToString();
	}

	public string Sign(X509Certificate2 certificate, bool sendX5C, bool useSha2AndPss)
	{
		string text = CreateJwtHeaderAndBody(certificate, sendX5C, useSha2AndPss);
		if (65536 < text.Length)
		{
			throw new MsalClientException("encoded_token_too_long");
		}
		byte[] inArray = _cryptographyManager.SignWithCertificate(text, certificate, useSha2AndPss ? RSASignaturePadding.Pss : RSASignaturePadding.Pkcs1);
		return text + "." + Base64UrlHelpers.Encode(inArray);
	}

	private static string CreateJsonHeader(X509Certificate2 certificate, bool sendX5C, bool useSha2AndPss)
	{
		string value = ComputeCertThumbprint(certificate, useSha2AndPss);
		string value2 = (useSha2AndPss ? "PS256" : "RS256");
		string value3 = (useSha2AndPss ? "x5t#S256" : "x5t");
		if (sendX5C)
		{
			string value4 = Convert.ToBase64String(certificate.RawData);
			return $"{{\"alg\":\"{value2}\",\"typ\":\"JWT\",\"{value3}\":\"{value}\",\"x5c\":\"{value4}\"}}";
		}
		return $"{{\"alg\":\"{value2}\",\"typ\":\"JWT\",\"{value3}\":\"{value}\"}}";
	}

	private static string ComputeCertThumbprint(X509Certificate2 certificate, bool useSha2)
	{
		string text = null;
		try
		{
			if (useSha2)
			{
				return Base64UrlHelpers.Encode(certificate.GetCertHash(HashAlgorithmName.SHA256));
			}
			return Base64UrlHelpers.Encode(certificate.GetCertHash());
		}
		catch (CryptographicException innerException)
		{
			throw new MsalClientException("cryptographic_error", "A cryptographic exception occurred. Possible cause: the certificate has been disposed. See inner exception for full details.", innerException);
		}
	}

	private string CreateJwtHeaderAndBody(X509Certificate2 certificate, bool addX5C, bool useSha2AndPss)
	{
		string text = Base64UrlHelpers.EncodeString(CreateJsonHeader(certificate, addX5C, useSha2AndPss));
		string text2 = Base64UrlHelpers.EncodeString(CreateJsonPayload());
		return text + "." + text2;
	}
}
