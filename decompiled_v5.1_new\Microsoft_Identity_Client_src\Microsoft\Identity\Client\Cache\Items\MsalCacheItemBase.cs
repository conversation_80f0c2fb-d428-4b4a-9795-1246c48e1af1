using System.Diagnostics;
using System.Text.Json.Nodes;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Cache.Items;

[DebuggerDisplay("env: {Environment} accountId: {HomeAccountId}")]
internal abstract class MsalCacheItemBase : MsalItemWithAdditionalFields
{
	internal string HomeAccountId { get; set; }

	internal string Environment { get; set; }

	internal string RawClientInfo { get; set; }

	internal override void PopulateFieldsFromJObject(JsonObject j)
	{
		HomeAccountId = JsonHelper.ExtractExistingOrEmptyString(j, "home_account_id");
		Environment = JsonHelper.ExtractExistingOrEmptyString(j, "environment");
		RawClientInfo = JsonHelper.ExtractExistingOrEmptyString(j, "client_info");
		base.PopulateFieldsFromJObject(j);
	}

	internal override JsonObject ToJObject()
	{
		JsonObject jsonObject = base.ToJObject();
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "home_account_id", HomeAccountId);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "environment", Environment);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "client_info", RawClientInfo);
		return jsonObject;
	}
}
