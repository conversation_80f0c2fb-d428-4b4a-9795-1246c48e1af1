using System;
using System.Threading.Tasks;

namespace Microsoft.Identity.Client.Extensibility;

public static class ConfidentialClientApplicationBuilderExtensions
{
	public static ConfidentialClientApplicationBuilder WithAppTokenProvider(this ConfidentialClientApplicationBuilder builder, Func<AppTokenProviderParameters, Task<AppTokenProviderResult>> appTokenProvider)
	{
		builder.Config.AppTokenProvider = appTokenProvider ?? throw new ArgumentNullException("appTokenProvider");
		return builder;
	}
}
