using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.AppConfig;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Internal.ClientCredential;
using Microsoft.Identity.Client.TelemetryCore.TelemetryClient;
using Microsoft.IdentityModel.Abstractions;

namespace Microsoft.Identity.Client;

public class ConfidentialClientApplicationBuilder : AbstractApplicationBuilder<ConfidentialClientApplicationBuilder>
{
	internal ConfidentialClientApplicationBuilder(ApplicationConfiguration configuration)
		: base(configuration)
	{
		ApplicationBase.GuardMobileFrameworks();
	}

	public static ConfidentialClientApplicationBuilder CreateWithApplicationOptions(ConfidentialClientApplicationOptions options)
	{
		ApplicationBase.GuardMobileFrameworks();
		ConfidentialClientApplicationBuilder confidentialClientApplicationBuilder = new ConfidentialClientApplicationBuilder(new ApplicationConfiguration(MsalClientType.ConfidentialClient)).WithOptions(options);
		if (!string.IsNullOrWhiteSpace(options.ClientSecret))
		{
			confidentialClientApplicationBuilder = confidentialClientApplicationBuilder.WithClientSecret(options.ClientSecret);
		}
		if (!string.IsNullOrWhiteSpace(options.AzureRegion))
		{
			confidentialClientApplicationBuilder = confidentialClientApplicationBuilder.WithAzureRegion(options.AzureRegion);
		}
		return confidentialClientApplicationBuilder.WithCacheSynchronization(options.EnableCacheSynchronization);
	}

	public static ConfidentialClientApplicationBuilder Create(string clientId)
	{
		ApplicationBase.GuardMobileFrameworks();
		return new ConfidentialClientApplicationBuilder(new ApplicationConfiguration(MsalClientType.ConfidentialClient)).WithClientId(clientId);
	}

	public ConfidentialClientApplicationBuilder WithCertificate(X509Certificate2 certificate)
	{
		return WithCertificate(certificate, sendX5C: false);
	}

	public ConfidentialClientApplicationBuilder WithCertificate(X509Certificate2 certificate, bool sendX5C)
	{
		if (certificate == null)
		{
			throw new ArgumentNullException("certificate");
		}
		if (!certificate.HasPrivateKey)
		{
			throw new MsalClientException("cert_without_private_key", MsalErrorMessage.CertMustHavePrivateKey("certificate"));
		}
		base.Config.ClientCredential = new CertificateClientCredential(certificate);
		base.Config.SendX5C = sendX5C;
		return this;
	}

	public ConfidentialClientApplicationBuilder WithClientClaims(X509Certificate2 certificate, IDictionary<string, string> claimsToSign, bool mergeWithDefaultClaims)
	{
		return WithClientClaims(certificate, claimsToSign, mergeWithDefaultClaims, sendX5C: false);
	}

	public ConfidentialClientApplicationBuilder WithClientClaims(X509Certificate2 certificate, IDictionary<string, string> claimsToSign, bool mergeWithDefaultClaims = true, bool sendX5C = false)
	{
		if (certificate == null)
		{
			throw new ArgumentNullException("certificate");
		}
		if (claimsToSign == null || !claimsToSign.Any())
		{
			throw new ArgumentNullException("claimsToSign");
		}
		base.Config.ClientCredential = new CertificateAndClaimsClientCredential(certificate, claimsToSign, mergeWithDefaultClaims);
		base.Config.SendX5C = sendX5C;
		return this;
	}

	public ConfidentialClientApplicationBuilder WithClientSecret(string clientSecret)
	{
		if (string.IsNullOrWhiteSpace(clientSecret))
		{
			throw new ArgumentNullException("clientSecret");
		}
		base.Config.ClientCredential = new SecretStringClientCredential(clientSecret);
		return this;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This method is not recommended. Use overload with Func<AssertionRequestOptions, Task<string>> instead, and return a non-expired assertion, which can be a Federated Credential. See https://aka.ms/msal-net-client-assertion", false)]
	public ConfidentialClientApplicationBuilder WithClientAssertion(string signedClientAssertion)
	{
		if (string.IsNullOrWhiteSpace(signedClientAssertion))
		{
			throw new ArgumentNullException("signedClientAssertion");
		}
		base.Config.ClientCredential = new SignedAssertionClientCredential(signedClientAssertion);
		return this;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	public ConfidentialClientApplicationBuilder WithClientAssertion(Func<string> clientAssertionDelegate)
	{
		if (clientAssertionDelegate == null)
		{
			throw new ArgumentNullException("clientAssertionDelegate");
		}
		Func<CancellationToken, Task<string>> signedAssertionDelegate = (CancellationToken _) => Task.FromResult(clientAssertionDelegate());
		base.Config.ClientCredential = new SignedAssertionDelegateClientCredential(signedAssertionDelegate);
		return this;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	public ConfidentialClientApplicationBuilder WithClientAssertion(Func<CancellationToken, Task<string>> clientAssertionAsyncDelegate)
	{
		if (clientAssertionAsyncDelegate == null)
		{
			throw new ArgumentNullException("clientAssertionAsyncDelegate");
		}
		base.Config.ClientCredential = new SignedAssertionDelegateClientCredential(clientAssertionAsyncDelegate);
		return this;
	}

	public ConfidentialClientApplicationBuilder WithClientAssertion(Func<AssertionRequestOptions, Task<string>> clientAssertionAsyncDelegate)
	{
		if (clientAssertionAsyncDelegate == null)
		{
			throw new ArgumentNullException("clientAssertionAsyncDelegate");
		}
		base.Config.ClientCredential = new SignedAssertionDelegateClientCredential(clientAssertionAsyncDelegate);
		return this;
	}

	public ConfidentialClientApplicationBuilder WithAzureRegion(string azureRegion = "TryAutoDetect")
	{
		if (string.IsNullOrEmpty(azureRegion))
		{
			throw new ArgumentNullException("azureRegion");
		}
		base.Config.AzureRegion = azureRegion;
		return this;
	}

	public ConfidentialClientApplicationBuilder WithCacheSynchronization(bool enableCacheSynchronization)
	{
		base.Config.CacheSynchronizationEnabled = enableCacheSynchronization;
		return this;
	}

	[Obsolete("This method has been renamed to WithOidcAuthority.", false)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public ConfidentialClientApplicationBuilder WithGenericAuthority(string authorityUri)
	{
		return WithOidcAuthority(authorityUri);
	}

	public ConfidentialClientApplicationBuilder WithOidcAuthority(string authorityUri)
	{
		AuthorityInfo authorityInfo = AuthorityInfo.FromGenericAuthority(authorityUri);
		base.Config.Authority = Authority.CreateAuthority(authorityInfo);
		return this;
	}

	public ConfidentialClientApplicationBuilder WithTelemetryClient(params ITelemetryClient[] telemetryClients)
	{
		if (telemetryClients == null)
		{
			throw new ArgumentNullException("telemetryClients");
		}
		if (telemetryClients.Length != 0)
		{
			for (int i = 0; i < telemetryClients.Length; i++)
			{
				(telemetryClients[i] ?? throw new ArgumentNullException("telemetryClient")).Initialize();
			}
			base.Config.TelemetryClients = telemetryClients;
		}
		TelemetryClientLogMsalVersion();
		return this;
	}

	private void TelemetryClientLogMsalVersion()
	{
		if (base.Config.TelemetryClients.HasEnabledClients("config_update"))
		{
			MsalTelemetryEventDetails msalTelemetryEventDetails = new MsalTelemetryEventDetails("config_update");
			msalTelemetryEventDetails.SetProperty("MsalVersion", MsalIdHelper.GetMsalVersion());
			base.Config.TelemetryClients.TrackEvent(msalTelemetryEventDetails);
		}
	}

	internal ConfidentialClientApplicationBuilder WithAppTokenCacheInternalForTest(ITokenCacheInternal tokenCacheInternal)
	{
		base.Config.AppTokenCacheInternalForTest = tokenCacheInternal;
		return this;
	}

	internal override void Validate()
	{
		base.Validate();
		if (string.IsNullOrWhiteSpace(base.Config.RedirectUri))
		{
			base.Config.RedirectUri = "https://replyUrlNotSet";
		}
		if (!Uri.TryCreate(base.Config.RedirectUri, UriKind.Absolute, out Uri _))
		{
			throw new InvalidOperationException(MsalErrorMessage.InvalidRedirectUriReceived(base.Config.RedirectUri));
		}
		if (!string.IsNullOrEmpty(base.Config.AzureRegion) && (base.Config.CustomInstanceDiscoveryMetadata != null || base.Config.CustomInstanceDiscoveryMetadataUri != null))
		{
			throw new MsalClientException("region_discovery_with_custom_instance_metadata", "Configure either region discovery or custom instance metadata. Custom instance discovery metadata overrides region discovery. ");
		}
	}

	public IConfidentialClientApplication Build()
	{
		return BuildConcrete();
	}

	internal ConfidentialClientApplication BuildConcrete()
	{
		return new ConfidentialClientApplication(BuildConfiguration());
	}
}
