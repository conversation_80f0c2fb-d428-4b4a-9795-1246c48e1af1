using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Extensibility;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Internal.Requests;

internal class ClientCredentialRequest : RequestBase
{
	private readonly AcquireTokenForClientParameters _clientParameters;

	private static readonly SemaphoreSlim s_semaphoreSlim = new SemaphoreSlim(1, 1);

	public ClientCredentialRequest(IServiceBundle serviceBundle, AuthenticationRequestParameters authenticationRequestParameters, AcquireTokenForClientParameters clientParameters)
		: base(serviceBundle, authenticationRequestParameters, clientParameters)
	{
		_clientParameters = clientParameters;
	}

	protected override async Task<AuthenticationResult> ExecuteAsync(CancellationToken cancellationToken)
	{
		if (base.AuthenticationRequestParameters.Scope == null || base.AuthenticationRequestParameters.Scope.Count == 0)
		{
			throw new MsalClientException("scopes_required_client_credentials", "At least one scope needs to be requested for this authentication flow. ");
		}
		ILoggerAdapter logger = base.AuthenticationRequestParameters.RequestContext.Logger;
		if (base.AuthenticationRequestParameters.Authority is AadAuthority aadAuthority && aadAuthority.IsCommonOrOrganizationsTenant())
		{
			logger.Error("The current authority is targeting the /common or /organizations endpoint which is not recommended. See https://aka.ms/msal-net-client-credentials for more details.");
		}
		if (_clientParameters.ForceRefresh || !string.IsNullOrEmpty(base.AuthenticationRequestParameters.Claims))
		{
			base.AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo = CacheRefreshReason.ForceRefreshOrClaims;
			logger.Info("[ClientCredentialRequest] Skipped looking for a cached access token because ForceRefresh or Claims were set.");
			return await GetAccessTokenAsync(cancellationToken, logger).ConfigureAwait(continueOnCapturedContext: false);
		}
		MsalAccessTokenCacheItem msalAccessTokenCacheItem = await GetCachedAccessTokenAsync().ConfigureAwait(continueOnCapturedContext: false);
		AuthenticationResult result;
		if (msalAccessTokenCacheItem != null)
		{
			result = CreateAuthenticationResultFromCache(msalAccessTokenCacheItem);
			try
			{
				if (SilentRequestHelper.NeedsRefresh(msalAccessTokenCacheItem))
				{
					base.AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo = CacheRefreshReason.ProactivelyRefreshed;
					SilentRequestHelper.ProcessFetchInBackground(msalAccessTokenCacheItem, delegate
					{
						using CancellationTokenSource cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
						return GetAccessTokenAsync(cancellationTokenSource.Token, logger);
					}, logger, base.ServiceBundle, base.AuthenticationRequestParameters.RequestContext.ApiEvent.ApiId);
				}
			}
			catch (MsalServiceException e)
			{
				return await HandleTokenRefreshErrorAsync(e, msalAccessTokenCacheItem).ConfigureAwait(continueOnCapturedContext: false);
			}
		}
		else
		{
			if (base.AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo != CacheRefreshReason.Expired)
			{
				base.AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo = CacheRefreshReason.NoCachedAccessToken;
			}
			result = await GetAccessTokenAsync(cancellationToken, logger).ConfigureAwait(continueOnCapturedContext: false);
		}
		return result;
	}

	private async Task<AuthenticationResult> GetAccessTokenAsync(CancellationToken cancellationToken, ILoggerAdapter logger)
	{
		await ResolveAuthorityAsync().ConfigureAwait(continueOnCapturedContext: false);
		if (base.ServiceBundle.Config.AppTokenProvider == null)
		{
			return await CacheTokenResponseAndCreateAuthenticationResultAsync(await SendTokenRequestAsync(GetBodyParameters(), cancellationToken).ConfigureAwait(continueOnCapturedContext: false)).ConfigureAwait(continueOnCapturedContext: false);
		}
		logger.Verbose(() => "[ClientCredentialRequest] Entering client credential request semaphore.");
		await s_semaphoreSlim.WaitAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		logger.Verbose(() => "[ClientCredentialRequest] Entered client credential request semaphore.");
		try
		{
			AuthenticationResult result;
			if (_clientParameters.ForceRefresh || base.AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo == CacheRefreshReason.ProactivelyRefreshed || !string.IsNullOrEmpty(base.AuthenticationRequestParameters.Claims))
			{
				result = await SendTokenRequestToAppTokenProviderAsync(logger, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			}
			else
			{
				MsalAccessTokenCacheItem msalAccessTokenCacheItem = await GetCachedAccessTokenAsync().ConfigureAwait(continueOnCapturedContext: false);
				if (msalAccessTokenCacheItem == null)
				{
					result = await SendTokenRequestToAppTokenProviderAsync(logger, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
				}
				else
				{
					logger.Verbose(() => "[ClientCredentialRequest] Checking for a cached access token.");
					result = CreateAuthenticationResultFromCache(msalAccessTokenCacheItem);
				}
			}
			return result;
		}
		finally
		{
			s_semaphoreSlim.Release();
			logger.Verbose(() => "[ClientCredentialRequest] Released client credential request semaphore.");
		}
	}

	private async Task<AuthenticationResult> SendTokenRequestToAppTokenProviderAsync(ILoggerAdapter logger, CancellationToken cancellationToken)
	{
		logger.Info("[ClientCredentialRequest] Acquiring a token from the token provider.");
		AppTokenProviderParameters appTokenProviderParameters = new AppTokenProviderParameters
		{
			Scopes = GetOverriddenScopes(base.AuthenticationRequestParameters.Scope),
			CorrelationId = base.AuthenticationRequestParameters.RequestContext.CorrelationId.ToString(),
			Claims = base.AuthenticationRequestParameters.Claims,
			TenantId = base.AuthenticationRequestParameters.Authority.TenantId,
			CancellationToken = cancellationToken
		};
		MsalTokenResponse msalTokenResponse = MsalTokenResponse.CreateFromAppProviderResponse(await base.ServiceBundle.Config.AppTokenProvider(appTokenProviderParameters).ConfigureAwait(continueOnCapturedContext: false));
		msalTokenResponse.Scope = appTokenProviderParameters.Scopes.AsSingleString();
		msalTokenResponse.CorrelationId = appTokenProviderParameters.CorrelationId;
		return await CacheTokenResponseAndCreateAuthenticationResultAsync(msalTokenResponse).ConfigureAwait(continueOnCapturedContext: false);
	}

	private async Task<MsalAccessTokenCacheItem> GetCachedAccessTokenAsync()
	{
		MsalAccessTokenCacheItem msalAccessTokenCacheItem = await base.CacheManager.FindAccessTokenAsync().ConfigureAwait(continueOnCapturedContext: false);
		if (msalAccessTokenCacheItem != null && !_clientParameters.ForceRefresh)
		{
			base.AuthenticationRequestParameters.RequestContext.ApiEvent.IsAccessTokenCacheHit = true;
			Metrics.IncrementTotalAccessTokensFromCache();
			return msalAccessTokenCacheItem;
		}
		return null;
	}

	private AuthenticationResult CreateAuthenticationResultFromCache(MsalAccessTokenCacheItem cachedAccessTokenItem)
	{
		return new AuthenticationResult(cachedAccessTokenItem, null, base.AuthenticationRequestParameters.AuthenticationScheme, base.AuthenticationRequestParameters.RequestContext.CorrelationId, TokenSource.Cache, base.AuthenticationRequestParameters.RequestContext.ApiEvent, null, null, null);
	}

	protected override SortedSet<string> GetOverriddenScopes(ISet<string> inputScopes)
	{
		return new SortedSet<string>(inputScopes);
	}

	private Dictionary<string, string> GetBodyParameters()
	{
		return new Dictionary<string, string>
		{
			["grant_type"] = "client_credentials",
			["scope"] = base.AuthenticationRequestParameters.Scope.AsSingleString()
		};
	}

	protected override KeyValuePair<string, string>? GetCcsHeader(IDictionary<string, string> additionalBodyParameters)
	{
		return null;
	}
}
