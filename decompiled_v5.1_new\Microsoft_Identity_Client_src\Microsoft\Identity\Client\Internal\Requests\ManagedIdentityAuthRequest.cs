using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.ManagedIdentity;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Internal.Requests;

internal class ManagedIdentityAuthRequest : RequestBase
{
	private readonly AcquireTokenForManagedIdentityParameters _managedIdentityParameters;

	private static readonly SemaphoreSlim s_semaphoreSlim = new SemaphoreSlim(1, 1);

	public ManagedIdentityAuthRequest(IServiceBundle serviceBundle, AuthenticationRequestParameters authenticationRequestParameters, AcquireTokenForManagedIdentityParameters managedIdentityParameters)
		: base(serviceBundle, authenticationRequestParameters, managedIdentityParameters)
	{
		_managedIdentityParameters = managedIdentityParameters;
	}

	protected override async Task<AuthenticationResult> ExecuteAsync(CancellationToken cancellationToken)
	{
		ILoggerAdapter logger = base.AuthenticationRequestParameters.RequestContext.Logger;
		if (_managedIdentityParameters.ForceRefresh)
		{
			base.AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo = CacheRefreshReason.ForceRefreshOrClaims;
			logger.Info("[ManagedIdentityRequest] Skipped looking for a cached access token because ForceRefresh was set.");
			return await GetAccessTokenAsync(cancellationToken, logger).ConfigureAwait(continueOnCapturedContext: false);
		}
		MsalAccessTokenCacheItem msalAccessTokenCacheItem = await GetCachedAccessTokenAsync().ConfigureAwait(continueOnCapturedContext: false);
		AuthenticationResult result;
		if (msalAccessTokenCacheItem != null)
		{
			result = CreateAuthenticationResultFromCache(msalAccessTokenCacheItem);
			logger.Info("[ManagedIdentityRequest] Access token retrieved from cache.");
			try
			{
				if (SilentRequestHelper.NeedsRefresh(msalAccessTokenCacheItem))
				{
					logger.Info("[ManagedIdentityRequest] Initiating a proactive refresh.");
					base.AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo = CacheRefreshReason.ProactivelyRefreshed;
					SilentRequestHelper.ProcessFetchInBackground(msalAccessTokenCacheItem, delegate
					{
						using CancellationTokenSource cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
						return GetAccessTokenAsync(cancellationTokenSource.Token, logger);
					}, logger, base.ServiceBundle, base.AuthenticationRequestParameters.RequestContext.ApiEvent.ApiId);
				}
			}
			catch (MsalServiceException e)
			{
				return await HandleTokenRefreshErrorAsync(e, msalAccessTokenCacheItem).ConfigureAwait(continueOnCapturedContext: false);
			}
		}
		else
		{
			if (base.AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo != CacheRefreshReason.Expired)
			{
				base.AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo = CacheRefreshReason.NoCachedAccessToken;
			}
			logger.Info("[ManagedIdentityRequest] No cached access token. Getting a token from the managed identity endpoint.");
			result = await GetAccessTokenAsync(cancellationToken, logger).ConfigureAwait(continueOnCapturedContext: false);
		}
		return result;
	}

	private async Task<AuthenticationResult> GetAccessTokenAsync(CancellationToken cancellationToken, ILoggerAdapter logger)
	{
		logger.Verbose(() => "[ManagedIdentityRequest] Entering managed identity request semaphore.");
		await s_semaphoreSlim.WaitAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		logger.Verbose(() => "[ManagedIdentityRequest] Entered managed identity request semaphore.");
		try
		{
			AuthenticationResult result;
			if (_managedIdentityParameters.ForceRefresh || base.AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo == CacheRefreshReason.ProactivelyRefreshed)
			{
				result = await SendTokenRequestForManagedIdentityAsync(logger, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			}
			else
			{
				logger.Info("[ManagedIdentityRequest] Checking for a cached access token.");
				MsalAccessTokenCacheItem msalAccessTokenCacheItem = await GetCachedAccessTokenAsync().ConfigureAwait(continueOnCapturedContext: false);
				result = ((msalAccessTokenCacheItem == null) ? (await SendTokenRequestForManagedIdentityAsync(logger, cancellationToken).ConfigureAwait(continueOnCapturedContext: false)) : CreateAuthenticationResultFromCache(msalAccessTokenCacheItem));
			}
			return result;
		}
		finally
		{
			s_semaphoreSlim.Release();
			logger.Verbose(() => "[ManagedIdentityRequest] Released managed identity request semaphore.");
		}
	}

	private async Task<AuthenticationResult> SendTokenRequestForManagedIdentityAsync(ILoggerAdapter logger, CancellationToken cancellationToken)
	{
		logger.Info("[ManagedIdentityRequest] Acquiring a token from the managed identity endpoint.");
		await ResolveAuthorityAsync().ConfigureAwait(continueOnCapturedContext: false);
		MsalTokenResponse msalTokenResponse = MsalTokenResponse.CreateFromManagedIdentityResponse(await new ManagedIdentityClient(base.AuthenticationRequestParameters.RequestContext).SendTokenRequestForManagedIdentityAsync(_managedIdentityParameters, cancellationToken).ConfigureAwait(continueOnCapturedContext: false));
		msalTokenResponse.Scope = base.AuthenticationRequestParameters.Scope.AsSingleString();
		return await CacheTokenResponseAndCreateAuthenticationResultAsync(msalTokenResponse).ConfigureAwait(continueOnCapturedContext: false);
	}

	private async Task<MsalAccessTokenCacheItem> GetCachedAccessTokenAsync()
	{
		MsalAccessTokenCacheItem msalAccessTokenCacheItem = await base.CacheManager.FindAccessTokenAsync().ConfigureAwait(continueOnCapturedContext: false);
		if (msalAccessTokenCacheItem != null)
		{
			base.AuthenticationRequestParameters.RequestContext.ApiEvent.IsAccessTokenCacheHit = true;
			Metrics.IncrementTotalAccessTokensFromCache();
			return msalAccessTokenCacheItem;
		}
		return null;
	}

	private AuthenticationResult CreateAuthenticationResultFromCache(MsalAccessTokenCacheItem cachedAccessTokenItem)
	{
		return new AuthenticationResult(cachedAccessTokenItem, null, base.AuthenticationRequestParameters.AuthenticationScheme, base.AuthenticationRequestParameters.RequestContext.CorrelationId, TokenSource.Cache, base.AuthenticationRequestParameters.RequestContext.ApiEvent, null, null, null);
	}

	protected override KeyValuePair<string, string>? GetCcsHeader(IDictionary<string, string> additionalBodyParameters)
	{
		return null;
	}
}
