using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Identity.Client.Extensibility;

public static class ConfidentialClientApplicationExtensions
{
	public static async Task<bool> StopLongRunningProcessInWebApiAsync(this ILongRunningWebApi clientApp, string longRunningProcessSessionKey, CancellationToken cancellationToken = default(CancellationToken))
	{
		return await ((ConfidentialClientApplication)clientApp).StopLongRunningProcessInWebApiAsync(longRunningProcessSessionKey, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}
}
