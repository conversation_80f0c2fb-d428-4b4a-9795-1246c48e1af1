namespace NPOI.OpenXml4Net.OPC;

public class PackageNamespaces
{
	public const string NAMESPACE_DCTERMS = "http://purl.org/dc/terms/";

	public const string NAMESPACE_DC = "http://purl.org/dc/elements/1.1/";

	public const string CONTENT_TYPES = "http://schemas.openxmlformats.org/package/2006/content-types";

	public const string CORE_PROPERTIES = "http://schemas.openxmlformats.org/package/2006/metadata/core-properties";

	public const string DIGITAL_SIGNATURE = "http://schemas.openxmlformats.org/package/2006/digital-signature";

	public const string RELATIONSHIPS = "http://schemas.openxmlformats.org/package/2006/relationships";

	public const string MARKUP_COMPATIBILITY = "http://schemas.openxmlformats.org/markup-compatibility/2006";

	public const string DCMITYPE = "http://purl.org/dc/dcmitype/";

	public const string SCHEMA_MAIN = "http://schemas.openxmlformats.org/spreadsheetml/2006/main";

	public const string SCHEMA_DRAWING = "http://schemas.openxmlformats.org/drawingml/2006/main";

	public const string SCHEMA_SHEETDRAWINGS = "http://schemas.openxmlformats.org/drawingml/2006/spreadsheetDrawing";

	public const string SCHEMA_VT = "http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes";

	public const string SCHEMA_CHART = "http://schemas.openxmlformats.org/drawingml/2006/chart";

	public const string SCHEMA_RELATIONSHIPS = "http://schemas.openxmlformats.org/officeDocument/2006/relationships";
}
