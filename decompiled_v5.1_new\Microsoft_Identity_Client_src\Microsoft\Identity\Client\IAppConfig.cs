using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Security.Cryptography.X509Certificates;

namespace Microsoft.Identity.Client;

public interface IAppConfig
{
	string ClientId { get; }

	bool EnablePiiLogging { get; }

	IMsalHttpClientFactory HttpClientFactory { get; }

	LogLevel LogLevel { get; }

	bool IsDefaultPlatformLoggingEnabled { get; }

	string RedirectUri { get; }

	string TenantId { get; }

	LogCallback LoggingCallback { get; }

	IDictionary<string, string> ExtraQueryParameters { get; }

	bool IsBrokerEnabled { get; }

	string ClientName { get; }

	string ClientVersion { get; }

	[Obsolete("Telemetry is sent automatically by MSAL.NET. See https://aka.ms/msal-net-telemetry.", false)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	ITelemetryConfig TelemetryConfig { get; }

	bool ExperimentalFeaturesEnabled { get; }

	IEnumerable<string> ClientCapabilities { get; }

	bool LegacyCacheCompatibilityEnabled { get; }

	string ClientSecret { get; }

	X509Certificate2 ClientCredentialCertificate { get; }

	Func<object> ParentActivityOrWindowFunc { get; }
}
