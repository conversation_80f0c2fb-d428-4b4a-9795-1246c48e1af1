<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AssemblyName>Microsoft.Identity.Client</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <TargetFramework>netcoreapp6.0</TargetFramework>
  </PropertyGroup>
  <PropertyGroup>
    <LangVersion>12.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <RootNamespace />
  </PropertyGroup>
  <ItemGroup>
    <None Remove="Microsoft.Identity.Client.Properties.Microsoft.Identity.Client.rd.xml" />
    <EmbeddedResource Include="Microsoft.Identity.Client.Properties.Microsoft.Identity.Client.rd.xml" LogicalName="Microsoft.Identity.Client.Properties.Microsoft.Identity.Client.rd.xml" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Microsoft.IdentityModel.Abstractions">
      <HintPath>..\..\Relesez5.1\Microsoft.IdentityModel.Abstractions.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>