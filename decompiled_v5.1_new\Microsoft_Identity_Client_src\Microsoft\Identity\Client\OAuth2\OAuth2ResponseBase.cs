using System.Text.Json.Serialization;
using Microsoft.Identity.Client.Platforms.net6;

namespace Microsoft.Identity.Client.OAuth2;

[JsonObject]
[Preserve(AllMembers = true)]
internal class OAuth2ResponseBase
{
	[JsonPropertyName("error")]
	public string Error { get; set; }

	[JsonPropertyName("suberror")]
	public string SubError { get; set; }

	[JsonPropertyName("error_description")]
	public string ErrorDescription { get; set; }

	[JsonPropertyName("error_codes")]
	public string[] ErrorCodes { get; set; }

	[JsonPropertyName("correlation_id")]
	public string CorrelationId { get; set; }

	[JsonPropertyName("claims")]
	public string Claims { get; set; }
}
