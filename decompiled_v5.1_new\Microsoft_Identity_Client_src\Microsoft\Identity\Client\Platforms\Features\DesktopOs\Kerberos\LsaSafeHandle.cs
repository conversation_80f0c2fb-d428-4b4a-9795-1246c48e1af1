using System;
using System.Runtime.InteropServices;

namespace Microsoft.Identity.Client.Platforms.Features.DesktopOs.Kerberos;

internal class LsaSafeHandle : SafeHandle
{
	public override bool IsInvalid => handle == IntPtr.Zero;

	public LsaSafeHandle()
		: base(IntPtr.Zero, ownsHandle: true)
	{
	}

	protected override bool ReleaseHandle()
	{
		NativeMethods.LsaThrowIfError(NativeMethods.LsaDeregisterLogonProcess(handle));
		handle = IntPtr.Zero;
		return true;
	}
}
