using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.Json.Nodes;
using Microsoft.Identity.Client.Cache.Keys;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Cache.Items;

[DebuggerDisplay("{PreferredUsername} {base.Environment}")]
internal class MsalAccountCacheItem : MsalCacheItemBase
{
	private Lazy<IiOSKey> iOSCacheKeyLazy;

	internal string TenantId { get; set; }

	internal string PreferredUsername { get; set; }

	internal string Name { get; set; }

	internal string GivenName { get; set; }

	internal string FamilyName { get; set; }

	internal string LocalAccountId { get; set; }

	internal string AuthorityType { get; set; }

	internal IDictionary<string, string> WamAccountIds { get; set; }

	public string CacheKey { get; private set; }

	public IiOSKey iOSCacheKey => iOSCacheKeyLazy.Value;

	internal MsalAccountCacheItem()
	{
		AuthorityType = CacheAuthorityType.MSSTS.ToString();
	}

	internal MsalAccountCacheItem(string preferredCacheEnv, string clientInfo, string homeAccountId, IdToken idToken, string preferredUsername, string tenantId, IDictionary<string, string> wamAccountIds)
		: this()
	{
		Init(preferredCacheEnv, idToken?.GetUniqueId(), clientInfo, homeAccountId, idToken?.Name, preferredUsername, tenantId, idToken?.GivenName, idToken?.FamilyName, wamAccountIds);
	}

	internal MsalAccountCacheItem(string environment, string localAccountId, string rawClientInfo, string homeAccountId, string name, string preferredUsername, string tenantId, string givenName, string familyName, IDictionary<string, string> wamAccountIds)
		: this()
	{
		Init(environment, localAccountId, rawClientInfo, homeAccountId, name, preferredUsername, tenantId, givenName, familyName, wamAccountIds);
	}

	internal MsalAccountCacheItem(string environment, string tenantId, string homeAccountId, string preferredUsername)
		: this()
	{
		base.Environment = environment;
		TenantId = tenantId;
		base.HomeAccountId = homeAccountId;
		PreferredUsername = preferredUsername;
		InitCacheKey();
	}

	private void Init(string environment, string localAccountId, string rawClientInfo, string homeAccountId, string name, string preferredUsername, string tenantId, string givenName, string familyName, IDictionary<string, string> wamAccountIds)
	{
		base.Environment = environment;
		PreferredUsername = preferredUsername;
		Name = name;
		TenantId = tenantId;
		LocalAccountId = localAccountId;
		base.RawClientInfo = rawClientInfo;
		GivenName = givenName;
		FamilyName = familyName;
		base.HomeAccountId = homeAccountId;
		WamAccountIds = wamAccountIds;
		InitCacheKey();
	}

	internal void InitCacheKey()
	{
		CacheKey = $"{base.HomeAccountId}{45}{base.Environment}{45}{TenantId}";
		iOSCacheKeyLazy = new Lazy<IiOSKey>(InitiOSKey);
	}

	private IiOSKey InitiOSKey()
	{
		string iOSAccount = MsalCacheKeys.GetiOSAccountKey(base.HomeAccountId, base.Environment);
		string iOSService = (TenantId ?? "").ToLowerInvariant();
		string iOSGeneric = PreferredUsername?.ToLowerInvariant();
		int iOSType = MsalCacheKeys.iOSAuthorityTypeToAttrType[CacheAuthorityType.MSSTS.ToString()];
		return new IosKey(iOSAccount, iOSService, iOSGeneric, iOSType);
	}

	internal static MsalAccountCacheItem FromJsonString(string json)
	{
		if (string.IsNullOrWhiteSpace(json))
		{
			return null;
		}
		return FromJObject(JsonHelper.ParseIntoJsonObject(json));
	}

	internal static MsalAccountCacheItem FromJObject(JsonObject j)
	{
		MsalAccountCacheItem msalAccountCacheItem = new MsalAccountCacheItem();
		msalAccountCacheItem.PreferredUsername = JsonHelper.ExtractExistingOrEmptyString(j, "username");
		msalAccountCacheItem.Name = JsonHelper.ExtractExistingOrEmptyString(j, "name");
		msalAccountCacheItem.GivenName = JsonHelper.ExtractExistingOrEmptyString(j, "given_name");
		msalAccountCacheItem.FamilyName = JsonHelper.ExtractExistingOrEmptyString(j, "family_name");
		msalAccountCacheItem.LocalAccountId = JsonHelper.ExtractExistingOrEmptyString(j, "local_account_id");
		msalAccountCacheItem.AuthorityType = JsonHelper.ExtractExistingOrEmptyString(j, "authority_type");
		msalAccountCacheItem.TenantId = JsonHelper.ExtractExistingOrEmptyString(j, "realm");
		msalAccountCacheItem.WamAccountIds = JsonHelper.ExtractInnerJsonAsDictionary(j, "wam_account_ids");
		msalAccountCacheItem.PopulateFieldsFromJObject(j);
		msalAccountCacheItem.InitCacheKey();
		return msalAccountCacheItem;
	}

	internal override JsonObject ToJObject()
	{
		JsonObject jsonObject = base.ToJObject();
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "username", PreferredUsername);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "name", Name);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "given_name", GivenName);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "family_name", FamilyName);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "local_account_id", LocalAccountId);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "authority_type", AuthorityType);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "realm", TenantId);
		if (WamAccountIds != null && WamAccountIds.Any())
		{
			JsonObject jsonObject2 = new JsonObject();
			foreach (KeyValuePair<string, string> wamAccountId in WamAccountIds)
			{
				jsonObject2[wamAccountId.Key] = wamAccountId.Value;
			}
			jsonObject["wam_account_ids"] = jsonObject2;
		}
		return jsonObject;
	}

	internal string ToJsonString()
	{
		return ToJObject().ToString();
	}
}
