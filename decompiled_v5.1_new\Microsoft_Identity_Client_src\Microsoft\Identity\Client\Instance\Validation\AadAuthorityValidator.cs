using System;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Instance.Discovery;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.Instance.Validation;

internal class AadAuthorityValidator : IAuthorityValidator
{
	private readonly RequestContext _requestContext;

	public AadAuthorityValidator(RequestContext requestContext)
	{
		_requestContext = requestContext;
	}

	public async Task ValidateAuthorityAsync(AuthorityInfo authorityInfo)
	{
		Uri canonicalAuthority = authorityInfo.CanonicalAuthority;
		bool isKnownEnv = KnownMetadataProvider.IsKnownEnvironment(canonicalAuthority.Host);
		_requestContext.Logger.Info(() => $"Authority validation enabled? {authorityInfo.ValidateAuthority}. ");
		_requestContext.Logger.Info(() => $"Authority validation - is known env? {isKnownEnv}. ");
		if (!isKnownEnv)
		{
			_requestContext.Logger.Info("Authority validation is being performed. ");
			await _requestContext.ServiceBundle.InstanceDiscoveryManager.GetMetadataEntryAsync(authorityInfo, _requestContext, forceValidation: true).ConfigureAwait(continueOnCapturedContext: false);
		}
	}
}
