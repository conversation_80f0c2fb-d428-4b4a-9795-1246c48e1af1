using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Security.Claims;
using System.Text.Json;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Internal;

internal class IdToken
{
	private const string DefaultIssuser = "LOCAL AUTHORITY";

	public string ObjectId { get; private set; }

	public string Subject { get; private set; }

	public string TenantId { get; private set; }

	public string PreferredUsername { get; private set; }

	public string Name { get; private set; }

	public string Email { get; private set; }

	public string Upn { get; private set; }

	public string GivenName { get; private set; }

	public string FamilyName { get; private set; }

	public ClaimsPrincipal ClaimsPrincipal { get; private set; }

	public string GetUniqueId()
	{
		return ObjectId ?? Subject;
	}

	private static IdToken ClaimsToToken(List<Claim> claims)
	{
		ClaimsPrincipal claimsPrincipal = new ClaimsPrincipal(new ClaimsIdentity(claims));
		return new IdToken
		{
			ClaimsPrincipal = claimsPrincipal,
			ObjectId = FindClaim(claims, "oid"),
			Subject = FindClaim(claims, "sub"),
			TenantId = FindClaim(claims, "tid"),
			PreferredUsername = FindClaim(claims, "preferred_username"),
			Name = FindClaim(claims, "name"),
			Email = FindClaim(claims, "email"),
			Upn = FindClaim(claims, "upn"),
			GivenName = FindClaim(claims, "given_name"),
			FamilyName = FindClaim(claims, "family_name")
		};
		static string FindClaim(List<Claim> source, string type)
		{
			return source.SingleOrDefault((Claim _) => string.Equals(_.Type, type, StringComparison.OrdinalIgnoreCase))?.Value;
		}
	}

	public static IdToken Parse(string idToken)
	{
		if (string.IsNullOrEmpty(idToken))
		{
			return null;
		}
		string[] array = idToken.Split(new char[1] { '.' });
		if (array.Length < 2)
		{
			throw new MsalClientException("invalid_jwt", "ID Token must have a valid JWT format. ");
		}
		try
		{
			return ClaimsToToken(GetClaimsFromRawToken(JsonDocument.Parse(Base64UrlHelpers.Decode(array[1]))));
		}
		catch (JsonException innerException)
		{
			throw new MsalClientException("json_parse_failed", "Failed to parse the returned id token. ", innerException);
		}
	}

	private static List<Claim> GetClaimsFromRawToken(JsonDocument jsonDocument)
	{
		JsonElement rootElement = jsonDocument.RootElement;
		List<Claim> list = new List<Claim>();
		string text = null;
		if (rootElement.TryGetProperty("iss", out var value))
		{
			text = ((value.ValueKind == JsonValueKind.String) ? value.GetString() : null);
		}
		if (text == null)
		{
			text = "LOCAL AUTHORITY";
		}
		foreach (JsonProperty item in rootElement.EnumerateObject())
		{
			if (item.Value.ValueKind == JsonValueKind.Null)
			{
				list.Add(new Claim(item.Name, string.Empty, "JSON_NULL", text, text));
				continue;
			}
			string text2 = ((item.Value.ValueKind == JsonValueKind.String) ? item.Value.GetString() : null);
			DateTime value3;
			if (text2 != null)
			{
				list.Add(new Claim(item.Name, text2, "http://www.w3.org/2001/XMLSchema#string", text, text));
			}
			else if (item.Value.ValueKind == JsonValueKind.Object)
			{
				AddClaimsFromJToken(list, item.Name, item.Value, text);
			}
			else if (item.Value.ValueKind == JsonValueKind.Array)
			{
				foreach (JsonElement item2 in item.Value.EnumerateArray())
				{
					text2 = ((item2.ValueKind == JsonValueKind.String) ? item2.GetString() : null);
					DateTime value2;
					if (text2 != null)
					{
						list.Add(new Claim(item.Name, text2, "http://www.w3.org/2001/XMLSchema#string", text, text));
					}
					else if (item2.ValueKind == JsonValueKind.Object)
					{
						AddDefaultClaimFromJToken(list, item.Name, item2, text);
					}
					else if (item2.ValueKind == JsonValueKind.String && item2.TryGetDateTime(out value2))
					{
						list.Add(new Claim(item.Name, value2.ToUniversalTime().ToString("o", CultureInfo.InvariantCulture), "http://www.w3.org/2001/XMLSchema#dateTime", text, text));
					}
					else
					{
						list.Add(new Claim(item.Name, item2.ToString(), GetClaimValueType(item2), text, text));
					}
				}
			}
			else if (item.Value.ValueKind == JsonValueKind.Object)
			{
				foreach (JsonProperty item3 in item.Value.EnumerateObject())
				{
					list.Add(new Claim(item.Name, "{" + item3.Name + ":" + item3.Value.ToString() + "}", GetClaimValueType(item3.Value), text, text));
				}
			}
			else if (item.Value.ValueKind == JsonValueKind.String && item.Value.TryGetDateTime(out value3))
			{
				list.Add(new Claim(item.Name, value3.ToUniversalTime().ToString("o", CultureInfo.InvariantCulture), "http://www.w3.org/2001/XMLSchema#dateTime", text, text));
			}
			else
			{
				list.Add(new Claim(item.Name, item.Value.ToString(), GetClaimValueType(item.Value), text, text));
			}
		}
		return list;
	}

	private static void AddClaimsFromJToken(List<Claim> claims, string claimType, JsonElement jtoken, string issuer)
	{
		if (jtoken.ValueKind == JsonValueKind.Object)
		{
			claims.Add(new Claim(claimType, jtoken.ToString(), "JSON"));
			return;
		}
		if (jtoken.ValueKind == JsonValueKind.Array)
		{
			foreach (JsonElement item in jtoken.EnumerateArray())
			{
				if (item.ValueKind == JsonValueKind.Object)
				{
					claims.Add(new Claim(claimType, item.ToString(), "JSON", issuer, issuer));
				}
				else if (item.ValueKind == JsonValueKind.Array)
				{
					claims.Add(new Claim(claimType, item.ToString(), "JSON_ARRAY", issuer, issuer));
				}
				else
				{
					AddDefaultClaimFromJToken(claims, claimType, item, issuer);
				}
			}
			return;
		}
		AddDefaultClaimFromJToken(claims, claimType, jtoken, issuer);
	}

	private static void AddDefaultClaimFromJToken(List<Claim> claims, string claimType, JsonElement jtoken, string issuer)
	{
		if (jtoken.ValueKind == JsonValueKind.String)
		{
			if (jtoken.TryGetDateTime(out var value))
			{
				claims.Add(new Claim(claimType, value.ToUniversalTime().ToString("o", CultureInfo.InvariantCulture), "http://www.w3.org/2001/XMLSchema#dateTime", issuer, issuer));
				return;
			}
			string text = jtoken.GetString();
			if (text != null)
			{
				claims.Add(new Claim(claimType, text, "http://www.w3.org/2001/XMLSchema#string", issuer, issuer));
			}
			else
			{
				claims.Add(new Claim(claimType, jtoken.ToString(), GetClaimValueType(jtoken), issuer, issuer));
			}
		}
		else
		{
			claims.Add(new Claim(claimType, jtoken.ToString(), GetClaimValueType(jtoken), issuer, issuer));
		}
	}

	private static string GetClaimValueType(JsonElement obj)
	{
		if (obj.ValueKind == JsonValueKind.Null)
		{
			return "JSON_NULL";
		}
		JsonValueKind valueKind = obj.ValueKind;
		switch (valueKind)
		{
		case JsonValueKind.True:
		case JsonValueKind.False:
			return "http://www.w3.org/2001/XMLSchema#boolean";
		case JsonValueKind.Number:
		{
			if (obj.TryGetInt32(out var _))
			{
				return "http://www.w3.org/2001/XMLSchema#integer";
			}
			if (obj.TryGetDouble(out var _))
			{
				return "http://www.w3.org/2001/XMLSchema#double";
			}
			long @int = obj.GetInt64();
			if (@int >= int.MinValue && @int <= int.MaxValue)
			{
				return "http://www.w3.org/2001/XMLSchema#integer";
			}
			return "http://www.w3.org/2001/XMLSchema#integer64";
		}
		case JsonValueKind.String:
		{
			if (obj.TryGetDateTime(out var _))
			{
				return "http://www.w3.org/2001/XMLSchema#dateTime";
			}
			return "http://www.w3.org/2001/XMLSchema#string";
		}
		case JsonValueKind.Object:
			return "JSON";
		case JsonValueKind.Array:
			return "JSON_ARRAY";
		default:
			return valueKind.ToString();
		}
	}
}
