using Microsoft.IdentityModel.Abstractions;

namespace Microsoft.Identity.Client.Internal.Logger;

internal class CallbackIdentityLogger : IIdentityLogger
{
	private LogCallback _logCallback;

	private readonly string _correlationId;

	private readonly string _clientInformation;

	private readonly bool _piiLoggingEnabled;

	private readonly LogLevel _minLogLevel;

	public CallbackIdentityLogger(LogCallback logCallback, string correlationId, string clientName, string clientVersion, bool enablePiiLogging, LogLevel minLogLevel)
	{
		_correlationId = correlationId;
		_clientInformation = LoggerHelper.GetClientInfo(clientName, clientVersion);
		_piiLoggingEnabled = enablePiiLogging;
		_logCallback = logCallback;
		_minLogLevel = minLogLevel;
	}

	public bool IsEnabled(EventLogLevel eventLevel)
	{
		if (_logCallback != null)
		{
			return GetLogLevel(eventLevel) <= _minLogLevel;
		}
		return false;
	}

	public void Log(LogEntry entry)
	{
		string message = LoggerHelper.FormatLogMessage(entry.Message, _piiLoggingEnabled, string.IsNullOrEmpty(entry.CorrelationId) ? entry.CorrelationId : _correlationId, _clientInformation);
		_logCallback(GetLogLevel(entry.EventLogLevel), message, _piiLoggingEnabled);
	}

	private static LogLevel GetLogLevel(EventLogLevel eventLogLevel)
	{
		if (eventLogLevel == EventLogLevel.LogAlways)
		{
			return LogLevel.Always;
		}
		return (LogLevel)(eventLogLevel - 2);
	}
}
