using System.ComponentModel;

namespace Microsoft.Identity.Client;

public class WindowsBrokerOptions
{
	[EditorBrowsable(EditorBrowsableState.Never)]
	public bool MsaPassthrough { get; set; }

	public bool ListWindowsWorkAndSchoolAccounts { get; set; }

	public string HeaderText { get; set; }

	public WindowsBrokerOptions()
	{
		ValidatePlatformAvailability();
	}

	internal static WindowsBrokerOptions CreateDefault()
	{
		return new WindowsBrokerOptions();
	}

	internal static void ValidatePlatformAvailability()
	{
	}
}
