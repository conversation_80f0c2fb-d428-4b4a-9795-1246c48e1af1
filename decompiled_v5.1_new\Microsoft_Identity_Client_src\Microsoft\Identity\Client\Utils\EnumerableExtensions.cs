using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Identity.Client.Core;

namespace Microsoft.Identity.Client.Utils;

internal static class EnumerableExtensions
{
	internal static bool IsNullOrEmpty<T>(this IEnumerable<T> input)
	{
		if (input != null)
		{
			return !input.Any();
		}
		return true;
	}

	internal static string AsSingleString(this IEnumerable<string> input)
	{
		if (input.IsNullOrEmpty())
		{
			return string.Empty;
		}
		return string.Join(" ", input);
	}

	internal static bool ContainsOrdinalIgnoreCase(this IEnumerable<string> set, string toLookFor)
	{
		return set.Any((string el) => el.Equals(toLookFor, StringComparison.OrdinalIgnoreCase));
	}

	internal static List<T> FilterWithLogging<T>(this List<T> list, Func<T, bool> predicate, ILoggerAdapter logger, string logPrefix, bool updateOriginalCollection = true)
	{
		logger.Verbose(() => $"{logPrefix} - item count before: {list.Count} ");
		if (updateOriginalCollection)
		{
			list.RemoveAll((T e) => !predicate(e));
		}
		else
		{
			list = list.Where(predicate).ToList();
		}
		logger.Verbose(() => $"{logPrefix} - item count after: {list.Count} ");
		return list;
	}

	internal static void MergeDifferentEntries<TKey, TValue>(this IDictionary<TKey, TValue> source, IDictionary<TKey, TValue> other)
	{
		if (source == null || other == null)
		{
			return;
		}
		foreach (KeyValuePair<TKey, TValue> item in other)
		{
			if (!source.ContainsKey(item.Key))
			{
				source[item.Key] = item.Value;
			}
		}
	}
}
