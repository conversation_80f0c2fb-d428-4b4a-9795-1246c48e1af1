using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Nodes;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Cache;

internal class TokenCacheDictionarySerializer : ITokenCacheSerializable
{
	private const string AccessTokenKey = "access_tokens";

	private const string RefreshTokenKey = "refresh_tokens";

	private const string IdTokenKey = "id_tokens";

	private const string AccountKey = "accounts";

	private readonly ITokenCacheAccessor _accessor;

	public TokenCacheDictionarySerializer(ITokenCacheAccessor accessor)
	{
		_accessor = accessor;
	}

	public byte[] Serialize(IDictionary<string, JsonNode> unknownNodes)
	{
		List<string> list = new List<string>();
		List<string> list2 = new List<string>();
		List<string> list3 = new List<string>();
		List<string> list4 = new List<string>();
		foreach (MsalAccessTokenCacheItem allAccessToken in _accessor.GetAllAccessTokens())
		{
			list.Add(allAccessToken.ToJsonString());
		}
		foreach (MsalRefreshTokenCacheItem allRefreshToken in _accessor.GetAllRefreshTokens())
		{
			list2.Add(allRefreshToken.ToJsonString());
		}
		foreach (MsalIdTokenCacheItem allIdToken in _accessor.GetAllIdTokens())
		{
			list3.Add(allIdToken.ToJsonString());
		}
		foreach (MsalAccountCacheItem allAccount in _accessor.GetAllAccounts())
		{
			list4.Add(allAccount.ToJsonString());
		}
		return JsonHelper.SerializeToJson(new Dictionary<string, IEnumerable<string>>
		{
			["access_tokens"] = list,
			["refresh_tokens"] = list2,
			["id_tokens"] = list3,
			["accounts"] = list4
		}.ToList()).ToByteArray();
	}

	public IDictionary<string, JsonNode> Deserialize(byte[] bytes, bool clearExistingCacheData)
	{
		List<KeyValuePair<string, IEnumerable<string>>> list;
		try
		{
			list = JsonHelper.DeserializeFromJson<List<KeyValuePair<string, IEnumerable<string>>>>(bytes);
		}
		catch (Exception innerException)
		{
			throw new MsalClientException("json_parse_failed", "MSAL V2 Deserialization failed to parse the cache contents. Is this possibly an earlier format needed for DeserializeMsalV3?  (See https://aka.ms/msal-net-3x-cache-breaking-change). ", innerException);
		}
		Dictionary<string, IEnumerable<string>> dictionary = list.ToDictionary((KeyValuePair<string, IEnumerable<string>> x) => x.Key, (KeyValuePair<string, IEnumerable<string>> x) => x.Value);
		if (clearExistingCacheData)
		{
			_accessor.Clear();
		}
		if (list == null || list.Count == 0)
		{
			return null;
		}
		if (dictionary.TryGetValue("access_tokens", out var value))
		{
			foreach (string item in value)
			{
				_accessor.SaveAccessToken(MsalAccessTokenCacheItem.FromJsonString(item));
			}
		}
		if (dictionary.TryGetValue("refresh_tokens", out var value2))
		{
			foreach (string item2 in value2)
			{
				_accessor.SaveRefreshToken(MsalRefreshTokenCacheItem.FromJsonString(item2));
			}
		}
		if (dictionary.TryGetValue("id_tokens", out var value3))
		{
			foreach (string item3 in value3)
			{
				_accessor.SaveIdToken(MsalIdTokenCacheItem.FromJsonString(item3));
			}
		}
		if (dictionary.TryGetValue("accounts", out var value4))
		{
			foreach (string item4 in value4)
			{
				_accessor.SaveAccount(MsalAccountCacheItem.FromJsonString(item4));
			}
		}
		return null;
	}
}
