using System.Globalization;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.OAuth2;

internal class DeviceAuthJWTResponse
{
	private readonly DeviceAuthHeader _header;

	private readonly DeviceAuthPayload _payload;

	public DeviceAuthJWTResponse(string audience, string nonce, string base64EncodedCertificate)
	{
		_header = new DeviceAuthHeader(base64EncodedCertificate);
		_payload = new DeviceAuthPayload(audience, nonce);
	}

	public string GetResponseToSign()
	{
		return string.Format(CultureInfo.InvariantCulture, "{0}.{1}", Base64UrlHelpers.Encode(JsonHelper.SerializeToJson(_header).ToByteArray()), Base64UrlHelpers.Encode(JsonHelper.SerializeToJson(_payload).ToByteArray()));
	}
}
