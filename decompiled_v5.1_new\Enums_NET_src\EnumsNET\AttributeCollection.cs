using System;
using System.Collections;
using System.Collections.Generic;
using EnumsNET.Utilities;

namespace EnumsNET;

public sealed class AttributeCollection : IList<Attribute>, ICollection<Attribute>, IEnumerable<Attribute>, IEnumerable, IReadOnlyList<Attribute>, IReadOnlyCollection<Attribute>
{
	internal static readonly AttributeCollection Empty = new AttributeCollection(ArrayHelper.Empty<Attribute>());

	private readonly Attribute[] _attributes;

	public Attribute this[int index] => _attributes[index];

	public int Count => _attributes.Length;

	bool ICollection<Attribute>.IsReadOnly => true;

	Attribute IList<Attribute>.this[int index]
	{
		get
		{
			return _attributes[index];
		}
		set
		{
			throw new NotSupportedException();
		}
	}

	internal AttributeCollection(Attribute[] attributes)
	{
		_attributes = attributes;
	}

	public bool Has<TAttribute>() where TAttribute : Attribute
	{
		return Get<TAttribute>() != null;
	}

	public bool Has(Type attributeType)
	{
		return Get(attributeType) != null;
	}

	public TAttribute? Get<TAttribute>() where TAttribute : Attribute
	{
		Attribute[] attributes = _attributes;
		for (int i = 0; i < attributes.Length; i++)
		{
			if (attributes[i] is TAttribute result)
			{
				return result;
			}
		}
		return null;
	}

	public Attribute? Get(Type attributeType)
	{
		Preconditions.NotNull(attributeType, "attributeType");
		Attribute[] attributes = _attributes;
		foreach (Attribute attribute in attributes)
		{
			if (attributeType.IsInstanceOfType(attribute))
			{
				return attribute;
			}
		}
		return null;
	}

	public IEnumerable<TAttribute> GetAll<TAttribute>() where TAttribute : Attribute
	{
		Attribute[] attributes = _attributes;
		for (int i = 0; i < attributes.Length; i++)
		{
			if (attributes[i] is TAttribute val)
			{
				yield return val;
			}
		}
	}

	public IEnumerable<Attribute> GetAll(Type attributeType)
	{
		Preconditions.NotNull(attributeType, "attributeType");
		Attribute[] attributes = _attributes;
		foreach (Attribute attribute in attributes)
		{
			if (attributeType.IsInstanceOfType(attribute))
			{
				yield return attribute;
			}
		}
	}

	public IEnumerator<Attribute> GetEnumerator()
	{
		return ((IEnumerable<Attribute>)_attributes).GetEnumerator();
	}

	bool ICollection<Attribute>.Contains(Attribute item)
	{
		return ((ICollection<Attribute>)_attributes).Contains(item);
	}

	void ICollection<Attribute>.CopyTo(Attribute[] array, int arrayIndex)
	{
		_attributes.CopyTo(array, arrayIndex);
	}

	int IList<Attribute>.IndexOf(Attribute item)
	{
		return ((IList<Attribute>)_attributes).IndexOf(item);
	}

	IEnumerator IEnumerable.GetEnumerator()
	{
		return GetEnumerator();
	}

	void ICollection<Attribute>.Add(Attribute item)
	{
		throw new NotSupportedException();
	}

	void ICollection<Attribute>.Clear()
	{
		throw new NotSupportedException();
	}

	bool ICollection<Attribute>.Remove(Attribute item)
	{
		throw new NotSupportedException();
	}

	void IList<Attribute>.Insert(int index, Attribute item)
	{
		throw new NotSupportedException();
	}

	void IList<Attribute>.RemoveAt(int index)
	{
		throw new NotSupportedException();
	}
}
