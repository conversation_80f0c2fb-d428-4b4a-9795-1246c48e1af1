namespace System.ClientModel.Primitives;

public class ModelReaderWriterOptions
{
	private static ModelReaderWriterOptions? s_jsonOptions;

	private static ModelReaderWriterOptions? s_xmlOptions;

	public static ModelReaderWriterOptions Json => s_jsonOptions ?? (s_jsonOptions = new ModelReaderWriterOptions("J"));

	public static ModelReaderWriterOptions Xml => s_xmlOptions ?? (s_xmlOptions = new ModelReaderWriterOptions("X"));

	public string Format { get; }

	public ModelReaderWriterOptions(string format)
	{
		Format = format;
	}
}
