using System;
using System.Collections.Generic;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Internal.Requests;
using Microsoft.Identity.Client.Kerberos;
using Microsoft.Identity.Client.PlatformsCommon.Shared;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.OAuth2;

internal class TokenClient
{
	private readonly AuthenticationRequestParameters _requestParams;

	private readonly IServiceBundle _serviceBundle;

	private readonly OAuth2Client _oAuth2Client;

	private volatile bool _requestInProgress;

	public TokenClient(AuthenticationRequestParameters requestParams)
	{
		_requestParams = requestParams ?? throw new ArgumentNullException("requestParams");
		_serviceBundle = _requestParams.RequestContext.ServiceBundle;
		_oAuth2Client = new OAuth2Client(_serviceBundle.ApplicationLogger, _serviceBundle.HttpManager);
	}

	public async Task<MsalTokenResponse> SendTokenRequestAsync(IDictionary<string, string> additionalBodyParameters, string scopeOverride = null, string tokenEndpointOverride = null, CancellationToken cancellationToken = default(CancellationToken))
	{
		using (_requestParams.RequestContext.Logger.LogMethodDuration(LogLevel.Verbose, "SendTokenRequestAsync", "/_/src/client/Microsoft.Identity.Client/OAuth2/TokenClient.cs"))
		{
			cancellationToken.ThrowIfCancellationRequested();
			string tokenEndpoint = tokenEndpointOverride;
			if (tokenEndpoint == null)
			{
				tokenEndpoint = await _requestParams.Authority.GetTokenEndpointAsync(_requestParams.RequestContext).ConfigureAwait(continueOnCapturedContext: false);
			}
			_requestParams.RequestContext.ApiEvent.TokenEndpoint = tokenEndpoint;
			string scopes = ((!string.IsNullOrEmpty(scopeOverride)) ? scopeOverride : GetDefaultScopes(_requestParams.Scope));
			await AddBodyParamsAndHeadersAsync(additionalBodyParameters, scopes, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			AddThrottlingHeader();
			_serviceBundle.ThrottlingManager.TryThrottle(_requestParams, _oAuth2Client.GetBodyParameters());
			MsalTokenResponse msalTokenResponse;
			try
			{
				msalTokenResponse = await SendHttpAndClearTelemetryAsync(tokenEndpoint, _requestParams.RequestContext.Logger).ConfigureAwait(continueOnCapturedContext: false);
			}
			catch (MsalServiceException ex)
			{
				_serviceBundle.ThrottlingManager.RecordException(_requestParams, _oAuth2Client.GetBodyParameters(), ex);
				throw;
			}
			if (string.IsNullOrEmpty(msalTokenResponse.Scope))
			{
				msalTokenResponse.Scope = _requestParams.Scope.AsSingleString();
				_requestParams.RequestContext.Logger.Info("ScopeSet was missing from the token response, so using developer provided scopes in the result. ");
			}
			if (string.IsNullOrEmpty(msalTokenResponse.TokenType))
			{
				throw new MsalClientException("token_type_missing", "The response from the token endpoint does not contain the token_type parameter. This happens if the identity provider (AAD, B2C, ADFS, etc.) did not include the access token type in the token response. Verify the configuration of the identity provider. ");
			}
			if (!string.Equals(msalTokenResponse.TokenType, _requestParams.AuthenticationScheme.AccessTokenType, StringComparison.OrdinalIgnoreCase))
			{
				throw new MsalClientException("token_type_mismatch", MsalErrorMessage.TokenTypeMismatch(_requestParams.AuthenticationScheme.AccessTokenType, msalTokenResponse.TokenType));
			}
			return msalTokenResponse;
		}
	}

	private void AddThrottlingHeader()
	{
		_oAuth2Client.AddHeader("x-ms-lib-capability", "retry-after, h429");
	}

	private async Task AddBodyParamsAndHeadersAsync(IDictionary<string, string> additionalBodyParameters, string scopes, CancellationToken cancellationToken)
	{
		_oAuth2Client.AddBodyParameter("client_id", _requestParams.AppConfig.ClientId);
		if (_serviceBundle.Config.ClientCredential != null)
		{
			_requestParams.RequestContext.Logger.Verbose(() => "[TokenClient] Before adding the client assertion / secret");
			string tokenEndpoint = await _requestParams.Authority.GetTokenEndpointAsync(_requestParams.RequestContext).ConfigureAwait(continueOnCapturedContext: false);
			bool isSha2CredentialSupported = _requestParams.AuthorityManager.Authority.AuthorityInfo.IsSha2CredentialSupported;
			await _serviceBundle.Config.ClientCredential.AddConfidentialClientParametersAsync(_oAuth2Client, _requestParams.RequestContext.Logger, _serviceBundle.PlatformProxy.CryptographyManager, _requestParams.AppConfig.ClientId, tokenEndpoint, _requestParams.SendX5C, isSha2CredentialSupported, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			_requestParams.RequestContext.Logger.Verbose(() => "[TokenClient] After adding the client assertion / secret");
		}
		_oAuth2Client.AddBodyParameter("scope", scopes);
		AddClaims();
		foreach (KeyValuePair<string, string> additionalBodyParameter in additionalBodyParameters)
		{
			_oAuth2Client.AddBodyParameter(additionalBodyParameter.Key, additionalBodyParameter.Value);
		}
		foreach (KeyValuePair<string, string> tokenRequestParam in _requestParams.AuthenticationScheme.GetTokenRequestParams())
		{
			_oAuth2Client.AddBodyParameter(tokenRequestParam.Key, tokenRequestParam.Value);
		}
		_oAuth2Client.AddHeader("x-client-current-telemetry", _serviceBundle.HttpTelemetryManager.GetCurrentRequestHeader(_requestParams.RequestContext.ApiEvent));
		if (!_requestInProgress)
		{
			_requestInProgress = true;
			_oAuth2Client.AddHeader("x-client-last-telemetry", _serviceBundle.HttpTelemetryManager.GetLastRequestHeader());
		}
		if (DeviceAuthHelper.CanOSPerformPKeyAuth())
		{
			_oAuth2Client.AddHeader("x-ms-PKeyAuth", "1.0");
		}
		AddExtraHttpHeaders();
	}

	private void AddClaims()
	{
		string kerberosTicketClaim = KerberosSupplementalTicketManager.GetKerberosTicketClaim(_requestParams.RequestContext.ServiceBundle.Config.KerberosServicePrincipalName, _requestParams.RequestContext.ServiceBundle.Config.TicketContainer);
		string resolvedClaims;
		if (string.IsNullOrEmpty(kerberosTicketClaim))
		{
			resolvedClaims = _requestParams.ClaimsAndClientCapabilities;
		}
		else if (!string.IsNullOrEmpty(_requestParams.ClaimsAndClientCapabilities))
		{
			JsonObject capabilitiesJson = JsonHelper.ParseIntoJsonObject(_requestParams.ClaimsAndClientCapabilities);
			JsonObject jsonObject = ClaimsHelper.MergeClaimsIntoCapabilityJson(kerberosTicketClaim, capabilitiesJson);
			resolvedClaims = JsonHelper.JsonObjectToString(jsonObject);
			_requestParams.RequestContext.Logger.Verbose(() => "Adding kerberos claim + Claims/ClientCapabilities to request: " + resolvedClaims);
		}
		else
		{
			resolvedClaims = kerberosTicketClaim;
			_requestParams.RequestContext.Logger.Verbose(() => "Adding kerberos claim to request: " + resolvedClaims);
		}
		_oAuth2Client.AddBodyParameter("claims", resolvedClaims);
	}

	private void AddExtraHttpHeaders()
	{
		if (_requestParams.ExtraHttpHeaders == null)
		{
			return;
		}
		foreach (KeyValuePair<string, string> extraHttpHeader in _requestParams.ExtraHttpHeaders)
		{
			if (!string.IsNullOrEmpty(extraHttpHeader.Key) && !string.IsNullOrEmpty(extraHttpHeader.Value))
			{
				_oAuth2Client.AddHeader(extraHttpHeader.Key, extraHttpHeader.Value);
			}
		}
	}

	public void AddHeaderToClient(string name, string value)
	{
		_oAuth2Client.AddHeader(name, value);
	}

	private async Task<MsalTokenResponse> SendHttpAndClearTelemetryAsync(string tokenEndpoint, ILoggerAdapter logger)
	{
		UriBuilder uriBuilder = new UriBuilder(tokenEndpoint);
		uriBuilder.AppendQueryParameters(_requestParams.ExtraQueryParameters);
		Uri tokenEndpointWithQueryParams = uriBuilder.Uri;
		try
		{
			logger.Verbose(() => "[Token Client] Fetching MsalTokenResponse .... ");
			MsalTokenResponse result = await _oAuth2Client.GetTokenAsync(tokenEndpointWithQueryParams, _requestParams.RequestContext, addCommonHeaders: true, _requestParams.OnBeforeTokenRequestHandler).ConfigureAwait(continueOnCapturedContext: false);
			_serviceBundle.HttpTelemetryManager.ResetPreviousUnsentData();
			return result;
		}
		catch (MsalServiceException ex)
		{
			if (!ex.IsRetryable)
			{
				_serviceBundle.HttpTelemetryManager.ResetPreviousUnsentData();
			}
			if (ex.StatusCode != 401 || !_serviceBundle.DeviceAuthManager.TryCreateDeviceAuthChallengeResponse(ex.Headers, new Uri(tokenEndpoint), out var responseHeader))
			{
				throw;
			}
			_oAuth2Client.AddHeader("Authorization", responseHeader);
			return await _oAuth2Client.GetTokenAsync(tokenEndpointWithQueryParams, _requestParams.RequestContext, addCommonHeaders: false, _requestParams.OnBeforeTokenRequestHandler).ConfigureAwait(continueOnCapturedContext: false);
		}
		finally
		{
			_requestInProgress = false;
		}
	}

	private static string GetDefaultScopes(ISet<string> inputScope)
	{
		SortedSet<string> sortedSet = new SortedSet<string>(inputScope, StringComparer.OrdinalIgnoreCase);
		sortedSet.UnionWith(OAuth2Value.ReservedScopes);
		return sortedSet.AsSingleString();
	}
}
