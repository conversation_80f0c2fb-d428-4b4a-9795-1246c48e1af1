using System.Collections.Generic;
using System.Globalization;

namespace Microsoft.Identity.Client;

internal sealed class Account : IAccount
{
	public string Username { get; }

	public string Environment { get; }

	public AccountId HomeAccountId { get; }

	public IEnumerable<TenantProfile> TenantProfiles { get; }

	internal IDictionary<string, string> WamAccountIds { get; }

	public Account(string homeAccountId, string username, string environment, IDictionary<string, string> wamAccountIds = null, IEnumerable<TenantProfile> tenantProfiles = null)
	{
		Username = username;
		Environment = environment;
		HomeAccountId = AccountId.ParseFromString(homeAccountId);
		WamAccountIds = wamAccountIds;
		TenantProfiles = tenantProfiles;
	}

	public override string ToString()
	{
		return string.Format(CultureInfo.CurrentCulture, "Account username: {0} environment {1} home account id: {2}", Username, Environment, HomeAccountId);
	}
}
