using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.OAuth2;

namespace Microsoft.Identity.Client.Instance.Validation;

internal class AdfsAuthorityValidator : IAuthorityValidator
{
	private readonly RequestContext _requestContext;

	public AdfsAuthorityValidator(RequestContext requestContext)
	{
		_requestContext = requestContext;
	}

	public async Task ValidateAuthorityAsync(AuthorityInfo authorityInfo)
	{
		if (authorityInfo.ValidateAuthority)
		{
			string resource = "https://" + authorityInfo.Host;
			string uriString = Constants.FormatAdfsWebFingerUrl(authorityInfo.Host, resource);
			HttpResponse httpResponse = await _requestContext.ServiceBundle.HttpManager.SendGetAsync(new Uri(uriString), null, _requestContext.Logger, retry: true, _requestContext.UserCancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			if (httpResponse.StatusCode != HttpStatusCode.OK)
			{
				_requestContext.Logger.Error($"Authority validation failed because the configured authority is invalid. Authority: {authorityInfo.CanonicalAuthority}");
				throw MsalServiceExceptionFactory.FromHttpResponse("invalid_authority", "Authority validation failed. ", httpResponse);
			}
			if (OAuth2Client.CreateResponse<AdfsWebFingerResponse>(httpResponse, _requestContext).Links.FirstOrDefault((LinksList a) => a.Rel.Equals("http://schemas.microsoft.com/rel/trusted-realm", StringComparison.OrdinalIgnoreCase) && a.Href.Equals(resource)) == null)
			{
				_requestContext.Logger.Error($"Authority validation failed because the configured authority is invalid. Authority: {authorityInfo.CanonicalAuthority}");
				throw new MsalClientException("invalid_authority", "invalid authority while getting the open id config endpoint. ");
			}
		}
	}
}
