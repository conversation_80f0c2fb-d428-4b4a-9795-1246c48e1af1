using System.Globalization;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.Instance;

internal class AdfsAuthority : Authority
{
	private const string TokenEndpointTemplate = "{0}oauth2/token";

	private const string AuthorizationEndpointTemplate = "{0}oauth2/authorize";

	private const string DeviceCodeEndpointTemplate = "{0}oauth2/devicecode";

	internal override string TenantId => null;

	public AdfsAuthority(AuthorityInfo authorityInfo)
		: base(authorityInfo)
	{
	}

	internal override Task<string> GetTokenEndpointAsync(RequestContext requestContext)
	{
		return Task.FromResult(string.Format(CultureInfo.InvariantCulture, "{0}oauth2/token", base.AuthorityInfo.CanonicalAuthority));
	}

	internal override Task<string> GetAuthorizationEndpointAsync(RequestContext requestContext)
	{
		return Task.FromResult(string.Format(CultureInfo.InvariantCulture, "{0}oauth2/authorize", base.AuthorityInfo.CanonicalAuthority));
	}

	internal override Task<string> GetDeviceCodeEndpointAsync(RequestContext requestContext)
	{
		return Task.FromResult(string.Format(CultureInfo.InvariantCulture, "{0}oauth2/devicecode", base.AuthorityInfo.CanonicalAuthority));
	}

	internal override string GetTenantedAuthority(string tenantId, bool forceSpecifiedTenant)
	{
		return base.AuthorityInfo.CanonicalAuthority.ToString();
	}
}
