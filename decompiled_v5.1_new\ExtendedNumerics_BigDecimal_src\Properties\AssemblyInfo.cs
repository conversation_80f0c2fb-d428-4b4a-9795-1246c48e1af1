using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.Versioning;

[assembly: Assembly<PERSON><PERSON><PERSON>y("<PERSON>, <PERSON>, <PERSON>")]
[assembly: AssemblyConfiguration("Release")]
[assembly: AssemblyCopyright("<PERSON> White. MIT License. See License.")]
[assembly: AssemblyDescription("An arbitrary precision decimal (base 10) floating point number type using a mantissa and exponent. Supports nth roots, trigonometric functions, logarithms, natural e, pi, etc.")]
[assembly: AssemblyFileVersion("2025.1001.2.129")]
[assembly: AssemblyInformationalVersion("2025.1001.2+8d3de28cf6ee59e9c6be864dacdba7d450c2a026")]
[assembly: AssemblyProduct("ExtendedNumerics.BigDecimal")]
[assembly: AssemblyTitle("ExtendedNumerics.BigDecimal")]
[assembly: AssemblyMetadata("RepositoryUrl", "https://github.com/AdamWhiteHat/BigDecimal")]
[assembly: AssemblyVersion("2025.1001.2.129")]
[module: RefSafetyRules(11)]
