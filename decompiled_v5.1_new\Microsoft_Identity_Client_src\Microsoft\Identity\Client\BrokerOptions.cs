using System;
using System.ComponentModel;
using Microsoft.Identity.Client.PlatformsCommon.Shared;

namespace Microsoft.Identity.Client;

public class BrokerOptions
{
	[Flags]
	public enum OperatingSystems
	{
		None = 0,
		Windows = 1
	}

	public OperatingSystems EnabledOn { get; }

	public string Title { get; set; }

	[EditorBrowsable(EditorBrowsableState.Never)]
	public bool MsaPassthrough { get; set; }

	public bool ListOperatingSystemAccounts { get; set; }

	public BrokerOptions(OperatingSystems enabledOn)
	{
		EnabledOn = enabledOn;
	}

	internal static BrokerOptions CreateFromWindowsOptions(WindowsBrokerOptions winOptions)
	{
		return new BrokerOptions(OperatingSystems.Windows)
		{
			Title = winOptions.HeaderText,
			MsaPassthrough = winOptions.MsaPassthrough,
			ListOperatingSystemAccounts = winOptions.ListWindowsWorkAndSchoolAccounts
		};
	}

	internal bool IsBrokerEnabledOnCurrentOs()
	{
		if (EnabledOn.HasFlag(OperatingSystems.Windows) && DesktopOsHelper.IsWindows())
		{
			return true;
		}
		return false;
	}
}
