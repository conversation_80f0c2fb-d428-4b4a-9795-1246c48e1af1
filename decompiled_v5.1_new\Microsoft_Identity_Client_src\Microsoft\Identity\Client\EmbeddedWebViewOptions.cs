using System;
using System.ComponentModel;
using Microsoft.Identity.Client.Core;

namespace Microsoft.Identity.Client;

public class EmbeddedWebViewOptions
{
	public string Title { get; set; }

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("In case when WebView2 is not available, MSAL.NET will fallback to legacy WebView.", true)]
	public string WebView2BrowserExecutableFolder { get; set; }

	public EmbeddedWebViewOptions()
	{
		ValidatePlatformAvailability();
	}

	internal static EmbeddedWebViewOptions GetDefaultOptions()
	{
		return new EmbeddedWebViewOptions();
	}

	internal void LogParameters(ILoggerAdapter logger)
	{
		logger.Info("WebView2Options configured");
		logger.Info(() => "Title: " + Title);
	}

	internal static void ValidatePlatformAvailability()
	{
	}
}
