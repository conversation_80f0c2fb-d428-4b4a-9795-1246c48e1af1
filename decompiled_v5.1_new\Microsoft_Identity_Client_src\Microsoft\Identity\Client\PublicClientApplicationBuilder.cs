using System;
using System.ComponentModel;
using Microsoft.Identity.Client.AppConfig;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.Kerberos;
using Microsoft.Identity.Client.PlatformsCommon.Factories;

namespace Microsoft.Identity.Client;

public sealed class PublicClientApplicationBuilder : AbstractApplicationBuilder<PublicClientApplicationBuilder>
{
	internal PublicClientApplicationBuilder(ApplicationConfiguration configuration)
		: base(configuration)
	{
	}

	public static PublicClientApplicationBuilder CreateWithApplicationOptions(PublicClientApplicationOptions options)
	{
		return new PublicClientApplicationBuilder(new ApplicationConfiguration(MsalClientType.PublicClient)).WithOptions(options).WithKerberosTicketClaim(options.KerberosServicePrincipalName, options.TicketContainer);
	}

	public static PublicClientApplicationBuilder Create(string clientId)
	{
		return new PublicClientApplicationBuilder(new ApplicationConfiguration(MsalClientType.PublicClient)).WithClientId(clientId);
	}

	internal PublicClientApplicationBuilder WithUserTokenLegacyCachePersistenceForTest(ILegacyCachePersistence legacyCachePersistence)
	{
		base.Config.UserTokenLegacyCachePersistenceForTest = legacyCachePersistence;
		return this;
	}

	public PublicClientApplicationBuilder WithDefaultRedirectUri()
	{
		base.Config.UseRecommendedDefaultRedirectUri = true;
		return this;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	public PublicClientApplicationBuilder WithMultiCloudSupport(bool enableMultiCloudSupport)
	{
		base.Config.MultiCloudSupportEnabled = enableMultiCloudSupport;
		return this;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	public PublicClientApplicationBuilder WithIosKeychainSecurityGroup(string keychainSecurityGroup)
	{
		return this;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("The desktop broker is not directly available in the MSAL package. Install the NuGet package Microsoft.Identity.Client.Broker and call the extension method .WithBroker(BrokerOptions). For details, see https://aka.ms/msal-net-wam", true)]
	public PublicClientApplicationBuilder WithBroker(bool enableBroker = true)
	{
		throw new PlatformNotSupportedException("The desktop broker is not directly available in the Microsoft.Identity.Client package. \n\rTo use it, install the NuGet package named Microsoft.Identity.Client.Broker and call the extension method .WithBroker(BrokerOptions) from namespace Microsoft.Identity.Client.Broker\n\rFor details see https://aka.ms/msal-net-wam ");
	}

	[Obsolete("This API has been replaced with WithBroker(BrokerOptions), which can be found in Microsoft.Identity.Client.Broker package. See https://aka.ms/msal-net-wam for details.", false)]
	public PublicClientApplicationBuilder WithWindowsBrokerOptions(WindowsBrokerOptions options)
	{
		WindowsBrokerOptions.ValidatePlatformAvailability();
		BrokerOptions brokerOptions = BrokerOptions.CreateFromWindowsOptions(options);
		base.Config.BrokerOptions = brokerOptions;
		return this;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	public PublicClientApplicationBuilder WithParentActivityOrWindow(Func<object> parentActivityOrWindowFunc)
	{
		return WithParentFunc(parentActivityOrWindowFunc);
	}

	private PublicClientApplicationBuilder WithParentFunc(Func<object> parentFunc)
	{
		base.Config.ParentActivityOrWindowFunc = parentFunc;
		return this;
	}

	public PublicClientApplicationBuilder WithOidcAuthority(string authorityUri)
	{
		ValidateUseOfExperimentalFeature("WithOidcAuthority");
		AuthorityInfo authorityInfo = AuthorityInfo.FromGenericAuthority(authorityUri);
		base.Config.Authority = Authority.CreateAuthority(authorityInfo);
		return this;
	}

	[CLSCompliant(false)]
	public PublicClientApplicationBuilder WithParentActivityOrWindow(Func<IntPtr> windowFunc)
	{
		if (windowFunc == null)
		{
			throw new ArgumentNullException("windowFunc");
		}
		return WithParentFunc(() => windowFunc());
	}

	public PublicClientApplicationBuilder WithKerberosTicketClaim(string servicePrincipalName, KerberosTicketContainer ticketContainer)
	{
		base.Config.KerberosServicePrincipalName = servicePrincipalName;
		base.Config.TicketContainer = ticketContainer;
		return this;
	}

	public bool IsBrokerAvailable()
	{
		return PlatformProxyFactory.CreatePlatformProxy(null).CreateBroker(base.Config, null).IsBrokerInstalledAndInvokable((base.Config.Authority?.AuthorityInfo?.AuthorityType).GetValueOrDefault());
	}

	public IPublicClientApplication Build()
	{
		return BuildConcrete();
	}

	internal PublicClientApplication BuildConcrete()
	{
		return new PublicClientApplication(BuildConfiguration());
	}

	internal override void Validate()
	{
		base.Validate();
		if (string.IsNullOrWhiteSpace(base.Config.RedirectUri))
		{
			base.Config.RedirectUri = PlatformProxyFactory.CreatePlatformProxy(null).GetDefaultRedirectUri(base.Config.ClientId, base.Config.UseRecommendedDefaultRedirectUri);
		}
		if (!Uri.TryCreate(base.Config.RedirectUri, UriKind.Absolute, out Uri _))
		{
			throw new InvalidOperationException(MsalErrorMessage.InvalidRedirectUriReceived(base.Config.RedirectUri));
		}
	}
}
