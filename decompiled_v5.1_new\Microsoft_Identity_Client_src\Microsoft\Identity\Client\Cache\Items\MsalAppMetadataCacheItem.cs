using System;
using System.Collections.Generic;
using System.Text.Json.Nodes;
using Microsoft.Identity.Client.Cache.Keys;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Cache.Items;

internal class MsalAppMetadataCacheItem : MsalItemWithAdditionalFields, IEquatable<MsalAppMetadataCacheItem>
{
	private Lazy<IiOSKey> iOSCacheKeyLazy;

	public string ClientId { get; }

	public string Environment { get; }

	public string FamilyId { get; }

	public string CacheKey { get; private set; }

	public IiOSKey iOSCacheKey => iOSCacheKeyLazy.Value;

	public MsalAppMetadataCacheItem(string clientId, string preferredCacheEnv, string familyId)
	{
		ClientId = clientId;
		Environment = preferredCacheEnv;
		FamilyId = familyId;
		InitCacheKey();
	}

	private void InitCacheKey()
	{
		CacheKey = $"{"appmetadata"}{45}{Environment}{45}{ClientId}".ToLowerInvariant();
		iOSCacheKeyLazy = new Lazy<IiOSKey>(InitiOSKey);
	}

	private IiOSKey InitiOSKey()
	{
		string iOSService = $"{"AppMetadata"}{45}{ClientId}".ToLowerInvariant();
		string iOSGeneric = "1";
		string iOSAccount = (Environment ?? "").ToLowerInvariant();
		int iOSType = 3001;
		return new IosKey(iOSAccount, iOSService, iOSGeneric, iOSType);
	}

	internal static MsalAppMetadataCacheItem FromJsonString(string json)
	{
		if (string.IsNullOrWhiteSpace(json))
		{
			return null;
		}
		return FromJObject(JsonHelper.ParseIntoJsonObject(json));
	}

	internal static MsalAppMetadataCacheItem FromJObject(JsonObject j)
	{
		string clientId = JsonHelper.ExtractExistingOrEmptyString(j, "client_id");
		string preferredCacheEnv = JsonHelper.ExtractExistingOrEmptyString(j, "environment");
		string familyId = JsonHelper.ExtractExistingOrEmptyString(j, "family_id");
		MsalAppMetadataCacheItem msalAppMetadataCacheItem = new MsalAppMetadataCacheItem(clientId, preferredCacheEnv, familyId);
		msalAppMetadataCacheItem.PopulateFieldsFromJObject(j);
		msalAppMetadataCacheItem.InitCacheKey();
		return msalAppMetadataCacheItem;
	}

	internal string ToJsonString()
	{
		return ToJObject().ToString();
	}

	internal override JsonObject ToJObject()
	{
		JsonObject jsonObject = base.ToJObject();
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "environment", Environment);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "client_id", ClientId);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "family_id", FamilyId);
		return jsonObject;
	}

	public override int GetHashCode()
	{
		return (((-1793347351 * -1521134295 + EqualityComparer<string>.Default.GetHashCode(ClientId)) * -1521134295 + EqualityComparer<string>.Default.GetHashCode(Environment)) * -1521134295 + EqualityComparer<string>.Default.GetHashCode(FamilyId)) * -1521134295 + EqualityComparer<string>.Default.GetHashCode(base.AdditionalFieldsJson);
	}

	public bool Equals(MsalAppMetadataCacheItem other)
	{
		if (ClientId == other.ClientId && Environment == other.Environment && FamilyId == other.FamilyId)
		{
			return base.AdditionalFieldsJson == other.AdditionalFieldsJson;
		}
		return false;
	}

	public override bool Equals(object obj)
	{
		if (obj is MsalAppMetadataCacheItem other)
		{
			return Equals(other);
		}
		return false;
	}
}
