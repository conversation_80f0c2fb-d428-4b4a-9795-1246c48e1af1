using System;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Region;

internal sealed class RegionManager : IRegionManager
{
	private class RegionInfo
	{
		public readonly string RegionDetails;

		public string Region { get; }

		public RegionAutodetectionSource RegionSource { get; }

		public RegionInfo(string region, RegionAutodetectionSource regionSource, string regionDetails)
		{
			Region = region;
			RegionSource = regionSource;
			RegionDetails = regionDetails;
		}
	}

	private const string ImdsEndpoint = "http://169.254.169.254/metadata/instance/compute/location";

	private const string DefaultApiVersion = "2020-06-01";

	private readonly IHttpManager _httpManager;

	private readonly int _imdsCallTimeoutMs;

	private static readonly SemaphoreSlim _lockDiscover = new SemaphoreSlim(1);

	private static string s_autoDiscoveredRegion;

	private static bool s_failedAutoDiscovery = false;

	private static string s_regionDiscoveryDetails;

	public RegionManager(IHttpManager httpManager, int imdsCallTimeout = 2000, bool shouldClearStaticCache = false)
	{
		_httpManager = httpManager;
		_imdsCallTimeoutMs = imdsCallTimeout;
		if (shouldClearStaticCache)
		{
			s_failedAutoDiscovery = false;
			s_autoDiscoveredRegion = null;
			s_regionDiscoveryDetails = null;
		}
	}

	public async Task<string> GetAzureRegionAsync(RequestContext requestContext)
	{
		string azureRegionConfig = requestContext.ServiceBundle.Config.AzureRegion;
		ILoggerAdapter logger = requestContext.Logger;
		if (string.IsNullOrEmpty(azureRegionConfig))
		{
			logger.Verbose(() => "[Region discovery] WithAzureRegion not configured. ");
			return null;
		}
		RegionInfo discoveredRegion = await DiscoverAndCacheAsync(logger, requestContext.UserCancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		RecordTelemetry(requestContext.ApiEvent, azureRegionConfig, discoveredRegion);
		if (IsAutoDiscoveryRequested(azureRegionConfig))
		{
			if (discoveredRegion.RegionSource != RegionAutodetectionSource.FailedAutoDiscovery)
			{
				logger.Verbose(() => "[Region discovery] Discovered Region " + discoveredRegion.Region);
				requestContext.ApiEvent.RegionUsed = discoveredRegion.Region;
				requestContext.ApiEvent.AutoDetectedRegion = discoveredRegion.Region;
				return discoveredRegion.Region;
			}
			logger.Verbose(() => "[Region discovery] " + s_regionDiscoveryDetails);
			requestContext.ApiEvent.RegionDiscoveryFailureReason = s_regionDiscoveryDetails;
			return null;
		}
		logger.Info(() => "[Region discovery] Returning user provided region: " + azureRegionConfig + ".");
		return azureRegionConfig;
	}

	private static bool IsAutoDiscoveryRequested(string azureRegionConfig)
	{
		return string.Equals(azureRegionConfig, "TryAutoDetect");
	}

	private static void RecordTelemetry(ApiEvent apiEvent, string azureRegionConfig, RegionInfo discoveredRegion)
	{
		if (IsTelemetryRecorded(apiEvent))
		{
			return;
		}
		bool num = IsAutoDiscoveryRequested(azureRegionConfig);
		apiEvent.RegionAutodetectionSource = discoveredRegion.RegionSource;
		if (num)
		{
			apiEvent.RegionUsed = discoveredRegion.Region;
			apiEvent.RegionOutcome = ((discoveredRegion.RegionSource == RegionAutodetectionSource.FailedAutoDiscovery) ? RegionOutcome.FallbackToGlobal : RegionOutcome.AutodetectSuccess);
			return;
		}
		apiEvent.RegionUsed = azureRegionConfig;
		apiEvent.RegionDiscoveryFailureReason = discoveredRegion.RegionDetails;
		if (discoveredRegion.RegionSource == RegionAutodetectionSource.FailedAutoDiscovery)
		{
			apiEvent.RegionOutcome = RegionOutcome.UserProvidedAutodetectionFailed;
		}
		if (!string.IsNullOrEmpty(discoveredRegion.Region))
		{
			apiEvent.RegionOutcome = (string.Equals(discoveredRegion.Region, azureRegionConfig, StringComparison.OrdinalIgnoreCase) ? RegionOutcome.UserProvidedValid : RegionOutcome.UserProvidedInvalid);
		}
	}

	private static bool IsTelemetryRecorded(ApiEvent apiEvent)
	{
		if (string.IsNullOrEmpty(apiEvent.RegionUsed) && apiEvent.RegionAutodetectionSource == RegionAutodetectionSource.None)
		{
			return apiEvent.RegionOutcome != RegionOutcome.None;
		}
		return true;
	}

	private async Task<RegionInfo> DiscoverAndCacheAsync(ILoggerAdapter logger, CancellationToken requestCancellationToken)
	{
		RegionInfo cachedRegion = GetCachedRegion(logger);
		if (cachedRegion != null)
		{
			return cachedRegion;
		}
		return await DiscoverAsync(logger, requestCancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}

	private async Task<RegionInfo> DiscoverAsync(ILoggerAdapter logger, CancellationToken requestCancellationToken)
	{
		RegionInfo result = null;
		await _lockDiscover.WaitAsync(requestCancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		try
		{
			RegionInfo cachedRegion = GetCachedRegion(logger);
			if (cachedRegion != null)
			{
				result = cachedRegion;
			}
			else
			{
				try
				{
					string region = Environment.GetEnvironmentVariable("REGION_NAME")?.Replace(" ", string.Empty).ToLowerInvariant();
					if (ValidateRegion(region, "REGION_NAME env variable", logger))
					{
						logger.Info(() => "[Region discovery] Region found in environment variable: " + region + ".");
						result = new RegionInfo(region, RegionAutodetectionSource.EnvVariable, null);
					}
					else
					{
						Dictionary<string, string> headers = new Dictionary<string, string> { { "Metadata", "true" } };
						Uri imdsUri = BuildImdsUri("2020-06-01");
						HttpResponse httpResponse = await _httpManager.SendGetAsync(imdsUri, headers, logger, retry: false, GetCancellationToken(requestCancellationToken)).ConfigureAwait(continueOnCapturedContext: false);
						if (httpResponse.StatusCode == HttpStatusCode.BadRequest)
						{
							string apiVersion = await GetImdsUriApiVersionAsync(logger, headers, requestCancellationToken).ConfigureAwait(continueOnCapturedContext: false);
							imdsUri = BuildImdsUri(apiVersion);
							httpResponse = await _httpManager.SendGetAsync(BuildImdsUri(apiVersion), headers, logger, retry: false, GetCancellationToken(requestCancellationToken)).ConfigureAwait(continueOnCapturedContext: false);
						}
						if (httpResponse.StatusCode == HttpStatusCode.OK && !httpResponse.Body.IsNullOrEmpty())
						{
							region = httpResponse.Body;
							if (ValidateRegion(region, "IMDS call to " + imdsUri.AbsoluteUri, logger))
							{
								logger.Info(() => $"[Region discovery] Call to local IMDS succeeded. Region: {region}. {DateTime.UtcNow}");
								result = new RegionInfo(region, RegionAutodetectionSource.Imds, null);
							}
						}
						else
						{
							s_regionDiscoveryDetails = $"Call to local IMDS failed with status code {httpResponse.StatusCode} or an empty response. {DateTime.UtcNow}";
							logger.Error("[Region discovery] " + s_regionDiscoveryDetails);
						}
					}
				}
				catch (Exception ex)
				{
					if (ex is MsalServiceException ex2 && "request_timeout".Equals(ex2?.ErrorCode))
					{
						s_regionDiscoveryDetails = $"Call to local IMDS timed out after {_imdsCallTimeoutMs}.";
						logger.Error("[Region discovery] " + s_regionDiscoveryDetails + ".");
					}
					else
					{
						s_regionDiscoveryDetails = $"IMDS call failed with exception {ex}. {DateTime.UtcNow}";
						logger.Error("[Region discovery] " + s_regionDiscoveryDetails);
					}
				}
			}
			if (result == null)
			{
				result = new RegionInfo(null, RegionAutodetectionSource.FailedAutoDiscovery, s_regionDiscoveryDetails);
			}
		}
		finally
		{
			s_failedAutoDiscovery = result.RegionSource == RegionAutodetectionSource.FailedAutoDiscovery;
			s_autoDiscoveredRegion = result.Region;
			s_regionDiscoveryDetails = result.RegionDetails;
			_lockDiscover.Release();
		}
		return result;
	}

	private static RegionInfo GetCachedRegion(ILoggerAdapter logger)
	{
		if (s_failedAutoDiscovery)
		{
			string autoDiscoveryError = $"[Region discovery] Auto-discovery failed in the past. Not trying again. {s_regionDiscoveryDetails}. {DateTime.UtcNow}";
			logger.Verbose(() => autoDiscoveryError);
			return new RegionInfo(null, RegionAutodetectionSource.FailedAutoDiscovery, autoDiscoveryError);
		}
		if (!s_failedAutoDiscovery && !string.IsNullOrEmpty(s_autoDiscoveredRegion))
		{
			logger.Info(() => "[Region discovery] Auto-discovery already ran and found " + s_autoDiscoveredRegion + ".");
			return new RegionInfo(s_autoDiscoveredRegion, RegionAutodetectionSource.Cache, null);
		}
		logger.Verbose(() => "[Region discovery] Auto-discovery did not run yet.");
		return null;
	}

	private static bool ValidateRegion(string region, string source, ILoggerAdapter logger)
	{
		if (string.IsNullOrEmpty(region))
		{
			logger.Verbose(() => $"[Region discovery] Region from {source} not detected. {DateTime.UtcNow}");
			return false;
		}
		if (!Uri.IsWellFormedUriString("https://" + region + ".login.microsoft.com", UriKind.Absolute))
		{
			logger.Error($"[Region discovery] Region from {source} was found but it's invalid: {region}. {DateTime.UtcNow}");
			return false;
		}
		return true;
	}

	private async Task<string> GetImdsUriApiVersionAsync(ILoggerAdapter logger, Dictionary<string, string> headers, CancellationToken userCancellationToken)
	{
		Uri endpoint = new Uri("http://169.254.169.254/metadata/instance/compute/location");
		HttpResponse response = await _httpManager.SendGetAsync(endpoint, headers, logger, retry: false, GetCancellationToken(userCancellationToken)).ConfigureAwait(continueOnCapturedContext: false);
		if (response.StatusCode == HttpStatusCode.BadRequest)
		{
			LocalImdsErrorResponse errorResponse = JsonHelper.DeserializeFromJson<LocalImdsErrorResponse>(response.Body);
			if (errorResponse != null && !errorResponse.NewestVersions.IsNullOrEmpty())
			{
				logger.Info(() => "[Region discovery] Updated the version for IMDS endpoint to: " + errorResponse.NewestVersions[0] + ".");
				return errorResponse.NewestVersions[0];
			}
			logger.Info(() => $"[Region discovery] The response is empty or does not contain the newest versions. {DateTime.UtcNow}");
		}
		logger.Info(() => $"[Region discovery] Failed to get the updated version for IMDS endpoint. HttpStatusCode: {response.StatusCode}. {DateTime.UtcNow}");
		throw MsalServiceExceptionFactory.FromImdsResponse("region_discovery_failed", "Region discovery for the instance failed. Region discovery can only be made if the service resides in Azure function or Azure VM. See https://aka.ms/msal-net-region-discovery for more details. ", response);
	}

	private static Uri BuildImdsUri(string apiVersion)
	{
		UriBuilder uriBuilder = new UriBuilder("http://169.254.169.254/metadata/instance/compute/location");
		uriBuilder.AppendQueryParameters("api-version=" + apiVersion);
		uriBuilder.AppendQueryParameters("format=text");
		return uriBuilder.Uri;
	}

	private CancellationToken GetCancellationToken(CancellationToken userCancellationToken)
	{
		CancellationTokenSource cancellationTokenSource = new CancellationTokenSource(_imdsCallTimeoutMs);
		return CancellationTokenSource.CreateLinkedTokenSource(userCancellationToken, cancellationTokenSource.Token).Token;
	}
}
