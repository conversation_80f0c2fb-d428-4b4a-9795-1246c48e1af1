using System;
using System.Globalization;
using System.Net.Http;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.ManagedIdentity;

internal class CloudShellManagedIdentitySource : AbstractManagedIdentity
{
	private readonly Uri _endpoint;

	private const string CloudShell = "Cloud Shell";

	public static AbstractManagedIdentity Create(RequestContext requestContext)
	{
		string msiEndpoint = EnvironmentVariables.MsiEndpoint;
		requestContext.Logger.Info(() => "[Managed Identity] Cloud shell managed identity is available.");
		Uri endpoint;
		try
		{
			endpoint = new Uri(msiEndpoint);
		}
		catch (FormatException innerException)
		{
			requestContext.Logger.Error("[Managed Identity] Invalid endpoint found for the environment variable MSI_ENDPOINT: " + msiEndpoint);
			string errorMessage = string.Format(CultureInfo.InvariantCulture, "[Managed Identity] The environment variable {0} contains an invalid Uri {1} in {2} managed identity source.", "MSI_ENDPOINT", msiEndpoint, "Cloud Shell");
			throw MsalServiceExceptionFactory.CreateManagedIdentityException("invalid_managed_identity_endpoint", errorMessage, innerException, ManagedIdentitySource.CloudShell, null);
		}
		requestContext.Logger.Verbose(() => "[Managed Identity] Creating cloud shell managed identity. Endpoint URI: " + msiEndpoint);
		return new CloudShellManagedIdentitySource(endpoint, requestContext);
	}

	private CloudShellManagedIdentitySource(Uri endpoint, RequestContext requestContext)
		: base(requestContext, ManagedIdentitySource.CloudShell)
	{
		_endpoint = endpoint;
		if (requestContext.ServiceBundle.Config.ManagedIdentityId.IsUserAssigned)
		{
			string errorMessage = string.Format(CultureInfo.InvariantCulture, "[Managed Identity] User assigned identity is not supported by the {0} Managed Identity. To authenticate with the system assigned identity omit the client id in ManagedIdentityApplicationBuilder.Create().", "Cloud Shell");
			throw MsalServiceExceptionFactory.CreateManagedIdentityException("user_assigned_managed_identity_not_supported", errorMessage, null, ManagedIdentitySource.CloudShell, null);
		}
	}

	protected override ManagedIdentityRequest CreateRequest(string resource)
	{
		return new ManagedIdentityRequest(HttpMethod.Post, _endpoint)
		{
			Headers = 
			{
				{ "ContentType", "application/x-www-form-urlencoded" },
				{ "Metadata", "true" }
			},
			BodyParameters = { { "resource", resource } }
		};
	}
}
