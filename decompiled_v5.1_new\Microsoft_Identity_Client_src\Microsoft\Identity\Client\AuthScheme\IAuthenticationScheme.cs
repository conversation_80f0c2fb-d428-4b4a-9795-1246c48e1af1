using System.Collections.Generic;
using Microsoft.Identity.Client.Cache.Items;

namespace Microsoft.Identity.Client.AuthScheme;

internal interface IAuthenticationScheme
{
	TokenType TelemetryTokenType { get; }

	string AuthorizationHeaderPrefix { get; }

	string KeyId { get; }

	string AccessTokenType { get; }

	IReadOnlyDictionary<string, string> GetTokenRequestParams();

	string FormatAccessToken(MsalAccessTokenCacheItem msalAccessTokenCacheItem);
}
