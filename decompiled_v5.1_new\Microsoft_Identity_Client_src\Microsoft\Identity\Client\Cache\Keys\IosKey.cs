namespace Microsoft.Identity.Client.Cache.Keys;

internal struct IosKey : <PERSON>i<PERSON><PERSON><PERSON>
{
	public string iOSAccount { get; }

	public string iOSGeneric { get; }

	public string iOSService { get; }

	public int iOSType { get; }

	internal IosKey(string iOSAccount, string iOSService, string iOSGeneric, int iOSType)
	{
		this.iOSAccount = iOSAccount;
		this.iOSGeneric = iOSGeneric;
		this.iOSService = iOSService;
		this.iOSType = iOSType;
	}
}
