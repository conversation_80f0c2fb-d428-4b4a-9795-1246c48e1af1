using System;
using System.ComponentModel;

namespace Microsoft.Identity.Client.Extensibility;

public static class AcquireTokenForClientBuilderExtensions
{
	[EditorBrowsable(EditorBrowsableState.Never)]
	public static AcquireTokenForClientParameterBuilder WithProofOfPosessionKeyId(this AcquireTokenForClientParameterBuilder builder, string keyId, string expectedTokenTypeFromAad = "Bearer")
	{
		if (string.IsNullOrEmpty(keyId))
		{
			throw new ArgumentNullException("keyId");
		}
		builder.ValidateUseOfExperimentalFeature("WithProofOfPosessionKeyId");
		builder.CommonParameters.AuthenticationScheme = new ExternalBoundTokenScheme(keyId, expectedTokenTypeFromAad);
		return builder;
	}
}
