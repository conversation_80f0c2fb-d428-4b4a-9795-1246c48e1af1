using System.Collections.Generic;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Core;

namespace Microsoft.Identity.Client.Cache;

internal interface ITokenCacheAccessor
{
	void SaveAccessToken(MsalAccessTokenCacheItem item);

	void SaveRefreshToken(MsalRefreshTokenCacheItem item);

	void SaveIdToken(MsalIdTokenCacheItem item);

	void SaveAccount(MsalAccountCacheItem item);

	void SaveAppMetadata(MsalAppMetadataCacheItem item);

	MsalIdTokenCacheItem GetIdToken(MsalAccessTokenCacheItem accessTokenCacheItem);

	MsalAccountCacheItem GetAccount(MsalAccountCacheItem accountCacheItem);

	MsalAppMetadataCacheItem GetAppMetadata(MsalAppMetadataCacheItem appMetadataItem);

	void DeleteAccessToken(MsalAccessTokenCacheItem item);

	void DeleteRefreshToken(MsalRefreshTokenCacheItem item);

	void DeleteIdToken(MsalIdTokenCacheItem item);

	void DeleteAccount(MsalAccountCacheItem item);

	List<MsalAccessTokenCacheItem> GetAllAccessTokens(string optionalPartitionKey = null, ILoggerAdapter requestlogger = null);

	List<MsalRefreshTokenCacheItem> GetAllRefreshTokens(string optionalPartitionKey = null, ILoggerAdapter requestlogger = null);

	List<MsalIdTokenCacheItem> GetAllIdTokens(string optionalPartitionKey = null, ILoggerAdapter requestlogger = null);

	List<MsalAccountCacheItem> GetAllAccounts(string optionalPartitionKey = null, ILoggerAdapter requestlogger = null);

	List<MsalAppMetadataCacheItem> GetAllAppMetadata();

	void Clear(ILoggerAdapter requestlogger = null);

	bool HasAccessOrRefreshTokens();
}
