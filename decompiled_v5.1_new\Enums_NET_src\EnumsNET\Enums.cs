using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Threading;
using EnumsNET.Numerics;
using EnumsNET.Utilities;

namespace EnumsNET;

public static class Enums
{
	internal static class Cache<TEnum> where TEnum : struct, Enum
	{
		public static readonly EnumCache Instance = Cacher.Create(typeof(TEnum)).CreateCache<TEnum>();
	}

	internal abstract class Cacher
	{
		public readonly Type EnumType;

		protected Cacher(Type enumType)
		{
			EnumType = enumType;
		}

		[MethodImpl(MethodImplOptions.NoInlining)]
		public static Cacher Create(Type enumType)
		{
			return Type.GetTypeCode(enumType) switch
			{
				TypeCode.Boolean => new Cacher<bool, UnderlyingOperations>(enumType), 
				TypeCode.Char => new Cacher<char, UnderlyingOperations>(enumType), 
				TypeCode.SByte => new Cacher<sbyte, UnderlyingOperations>(enumType), 
				TypeCode.Byte => new Cacher<byte, UnderlyingOperations>(enumType), 
				TypeCode.Int16 => new Cacher<short, UnderlyingOperations>(enumType), 
				TypeCode.UInt16 => new Cacher<ushort, UnderlyingOperations>(enumType), 
				TypeCode.Int32 => new Cacher<int, UnderlyingOperations>(enumType), 
				TypeCode.UInt32 => new Cacher<uint, UnderlyingOperations>(enumType), 
				TypeCode.Int64 => new Cacher<long, UnderlyingOperations>(enumType), 
				TypeCode.UInt64 => new Cacher<ulong, UnderlyingOperations>(enumType), 
				_ => ThrowUnderlyingTypeNotSupportedException(enumType), 
			};
		}

		[MethodImpl(MethodImplOptions.NoInlining)]
		private static Cacher ThrowUnderlyingTypeNotSupportedException(Type enumType)
		{
			throw new NotSupportedException($"Enum underlying type of {Enum.GetUnderlyingType(enumType)} is not supported");
		}

		public abstract EnumCache CreateCache<TEnum>() where TEnum : struct, Enum;
	}

	internal sealed class Cacher<TUnderlying, TUnderlyingOperations> : Cacher where TUnderlying : struct, IComparable<TUnderlying>, IEquatable<TUnderlying>, IConvertible where TUnderlyingOperations : struct, IUnderlyingOperations<TUnderlying>
	{
		public Cacher(Type enumType)
			: base(enumType)
		{
		}

		public override EnumCache CreateCache<TEnum>()
		{
			return CreateCache(new EnumBridge<TEnum, TUnderlying, TUnderlyingOperations>());
		}

		private EnumCache CreateCache(IEnumBridge<TUnderlying, TUnderlyingOperations> enumBridge)
		{
			Type enumType = EnumType;
			FieldInfo[] fields = enumType.GetFields(BindingFlags.Static | BindingFlags.Public);
			int num = HashHelpers.PowerOf2(Math.Max(fields.Length, 1));
			EnumMemberInternal<TUnderlying, TUnderlyingOperations>[] array = new EnumMemberInternal<TUnderlying, TUnderlyingOperations>[num];
			EnumMemberInternal<TUnderlying, TUnderlyingOperations>[] array2 = new EnumMemberInternal<TUnderlying, TUnderlyingOperations>[fields.Length];
			Dictionary<string, TUnderlying> dictionary = null;
			if (typeof(TUnderlying) == typeof(bool))
			{
				dictionary = new Dictionary<string, TUnderlying>();
				TUnderlying[] array3 = (TUnderlying[])Enum.GetValues(enumType);
				string[] names = Enum.GetNames(enumType);
				for (int i = 0; i < names.Length; i++)
				{
					dictionary.Add(names[i], array3[i]);
				}
			}
			TUnderlyingOperations val = default(TUnderlyingOperations);
			int num2 = 0;
			TUnderlying val2 = default(TUnderlying);
			for (int j = 0; j < fields.Length; j++)
			{
				FieldInfo fieldInfo = fields[j];
				string name = fieldInfo.Name;
				TUnderlying val3 = dictionary?[name] ?? ((TUnderlying)fieldInfo.GetValue(null));
				Attribute[] customAttributes = Attribute.GetCustomAttributes(fieldInfo, inherit: false);
				AttributeCollection attributeCollection = ((customAttributes.Length == 0) ? AttributeCollection.Empty : new AttributeCollection(customAttributes));
				EnumMemberInternal<TUnderlying, TUnderlyingOperations> enumMemberInternal = new EnumMemberInternal<TUnderlying, TUnderlyingOperations>(val3, name, attributeCollection);
				int num3 = j;
				bool flag = attributeCollection.Has<PrimaryEnumMemberAttribute>();
				while (num3 > 0 && (flag ? (!val.LessThan(array2[num3 - 1].Value, val3)) : val.LessThan(val3, array2[num3 - 1].Value)))
				{
					num3--;
				}
				if (num3 < j)
				{
					Array.Copy(array2, num3, array2, num3 + 1, j - num3);
				}
				array2[num3] = enumMemberInternal;
				ref EnumMemberInternal<TUnderlying, TUnderlyingOperations> reference;
				for (reference = ref array[val3.GetHashCode() & (num - 1)]; reference != null; reference = ref reference.Next)
				{
					if (reference.Value.Equals(val3))
					{
						if (flag)
						{
							enumMemberInternal.Next = reference.Next;
							reference.Next = null;
							reference = enumMemberInternal;
						}
						break;
					}
				}
				if (reference == null)
				{
					reference = enumMemberInternal;
					num2++;
					if (val.BitCount(val3) == 1)
					{
						val2 = val.Or(val2, val3);
					}
				}
			}
			object enumValidatorAttribute = GetEnumValidatorAttribute(enumType);
			bool flag2 = array2.Length != 0 && val.Subtract(array2[^1].Value, val.Create(num2 - 1)).Equals(array2[0].Value);
			if (!enumType.IsDefined(typeof(FlagsAttribute), inherit: false))
			{
				if (!flag2)
				{
					return new NonContiguousStandardEnumCache<TUnderlying, TUnderlyingOperations>(enumType, enumBridge, array2, array, val2, num2, enumValidatorAttribute);
				}
				return new ContiguousStandardEnumCache<TUnderlying, TUnderlyingOperations>(enumType, enumBridge, array2, array, val2, num2, enumValidatorAttribute);
			}
			return new FlagEnumCache<TUnderlying, TUnderlyingOperations>(enumType, enumBridge, array2, array, val2, num2, flag2, enumValidatorAttribute);
		}
	}

	internal static class UnsafeCache<TEnum>
	{
		public static readonly EnumCache? Instance = GetEnumCache(typeof(TEnum));
	}

	private const int s_startingCustomEnumFormatValue = 7;

	private static Func<EnumMember, string?>[] s_customEnumMemberFormatters = ArrayHelper.Empty<Func<EnumMember, string>>();

	private static EnumCache?[] s_enumCacheBuckets = new EnumCache[4];

	private static readonly object s_lockObject = new object();

	private static int s_enumCacheCount;

	internal static ValueCollection<EnumFormat> DefaultFormats
	{
		[MethodImpl(MethodImplOptions.AggressiveInlining)]
		get
		{
			return ValueCollection.Create(EnumFormat.Name, EnumFormat.UnderlyingValue);
		}
	}

	internal static ValueCollection<EnumFormat> NameFormat
	{
		[MethodImpl(MethodImplOptions.AggressiveInlining)]
		get
		{
			return ValueCollection.Create(EnumFormat.Name);
		}
	}

	public static EnumFormat RegisterCustomEnumFormat(Func<EnumMember, string?> enumMemberFormatter)
	{
		Preconditions.NotNull(enumMemberFormatter, "enumMemberFormatter");
		Func<EnumMember, string>[] array = s_customEnumMemberFormatters;
		Func<EnumMember, string>[] array2;
		do
		{
			array2 = array;
			array = new Func<EnumMember, string>[array2.Length + 1];
			array2.CopyTo(array, 0);
			array[array2.Length] = enumMemberFormatter;
		}
		while ((array = Interlocked.CompareExchange(ref s_customEnumMemberFormatters, array, array2)) != array2);
		return (EnumFormat)(array2.Length + 7);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	internal static bool EnumFormatIsValid(EnumFormat format)
	{
		return (uint)format <= (uint)(s_customEnumMemberFormatters.Length - 1 + 7);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	internal static string? CustomEnumMemberFormat(EnumMember? member, EnumFormat format)
	{
		if (member == null)
		{
			return null;
		}
		return s_customEnumMemberFormatters[(int)(format - 7)](member);
	}

	public static Type GetUnderlyingType<TEnum>() where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.UnderlyingType;
	}

	public static TypeCode GetTypeCode<TEnum>() where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.TypeCode;
	}

	public static int GetMemberCount<TEnum>(EnumMemberSelection selection = EnumMemberSelection.All) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.GetMemberCount(selection);
	}

	public static IReadOnlyList<EnumMember<TEnum>> GetMembers<TEnum>(EnumMemberSelection selection = EnumMemberSelection.All) where TEnum : struct, Enum
	{
		return UnsafeUtility.As<IReadOnlyList<EnumMember<TEnum>>>(Cache<TEnum>.Instance.GetMembers(selection));
	}

	public static IReadOnlyList<string> GetNames<TEnum>(EnumMemberSelection selection = EnumMemberSelection.All) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.GetNames(selection);
	}

	public static IReadOnlyList<TEnum> GetValues<TEnum>(EnumMemberSelection selection = EnumMemberSelection.All) where TEnum : struct, Enum
	{
		return UnsafeUtility.As<IReadOnlyList<TEnum>>(Cache<TEnum>.Instance.GetValues(selection));
	}

	public static TEnum ToObject<TEnum>(object value, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		TEnum source = default(TEnum);
		Cache<TEnum>.Instance.ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	[CLSCompliant(false)]
	public static TEnum ToObject<TEnum>(sbyte value, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		TEnum source = default(TEnum);
		Cache<TEnum>.Instance.ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ToObject<TEnum>(byte value, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		TEnum source = default(TEnum);
		Cache<TEnum>.Instance.ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ToObject<TEnum>(short value, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		TEnum source = default(TEnum);
		Cache<TEnum>.Instance.ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	[CLSCompliant(false)]
	public static TEnum ToObject<TEnum>(ushort value, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		TEnum source = default(TEnum);
		Cache<TEnum>.Instance.ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ToObject<TEnum>(int value, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		TEnum source = default(TEnum);
		Cache<TEnum>.Instance.ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	[CLSCompliant(false)]
	public static TEnum ToObject<TEnum>(uint value, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		TEnum source = default(TEnum);
		Cache<TEnum>.Instance.ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ToObject<TEnum>(long value, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		TEnum source = default(TEnum);
		Cache<TEnum>.Instance.ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	[CLSCompliant(false)]
	public static TEnum ToObject<TEnum>(ulong value, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		TEnum source = default(TEnum);
		Cache<TEnum>.Instance.ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static bool TryToObject<TEnum>(object? value, out TEnum result, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		result = default(TEnum);
		return Cache<TEnum>.Instance.TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	[CLSCompliant(false)]
	public static bool TryToObject<TEnum>(sbyte value, out TEnum result, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		result = default(TEnum);
		return Cache<TEnum>.Instance.TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	public static bool TryToObject<TEnum>(byte value, out TEnum result, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		result = default(TEnum);
		return Cache<TEnum>.Instance.TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	public static bool TryToObject<TEnum>(short value, out TEnum result, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		result = default(TEnum);
		return Cache<TEnum>.Instance.TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	[CLSCompliant(false)]
	public static bool TryToObject<TEnum>(ushort value, out TEnum result, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		result = default(TEnum);
		return Cache<TEnum>.Instance.TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	public static bool TryToObject<TEnum>(int value, out TEnum result, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		result = default(TEnum);
		return Cache<TEnum>.Instance.TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	[CLSCompliant(false)]
	public static bool TryToObject<TEnum>(uint value, out TEnum result, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		result = default(TEnum);
		return Cache<TEnum>.Instance.TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	public static bool TryToObject<TEnum>(long value, out TEnum result, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		result = default(TEnum);
		return Cache<TEnum>.Instance.TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	[CLSCompliant(false)]
	public static bool TryToObject<TEnum>(ulong value, out TEnum result, EnumValidation validation = EnumValidation.None) where TEnum : struct, Enum
	{
		result = default(TEnum);
		return Cache<TEnum>.Instance.TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	public static bool IsValid<TEnum>(this TEnum value, EnumValidation validation = EnumValidation.Default) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.IsValid(ref UnsafeUtility.As<TEnum, byte>(ref value), validation);
	}

	public static bool IsDefined<TEnum>(this TEnum value) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.IsDefined(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static TEnum Validate<TEnum>(this TEnum value, string paramName, EnumValidation validation = EnumValidation.Default) where TEnum : struct, Enum
	{
		Cache<TEnum>.Instance.Validate(ref UnsafeUtility.As<TEnum, byte>(ref value), paramName, validation);
		return value;
	}

	public static string AsString<TEnum>(this TEnum value) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.AsString(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static string AsString<TEnum>(this TEnum value, string? format) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.AsString(ref UnsafeUtility.As<TEnum, byte>(ref value), string.IsNullOrEmpty(format) ? "G" : format);
	}

	public static string? AsString<TEnum>(this TEnum value, EnumFormat format) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.AsString(ref UnsafeUtility.As<TEnum, byte>(ref value), format);
	}

	public static string? AsString<TEnum>(this TEnum value, EnumFormat format0, EnumFormat format1) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.AsString(ref UnsafeUtility.As<TEnum, byte>(ref value), ValueCollection.Create(format0, format1));
	}

	public static string? AsString<TEnum>(this TEnum value, EnumFormat format0, EnumFormat format1, EnumFormat format2) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.AsString(ref UnsafeUtility.As<TEnum, byte>(ref value), ValueCollection.Create(format0, format1, format2));
	}

	public static string? AsString<TEnum>(this TEnum value, params EnumFormat[]? formats) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.AsString(ref UnsafeUtility.As<TEnum, byte>(ref value), (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	public static bool TryFormat<TEnum>(this TEnum value, Span<char> destination, out int charsWritten) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.TryFormat(ref UnsafeUtility.As<TEnum, byte>(ref value), destination, out charsWritten);
	}

	public static bool TryFormat<TEnum>(this TEnum value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> format = default(ReadOnlySpan<char>)) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.TryFormat(ref UnsafeUtility.As<TEnum, byte>(ref value), destination, out charsWritten, (format.Length == 0) ? ((ReadOnlySpan<char>)"G") : format);
	}

	public static bool TryFormat<TEnum>(this TEnum value, Span<char> destination, out int charsWritten, params EnumFormat[]? formats) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.TryFormat(ref UnsafeUtility.As<TEnum, byte>(ref value), destination, out charsWritten, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	public static string Format<TEnum>(TEnum value, string format) where TEnum : struct, Enum
	{
		Preconditions.NotNull(format, "format");
		return Cache<TEnum>.Instance.AsString(ref UnsafeUtility.As<TEnum, byte>(ref value), format);
	}

	public static object GetUnderlyingValue<TEnum>(TEnum value) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.GetUnderlyingValue(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	[CLSCompliant(false)]
	public static sbyte ToSByte<TEnum>(TEnum value) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.ToSByte(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static byte ToByte<TEnum>(TEnum value) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.ToByte(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static short ToInt16<TEnum>(TEnum value) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.ToInt16(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	[CLSCompliant(false)]
	public static ushort ToUInt16<TEnum>(TEnum value) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.ToUInt16(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static int ToInt32<TEnum>(TEnum value) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.ToInt32(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	[CLSCompliant(false)]
	public static uint ToUInt32<TEnum>(TEnum value) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.ToUInt32(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static long ToInt64<TEnum>(TEnum value) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.ToInt64(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	[CLSCompliant(false)]
	public static ulong ToUInt64<TEnum>(TEnum value) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.ToUInt64(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static int GetHashCode<TEnum>(TEnum value) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.GetHashCode(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static bool Equals<TEnum>(this TEnum value, TEnum other) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.Equals(ref UnsafeUtility.As<TEnum, byte>(ref value), ref UnsafeUtility.As<TEnum, byte>(ref other));
	}

	public static int CompareTo<TEnum>(this TEnum value, TEnum other) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.CompareTo(ref UnsafeUtility.As<TEnum, byte>(ref value), ref UnsafeUtility.As<TEnum, byte>(ref other));
	}

	public static string? GetName<TEnum>(this TEnum value) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.GetMember(ref UnsafeUtility.As<TEnum, byte>(ref value))?.Name;
	}

	public static AttributeCollection? GetAttributes<TEnum>(this TEnum value) where TEnum : struct, Enum
	{
		return Cache<TEnum>.Instance.GetMember(ref UnsafeUtility.As<TEnum, byte>(ref value))?.Attributes;
	}

	public static EnumMember<TEnum>? GetMember<TEnum>(this TEnum value) where TEnum : struct, Enum
	{
		return UnsafeUtility.As<EnumMember<TEnum>>(Cache<TEnum>.Instance.GetMember(ref UnsafeUtility.As<TEnum, byte>(ref value))?.EnumMember);
	}

	public static EnumMember<TEnum>? GetMember<TEnum>(string name) where TEnum : struct, Enum
	{
		return GetMember<TEnum>(name, ignoreCase: false);
	}

	public static EnumMember<TEnum>? GetMember<TEnum>(string name, bool ignoreCase) where TEnum : struct, Enum
	{
		Preconditions.NotNull(name, "name");
		return UnsafeUtility.As<EnumMember<TEnum>>(Cache<TEnum>.Instance.GetMember(name, ignoreCase, NameFormat));
	}

	public static EnumMember<TEnum>? GetMember<TEnum>(string value, bool ignoreCase, EnumFormat format) where TEnum : struct, Enum
	{
		return GetMember<TEnum>(value, ignoreCase, ValueCollection.Create(format));
	}

	public static EnumMember<TEnum>? GetMember<TEnum>(string value, bool ignoreCase, EnumFormat format0, EnumFormat format1) where TEnum : struct, Enum
	{
		return GetMember<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1));
	}

	public static EnumMember<TEnum>? GetMember<TEnum>(string value, bool ignoreCase, EnumFormat format0, EnumFormat format1, EnumFormat format2) where TEnum : struct, Enum
	{
		return GetMember<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1, format2));
	}

	public static EnumMember<TEnum>? GetMember<TEnum>(string value, bool ignoreCase, params EnumFormat[]? formats) where TEnum : struct, Enum
	{
		return GetMember<TEnum>(value, ignoreCase, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : NameFormat);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static EnumMember<TEnum>? GetMember<TEnum>(string value, bool ignoreCase, ValueCollection<EnumFormat> formats) where TEnum : struct, Enum
	{
		Preconditions.NotNull(value, "value");
		return UnsafeUtility.As<EnumMember<TEnum>>(Cache<TEnum>.Instance.GetMember(value, ignoreCase, formats));
	}

	public static EnumMember<TEnum>? GetMember<TEnum>(ReadOnlySpan<char> name, bool ignoreCase = false) where TEnum : struct, Enum
	{
		return GetMember<TEnum>(name, ignoreCase, NameFormat);
	}

	public static EnumMember<TEnum>? GetMember<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format) where TEnum : struct, Enum
	{
		return GetMember<TEnum>(value, ignoreCase, ValueCollection.Create(format));
	}

	public static EnumMember<TEnum>? GetMember<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format0, EnumFormat format1) where TEnum : struct, Enum
	{
		return GetMember<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1));
	}

	public static EnumMember<TEnum>? GetMember<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format0, EnumFormat format1, EnumFormat format2) where TEnum : struct, Enum
	{
		return GetMember<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1, format2));
	}

	public static EnumMember<TEnum>? GetMember<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, params EnumFormat[]? formats) where TEnum : struct, Enum
	{
		return GetMember<TEnum>(value, ignoreCase, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : NameFormat);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static EnumMember<TEnum>? GetMember<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, ValueCollection<EnumFormat> formats) where TEnum : struct, Enum
	{
		return UnsafeUtility.As<EnumMember<TEnum>>(Cache<TEnum>.Instance.GetMember(value, ignoreCase, formats));
	}

	public static TEnum Parse<TEnum>(string value) where TEnum : struct, Enum
	{
		return Parse<TEnum>(value, ignoreCase: false);
	}

	public static TEnum Parse<TEnum>(string value, bool ignoreCase) where TEnum : struct, Enum
	{
		Preconditions.NotNull(value, "value");
		TEnum source = default(TEnum);
		Cache<TEnum>.Instance.Parse(value, ignoreCase, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum Parse<TEnum>(string value, bool ignoreCase, EnumFormat format) where TEnum : struct, Enum
	{
		return Parse<TEnum>(value, ignoreCase, ValueCollection.Create(format));
	}

	public static TEnum Parse<TEnum>(string value, bool ignoreCase, EnumFormat format0, EnumFormat format1) where TEnum : struct, Enum
	{
		return Parse<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1));
	}

	public static TEnum Parse<TEnum>(string value, bool ignoreCase, EnumFormat format0, EnumFormat format1, EnumFormat format2) where TEnum : struct, Enum
	{
		return Parse<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1, format2));
	}

	public static TEnum Parse<TEnum>(string value, bool ignoreCase, params EnumFormat[]? formats) where TEnum : struct, Enum
	{
		return Parse<TEnum>(value, ignoreCase, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static TEnum Parse<TEnum>(string value, bool ignoreCase, ValueCollection<EnumFormat> formats) where TEnum : struct, Enum
	{
		Preconditions.NotNull(value, "value");
		TEnum source = default(TEnum);
		Cache<TEnum>.Instance.Parse(value, ignoreCase, formats, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum Parse<TEnum>(ReadOnlySpan<char> value, bool ignoreCase = false) where TEnum : struct, Enum
	{
		TEnum source = default(TEnum);
		Cache<TEnum>.Instance.Parse(value, ignoreCase, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum Parse<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format) where TEnum : struct, Enum
	{
		return ParseInternal<TEnum>(value, ignoreCase, ValueCollection.Create(format));
	}

	public static TEnum Parse<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format0, EnumFormat format1) where TEnum : struct, Enum
	{
		return ParseInternal<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1));
	}

	public static TEnum Parse<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format0, EnumFormat format1, EnumFormat format2) where TEnum : struct, Enum
	{
		return ParseInternal<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1, format2));
	}

	public static TEnum Parse<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, params EnumFormat[]? formats) where TEnum : struct, Enum
	{
		return ParseInternal<TEnum>(value, ignoreCase, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static TEnum ParseInternal<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, ValueCollection<EnumFormat> formats = default(ValueCollection<EnumFormat>)) where TEnum : struct, Enum
	{
		TEnum source = default(TEnum);
		Cache<TEnum>.Instance.Parse(value, ignoreCase, formats, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static bool TryParse<TEnum>(string? value, out TEnum result) where TEnum : struct, Enum
	{
		return TryParse<TEnum>(value, ignoreCase: false, out result);
	}

	public static bool TryParse<TEnum>(string? value, bool ignoreCase, out TEnum result) where TEnum : struct, Enum
	{
		result = default(TEnum);
		return Cache<TEnum>.Instance.TryParse(value, ignoreCase, ref UnsafeUtility.As<TEnum, byte>(ref result));
	}

	public static bool TryParse<TEnum>(string? value, bool ignoreCase, out TEnum result, EnumFormat format) where TEnum : struct, Enum
	{
		return TryParse<TEnum>(value, ignoreCase, out result, ValueCollection.Create(format));
	}

	public static bool TryParse<TEnum>(string? value, bool ignoreCase, out TEnum result, EnumFormat format0, EnumFormat format1) where TEnum : struct, Enum
	{
		return TryParse<TEnum>(value, ignoreCase, out result, ValueCollection.Create(format0, format1));
	}

	public static bool TryParse<TEnum>(string? value, bool ignoreCase, out TEnum result, EnumFormat format0, EnumFormat format1, EnumFormat format2) where TEnum : struct, Enum
	{
		return TryParse<TEnum>(value, ignoreCase, out result, ValueCollection.Create(format0, format1, format2));
	}

	public static bool TryParse<TEnum>(string? value, bool ignoreCase, out TEnum result, params EnumFormat[]? formats) where TEnum : struct, Enum
	{
		return TryParse<TEnum>(value, ignoreCase, out result, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	public static bool TryParse<TEnum>(ReadOnlySpan<char> value, out TEnum result) where TEnum : struct, Enum
	{
		return TryParse<TEnum>(value, ignoreCase: false, out result);
	}

	public static bool TryParse<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, out TEnum result) where TEnum : struct, Enum
	{
		result = default(TEnum);
		return Cache<TEnum>.Instance.TryParse(value, ignoreCase, ref UnsafeUtility.As<TEnum, byte>(ref result));
	}

	public static bool TryParse<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, out TEnum result, EnumFormat format) where TEnum : struct, Enum
	{
		return TryParse<TEnum>(value, ignoreCase, out result, ValueCollection.Create(format));
	}

	public static bool TryParse<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, out TEnum result, EnumFormat format0, EnumFormat format1) where TEnum : struct, Enum
	{
		return TryParse<TEnum>(value, ignoreCase, out result, ValueCollection.Create(format0, format1));
	}

	public static bool TryParse<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, out TEnum result, EnumFormat format0, EnumFormat format1, EnumFormat format2) where TEnum : struct, Enum
	{
		return TryParse<TEnum>(value, ignoreCase, out result, ValueCollection.Create(format0, format1, format2));
	}

	public static bool TryParse<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, out TEnum result, params EnumFormat[]? formats) where TEnum : struct, Enum
	{
		return TryParse<TEnum>(value, ignoreCase, out result, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool TryParse<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, out TEnum result, ValueCollection<EnumFormat> formats) where TEnum : struct, Enum
	{
		result = default(TEnum);
		return Cache<TEnum>.Instance.TryParse(value, ignoreCase, ref UnsafeUtility.As<TEnum, byte>(ref result), formats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	internal static EnumCache GetCacheUnsafe<TEnum>()
	{
		return UnsafeCache<TEnum>.Instance ?? throw new ArgumentException("Type argument TEnum must be an enum");
	}

	public static Type GetUnderlyingTypeUnsafe<TEnum>()
	{
		return GetCacheUnsafe<TEnum>().UnderlyingType;
	}

	public static TypeCode GetTypeCodeUnsafe<TEnum>()
	{
		return GetCacheUnsafe<TEnum>().TypeCode;
	}

	public static int GetMemberCountUnsafe<TEnum>(EnumMemberSelection selection = EnumMemberSelection.All)
	{
		return GetCacheUnsafe<TEnum>().GetMemberCount(selection);
	}

	public static IReadOnlyList<EnumMember<TEnum>> GetMembersUnsafe<TEnum>(EnumMemberSelection selection = EnumMemberSelection.All)
	{
		return UnsafeUtility.As<IReadOnlyList<EnumMember<TEnum>>>(GetCacheUnsafe<TEnum>().GetMembers(selection));
	}

	public static IReadOnlyList<string> GetNamesUnsafe<TEnum>(EnumMemberSelection selection = EnumMemberSelection.All)
	{
		return GetCacheUnsafe<TEnum>().GetNames(selection);
	}

	public static IReadOnlyList<TEnum> GetValuesUnsafe<TEnum>(EnumMemberSelection selection = EnumMemberSelection.All)
	{
		return UnsafeUtility.As<IReadOnlyList<TEnum>>(GetCacheUnsafe<TEnum>().GetValues(selection));
	}

	public static TEnum ToObjectUnsafe<TEnum>(object value, EnumValidation validation = EnumValidation.None)
	{
		TEnum source = default(TEnum);
		GetCacheUnsafe<TEnum>().ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	[CLSCompliant(false)]
	public static TEnum ToObjectUnsafe<TEnum>(sbyte value, EnumValidation validation = EnumValidation.None)
	{
		TEnum source = default(TEnum);
		GetCacheUnsafe<TEnum>().ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ToObjectUnsafe<TEnum>(byte value, EnumValidation validation = EnumValidation.None)
	{
		TEnum source = default(TEnum);
		GetCacheUnsafe<TEnum>().ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ToObjectUnsafe<TEnum>(short value, EnumValidation validation = EnumValidation.None)
	{
		TEnum source = default(TEnum);
		GetCacheUnsafe<TEnum>().ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	[CLSCompliant(false)]
	public static TEnum ToObjectUnsafe<TEnum>(ushort value, EnumValidation validation = EnumValidation.None)
	{
		TEnum source = default(TEnum);
		GetCacheUnsafe<TEnum>().ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ToObjectUnsafe<TEnum>(int value, EnumValidation validation = EnumValidation.None)
	{
		TEnum source = default(TEnum);
		GetCacheUnsafe<TEnum>().ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	[CLSCompliant(false)]
	public static TEnum ToObjectUnsafe<TEnum>(uint value, EnumValidation validation = EnumValidation.None)
	{
		TEnum source = default(TEnum);
		GetCacheUnsafe<TEnum>().ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ToObjectUnsafe<TEnum>(long value, EnumValidation validation = EnumValidation.None)
	{
		TEnum source = default(TEnum);
		GetCacheUnsafe<TEnum>().ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	[CLSCompliant(false)]
	public static TEnum ToObjectUnsafe<TEnum>(ulong value, EnumValidation validation = EnumValidation.None)
	{
		TEnum source = default(TEnum);
		GetCacheUnsafe<TEnum>().ToObject(value, validation, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static bool TryToObjectUnsafe<TEnum>(object? value, out TEnum result, EnumValidation validation = EnumValidation.None)
	{
		result = default(TEnum);
		return GetCacheUnsafe<TEnum>().TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	[CLSCompliant(false)]
	public static bool TryToObjectUnsafe<TEnum>(sbyte value, out TEnum result, EnumValidation validation = EnumValidation.None)
	{
		result = default(TEnum);
		return GetCacheUnsafe<TEnum>().TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	public static bool TryToObjectUnsafe<TEnum>(byte value, out TEnum result, EnumValidation validation = EnumValidation.None)
	{
		result = default(TEnum);
		return GetCacheUnsafe<TEnum>().TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	public static bool TryToObjectUnsafe<TEnum>(short value, out TEnum result, EnumValidation validation = EnumValidation.None)
	{
		result = default(TEnum);
		return GetCacheUnsafe<TEnum>().TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	[CLSCompliant(false)]
	public static bool TryToObjectUnsafe<TEnum>(ushort value, out TEnum result, EnumValidation validation = EnumValidation.None)
	{
		result = default(TEnum);
		return GetCacheUnsafe<TEnum>().TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	public static bool TryToObjectUnsafe<TEnum>(int value, out TEnum result, EnumValidation validation = EnumValidation.None)
	{
		result = default(TEnum);
		return GetCacheUnsafe<TEnum>().TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	[CLSCompliant(false)]
	public static bool TryToObjectUnsafe<TEnum>(uint value, out TEnum result, EnumValidation validation = EnumValidation.None)
	{
		result = default(TEnum);
		return GetCacheUnsafe<TEnum>().TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	public static bool TryToObjectUnsafe<TEnum>(long value, out TEnum result, EnumValidation validation = EnumValidation.None)
	{
		result = default(TEnum);
		return GetCacheUnsafe<TEnum>().TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	[CLSCompliant(false)]
	public static bool TryToObjectUnsafe<TEnum>(ulong value, out TEnum result, EnumValidation validation = EnumValidation.None)
	{
		result = default(TEnum);
		return GetCacheUnsafe<TEnum>().TryToObject(value, ref UnsafeUtility.As<TEnum, byte>(ref result), validation);
	}

	public static bool IsValidUnsafe<TEnum>(TEnum value, EnumValidation validation = EnumValidation.Default)
	{
		return GetCacheUnsafe<TEnum>().IsValid(ref UnsafeUtility.As<TEnum, byte>(ref value), validation);
	}

	public static bool IsDefinedUnsafe<TEnum>(TEnum value)
	{
		return GetCacheUnsafe<TEnum>().IsDefined(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static TEnum ValidateUnsafe<TEnum>(TEnum value, string paramName, EnumValidation validation = EnumValidation.Default)
	{
		GetCacheUnsafe<TEnum>().Validate(ref UnsafeUtility.As<TEnum, byte>(ref value), paramName, validation);
		return value;
	}

	public static string AsStringUnsafe<TEnum>(TEnum value)
	{
		return GetCacheUnsafe<TEnum>().AsString(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static string AsStringUnsafe<TEnum>(TEnum value, string? format)
	{
		return GetCacheUnsafe<TEnum>().AsString(ref UnsafeUtility.As<TEnum, byte>(ref value), string.IsNullOrEmpty(format) ? "G" : format);
	}

	public static string? AsStringUnsafe<TEnum>(TEnum value, EnumFormat format)
	{
		return GetCacheUnsafe<TEnum>().AsString(ref UnsafeUtility.As<TEnum, byte>(ref value), format);
	}

	public static string? AsStringUnsafe<TEnum>(TEnum value, EnumFormat format0, EnumFormat format1)
	{
		return GetCacheUnsafe<TEnum>().AsString(ref UnsafeUtility.As<TEnum, byte>(ref value), ValueCollection.Create(format0, format1));
	}

	public static string? AsStringUnsafe<TEnum>(TEnum value, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return GetCacheUnsafe<TEnum>().AsString(ref UnsafeUtility.As<TEnum, byte>(ref value), ValueCollection.Create(format0, format1, format2));
	}

	public static string? AsStringUnsafe<TEnum>(TEnum value, params EnumFormat[]? formats)
	{
		return GetCacheUnsafe<TEnum>().AsString(ref UnsafeUtility.As<TEnum, byte>(ref value), (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	public static bool TryFormatUnsafe<TEnum>(TEnum value, Span<char> destination, out int charsWritten)
	{
		return GetCacheUnsafe<TEnum>().TryFormat(ref UnsafeUtility.As<TEnum, byte>(ref value), destination, out charsWritten);
	}

	public static bool TryFormatUnsafe<TEnum>(TEnum value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> format = default(ReadOnlySpan<char>))
	{
		return GetCacheUnsafe<TEnum>().TryFormat(ref UnsafeUtility.As<TEnum, byte>(ref value), destination, out charsWritten, (format.Length == 0) ? ((ReadOnlySpan<char>)"G") : format);
	}

	public static bool TryFormatUnsafe<TEnum>(TEnum value, Span<char> destination, out int charsWritten, params EnumFormat[]? formats)
	{
		return GetCacheUnsafe<TEnum>().TryFormat(ref UnsafeUtility.As<TEnum, byte>(ref value), destination, out charsWritten, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	public static string FormatUnsafe<TEnum>(TEnum value, string format)
	{
		Preconditions.NotNull(format, "format");
		return GetCacheUnsafe<TEnum>().AsString(ref UnsafeUtility.As<TEnum, byte>(ref value), format);
	}

	public static object GetUnderlyingValueUnsafe<TEnum>(TEnum value)
	{
		return GetCacheUnsafe<TEnum>().GetUnderlyingValue(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	[CLSCompliant(false)]
	public static sbyte ToSByteUnsafe<TEnum>(TEnum value)
	{
		return GetCacheUnsafe<TEnum>().ToSByte(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static byte ToByteUnsafe<TEnum>(TEnum value)
	{
		return GetCacheUnsafe<TEnum>().ToByte(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static short ToInt16Unsafe<TEnum>(TEnum value)
	{
		return GetCacheUnsafe<TEnum>().ToInt16(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	[CLSCompliant(false)]
	public static ushort ToUInt16Unsafe<TEnum>(TEnum value)
	{
		return GetCacheUnsafe<TEnum>().ToUInt16(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static int ToInt32Unsafe<TEnum>(TEnum value)
	{
		return GetCacheUnsafe<TEnum>().ToInt32(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	[CLSCompliant(false)]
	public static uint ToUInt32Unsafe<TEnum>(TEnum value)
	{
		return GetCacheUnsafe<TEnum>().ToUInt32(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static long ToInt64Unsafe<TEnum>(TEnum value)
	{
		return GetCacheUnsafe<TEnum>().ToInt64(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	[CLSCompliant(false)]
	public static ulong ToUInt64Unsafe<TEnum>(TEnum value)
	{
		return GetCacheUnsafe<TEnum>().ToUInt64(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static int GetHashCodeUnsafe<TEnum>(TEnum value)
	{
		return GetCacheUnsafe<TEnum>().GetHashCode(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static bool EqualsUnsafe<TEnum>(TEnum value, TEnum other)
	{
		return GetCacheUnsafe<TEnum>().Equals(ref UnsafeUtility.As<TEnum, byte>(ref value), ref UnsafeUtility.As<TEnum, byte>(ref other));
	}

	public static int CompareToUnsafe<TEnum>(TEnum value, TEnum other)
	{
		return GetCacheUnsafe<TEnum>().CompareTo(ref UnsafeUtility.As<TEnum, byte>(ref value), ref UnsafeUtility.As<TEnum, byte>(ref other));
	}

	public static string? GetNameUnsafe<TEnum>(TEnum value)
	{
		return GetCacheUnsafe<TEnum>().GetMember(ref UnsafeUtility.As<TEnum, byte>(ref value))?.Name;
	}

	public static AttributeCollection? GetAttributesUnsafe<TEnum>(TEnum value)
	{
		return GetCacheUnsafe<TEnum>().GetMember(ref UnsafeUtility.As<TEnum, byte>(ref value))?.Attributes;
	}

	public static EnumMember<TEnum>? GetMemberUnsafe<TEnum>(TEnum value)
	{
		return UnsafeUtility.As<EnumMember<TEnum>>(GetCacheUnsafe<TEnum>().GetMember(ref UnsafeUtility.As<TEnum, byte>(ref value))?.EnumMember);
	}

	public static EnumMember<TEnum>? GetMemberUnsafe<TEnum>(string name)
	{
		return GetMemberUnsafe<TEnum>(name, ignoreCase: false);
	}

	public static EnumMember<TEnum>? GetMemberUnsafe<TEnum>(string name, bool ignoreCase)
	{
		Preconditions.NotNull(name, "name");
		return UnsafeUtility.As<EnumMember<TEnum>>(GetCacheUnsafe<TEnum>().GetMember(name, ignoreCase, NameFormat));
	}

	public static EnumMember<TEnum>? GetMemberUnsafe<TEnum>(string value, bool ignoreCase, EnumFormat format)
	{
		return GetMemberUnsafe<TEnum>(value, ignoreCase, ValueCollection.Create(format));
	}

	public static EnumMember<TEnum>? GetMemberUnsafe<TEnum>(string value, bool ignoreCase, EnumFormat format0, EnumFormat format1)
	{
		return GetMemberUnsafe<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1));
	}

	public static EnumMember<TEnum>? GetMemberUnsafe<TEnum>(string value, bool ignoreCase, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return GetMemberUnsafe<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1, format2));
	}

	public static EnumMember<TEnum>? GetMemberUnsafe<TEnum>(string value, bool ignoreCase, params EnumFormat[]? formats)
	{
		return GetMemberUnsafe<TEnum>(value, ignoreCase, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : NameFormat);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static EnumMember<TEnum>? GetMemberUnsafe<TEnum>(string value, bool ignoreCase, ValueCollection<EnumFormat> formats)
	{
		Preconditions.NotNull(value, "value");
		return UnsafeUtility.As<EnumMember<TEnum>>(GetCacheUnsafe<TEnum>().GetMember(value, ignoreCase, formats));
	}

	public static EnumMember<TEnum>? GetMemberUnsafe<TEnum>(ReadOnlySpan<char> name, bool ignoreCase = false)
	{
		return GetMemberUnsafe<TEnum>(name, ignoreCase, DefaultFormats);
	}

	public static EnumMember<TEnum>? GetMemberUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format)
	{
		return GetMemberUnsafe<TEnum>(value, ignoreCase, ValueCollection.Create(format));
	}

	public static EnumMember<TEnum>? GetMemberUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format0, EnumFormat format1)
	{
		return GetMemberUnsafe<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1));
	}

	public static EnumMember<TEnum>? GetMemberUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return GetMemberUnsafe<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1, format2));
	}

	public static EnumMember<TEnum>? GetMemberUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, params EnumFormat[]? formats)
	{
		return GetMemberUnsafe<TEnum>(value, ignoreCase, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : NameFormat);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static EnumMember<TEnum>? GetMemberUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, ValueCollection<EnumFormat> formats)
	{
		return UnsafeUtility.As<EnumMember<TEnum>>(GetCacheUnsafe<TEnum>().GetMember(value, ignoreCase, formats));
	}

	public static TEnum ParseUnsafe<TEnum>(string value)
	{
		return ParseUnsafe<TEnum>(value, ignoreCase: false);
	}

	public static TEnum ParseUnsafe<TEnum>(string value, bool ignoreCase)
	{
		Preconditions.NotNull(value, "value");
		TEnum source = default(TEnum);
		GetCacheUnsafe<TEnum>().Parse(value, ignoreCase, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ParseUnsafe<TEnum>(string value, bool ignoreCase, EnumFormat format)
	{
		return ParseUnsafe<TEnum>(value, ignoreCase, ValueCollection.Create(format));
	}

	public static TEnum ParseUnsafe<TEnum>(string value, bool ignoreCase, EnumFormat format0, EnumFormat format1)
	{
		return ParseUnsafe<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1));
	}

	public static TEnum ParseUnsafe<TEnum>(string value, bool ignoreCase, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return ParseUnsafe<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1, format2));
	}

	public static TEnum ParseUnsafe<TEnum>(string value, bool ignoreCase, params EnumFormat[]? formats)
	{
		return ParseUnsafe<TEnum>(value, ignoreCase, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static TEnum ParseUnsafe<TEnum>(string value, bool ignoreCase, ValueCollection<EnumFormat> formats)
	{
		Preconditions.NotNull(value, "value");
		TEnum source = default(TEnum);
		GetCacheUnsafe<TEnum>().Parse(value, ignoreCase, formats, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ParseUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase = false)
	{
		TEnum source = default(TEnum);
		GetCacheUnsafe<TEnum>().Parse(value, ignoreCase, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ParseUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format)
	{
		return ParseUnsafe<TEnum>(value, ignoreCase, ValueCollection.Create(format));
	}

	public static TEnum ParseUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format0, EnumFormat format1)
	{
		return ParseUnsafe<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1));
	}

	public static TEnum ParseUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return ParseUnsafe<TEnum>(value, ignoreCase, ValueCollection.Create(format0, format1, format2));
	}

	public static TEnum ParseUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, params EnumFormat[]? formats)
	{
		return ParseUnsafe<TEnum>(value, ignoreCase, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static TEnum ParseUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, ValueCollection<EnumFormat> formats)
	{
		TEnum source = default(TEnum);
		GetCacheUnsafe<TEnum>().Parse(value, ignoreCase, formats, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static bool TryParseUnsafe<TEnum>(string? value, out TEnum result)
	{
		return TryParseUnsafe<TEnum>(value, ignoreCase: false, out result);
	}

	public static bool TryParseUnsafe<TEnum>(string? value, bool ignoreCase, out TEnum result)
	{
		result = default(TEnum);
		return GetCacheUnsafe<TEnum>().TryParse(value, ignoreCase, ref UnsafeUtility.As<TEnum, byte>(ref result));
	}

	public static bool TryParseUnsafe<TEnum>(string? value, bool ignoreCase, out TEnum result, EnumFormat format)
	{
		return TryParseUnsafe<TEnum>(value, ignoreCase, out result, ValueCollection.Create(format));
	}

	public static bool TryParseUnsafe<TEnum>(string? value, bool ignoreCase, out TEnum result, EnumFormat format0, EnumFormat format1)
	{
		return TryParseUnsafe<TEnum>(value, ignoreCase, out result, ValueCollection.Create(format0, format1));
	}

	public static bool TryParseUnsafe<TEnum>(string? value, bool ignoreCase, out TEnum result, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return TryParseUnsafe<TEnum>(value, ignoreCase, out result, ValueCollection.Create(format0, format1, format2));
	}

	public static bool TryParseUnsafe<TEnum>(string? value, bool ignoreCase, out TEnum result, params EnumFormat[]? formats)
	{
		return TryParseUnsafe<TEnum>(value, ignoreCase, out result, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	public static bool TryParseUnsafe<TEnum>(ReadOnlySpan<char> value, out TEnum result)
	{
		return TryParseUnsafe<TEnum>(value, ignoreCase: false, out result);
	}

	public static bool TryParseUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, out TEnum result)
	{
		result = default(TEnum);
		return GetCacheUnsafe<TEnum>().TryParse(value, ignoreCase, ref UnsafeUtility.As<TEnum, byte>(ref result));
	}

	public static bool TryParseUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, out TEnum result, EnumFormat format)
	{
		return TryParseUnsafe<TEnum>(value, ignoreCase, out result, ValueCollection.Create(format));
	}

	public static bool TryParseUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, out TEnum result, EnumFormat format0, EnumFormat format1)
	{
		return TryParseUnsafe<TEnum>(value, ignoreCase, out result, ValueCollection.Create(format0, format1));
	}

	public static bool TryParseUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, out TEnum result, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return TryParseUnsafe<TEnum>(value, ignoreCase, out result, ValueCollection.Create(format0, format1, format2));
	}

	public static bool TryParseUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, out TEnum result, params EnumFormat[]? formats)
	{
		return TryParseUnsafe<TEnum>(value, ignoreCase, out result, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool TryParseUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, out TEnum result, ValueCollection<EnumFormat> formats)
	{
		result = default(TEnum);
		return GetCacheUnsafe<TEnum>().TryParse(value, ignoreCase, ref UnsafeUtility.As<TEnum, byte>(ref result), formats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	internal static EnumCache GetCache(Type enumType)
	{
		Preconditions.NotNull(enumType, "enumType");
		EnumCache[] array = s_enumCacheBuckets;
		for (EnumCache enumCache = array[enumType.GetHashCode() & (array.Length - 1)]; enumCache != null; enumCache = enumCache.Next)
		{
			if (enumCache.EnumType.Equals(enumType))
			{
				return enumCache;
			}
		}
		return GetOrAddCache(enumType);
	}

	[MethodImpl(MethodImplOptions.NoInlining)]
	private static EnumCache GetOrAddCache(Type enumType)
	{
		lock (s_lockObject)
		{
			EnumCache[] array = s_enumCacheBuckets;
			ref EnumCache reference = ref array[enumType.GetHashCode() & (array.Length - 1)];
			for (EnumCache enumCache = reference; enumCache != null; enumCache = enumCache.Next)
			{
				if (enumCache.EnumType.Equals(enumType))
				{
					return enumCache;
				}
			}
			int num = s_enumCacheCount;
			if (array.Length == num)
			{
				int num2 = num << 1;
				EnumCache[] array2 = new EnumCache[num2];
				for (int i = 0; i < array.Length; i++)
				{
					EnumCache enumCache2 = array[i];
					while (enumCache2 != null)
					{
						EnumCache? next = enumCache2.Next;
						ref EnumCache reference2 = ref array2[enumCache2.EnumType.GetHashCode() & (num2 - 1)];
						enumCache2.Next = reference2;
						reference2 = enumCache2;
						enumCache2 = next;
					}
				}
				s_enumCacheBuckets = array2;
				reference = ref array2[enumType.GetHashCode() & (array2.Length - 1)];
			}
			EnumCache enumCache3 = GetEnumCache(enumType) ?? throw new ArgumentException("must be an enum type", "enumType");
			enumCache3.Next = reference;
			reference = enumCache3;
			s_enumCacheCount++;
			return enumCache3;
		}
	}

	public static Type GetUnderlyingType(Type enumType)
	{
		return GetCache(enumType).UnderlyingType;
	}

	public static TypeCode GetTypeCode(Type enumType)
	{
		return GetCache(enumType).TypeCode;
	}

	public static int GetMemberCount(Type enumType, EnumMemberSelection selection = EnumMemberSelection.All)
	{
		return GetCache(enumType).GetMemberCount(selection);
	}

	public static IReadOnlyList<EnumMember> GetMembers(Type enumType, EnumMemberSelection selection = EnumMemberSelection.All)
	{
		return GetCache(enumType).GetMembers(selection);
	}

	public static IReadOnlyList<string> GetNames(Type enumType, EnumMemberSelection selection = EnumMemberSelection.All)
	{
		return GetCache(enumType).GetNames(selection);
	}

	public static IReadOnlyList<object> GetValues(Type enumType, EnumMemberSelection selection = EnumMemberSelection.All)
	{
		return GetCache(enumType).GetValues(selection).GetNonGenericContainer();
	}

	public static object ToObject(Type enumType, object value, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).ToObject(value, validation);
	}

	[CLSCompliant(false)]
	public static object ToObject(Type enumType, sbyte value, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).ToObject(value, validation);
	}

	public static object ToObject(Type enumType, byte value, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).ToObject(value, validation);
	}

	public static object ToObject(Type enumType, short value, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).ToObject(value, validation);
	}

	[CLSCompliant(false)]
	public static object ToObject(Type enumType, ushort value, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).ToObject(value, validation);
	}

	public static object ToObject(Type enumType, int value, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).ToObject(value, validation);
	}

	[CLSCompliant(false)]
	public static object ToObject(Type enumType, uint value, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).ToObject(value, validation);
	}

	public static object ToObject(Type enumType, long value, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).ToObject(value, validation);
	}

	[CLSCompliant(false)]
	public static object ToObject(Type enumType, ulong value, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).ToObject(value, validation);
	}

	public static bool TryToObject(Type enumType, object? value, out object? result, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).TryToObject(value, out result, validation);
	}

	[CLSCompliant(false)]
	public static bool TryToObject(Type enumType, sbyte value, out object? result, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).TryToObject(value, out result, validation);
	}

	public static bool TryToObject(Type enumType, byte value, out object? result, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).TryToObject(value, out result, validation);
	}

	public static bool TryToObject(Type enumType, short value, out object? result, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).TryToObject(value, out result, validation);
	}

	[CLSCompliant(false)]
	public static bool TryToObject(Type enumType, ushort value, out object? result, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).TryToObject(value, out result, validation);
	}

	public static bool TryToObject(Type enumType, int value, out object? result, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).TryToObject(value, out result, validation);
	}

	[CLSCompliant(false)]
	public static bool TryToObject(Type enumType, uint value, out object? result, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).TryToObject(value, out result, validation);
	}

	public static bool TryToObject(Type enumType, long value, out object? result, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).TryToObject(value, out result, validation);
	}

	[CLSCompliant(false)]
	public static bool TryToObject(Type enumType, ulong value, out object? result, EnumValidation validation = EnumValidation.None)
	{
		return GetCache(enumType).TryToObject(value, out result, validation);
	}

	public static bool IsValid(Type enumType, object value, EnumValidation validation = EnumValidation.Default)
	{
		return GetCache(enumType).IsValid(value, validation);
	}

	public static bool IsDefined(Type enumType, object value)
	{
		return GetCache(enumType).IsDefined(value);
	}

	public static object Validate(Type enumType, object value, string paramName, EnumValidation validation = EnumValidation.Default)
	{
		return GetCache(enumType).Validate(value, paramName, validation);
	}

	public static string AsString(Type enumType, object value)
	{
		return GetCache(enumType).AsString(value);
	}

	public static string AsString(Type enumType, object value, string? format)
	{
		return GetCache(enumType).AsString(value, string.IsNullOrEmpty(format) ? "G" : format);
	}

	public static string? AsString(Type enumType, object value, EnumFormat format)
	{
		return GetCache(enumType).AsString(value, format);
	}

	public static string? AsString(Type enumType, object value, EnumFormat format0, EnumFormat format1)
	{
		return GetCache(enumType).AsString(value, ValueCollection.Create(format0, format1));
	}

	public static string? AsString(Type enumType, object value, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return GetCache(enumType).AsString(value, ValueCollection.Create(format0, format1, format2));
	}

	public static string? AsString(Type enumType, object value, params EnumFormat[]? formats)
	{
		return GetCache(enumType).AsString(value, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	public static bool TryFormat(Type enumType, object value, Span<char> destination, out int charsWritten)
	{
		return GetCache(enumType).TryFormat(value, destination, out charsWritten);
	}

	public static bool TryFormat(Type enumType, object value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> format = default(ReadOnlySpan<char>))
	{
		return GetCache(enumType).TryFormat(value, destination, out charsWritten, (format.Length == 0) ? ((ReadOnlySpan<char>)"G") : format);
	}

	public static bool TryFormat(Type enumType, object value, Span<char> destination, out int charsWritten, params EnumFormat[]? formats)
	{
		return GetCache(enumType).TryFormat(value, destination, out charsWritten, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	[return: NotNullIfNotNull("value")]
	public static string? Format(Type enumType, object value, string format)
	{
		Preconditions.NotNull(format, "format");
		return GetCache(enumType).AsString(value, format);
	}

	public static object GetUnderlyingValue(Type enumType, object value)
	{
		return GetCache(enumType).GetUnderlyingValue(value);
	}

	[CLSCompliant(false)]
	public static sbyte ToSByte(Type enumType, object value)
	{
		return GetCache(enumType).ToSByte(value);
	}

	public static byte ToByte(Type enumType, object value)
	{
		return GetCache(enumType).ToByte(value);
	}

	public static short ToInt16(Type enumType, object value)
	{
		return GetCache(enumType).ToInt16(value);
	}

	[CLSCompliant(false)]
	public static ushort ToUInt16(Type enumType, object value)
	{
		return GetCache(enumType).ToUInt16(value);
	}

	public static int ToInt32(Type enumType, object value)
	{
		return GetCache(enumType).ToInt32(value);
	}

	[CLSCompliant(false)]
	public static uint ToUInt32(Type enumType, object value)
	{
		return GetCache(enumType).ToUInt32(value);
	}

	public static long ToInt64(Type enumType, object value)
	{
		return GetCache(enumType).ToInt64(value);
	}

	[CLSCompliant(false)]
	public static ulong ToUInt64(Type enumType, object value)
	{
		return GetCache(enumType).ToUInt64(value);
	}

	public static bool Equals(Type enumType, object value, object other)
	{
		return GetCache(enumType).Equals(value, other);
	}

	public static int CompareTo(Type enumType, object value, object other)
	{
		return GetCache(enumType).CompareTo(value, other);
	}

	public static string? GetName(Type enumType, object value)
	{
		return GetCache(enumType).GetMember(value)?.Name;
	}

	public static AttributeCollection? GetAttributes(Type enumType, object value)
	{
		return GetCache(enumType).GetMember(value)?.Attributes;
	}

	public static EnumMember? GetMember(Type enumType, object value)
	{
		return GetCache(enumType).GetMember(value)?.EnumMember;
	}

	public static EnumMember? GetMember(Type enumType, string name)
	{
		return GetMember(enumType, name, ignoreCase: false);
	}

	public static EnumMember? GetMember(Type enumType, string name, bool ignoreCase)
	{
		Preconditions.NotNull(name, "name");
		return GetCache(enumType).GetMember(name, ignoreCase, NameFormat);
	}

	public static EnumMember? GetMember(Type enumType, string value, bool ignoreCase, EnumFormat format)
	{
		return GetMember(enumType, value, ignoreCase, ValueCollection.Create(format));
	}

	public static EnumMember? GetMember(Type enumType, string value, bool ignoreCase, EnumFormat format0, EnumFormat format1)
	{
		return GetMember(enumType, value, ignoreCase, ValueCollection.Create(format0, format1));
	}

	public static EnumMember? GetMember(Type enumType, string value, bool ignoreCase, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return GetMember(enumType, value, ignoreCase, ValueCollection.Create(format0, format1, format2));
	}

	public static EnumMember? GetMember(Type enumType, string value, bool ignoreCase, params EnumFormat[]? formats)
	{
		return GetMember(enumType, value, ignoreCase, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : NameFormat);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static EnumMember? GetMember(Type enumType, string value, bool ignoreCase, ValueCollection<EnumFormat> formats)
	{
		Preconditions.NotNull(value, "value");
		return GetCache(enumType).GetMember(value, ignoreCase, formats);
	}

	public static EnumMember? GetMember(Type enumType, ReadOnlySpan<char> name, bool ignoreCase = false)
	{
		return GetCache(enumType).GetMember(name, ignoreCase, NameFormat);
	}

	public static EnumMember? GetMember(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format)
	{
		return GetCache(enumType).GetMember(value, ignoreCase, ValueCollection.Create(format));
	}

	public static EnumMember? GetMember(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format0, EnumFormat format1)
	{
		return GetCache(enumType).GetMember(value, ignoreCase, ValueCollection.Create(format0, format1));
	}

	public static EnumMember? GetMember(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return GetCache(enumType).GetMember(value, ignoreCase, ValueCollection.Create(format0, format1, format2));
	}

	public static EnumMember? GetMember(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, params EnumFormat[]? formats)
	{
		return GetCache(enumType).GetMember(value, ignoreCase, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : NameFormat);
	}

	public static object Parse(Type enumType, string value)
	{
		return Parse(enumType, value, ignoreCase: false);
	}

	public static object Parse(Type enumType, string value, bool ignoreCase)
	{
		Preconditions.NotNull(value, "value");
		return GetCache(enumType).Parse(value, ignoreCase);
	}

	public static object Parse(Type enumType, string value, bool ignoreCase, EnumFormat format)
	{
		return Parse(enumType, value, ignoreCase, ValueCollection.Create(format));
	}

	public static object Parse(Type enumType, string value, bool ignoreCase, EnumFormat format0, EnumFormat format1)
	{
		return Parse(enumType, value, ignoreCase, ValueCollection.Create(format0, format1));
	}

	public static object Parse(Type enumType, string value, bool ignoreCase, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return Parse(enumType, value, ignoreCase, ValueCollection.Create(format0, format1, format2));
	}

	public static object Parse(Type enumType, string value, bool ignoreCase, params EnumFormat[]? formats)
	{
		return Parse(enumType, value, ignoreCase, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static object Parse(Type enumType, string value, bool ignoreCase, ValueCollection<EnumFormat> formats)
	{
		Preconditions.NotNull(value, "value");
		return GetCache(enumType).Parse(value, ignoreCase, formats);
	}

	public static object Parse(Type enumType, ReadOnlySpan<char> value, bool ignoreCase = false)
	{
		return GetCache(enumType).Parse(value, ignoreCase);
	}

	public static object Parse(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format)
	{
		return GetCache(enumType).Parse(value, ignoreCase, ValueCollection.Create(format));
	}

	public static object Parse(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format0, EnumFormat format1)
	{
		return GetCache(enumType).Parse(value, ignoreCase, ValueCollection.Create(format0, format1));
	}

	public static object Parse(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return GetCache(enumType).Parse(value, ignoreCase, ValueCollection.Create(format0, format1, format2));
	}

	public static object Parse(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, params EnumFormat[]? formats)
	{
		return GetCache(enumType).Parse(value, ignoreCase, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	public static bool TryParse(Type enumType, string? value, out object? result)
	{
		return TryParse(enumType, value, ignoreCase: false, out result);
	}

	public static bool TryParse(Type enumType, string? value, bool ignoreCase, out object? result)
	{
		return GetCache(enumType).TryParse(value, ignoreCase, out result);
	}

	public static bool TryParse(Type enumType, string? value, bool ignoreCase, out object? result, EnumFormat format)
	{
		return GetCache(enumType).TryParse(value, ignoreCase, out result, ValueCollection.Create(format));
	}

	public static bool TryParse(Type enumType, string? value, bool ignoreCase, out object? result, EnumFormat format0, EnumFormat format1)
	{
		return GetCache(enumType).TryParse(value, ignoreCase, out result, ValueCollection.Create(format0, format1));
	}

	public static bool TryParse(Type enumType, string? value, bool ignoreCase, out object? result, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return GetCache(enumType).TryParse(value, ignoreCase, out result, ValueCollection.Create(format0, format1, format2));
	}

	public static bool TryParse(Type enumType, string? value, bool ignoreCase, out object? result, params EnumFormat[]? formats)
	{
		return GetCache(enumType).TryParse(value, ignoreCase, out result, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	public static bool TryParse(Type enumType, ReadOnlySpan<char> value, out object? result)
	{
		return GetCache(enumType).TryParse(value, ignoreCase: false, out result);
	}

	public static bool TryParse(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, out object? result)
	{
		return GetCache(enumType).TryParse(value, ignoreCase, out result);
	}

	public static bool TryParse(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, out object? result, EnumFormat format)
	{
		return GetCache(enumType).TryParse(value, ignoreCase, out result, ValueCollection.Create(format));
	}

	public static bool TryParse(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, out object? result, EnumFormat format0, EnumFormat format1)
	{
		return GetCache(enumType).TryParse(value, ignoreCase, out result, ValueCollection.Create(format0, format1));
	}

	public static bool TryParse(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, out object? result, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return GetCache(enumType).TryParse(value, ignoreCase, out result, ValueCollection.Create(format0, format1, format2));
	}

	public static bool TryParse(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, out object? result, params EnumFormat[]? formats)
	{
		return GetCache(enumType).TryParse(value, ignoreCase, out result, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : DefaultFormats);
	}

	internal static EnumCache? GetEnumCache(Type enumType)
	{
		if (!enumType.IsEnum())
		{
			return null;
		}
		return (EnumCache)typeof(Cache<>).MakeGenericType(enumType).GetField("Instance", BindingFlags.Static | BindingFlags.Public).GetValue(null);
	}

	private static object? GetEnumValidatorAttribute(Type enumType)
	{
		Type type = typeof(IEnumValidatorAttribute<>).MakeGenericType(enumType);
		object[] customAttributes = enumType.GetCustomAttributes(inherit: false);
		foreach (object obj in customAttributes)
		{
			Type[] interfaces = obj.GetType().GetInterfaces();
			for (int j = 0; j < interfaces.Length; j++)
			{
				if (interfaces[j] == type)
				{
					return obj;
				}
			}
		}
		return null;
	}
}
