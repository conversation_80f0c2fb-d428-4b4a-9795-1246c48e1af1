using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal.Logger;
using Microsoft.Identity.Client.PlatformsCommon.Factories;

namespace Microsoft.Identity.Client;

public class SystemWebViewOptions
{
	public string HtmlMessageSuccess { get; set; }

	public string HtmlMessageError { get; set; }

	public Uri BrowserRedirectSuccess { get; set; }

	public Uri BrowserRedirectError { get; set; }

	public bool iOSHidePrivacyPrompt { get; set; }

	public Func<Uri, Task> OpenBrowserAsync { get; set; }

	public SystemWebViewOptions()
	{
		ValidatePlatformAvailability();
	}

	internal void LogParameters(ILoggerAdapter logger)
	{
		logger.Info($"DefaultBrowserOptions configured. HidePrivacyPrompt {iOSHidePrivacyPrompt}");
		if (logger.IsLoggingEnabled(LogLevel.Verbose))
		{
			logger.VerbosePii(() => "HtmlMessageSuccess " + HtmlMessageSuccess, () => "HtmlMessageSuccess? " + !string.IsNullOrEmpty(HtmlMessageSuccess));
			logger.VerbosePii(() => "HtmlMessageError " + HtmlMessageError, () => "HtmlMessageError? " + !string.IsNullOrEmpty(HtmlMessageError));
			logger.VerbosePii(() => "BrowserRedirectSuccess " + BrowserRedirectSuccess, () => "BrowserRedirectSuccess? " + (BrowserRedirectSuccess != null));
			logger.VerbosePii(() => "BrowserRedirectError " + BrowserRedirectError, () => "BrowserRedirectError? " + (BrowserRedirectError != null));
		}
	}

	internal static void ValidatePlatformAvailability()
	{
	}

	public static async Task OpenWithEdgeBrowserAsync(Uri uri)
	{
		if (uri == null)
		{
			throw new ArgumentNullException("uri");
		}
		string absoluteUri = uri.AbsoluteUri;
		if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
		{
			absoluteUri = absoluteUri.Replace("&", "^&");
			Process.Start(new ProcessStartInfo("cmd", "/c start microsoft-edge:" + absoluteUri)
			{
				CreateNoWindow = true
			});
		}
		else
		{
			await PlatformProxyFactory.CreatePlatformProxy(new NullLogger()).StartDefaultOsBrowserAsync(absoluteUri, isBrokerConfigured: true).ConfigureAwait(continueOnCapturedContext: false);
		}
	}

	public static async Task OpenWithChromeEdgeBrowserAsync(Uri uri)
	{
		if (uri == null)
		{
			throw new ArgumentNullException("uri");
		}
		string absoluteUri = uri.AbsoluteUri;
		if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
		{
			absoluteUri = absoluteUri.Replace("&", "^&");
			Process.Start(new ProcessStartInfo("cmd", "/c start msedge " + absoluteUri)
			{
				CreateNoWindow = true
			});
			return;
		}
		if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
		{
			await PlatformProxyFactory.CreatePlatformProxy(new NullLogger()).StartDefaultOsBrowserAsync(absoluteUri, isBrokerConfigured: true).ConfigureAwait(continueOnCapturedContext: false);
			return;
		}
		if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
		{
			Process.Start("msedge", absoluteUri);
			return;
		}
		throw new PlatformNotSupportedException(RuntimeInformation.OSDescription);
	}
}
