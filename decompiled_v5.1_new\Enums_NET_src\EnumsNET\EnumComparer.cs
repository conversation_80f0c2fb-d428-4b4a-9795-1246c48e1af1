using System;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace EnumsNET;

public abstract class EnumComparer : IEqualityComparer, IComparer
{
	private protected readonly EnumCache _enumCache;

	public static EnumComparer GetInstance(Type enumType)
	{
		return Enums.GetCache(enumType).EnumComparer;
	}

	private protected EnumComparer(EnumCache enumCache)
	{
		_enumCache = enumCache;
	}

	public new bool Equals(object? x, object? y)
	{
		if (x == null)
		{
			return y == null;
		}
		if (y != null)
		{
			return _enumCache.Equals(x, y);
		}
		return false;
	}

	public int GetHashCode(object? obj)
	{
		return obj?.GetHashCode() ?? 0;
	}

	public int Compare(object? x, object? y)
	{
		if (x == null)
		{
			if (y != null)
			{
				return -1;
			}
			return 0;
		}
		if (y == null)
		{
			return 1;
		}
		return _enumCache.CompareTo(x, y);
	}
}
public sealed class EnumComparer<TEnum> : EnumComparer, IEqualityComparer<TEnum>, IComparer<TEnum>
{
	public static EnumComparer<TEnum> Instance => UnsafeUtility.As<EnumComparer<TEnum>>(Enums.GetCacheUnsafe<TEnum>().EnumComparer);

	internal EnumComparer(EnumCache enumCache)
		: base(enumCache)
	{
	}

	public bool Equals(TEnum x, TEnum y)
	{
		return _enumCache.Equals(ref UnsafeUtility.As<TEnum, byte>(ref x), ref UnsafeUtility.As<TEnum, byte>(ref y));
	}

	public int GetHashCode(TEnum obj)
	{
		return _enumCache.GetHashCode(ref UnsafeUtility.As<TEnum, byte>(ref obj));
	}

	public int Compare(TEnum x, TEnum y)
	{
		return _enumCache.CompareTo(ref UnsafeUtility.As<TEnum, byte>(ref x), ref UnsafeUtility.As<TEnum, byte>(ref y));
	}
}
