using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.Versioning;

[assembly: AssemblyCompany("Jinjun Xie")]
[assembly: AssemblyConfiguration("Release")]
[assembly: AssemblyCopyright("Copyright Jinjun Xie 2016")]
[assembly: AssemblyDescription("Supported on dotnet standard2(Windows/Linux/OSX), provide similar interface to Google offical API.Quality and window control is supported.\r\nThe library use the native runtime and its performance should be better than System.IO.Compress.BrotliStream.\r\nFor more document,please visit https://github.com/XieJJ99/brotli.net.")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyInformationalVersion("*******")]
[assembly: AssemblyProduct("Brotli.Core")]
[assembly: AssemblyTitle("Brotli.Core")]
[assembly: AssemblyVersion("*******")]
