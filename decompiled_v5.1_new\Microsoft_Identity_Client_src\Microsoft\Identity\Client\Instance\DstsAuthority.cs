using System;
using System.Globalization;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.Instance;

internal class DstsAuthority : Authority
{
	public const string DstsCanonicalAuthorityTemplate = "https://{0}/dstsv2/{1}/";

	private const string TokenEndpointTemplate = "{0}oauth2/v2.0/token";

	private const string AuthorizationEndpointTemplate = "{0}oauth2/v2.0/authorize";

	private const string DeviceCodeEndpointTemplate = "{0}oauth2/v2.0/devicecode";

	internal override string TenantId { get; }

	public DstsAuthority(AuthorityInfo authorityInfo)
		: base(authorityInfo)
	{
		TenantId = AuthorityInfo.GetSecondPathSegment(base.AuthorityInfo.CanonicalAuthority);
	}

	internal override string GetTenantedAuthority(string tenantId, bool forceSpecifiedTenant = false)
	{
		if (!string.IsNullOrEmpty(tenantId) && (forceSpecifiedTenant || AadAuthority.IsCommonOrganizationsOrConsumersTenant(TenantId)))
		{
			Uri canonicalAuthority = base.AuthorityInfo.CanonicalAuthority;
			return string.Format(CultureInfo.InvariantCulture, "https://{0}/dstsv2/{1}/", canonicalAuthority.Authority, tenantId);
		}
		return base.AuthorityInfo.CanonicalAuthority.ToString();
	}

	internal override Task<string> GetTokenEndpointAsync(RequestContext requestContext)
	{
		return Task.FromResult(string.Format(CultureInfo.InvariantCulture, "{0}oauth2/v2.0/token", base.AuthorityInfo.CanonicalAuthority));
	}

	internal override Task<string> GetAuthorizationEndpointAsync(RequestContext requestContext)
	{
		return Task.FromResult(string.Format(CultureInfo.InvariantCulture, "{0}oauth2/v2.0/authorize", base.AuthorityInfo.CanonicalAuthority));
	}

	internal override Task<string> GetDeviceCodeEndpointAsync(RequestContext requestContext)
	{
		return Task.FromResult(string.Format(CultureInfo.InvariantCulture, "{0}oauth2/v2.0/devicecode", base.AuthorityInfo.CanonicalAuthority));
	}
}
