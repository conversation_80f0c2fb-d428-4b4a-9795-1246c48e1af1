using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Internal.Requests;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

internal interface ITokenCacheInternal : ITokenCache, ITokenCacheSerializer
{
	OptionalSemaphoreSlim Semaphore { get; }

	ILegacyCachePersistence LegacyPersistence { get; }

	ITokenCacheAccessor Accessor { get; }

	bool IsApplicationCache { get; }

	Task RemoveAccountAsync(IAccount account, AuthenticationRequestParameters requestParameters);

	Task<bool> StopLongRunningOboProcessAsync(string longRunningOboCacheKey, AuthenticationRequestParameters requestParameters);

	Task<IEnumerable<IAccount>> GetAccountsAsync(AuthenticationRequestParameters requestParameters);

	Task<Tuple<MsalAccessTokenCacheItem, MsalIdTokenCacheItem, Account>> SaveTokenResponseAsync(AuthenticationRequestParameters requestParams, MsalTokenResponse response);

	Task<MsalAccessTokenCacheItem> FindAccessTokenAsync(AuthenticationRequestParameters requestParams);

	MsalIdTokenCacheItem GetIdTokenCacheItem(MsalAccessTokenCacheItem msalAccessTokenCacheItem);

	Task<MsalRefreshTokenCacheItem> FindRefreshTokenAsync(AuthenticationRequestParameters requestParams, string familyId = null);

	Task<Account> GetAccountAssociatedWithAccessTokenAsync(AuthenticationRequestParameters requestParameters, MsalAccessTokenCacheItem msalAccessTokenCacheItem);

	Task<bool?> IsFociMemberAsync(AuthenticationRequestParameters requestParams, string familyId);

	void SetIosKeychainSecurityGroup(string securityGroup);

	Task OnAfterAccessAsync(TokenCacheNotificationArgs args);

	Task OnBeforeAccessAsync(TokenCacheNotificationArgs args);

	Task OnBeforeWriteAsync(TokenCacheNotificationArgs args);

	bool HasTokensNoLocks();

	bool IsAppSubscribedToSerializationEvents();
}
