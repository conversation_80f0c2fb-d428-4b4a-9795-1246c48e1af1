using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading.Tasks;

namespace Microsoft.Identity.Client;

public interface IConfidentialClientApplication : IClientApplicationBase, IApplicationBase
{
	ITokenCache AppTokenCache { get; }

	AcquireTokenByAuthorizationCodeParameterBuilder AcquireTokenByAuthorizationCode(IEnumerable<string> scopes, string authorizationCode);

	AcquireTokenForClientParameterBuilder AcquireTokenForClient(IEnumerable<string> scopes);

	AcquireTokenOnBehalfOfParameterBuilder AcquireTokenOnBehalfOf(IEnumerable<string> scopes, UserAssertion userAssertion);

	GetAuthorizationRequestUrlParameterBuilder GetAuthorizationRequestUrl(IEnumerable<string> scopes);

	[Obsolete("In confidential client apps use AcquireTokenSilent(scopes, account) instead.")]
	[EditorBrowsable(EditorBrowsableState.Never)]
	new AcquireTokenSilentParameterBuilder AcquireTokenSilent(IEnumerable<string> scopes, string loginHint);

	[Obsolete("Use GetAccountAsync(identifier) in web apps and web APIs, and use a token cache serializer for better security and performance. See https://aka.ms/msal-net-cca-token-cache-serialization.")]
	[EditorBrowsable(EditorBrowsableState.Never)]
	new Task<IEnumerable<IAccount>> GetAccountsAsync();

	[Obsolete("Use AcquireTokenOnBehalfOf instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	Task<AuthenticationResult> AcquireTokenOnBehalfOfAsync(IEnumerable<string> scopes, UserAssertion userAssertion);

	[Obsolete("Use AcquireTokenOnBehalfOf instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	Task<AuthenticationResult> AcquireTokenOnBehalfOfAsync(IEnumerable<string> scopes, UserAssertion userAssertion, string authority);

	[Obsolete("Use AcquireTokenByAuthorizationCode instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	Task<AuthenticationResult> AcquireTokenByAuthorizationCodeAsync(string authorizationCode, IEnumerable<string> scopes);

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenForClient instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	Task<AuthenticationResult> AcquireTokenForClientAsync(IEnumerable<string> scopes);

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenForClient instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	Task<AuthenticationResult> AcquireTokenForClientAsync(IEnumerable<string> scopes, bool forceRefresh);

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use GetAuthorizationRequestUrl instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	Task<Uri> GetAuthorizationRequestUrlAsync(IEnumerable<string> scopes, string loginHint, string extraQueryParameters);

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use GetAuthorizationRequestUrl instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	Task<Uri> GetAuthorizationRequestUrlAsync(IEnumerable<string> scopes, string redirectUri, string loginHint, string extraQueryParameters, IEnumerable<string> extraScopesToConsent, string authority);
}
