<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AssemblyName>System.Runtime.Caching</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <TargetFramework>netcoreapp8.0</TargetFramework>
  </PropertyGroup>
  <PropertyGroup>
    <LangVersion>12.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <RootNamespace />
  </PropertyGroup>
  <ItemGroup>
    <None Remove="ILLink.Substitutions.xml" />
    <EmbeddedResource Include="ILLink.Substitutions.xml" LogicalName="ILLink.Substitutions.xml" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Configuration.ConfigurationManager">
      <HintPath>..\..\Relesez5.1\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>