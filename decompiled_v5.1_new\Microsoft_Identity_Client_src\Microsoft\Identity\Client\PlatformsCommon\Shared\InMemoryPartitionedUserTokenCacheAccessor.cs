using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.PlatformsCommon.Shared;

internal class InMemoryPartitionedUserTokenCacheAccessor : ITokenCacheAccessor
{
	internal readonly ConcurrentDictionary<string, ConcurrentDictionary<string, MsalAccessTokenCacheItem>> AccessTokenCacheDictionary;

	internal readonly ConcurrentDictionary<string, ConcurrentDictionary<string, MsalRefreshTokenCacheItem>> RefreshTokenCacheDictionary;

	internal readonly ConcurrentDictionary<string, ConcurrentDictionary<string, MsalIdTokenCacheItem>> IdTokenCacheDictionary;

	internal readonly ConcurrentDictionary<string, ConcurrentDictionary<string, MsalAccountCacheItem>> AccountCacheDictionary;

	internal readonly ConcurrentDictionary<string, MsalAppMetadataCacheItem> AppMetadataDictionary;

	private static readonly ConcurrentDictionary<string, ConcurrentDictionary<string, MsalAccessTokenCacheItem>> s_accessTokenCacheDictionary = new ConcurrentDictionary<string, ConcurrentDictionary<string, MsalAccessTokenCacheItem>>();

	private static readonly ConcurrentDictionary<string, ConcurrentDictionary<string, MsalRefreshTokenCacheItem>> s_refreshTokenCacheDictionary = new ConcurrentDictionary<string, ConcurrentDictionary<string, MsalRefreshTokenCacheItem>>();

	private static readonly ConcurrentDictionary<string, ConcurrentDictionary<string, MsalIdTokenCacheItem>> s_idTokenCacheDictionary = new ConcurrentDictionary<string, ConcurrentDictionary<string, MsalIdTokenCacheItem>>();

	private static readonly ConcurrentDictionary<string, ConcurrentDictionary<string, MsalAccountCacheItem>> s_accountCacheDictionary = new ConcurrentDictionary<string, ConcurrentDictionary<string, MsalAccountCacheItem>>();

	private static readonly ConcurrentDictionary<string, MsalAppMetadataCacheItem> s_appMetadataDictionary = new ConcurrentDictionary<string, MsalAppMetadataCacheItem>();

	protected readonly ILoggerAdapter _logger;

	private readonly CacheOptions _tokenCacheAccessorOptions;

	public InMemoryPartitionedUserTokenCacheAccessor(ILoggerAdapter logger, CacheOptions tokenCacheAccessorOptions)
	{
		_logger = logger ?? throw new ArgumentNullException("logger");
		_tokenCacheAccessorOptions = tokenCacheAccessorOptions ?? new CacheOptions();
		if (_tokenCacheAccessorOptions.UseSharedCache)
		{
			AccessTokenCacheDictionary = s_accessTokenCacheDictionary;
			RefreshTokenCacheDictionary = s_refreshTokenCacheDictionary;
			IdTokenCacheDictionary = s_idTokenCacheDictionary;
			AccountCacheDictionary = s_accountCacheDictionary;
			AppMetadataDictionary = s_appMetadataDictionary;
		}
		else
		{
			AccessTokenCacheDictionary = new ConcurrentDictionary<string, ConcurrentDictionary<string, MsalAccessTokenCacheItem>>();
			RefreshTokenCacheDictionary = new ConcurrentDictionary<string, ConcurrentDictionary<string, MsalRefreshTokenCacheItem>>();
			IdTokenCacheDictionary = new ConcurrentDictionary<string, ConcurrentDictionary<string, MsalIdTokenCacheItem>>();
			AccountCacheDictionary = new ConcurrentDictionary<string, ConcurrentDictionary<string, MsalAccountCacheItem>>();
			AppMetadataDictionary = new ConcurrentDictionary<string, MsalAppMetadataCacheItem>();
		}
	}

	public void SaveAccessToken(MsalAccessTokenCacheItem item)
	{
		string cacheKey = item.CacheKey;
		string keyFromCachedItem = CacheKeyFactory.GetKeyFromCachedItem(item);
		AccessTokenCacheDictionary.GetOrAdd(keyFromCachedItem, new ConcurrentDictionary<string, MsalAccessTokenCacheItem>())[cacheKey] = item;
	}

	public void SaveRefreshToken(MsalRefreshTokenCacheItem item)
	{
		string cacheKey = item.CacheKey;
		string keyFromCachedItem = CacheKeyFactory.GetKeyFromCachedItem(item);
		RefreshTokenCacheDictionary.GetOrAdd(keyFromCachedItem, new ConcurrentDictionary<string, MsalRefreshTokenCacheItem>())[cacheKey] = item;
	}

	public void SaveIdToken(MsalIdTokenCacheItem item)
	{
		string cacheKey = item.CacheKey;
		string keyFromCachedItem = CacheKeyFactory.GetKeyFromCachedItem(item);
		IdTokenCacheDictionary.GetOrAdd(keyFromCachedItem, new ConcurrentDictionary<string, MsalIdTokenCacheItem>())[cacheKey] = item;
	}

	public void SaveAccount(MsalAccountCacheItem item)
	{
		string cacheKey = item.CacheKey;
		string keyFromCachedItem = CacheKeyFactory.GetKeyFromCachedItem(item);
		AccountCacheDictionary.GetOrAdd(keyFromCachedItem, new ConcurrentDictionary<string, MsalAccountCacheItem>())[cacheKey] = item;
	}

	public void SaveAppMetadata(MsalAppMetadataCacheItem item)
	{
		string cacheKey = item.CacheKey;
		AppMetadataDictionary[cacheKey] = item;
	}

	public MsalIdTokenCacheItem GetIdToken(MsalAccessTokenCacheItem accessTokenCacheItem)
	{
		string idTokenKeyFromCachedItem = CacheKeyFactory.GetIdTokenKeyFromCachedItem(accessTokenCacheItem);
		IdTokenCacheDictionary.TryGetValue(idTokenKeyFromCachedItem, out var value);
		if (value != null && value.TryGetValue(accessTokenCacheItem.GetIdTokenItem().CacheKey, out var value2))
		{
			return value2;
		}
		_logger.WarningPii("[Internal cache] Could not find an id token for the access token with key " + accessTokenCacheItem.CacheKey, "[Internal cache] Could not find an id token for the access token for realm " + accessTokenCacheItem.TenantId + " ");
		return null;
	}

	public MsalAccountCacheItem GetAccount(MsalAccountCacheItem accountCacheItem)
	{
		string keyFromAccount = CacheKeyFactory.GetKeyFromAccount(accountCacheItem);
		AccountCacheDictionary.TryGetValue(keyFromAccount, out var value);
		MsalAccountCacheItem value2 = null;
		value?.TryGetValue(accountCacheItem.CacheKey, out value2);
		return value2;
	}

	public MsalAppMetadataCacheItem GetAppMetadata(MsalAppMetadataCacheItem appMetadataItem)
	{
		AppMetadataDictionary.TryGetValue(appMetadataItem.CacheKey, out var value);
		return value;
	}

	public void DeleteAccessToken(MsalAccessTokenCacheItem item)
	{
		string keyFromCachedItem = CacheKeyFactory.GetKeyFromCachedItem(item);
		AccessTokenCacheDictionary.TryGetValue(keyFromCachedItem, out var value);
		if (value == null || !value.TryRemove(item.CacheKey, out var _))
		{
			_logger.InfoPii(() => "[Internal cache] Cannot delete access token because it was not found in the cache. Key " + item.CacheKey + ".", () => "[Internal cache] Cannot delete access token because it was not found in the cache.");
		}
	}

	public void DeleteRefreshToken(MsalRefreshTokenCacheItem item)
	{
		string keyFromCachedItem = CacheKeyFactory.GetKeyFromCachedItem(item);
		RefreshTokenCacheDictionary.TryGetValue(keyFromCachedItem, out var value);
		if (value == null || !value.TryRemove(item.CacheKey, out var _))
		{
			_logger.InfoPii(() => "[Internal cache] Cannot delete refresh token because it was not found in the cache. Key " + item.CacheKey + ".", () => "[Internal cache] Cannot delete refresh token because it was not found in the cache.");
		}
	}

	public void DeleteIdToken(MsalIdTokenCacheItem item)
	{
		string keyFromCachedItem = CacheKeyFactory.GetKeyFromCachedItem(item);
		IdTokenCacheDictionary.TryGetValue(keyFromCachedItem, out var value);
		if (value == null || !value.TryRemove(item.CacheKey, out var _))
		{
			_logger.InfoPii(() => "[Internal cache] Cannot delete ID token because it was not found in the cache. Key " + item.CacheKey + ".", () => "[Internal cache] Cannot delete ID token because it was not found in the cache.");
		}
	}

	public void DeleteAccount(MsalAccountCacheItem item)
	{
		string keyFromCachedItem = CacheKeyFactory.GetKeyFromCachedItem(item);
		AccountCacheDictionary.TryGetValue(keyFromCachedItem, out var value);
		if (value == null || !value.TryRemove(item.CacheKey, out var _))
		{
			_logger.InfoPii(() => "[Internal cache] Cannot delete account because it was not found in the cache. Key " + item.CacheKey + ".", () => "[Internal cache] Cannot delete account because it was not found in the cache");
		}
	}

	public virtual List<MsalAccessTokenCacheItem> GetAllAccessTokens(string partitionKey = null, ILoggerAdapter requestlogger = null)
	{
		(requestlogger ?? _logger).Always($"[Internal cache] Total number of cache partitions found while getting access tokens: {AccessTokenCacheDictionary.Count}");
		if (string.IsNullOrEmpty(partitionKey))
		{
			return (from kv in AccessTokenCacheDictionary.SelectMany((KeyValuePair<string, ConcurrentDictionary<string, MsalAccessTokenCacheItem>> dict) => dict.Value)
				select kv.Value).ToList();
		}
		AccessTokenCacheDictionary.TryGetValue(partitionKey, out var value);
		return value?.Select((KeyValuePair<string, MsalAccessTokenCacheItem> kv) => kv.Value)?.ToList() ?? CollectionHelpers.GetEmptyList<MsalAccessTokenCacheItem>();
	}

	public virtual List<MsalRefreshTokenCacheItem> GetAllRefreshTokens(string partitionKey = null, ILoggerAdapter requestlogger = null)
	{
		(requestlogger ?? _logger).Always($"[Internal cache] Total number of cache partitions found while getting refresh tokens: {RefreshTokenCacheDictionary.Count}");
		if (string.IsNullOrEmpty(partitionKey))
		{
			return (from kv in RefreshTokenCacheDictionary.SelectMany((KeyValuePair<string, ConcurrentDictionary<string, MsalRefreshTokenCacheItem>> dict) => dict.Value)
				select kv.Value).ToList();
		}
		RefreshTokenCacheDictionary.TryGetValue(partitionKey, out var value);
		return value?.Select((KeyValuePair<string, MsalRefreshTokenCacheItem> kv) => kv.Value)?.ToList() ?? CollectionHelpers.GetEmptyList<MsalRefreshTokenCacheItem>();
	}

	public virtual List<MsalIdTokenCacheItem> GetAllIdTokens(string partitionKey = null, ILoggerAdapter requestlogger = null)
	{
		if (string.IsNullOrEmpty(partitionKey))
		{
			return (from kv in IdTokenCacheDictionary.SelectMany((KeyValuePair<string, ConcurrentDictionary<string, MsalIdTokenCacheItem>> dict) => dict.Value)
				select kv.Value).ToList();
		}
		IdTokenCacheDictionary.TryGetValue(partitionKey, out var value);
		return value?.Select((KeyValuePair<string, MsalIdTokenCacheItem> kv) => kv.Value)?.ToList() ?? CollectionHelpers.GetEmptyList<MsalIdTokenCacheItem>();
	}

	public virtual List<MsalAccountCacheItem> GetAllAccounts(string partitionKey = null, ILoggerAdapter requestlogger = null)
	{
		if (string.IsNullOrEmpty(partitionKey))
		{
			return (from kv in AccountCacheDictionary.SelectMany((KeyValuePair<string, ConcurrentDictionary<string, MsalAccountCacheItem>> dict) => dict.Value)
				select kv.Value).ToList();
		}
		AccountCacheDictionary.TryGetValue(partitionKey, out var value);
		return value?.Select((KeyValuePair<string, MsalAccountCacheItem> kv) => kv.Value)?.ToList() ?? CollectionHelpers.GetEmptyList<MsalAccountCacheItem>();
	}

	public virtual List<MsalAppMetadataCacheItem> GetAllAppMetadata()
	{
		return AppMetadataDictionary.Select((KeyValuePair<string, MsalAppMetadataCacheItem> kv) => kv.Value).ToList();
	}

	public void SetiOSKeychainSecurityGroup(string keychainSecurityGroup)
	{
		throw new NotImplementedException();
	}

	public virtual void Clear(ILoggerAdapter requestlogger = null)
	{
		(requestlogger ?? _logger).Always("[Internal cache] Clearing user token cache accessor.");
		AccessTokenCacheDictionary.Clear();
		RefreshTokenCacheDictionary.Clear();
		IdTokenCacheDictionary.Clear();
		AccountCacheDictionary.Clear();
	}

	public virtual bool HasAccessOrRefreshTokens()
	{
		if (!RefreshTokenCacheDictionary.Any((KeyValuePair<string, ConcurrentDictionary<string, MsalRefreshTokenCacheItem>> partition) => partition.Value.Count > 0))
		{
			return AccessTokenCacheDictionary.Any((KeyValuePair<string, ConcurrentDictionary<string, MsalAccessTokenCacheItem>> partition) => partition.Value.Any((KeyValuePair<string, MsalAccessTokenCacheItem> token) => !token.Value.IsExpiredWithBuffer()));
		}
		return true;
	}
}
