using System;
using System.Runtime.CompilerServices;
using EnumsNET.Numerics;

namespace EnumsNET;

internal sealed class ContiguousStandardEnumCache<TUnderlying, TUnderlyingOperations> : StandardEnumCache<TUnderlying, TUnderlyingOperations> where TUnderlying : struct, IComparable<TUnderlying>, IEquatable<TUnderlying>, IConvertible where TUnderlyingOperations : struct, IUnderlyingOperations<TUnderlying>
{
	private readonly EnumMemberInternal<TUnderlying, TUnderlyingOperations>[] _distinctMembers;

	public ContiguousStandardEnumCache(Type enumType, IEnumBridge<TUnderlying, TUnderlyingOperations> enumBridge, EnumMemberInternal<TUnderlying, TUnderlyingOperations>[] members, EnumMemberInternal<TUnderlying, TUnderlyingOperations>?[] buckets, TUnderlying allFlags, int distinctCount, object? customValidator)
		: base(enumType, enumBridge, members, buckets, allFlags, distinctCount, isContiguous: true, customValidator)
	{
		if (distinctCount == members.Length)
		{
			_distinctMembers = members;
			return;
		}
		EnumMemberInternal<TUnderlying, TUnderlyingOperations>[] array = new EnumMemberInternal<TUnderlying, TUnderlyingOperations>[distinctCount];
		EnumMemberInternal<TUnderlying, TUnderlyingOperations> enumMemberInternal = (array[0] = members[0]);
		int num = 1;
		for (int i = 1; i < members.Length; i++)
		{
			EnumMemberInternal<TUnderlying, TUnderlyingOperations> enumMemberInternal2 = members[i];
			if (!enumMemberInternal2.Value.Equals(enumMemberInternal.Value))
			{
				array[num++] = enumMemberInternal2;
				enumMemberInternal = enumMemberInternal2;
			}
		}
		_distinctMembers = array;
	}

	public sealed override string AsString(ref byte value)
	{
		return AsString(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	public sealed override string AsString(object value)
	{
		return AsString(ToObject(value));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public new string AsString(TUnderlying value)
	{
		EnumMemberInternal<TUnderlying, TUnderlyingOperations> member = GetMember(value);
		if (member == null)
		{
			return value.ToString();
		}
		return member.Name;
	}

	public override bool TryFormat(ref byte value, Span<char> destination, out int charsWritten)
	{
		return TryFormat(UnsafeUtility.As<byte, TUnderlying>(ref value), destination, out charsWritten);
	}

	public override bool TryFormat(object value, Span<char> destination, out int charsWritten)
	{
		return TryFormat(ToObject(value), destination, out charsWritten);
	}

	public bool TryFormat(TUnderlying value, Span<char> destination, out int charsWritten)
	{
		EnumMemberInternal<TUnderlying, TUnderlyingOperations> member = GetMember(value);
		if (member != null)
		{
			return EnumCache.TryWriteNonNullableStringToSpan(member.Name, destination, out charsWritten);
		}
		return default(TUnderlyingOperations).TryFormat(value, destination, out charsWritten);
	}

	public override bool IsDefined(ref byte value)
	{
		return IsDefined(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	public override bool IsDefined(object value)
	{
		return IsDefined(ToObject(value));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public new bool IsDefined(TUnderlying value)
	{
		return default(TUnderlyingOperations).InRange(value, _minDefined, _maxDefined);
	}

	public override EnumMemberInternal? GetMember(ref byte value)
	{
		return GetMember(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	public override EnumMemberInternal? GetMember(object value)
	{
		return GetMember(ToObject(value));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public new EnumMemberInternal<TUnderlying, TUnderlyingOperations>? GetMember(TUnderlying value)
	{
		TUnderlyingOperations val = default(TUnderlyingOperations);
		if (val.InRange(value, _minDefined, _maxDefined))
		{
			TUnderlying val2 = val.Subtract(value, _minDefined);
			return _distinctMembers[val2.ToInt32(null)];
		}
		return null;
	}
}
