using System;
using System.Collections.Generic;
using System.Text;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal.Requests;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;

namespace Microsoft.Identity.Client.OAuth2.Throttling;

internal class UiRequiredProvider : IThrottlingProvider
{
	internal static readonly TimeSpan s_uiRequiredExpiration = TimeSpan.FromSeconds(120.0);

	internal ThrottlingCache ThrottlingCache { get; }

	public UiRequiredProvider()
	{
		ThrottlingCache = new ThrottlingCache();
	}

	public void RecordException(AuthenticationRequestParameters requestParams, IReadOnlyDictionary<string, string> bodyParams, MsalServiceException ex)
	{
		if (ex is MsalUiRequiredException && IsRequestSupported(requestParams))
		{
			ILoggerAdapter logger = requestParams.RequestContext.Logger;
			logger.Info(() => $"[Throttling] MsalUiRequiredException encountered - throttling for {s_uiRequiredExpiration.TotalSeconds} seconds. ");
			string requestStrictThumbprint = GetRequestStrictThumbprint(bodyParams, requestParams.AuthorityInfo.CanonicalAuthority.ToString(), requestParams.RequestContext.ServiceBundle.PlatformProxy.CryptographyManager);
			ThrottlingCacheEntry entry = new ThrottlingCacheEntry(ex, s_uiRequiredExpiration);
			ThrottlingCache.AddAndCleanup(requestStrictThumbprint, entry, logger);
		}
	}

	public void ResetCache()
	{
		ThrottlingCache.Clear();
	}

	public void TryThrottle(AuthenticationRequestParameters requestParams, IReadOnlyDictionary<string, string> bodyParams)
	{
		if (!ThrottlingCache.IsEmpty() && IsRequestSupported(requestParams))
		{
			ILoggerAdapter logger = requestParams.RequestContext.Logger;
			string requestStrictThumbprint = GetRequestStrictThumbprint(bodyParams, requestParams.AuthorityInfo.CanonicalAuthority.ToString(), requestParams.RequestContext.ServiceBundle.PlatformProxy.CryptographyManager);
			TryThrowException(requestStrictThumbprint, logger);
		}
	}

	private void TryThrowException(string thumbprint, ILoggerAdapter logger)
	{
		if (ThrottlingCache.TryGetOrRemoveExpired(thumbprint, logger, out var ex) && ex is MsalUiRequiredException originalException)
		{
			logger.WarningPii("[Throttling] Exception thrown because of throttling rule UiRequired - thumbprint: " + thumbprint, "[Throttling] Exception thrown because of throttling rule UiRequired ");
			throw new MsalThrottledUiRequiredException(originalException);
		}
	}

	private static bool IsRequestSupported(AuthenticationRequestParameters requestParams)
	{
		if (!requestParams.AppConfig.IsConfidentialClient)
		{
			return requestParams.ApiId == ApiEvent.ApiIds.AcquireTokenSilent;
		}
		return false;
	}

	private static string GetRequestStrictThumbprint(IReadOnlyDictionary<string, string> bodyParams, string authority, ICryptographyManager crypto)
	{
		StringBuilder stringBuilder = new StringBuilder();
		if (bodyParams.TryGetValue("client_id", out var value))
		{
			stringBuilder.Append(value);
			stringBuilder.Append('.');
		}
		stringBuilder.Append(authority);
		stringBuilder.Append('.');
		if (bodyParams.TryGetValue("scope", out var value2))
		{
			stringBuilder.Append(value2);
			stringBuilder.Append('.');
		}
		if (bodyParams.TryGetValue("refresh_token", out var value3) && !string.IsNullOrEmpty(value3))
		{
			stringBuilder.Append(crypto.CreateSha256Hash(value3));
			stringBuilder.Append('.');
		}
		if (bodyParams.TryGetValue("microsoft_enrollment_id", out var value4))
		{
			stringBuilder.Append(crypto.CreateSha256Hash(value4));
			stringBuilder.Append('.');
		}
		return stringBuilder.ToString();
	}
}
