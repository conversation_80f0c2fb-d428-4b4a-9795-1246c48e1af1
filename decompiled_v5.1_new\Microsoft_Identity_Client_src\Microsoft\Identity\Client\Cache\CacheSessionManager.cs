using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Internal.Requests;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.TelemetryCore.TelemetryClient;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Cache;

internal class CacheSessionManager : ICacheSessionManager
{
	private readonly AuthenticationRequestParameters _requestParams;

	private bool _cacheRefreshedForRead;

	public RequestContext RequestContext { get; }

	public ITokenCacheInternal TokenCacheInternal { get; }

	public CacheSessionManager(ITokenCacheInternal tokenCacheInternal, AuthenticationRequestParameters requestParams)
	{
		TokenCacheInternal = tokenCacheInternal ?? throw new ArgumentNullException("tokenCacheInternal");
		_requestParams = requestParams ?? throw new ArgumentNullException("requestParams");
		RequestContext = _requestParams.RequestContext;
	}

	public async Task<MsalAccessTokenCacheItem> FindAccessTokenAsync()
	{
		await RefreshCacheForReadOperationsAsync().ConfigureAwait(continueOnCapturedContext: false);
		return await TokenCacheInternal.FindAccessTokenAsync(_requestParams).ConfigureAwait(continueOnCapturedContext: false);
	}

	public Task<Tuple<MsalAccessTokenCacheItem, MsalIdTokenCacheItem, Account>> SaveTokenResponseAsync(MsalTokenResponse tokenResponse)
	{
		return TokenCacheInternal.SaveTokenResponseAsync(_requestParams, tokenResponse);
	}

	public async Task<Account> GetAccountAssociatedWithAccessTokenAsync(MsalAccessTokenCacheItem msalAccessTokenCacheItem)
	{
		await RefreshCacheForReadOperationsAsync().ConfigureAwait(continueOnCapturedContext: false);
		return await TokenCacheInternal.GetAccountAssociatedWithAccessTokenAsync(_requestParams, msalAccessTokenCacheItem).ConfigureAwait(continueOnCapturedContext: false);
	}

	public async Task<MsalIdTokenCacheItem> GetIdTokenCacheItemAsync(MsalAccessTokenCacheItem accessTokenCacheItem)
	{
		await RefreshCacheForReadOperationsAsync().ConfigureAwait(continueOnCapturedContext: false);
		return TokenCacheInternal.GetIdTokenCacheItem(accessTokenCacheItem);
	}

	public async Task<MsalRefreshTokenCacheItem> FindFamilyRefreshTokenAsync(string familyId)
	{
		await RefreshCacheForReadOperationsAsync().ConfigureAwait(continueOnCapturedContext: false);
		if (string.IsNullOrEmpty(familyId))
		{
			throw new ArgumentNullException("familyId");
		}
		return await TokenCacheInternal.FindRefreshTokenAsync(_requestParams, familyId).ConfigureAwait(continueOnCapturedContext: false);
	}

	public async Task<MsalRefreshTokenCacheItem> FindRefreshTokenAsync()
	{
		await RefreshCacheForReadOperationsAsync().ConfigureAwait(continueOnCapturedContext: false);
		return await TokenCacheInternal.FindRefreshTokenAsync(_requestParams).ConfigureAwait(continueOnCapturedContext: false);
	}

	public async Task<bool?> IsAppFociMemberAsync(string familyId)
	{
		await RefreshCacheForReadOperationsAsync().ConfigureAwait(continueOnCapturedContext: false);
		return await TokenCacheInternal.IsFociMemberAsync(_requestParams, familyId).ConfigureAwait(continueOnCapturedContext: false);
	}

	public async Task<IEnumerable<IAccount>> GetAccountsAsync()
	{
		await RefreshCacheForReadOperationsAsync().ConfigureAwait(continueOnCapturedContext: false);
		return await TokenCacheInternal.GetAccountsAsync(_requestParams).ConfigureAwait(continueOnCapturedContext: false);
	}

	private async Task RefreshCacheForReadOperationsAsync()
	{
		if (TokenCacheInternal.IsAppSubscribedToSerializationEvents())
		{
			if (_cacheRefreshedForRead)
			{
				return;
			}
			_requestParams.RequestContext.Logger.Verbose(() => "[Cache Session Manager] Entering the cache semaphore. " + TokenCacheInternal.Semaphore.GetCurrentCountLogMessage());
			await TokenCacheInternal.Semaphore.WaitAsync(_requestParams.RequestContext.UserCancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			_requestParams.RequestContext.Logger.Verbose(() => "[Cache Session Manager] Entered cache semaphore");
			TelemetryData telemetryData = new TelemetryData();
			try
			{
				if (_cacheRefreshedForRead)
				{
					return;
				}
				string key = CacheKeyFactory.GetKeyFromRequest(_requestParams);
				try
				{
					ITokenCacheInternal tokenCacheInternal = TokenCacheInternal;
					string clientId = _requestParams.AppConfig.ClientId;
					IAccount account = _requestParams.Account;
					bool isApplicationCache = TokenCacheInternal.IsApplicationCache;
					string suggestedCacheKey = key;
					bool hasTokens = TokenCacheInternal.HasTokensNoLocks();
					CancellationToken userCancellationToken = _requestParams.RequestContext.UserCancellationToken;
					TokenCacheNotificationArgs args = new TokenCacheNotificationArgs(tokenCacheInternal, clientId, account, hasStateChanged: false, isApplicationCache, suggestedCacheKey, hasTokens, null, userCancellationToken, _requestParams.RequestContext.CorrelationId, _requestParams.Scope, _requestParams.AuthorityManager.OriginalAuthority.TenantId, _requestParams.RequestContext.Logger.IdentityLogger, _requestParams.RequestContext.Logger.PiiLoggingEnabled, telemetryData);
					MeasureDurationResult measureDurationResult = await TokenCacheInternal.OnBeforeAccessAsync(args).MeasureAsync().ConfigureAwait(continueOnCapturedContext: false);
					RequestContext.ApiEvent.DurationInCacheInMs += measureDurationResult.Milliseconds;
				}
				finally
				{
					MeasureDurationResult measureDurationResult2 = await StopwatchService.MeasureCodeBlockAsync(async delegate
					{
						ITokenCacheInternal tokenCacheInternal2 = TokenCacheInternal;
						string clientId2 = _requestParams.AppConfig.ClientId;
						IAccount account2 = _requestParams.Account;
						bool isApplicationCache2 = TokenCacheInternal.IsApplicationCache;
						string suggestedCacheKey2 = key;
						bool hasTokens2 = TokenCacheInternal.HasTokensNoLocks();
						CancellationToken userCancellationToken2 = _requestParams.RequestContext.UserCancellationToken;
						TokenCacheNotificationArgs args2 = new TokenCacheNotificationArgs(tokenCacheInternal2, clientId2, account2, hasStateChanged: false, isApplicationCache2, suggestedCacheKey2, hasTokens2, null, userCancellationToken2, _requestParams.RequestContext.CorrelationId, _requestParams.Scope, _requestParams.AuthorityManager.OriginalAuthority.TenantId, _requestParams.RequestContext.Logger.IdentityLogger, _requestParams.RequestContext.Logger.PiiLoggingEnabled, telemetryData);
						await TokenCacheInternal.OnAfterAccessAsync(args2).ConfigureAwait(continueOnCapturedContext: false);
					}).ConfigureAwait(continueOnCapturedContext: false);
					RequestContext.ApiEvent.DurationInCacheInMs += measureDurationResult2.Milliseconds;
				}
				_cacheRefreshedForRead = true;
			}
			finally
			{
				TokenCacheInternal.Semaphore.Release();
				_requestParams.RequestContext.Logger.Verbose(() => "[Cache Session Manager] Released cache semaphore");
				RequestContext.ApiEvent.CacheLevel = telemetryData.CacheLevel;
			}
		}
		else
		{
			RequestContext.ApiEvent.CacheLevel = CacheLevel.L1Cache;
		}
	}
}
