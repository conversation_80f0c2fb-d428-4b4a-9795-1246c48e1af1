using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.InteropServices;
using Microsoft.Identity.Client.PlatformsCommon.Shared;

namespace Microsoft.Identity.Client.Platforms.Features.DesktopOs.Kerberos;

internal class SspiSecurityContext : IDisposable
{
	private const int SECPKG_CRED_BOTH = 3;

	private const int SECURITY_NETWORK_DREP = 0;

	private const int _maxTokenSize = 16384;

	private const InitContextFlag _defaultRequiredFlags = InitContextFlag.Delegate | InitContextFlag.ReplayDetect | InitContextFlag.SequenceDetect | InitContextFlag.Confidentiality | InitContextFlag.AllocateMemory | InitContextFlag.Connection | InitContextFlag.InitExtendedError;

	private readonly HashSet<object> _disposable = new HashSet<object>();

	private readonly Credential _credential;

	private readonly InitContextFlag _clientFlags;

	private NativeMethods.SECURITY_HANDLE _credentialsHandle;

	private NativeMethods.SECURITY_HANDLE _securityContext;

	private long _logonId;

	public string Package { get; private set; }

	public SspiSecurityContext(Credential credential, string package, long logonId = 0L, InitContextFlag clientFlags = InitContextFlag.Delegate | InitContextFlag.ReplayDetect | InitContextFlag.SequenceDetect | InitContextFlag.Confidentiality | InitContextFlag.AllocateMemory | InitContextFlag.Connection | InitContextFlag.InitExtendedError)
	{
		if (!DesktopOsHelper.IsWindows())
		{
			throw new PlatformNotSupportedException("Ticket Cache interface is not supported for this OS platform.");
		}
		_credential = credential;
		_clientFlags = clientFlags;
		Package = package;
		_logonId = logonId;
	}

	private static void ThrowIfError(uint result)
	{
		if (result != 0 && result != 2148074241u)
		{
			throw new Win32Exception((int)result);
		}
	}

	public ContextStatus InitializeSecurityContext(string targetName, out byte[] clientRequest)
	{
		string pszTargetName = targetName.ToLowerInvariant();
		clientRequest = null;
		SecStatus secStatus = SecStatus.SEC_E_OK;
		int num = 0;
		NativeMethods.SecBufferDesc pOutput = default(NativeMethods.SecBufferDesc);
		try
		{
			do
			{
				pOutput = new NativeMethods.SecBufferDesc(num);
				if (!_credentialsHandle.IsSet || secStatus == SecStatus.SEC_I_CONTINUE_NEEDED)
				{
					AcquireCredentials();
				}
				secStatus = NativeMethods.InitializeSecurityContext_0(ref _credentialsHandle, IntPtr.Zero, pszTargetName, _clientFlags, 0, 0, IntPtr.Zero, 0, ref _securityContext, ref pOutput, out var _, IntPtr.Zero);
				if (secStatus == SecStatus.SEC_E_INSUFFICIENT_MEMORY)
				{
					if (num > 16384)
					{
						break;
					}
					num += 1000;
				}
			}
			while (secStatus == SecStatus.SEC_I_INCOMPLETE_CREDENTIALS || secStatus == SecStatus.SEC_E_INSUFFICIENT_MEMORY);
			if ((uint)secStatus > 2147483648u)
			{
				throw new Win32Exception((int)secStatus);
			}
			clientRequest = pOutput.ReadBytes();
			if (secStatus == SecStatus.SEC_I_CONTINUE_NEEDED)
			{
				return ContextStatus.RequiresContinuation;
			}
			return ContextStatus.Accepted;
		}
		finally
		{
			pOutput.Dispose();
		}
	}

	private void TrackUnmanaged(object thing)
	{
		_disposable.Add(thing);
	}

	private unsafe void AcquireCredentials()
	{
		CredentialHandle credentialHandle = _credential.Structify();
		TrackUnmanaged(credentialHandle);
		IntPtr intPtr = IntPtr.Zero;
		if (_logonId != 0L)
		{
			intPtr = Marshal.AllocHGlobal(Marshal.SizeOf<long>());
			Marshal.StructureToPtr(_logonId, intPtr, fDeleteOld: false);
		}
		SecStatus secStatus = NativeMethods.AcquireCredentialsHandle(null, Package, 3, intPtr, (void*)credentialHandle.DangerousGetHandle(), IntPtr.Zero, IntPtr.Zero, ref _credentialsHandle, IntPtr.Zero);
		if (secStatus != SecStatus.SEC_E_OK)
		{
			throw new Win32Exception((int)secStatus);
		}
		TrackUnmanaged(_credentialsHandle);
	}

	public unsafe void Dispose()
	{
		foreach (object item in _disposable)
		{
			if (item is IDisposable disposable)
			{
				disposable.Dispose();
			}
			else if (item is NativeMethods.SECURITY_HANDLE sECURITY_HANDLE)
			{
				NativeMethods.DeleteSecurityContext(&sECURITY_HANDLE);
				ThrowIfError(NativeMethods.FreeCredentialsHandle(&sECURITY_HANDLE));
			}
			else if (item is IntPtr hglobal)
			{
				Marshal.FreeHGlobal(hglobal);
			}
		}
	}
}
