using System.IO;

namespace Newtonsoft.Json.Bson;

internal class AsyncBinaryWriterOwningWriter : AsyncBinaryWriter
{
	private readonly BinaryWriter _writer;

	public AsyncBinaryWriterOwningWriter(BinaryWriter writer)
		: base(writer.BaseStream)
	{
		_writer = writer;
	}

	public override void Close()
	{
		_writer.Close();
	}

	protected override void Dispose(bool disposing)
	{
		_writer.Dispose();
	}
}
