using System;
using System.Collections.Generic;
using System.Threading;

namespace Microsoft.Identity.Client.Extensibility;

public sealed class OnBeforeTokenRequestData
{
	public IDictionary<string, string> BodyParameters { get; }

	public IDictionary<string, string> Headers { get; }

	public Uri RequestUri { get; set; }

	public CancellationToken CancellationToken { get; }

	public OnBeforeTokenRequestData(IDictionary<string, string> bodyParameters, IDictionary<string, string> headers, Uri requestUri, CancellationToken cancellationToken)
	{
		BodyParameters = bodyParameters;
		Headers = headers;
		RequestUri = requestUri;
		CancellationToken = cancellationToken;
	}
}
