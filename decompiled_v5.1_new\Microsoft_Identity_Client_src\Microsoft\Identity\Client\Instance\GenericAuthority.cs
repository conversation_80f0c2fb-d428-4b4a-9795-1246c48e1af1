using System;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Instance.Oidc;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.Instance;

internal class GenericAuthority : Authority
{
	internal override string TenantId => null;

	internal GenericAuthority(AuthorityInfo authorityInfo)
		: base(authorityInfo)
	{
	}

	internal override async Task<string> GetTokenEndpointAsync(RequestContext requestContext)
	{
		return (await OidcRetrieverWithCache.GetOidcAsync(base.AuthorityInfo.CanonicalAuthority.AbsoluteUri, requestContext).ConfigureAwait(continueOnCapturedContext: false)).TokenEndpoint;
	}

	internal override async Task<string> GetAuthorizationEndpointAsync(RequestContext requestContext)
	{
		return (await OidcRetrieverWithCache.GetOidcAsync(base.AuthorityInfo.CanonicalAuthority.AbsoluteUri, requestContext).ConfigureAwait(continueOnCapturedContext: false)).AuthorizationEndpoint;
	}

	internal override Task<string> GetDeviceCodeEndpointAsync(RequestContext requestContext)
	{
		throw new NotImplementedException();
	}

	internal override string GetTenantedAuthority(string tenantId, bool forceSpecifiedTenant)
	{
		return base.AuthorityInfo.CanonicalAuthority.ToString();
	}
}
