using System;
using System.Text.RegularExpressions;
using Newtonsoft.Json.Bson.Utilities;

namespace Newtonsoft.Json.Bson.Converters;

public class BsonDataRegexConverter : JsonConverter
{
	private const string PatternName = "Pattern";

	private const string OptionsName = "Options";

	public override void WriteJson(JsonWriter writer, object value, JsonSerializer serializer)
	{
		Regex regex = (Regex)value;
		BsonDataWriter bsonDataWriter = writer as BsonDataWriter;
		if (bsonDataWriter == null)
		{
			throw ExceptionUtils.CreateJsonSerializationException(bsonDataWriter as IJsonLineInfo, bsonDataWriter.Path, "BsonDataRegexConverter only supports writing a regex with BsonDataWriter.", null);
		}
		WriteBson(bsonDataWriter, regex);
	}

	private bool HasFlag(RegexOptions options, RegexOptions flag)
	{
		return (options & flag) == flag;
	}

	private void WriteBson(BsonDataWriter writer, Regex regex)
	{
		string text = null;
		if (HasFlag(regex.Options, RegexOptions.IgnoreCase))
		{
			text += "i";
		}
		if (HasFlag(regex.Options, RegexOptions.Multiline))
		{
			text += "m";
		}
		if (HasFlag(regex.Options, RegexOptions.Singleline))
		{
			text += "s";
		}
		text += "u";
		if (HasFlag(regex.Options, RegexOptions.ExplicitCapture))
		{
			text += "x";
		}
		writer.WriteRegex(regex.ToString(), text);
	}

	public override object ReadJson(JsonReader reader, Type objectType, object existingValue, JsonSerializer serializer)
	{
		return reader.TokenType switch
		{
			JsonToken.StartObject => ReadRegexObject(reader, serializer), 
			JsonToken.String => ReadRegexString(reader), 
			JsonToken.Null => null, 
			_ => throw ExceptionUtils.CreateJsonSerializationException(reader as IJsonLineInfo, reader.Path, "Unexpected token when reading Regex.", null), 
		};
	}

	private object ReadRegexString(JsonReader reader)
	{
		string obj = (string)reader.Value;
		int num = obj.LastIndexOf('/');
		string pattern = obj.Substring(1, num - 1);
		string text = obj.Substring(num + 1);
		RegexOptions regexOptions = RegexOptions.None;
		string text2 = text;
		for (int i = 0; i < text2.Length; i++)
		{
			switch (text2[i])
			{
			case 'i':
				regexOptions |= RegexOptions.IgnoreCase;
				break;
			case 'm':
				regexOptions |= RegexOptions.Multiline;
				break;
			case 's':
				regexOptions |= RegexOptions.Singleline;
				break;
			case 'x':
				regexOptions |= RegexOptions.ExplicitCapture;
				break;
			}
		}
		return new Regex(pattern, regexOptions);
	}

	private Regex ReadRegexObject(JsonReader reader, JsonSerializer serializer)
	{
		string text = null;
		RegexOptions? regexOptions = null;
		while (reader.Read())
		{
			switch (reader.TokenType)
			{
			case JsonToken.PropertyName:
			{
				string a = reader.Value.ToString();
				if (!reader.Read())
				{
					throw ExceptionUtils.CreateJsonSerializationException(reader as IJsonLineInfo, reader.Path, "Unexpected end when reading Regex.", null);
				}
				if (string.Equals(a, "Pattern", StringComparison.OrdinalIgnoreCase))
				{
					text = (string)reader.Value;
				}
				else if (string.Equals(a, "Options", StringComparison.OrdinalIgnoreCase))
				{
					regexOptions = serializer.Deserialize<RegexOptions>(reader);
				}
				else
				{
					reader.Skip();
				}
				break;
			}
			case JsonToken.EndObject:
				if (text == null)
				{
					throw ExceptionUtils.CreateJsonSerializationException(reader as IJsonLineInfo, reader.Path, "Error deserializing Regex. No pattern found.", null);
				}
				return new Regex(text, regexOptions ?? RegexOptions.None);
			}
		}
		throw ExceptionUtils.CreateJsonSerializationException(reader as IJsonLineInfo, reader.Path, "Unexpected end when reading Regex.", null);
	}

	public override bool CanConvert(Type objectType)
	{
		return objectType == typeof(Regex);
	}
}
