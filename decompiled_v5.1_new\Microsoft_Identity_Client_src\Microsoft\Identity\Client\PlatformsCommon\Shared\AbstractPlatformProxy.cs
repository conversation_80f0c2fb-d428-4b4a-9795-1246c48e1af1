using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Microsoft.Identity.Client.AuthScheme.PoP;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal.Broker;
using Microsoft.Identity.Client.Platforms.Features.OpenTelemetry;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.TelemetryCore.OpenTelemetry;
using Microsoft.Identity.Client.UI;

namespace Microsoft.Identity.Client.PlatformsCommon.Shared;

internal abstract class AbstractPlatformProxy : IPlatformProxy
{
	public const string MacOsDescriptionForSTS = "MacOS";

	public const string LinuxOSDescriptionForSTS = "Linux";

	private readonly Lazy<string> _callingApplicationName;

	private readonly Lazy<string> _callingApplicationVersion;

	private readonly Lazy<ICryptographyManager> _cryptographyManager;

	private readonly Lazy<string> _deviceId;

	private readonly Lazy<string> _deviceModel;

	private readonly Lazy<string> _operatingSystem;

	private readonly Lazy<IPlatformLogger> _platformLogger;

	private readonly Lazy<string> _processorArchitecture;

	private readonly Lazy<string> _productName;

	private readonly Lazy<string> _runtimeVersion;

	private readonly Lazy<IOtelInstrumentation> _otelInstrumentation;

	protected IFeatureFlags OverloadFeatureFlags { get; set; }

	protected ILoggerAdapter Logger { get; }

	public ITokenCacheAccessor UserTokenCacheAccessorForTest { get; set; }

	public ITokenCacheAccessor AppTokenCacheAccessorForTest { get; set; }

	public ICryptographyManager CryptographyManager => _cryptographyManager.Value;

	public IPlatformLogger PlatformLogger => _platformLogger.Value;

	public IOtelInstrumentation OtelInstrumentation => _otelInstrumentation.Value;

	public virtual bool BrokerSupportsWamAccounts => false;

	public virtual bool LegacyCacheRequiresSerialization => true;

	protected AbstractPlatformProxy(ILoggerAdapter logger)
	{
		Logger = logger;
		_deviceModel = new Lazy<string>(InternalGetDeviceModel);
		_operatingSystem = new Lazy<string>(InternalGetOperatingSystem);
		_processorArchitecture = new Lazy<string>(InternalGetProcessorArchitecture);
		_callingApplicationName = new Lazy<string>(InternalGetCallingApplicationName);
		_callingApplicationVersion = new Lazy<string>(InternalGetCallingApplicationVersion);
		_deviceId = new Lazy<string>(InternalGetDeviceId);
		_productName = new Lazy<string>(InternalGetProductName);
		_cryptographyManager = new Lazy<ICryptographyManager>(InternalGetCryptographyManager);
		_platformLogger = new Lazy<IPlatformLogger>(InternalGetPlatformLogger);
		_runtimeVersion = new Lazy<string>(InternalGetRuntimeVersion);
		_otelInstrumentation = new Lazy<IOtelInstrumentation>(InternalGetOtelInstrumentation);
	}

	private IOtelInstrumentation InternalGetOtelInstrumentation()
	{
		try
		{
			return new OtelInstrumentation();
		}
		catch (FileNotFoundException ex)
		{
			Logger.Warning("Failed instantiating OpenTelemetry instrumentation. Exception: " + ex.Message);
			return new NullOtelInstrumentation();
		}
	}

	public IWebUIFactory GetWebUiFactory(ApplicationConfiguration appConfig)
	{
		if (appConfig.WebUiFactoryCreator == null)
		{
			return CreateWebUiFactory();
		}
		return appConfig.WebUiFactoryCreator();
	}

	public string GetDeviceModel()
	{
		return _deviceModel.Value;
	}

	public string GetOperatingSystem()
	{
		return _operatingSystem.Value;
	}

	public string GetProcessorArchitecture()
	{
		return _processorArchitecture.Value;
	}

	public abstract Task<string> GetUserPrincipalNameAsync();

	public string GetCallingApplicationName()
	{
		return _callingApplicationName.Value;
	}

	public string GetCallingApplicationVersion()
	{
		return _callingApplicationVersion.Value;
	}

	public string GetDeviceId()
	{
		return _deviceId.Value;
	}

	public abstract string GetDefaultRedirectUri(string clientId, bool useRecommendedRedirectUri = false);

	public string GetProductName()
	{
		return _productName.Value;
	}

	public string GetRuntimeVersion()
	{
		return _runtimeVersion.Value;
	}

	public abstract ILegacyCachePersistence CreateLegacyCachePersistence();

	public virtual ITokenCacheAccessor CreateTokenCacheAccessor(CacheOptions tokenCacheAccessorOptions, bool isApplicationTokenCache = false)
	{
		if (isApplicationTokenCache)
		{
			return AppTokenCacheAccessorForTest ?? new InMemoryPartitionedAppTokenCacheAccessor(Logger, tokenCacheAccessorOptions);
		}
		return UserTokenCacheAccessorForTest ?? new InMemoryPartitionedUserTokenCacheAccessor(Logger, tokenCacheAccessorOptions);
	}

	protected abstract IWebUIFactory CreateWebUiFactory();

	protected abstract IFeatureFlags CreateFeatureFlags();

	protected abstract string InternalGetDeviceModel();

	protected abstract string InternalGetOperatingSystem();

	protected abstract string InternalGetProcessorArchitecture();

	protected abstract string InternalGetCallingApplicationName();

	protected abstract string InternalGetCallingApplicationVersion();

	protected abstract string InternalGetDeviceId();

	protected abstract string InternalGetProductName();

	protected abstract ICryptographyManager InternalGetCryptographyManager();

	protected abstract IPlatformLogger InternalGetPlatformLogger();

	protected virtual string InternalGetRuntimeVersion()
	{
		return RuntimeInformation.FrameworkDescription;
	}

	public virtual IFeatureFlags GetFeatureFlags()
	{
		return OverloadFeatureFlags ?? CreateFeatureFlags();
	}

	public void SetFeatureFlags(IFeatureFlags featureFlags)
	{
		OverloadFeatureFlags = featureFlags;
	}

	public virtual Task StartDefaultOsBrowserAsync(string url, bool IBrokerConfigured)
	{
		throw new NotImplementedException();
	}

	public virtual IBroker CreateBroker(ApplicationConfiguration appConfig, CoreUIParent uiParent)
	{
		if (appConfig.BrokerCreatorFunc == null)
		{
			return new NullBroker(Logger);
		}
		return appConfig.BrokerCreatorFunc(uiParent, appConfig, Logger);
	}

	public virtual bool CanBrokerSupportSilentAuth()
	{
		return true;
	}

	public virtual IPoPCryptoProvider GetDefaultPoPCryptoProvider()
	{
		throw new NotImplementedException();
	}

	public virtual IDeviceAuthManager CreateDeviceAuthManager()
	{
		return new NullDeviceAuthManager();
	}

	public virtual IMsalHttpClientFactory CreateDefaultHttpClientFactory()
	{
		return new SimpleHttpClientFactory();
	}
}
