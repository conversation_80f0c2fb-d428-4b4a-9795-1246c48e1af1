using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Http;

internal class HttpManager : IHttpManager
{
	protected readonly IMsalHttpClientFactory _httpClientFactory;

	public long LastRequestDurationInMs { get; private set; }

	public HttpManager(IMsalHttpClientFactory httpClientFactory)
	{
		_httpClientFactory = httpClientFactory ?? throw new ArgumentNullException("httpClientFactory");
	}

	protected virtual HttpClient GetHttpClient()
	{
		return _httpClientFactory.GetHttpClient();
	}

	public async Task<HttpResponse> SendPostAsync(Uri endpoint, IDictionary<string, string> headers, IDictionary<string, string> bodyParameters, ILoggerAdapter logger, CancellationToken cancellationToken = default(CancellationToken))
	{
		HttpContent body = ((bodyParameters == null) ? null : new FormUrlEncodedContent(bodyParameters));
		return await SendPostAsync(endpoint, headers, body, logger, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}

	public virtual Task<HttpResponse> SendPostAsync(Uri endpoint, IDictionary<string, string> headers, HttpContent body, ILoggerAdapter logger, CancellationToken cancellationToken = default(CancellationToken))
	{
		return SendRequestAsync(endpoint, headers, body, HttpMethod.Post, logger, doNotThrow: false, retry: false, cancellationToken);
	}

	public virtual Task<HttpResponse> SendGetAsync(Uri endpoint, IDictionary<string, string> headers, ILoggerAdapter logger, bool retry = true, CancellationToken cancellationToken = default(CancellationToken))
	{
		return SendRequestAsync(endpoint, headers, null, HttpMethod.Get, logger, doNotThrow: false, retry: false, cancellationToken);
	}

	public virtual Task<HttpResponse> SendGetForceResponseAsync(Uri endpoint, IDictionary<string, string> headers, ILoggerAdapter logger, bool retry = true, CancellationToken cancellationToken = default(CancellationToken))
	{
		return SendRequestAsync(endpoint, headers, null, HttpMethod.Get, logger, doNotThrow: true, retry: false, cancellationToken);
	}

	public virtual Task<HttpResponse> SendPostForceResponseAsync(Uri uri, IDictionary<string, string> headers, IDictionary<string, string> bodyParameters, ILoggerAdapter logger, CancellationToken cancellationToken = default(CancellationToken))
	{
		HttpContent body = ((bodyParameters == null) ? null : new FormUrlEncodedContent(bodyParameters));
		return SendRequestAsync(uri, headers, body, HttpMethod.Post, logger, doNotThrow: true, retry: false, cancellationToken);
	}

	public virtual Task<HttpResponse> SendPostForceResponseAsync(Uri uri, IDictionary<string, string> headers, StringContent body, ILoggerAdapter logger, CancellationToken cancellationToken = default(CancellationToken))
	{
		return SendRequestAsync(uri, headers, body, HttpMethod.Post, logger, doNotThrow: true, retry: false, cancellationToken);
	}

	private static HttpRequestMessage CreateRequestMessage(Uri endpoint, IDictionary<string, string> headers)
	{
		HttpRequestMessage httpRequestMessage = new HttpRequestMessage
		{
			RequestUri = endpoint
		};
		httpRequestMessage.Headers.Accept.Clear();
		if (headers != null)
		{
			foreach (KeyValuePair<string, string> header in headers)
			{
				httpRequestMessage.Headers.Add(header.Key, header.Value);
			}
		}
		return httpRequestMessage;
	}

	protected virtual async Task<HttpResponse> SendRequestAsync(Uri endpoint, IDictionary<string, string> headers, HttpContent body, HttpMethod method, ILoggerAdapter logger, bool doNotThrow = false, bool retry = false, CancellationToken cancellationToken = default(CancellationToken))
	{
		HttpResponse response = null;
		try
		{
			HttpContent body2 = body;
			if (body != null)
			{
				body2 = await CloneHttpContentAsync(body).ConfigureAwait(continueOnCapturedContext: false);
			}
			using (logger.LogBlockDuration("[HttpManager] ExecuteAsync"))
			{
				response = await ExecuteAsync(endpoint, headers, body2, method, logger, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			}
			if (response.StatusCode == HttpStatusCode.OK)
			{
				return response;
			}
			logger.Info(() => string.Format(CultureInfo.InvariantCulture, "Response status code does not indicate success: {0} ({1}). ", (int)response.StatusCode, response.StatusCode));
		}
		catch (TaskCanceledException ex)
		{
			if (cancellationToken.IsCancellationRequested)
			{
				logger.Info("The HTTP request was cancelled. ");
				throw;
			}
			logger.Error("The HTTP request failed. " + ex.Message);
			throw new MsalServiceException("request_timeout", "Request to the endpoint timed out.", ex);
		}
		if (doNotThrow)
		{
			return response;
		}
		if (IsRetryableStatusCode((int)response.StatusCode))
		{
			throw MsalServiceExceptionFactory.FromHttpResponse("service_not_available", "Service is unavailable to process the request", response);
		}
		return response;
	}

	protected async Task<HttpResponse> ExecuteAsync(Uri endpoint, IDictionary<string, string> headers, HttpContent body, HttpMethod method, ILoggerAdapter logger, CancellationToken cancellationToken = default(CancellationToken))
	{
		HttpRequestMessage requestMessage = CreateRequestMessage(endpoint, headers);
		try
		{
			requestMessage.Method = method;
			requestMessage.Content = body;
			logger.VerbosePii(() => $"[HttpManager] Sending request. Method: {method}. URI: {((endpoint == null) ? "NULL" : (endpoint.Scheme + "://" + endpoint.Authority + endpoint.AbsolutePath))}. ", () => $"[HttpManager] Sending request. Method: {method}. Host: {((endpoint == null) ? "NULL" : (endpoint.Scheme + "://" + endpoint.Authority))}. ");
			MeasureDurationResult<HttpResponseMessage> measureDurationResult = await StopwatchService.MeasureCodeBlockAsync(async () => await GetHttpClient().SendAsync(requestMessage, cancellationToken).ConfigureAwait(continueOnCapturedContext: false)).ConfigureAwait(continueOnCapturedContext: false);
			HttpResponseMessage responseMessage = measureDurationResult.Result;
			try
			{
				LastRequestDurationInMs = measureDurationResult.Milliseconds;
				logger.Verbose(() => $"[HttpManager] Received response. Status code: {responseMessage.StatusCode}. ");
				HttpResponse obj = await CreateResponseAsync(responseMessage).ConfigureAwait(continueOnCapturedContext: false);
				obj.UserAgent = requestMessage.Headers.UserAgent.ToString();
				return obj;
			}
			finally
			{
				if (responseMessage != null)
				{
					((IDisposable)responseMessage).Dispose();
				}
			}
		}
		finally
		{
			if (requestMessage != null)
			{
				((IDisposable)requestMessage).Dispose();
			}
		}
	}

	internal static async Task<HttpResponse> CreateResponseAsync(HttpResponseMessage response)
	{
		string text = ((response.Content != null) ? (await response.Content.ReadAsStringAsync().ConfigureAwait(continueOnCapturedContext: false)) : null);
		string body = text;
		return new HttpResponse
		{
			Headers = response.Headers,
			Body = body,
			StatusCode = response.StatusCode
		};
	}

	protected static async Task<HttpContent> CloneHttpContentAsync(HttpContent httpContent)
	{
		MemoryStream temp = new MemoryStream();
		await httpContent.CopyToAsync(temp).ConfigureAwait(continueOnCapturedContext: false);
		temp.Position = 0L;
		StreamContent streamContent = new StreamContent(temp);
		if (httpContent.Headers != null)
		{
			foreach (KeyValuePair<string, IEnumerable<string>> header in httpContent.Headers)
			{
				streamContent.Headers.Add(header.Key, header.Value);
			}
		}
		return streamContent;
	}

	protected virtual bool IsRetryableStatusCode(int statusCode)
	{
		if (statusCode >= 500)
		{
			return statusCode < 600;
		}
		return false;
	}
}
