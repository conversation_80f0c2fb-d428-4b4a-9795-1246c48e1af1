namespace Newtonsoft.Json.Bson;

internal class BsonRegex : Newtonsoft.Json.Bson.BsonToken
{
	public Newtonsoft.Json.Bson.BsonString Pattern { get; set; }

	public Newtonsoft.Json.Bson.BsonString Options { get; set; }

	public override Newtonsoft.Json.Bson.BsonType Type => Newtonsoft.Json.Bson.BsonType.Regex;

	public BsonRegex(string pattern, string options)
	{
		Pattern = new Newtonsoft.Json.Bson.BsonString(pattern, includeLength: false);
		Options = new Newtonsoft.Json.Bson.BsonString(options, includeLength: false);
	}
}
