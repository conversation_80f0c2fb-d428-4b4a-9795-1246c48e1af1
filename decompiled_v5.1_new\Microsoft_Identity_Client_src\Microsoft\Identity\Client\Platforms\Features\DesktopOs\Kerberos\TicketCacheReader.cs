using System;
using System.ComponentModel;
using System.Runtime.InteropServices;

namespace Microsoft.Identity.Client.Platforms.Features.DesktopOs.Kerberos;

internal class TicketCacheReader : IDisposable
{
	private readonly string _spn;

	private readonly SspiSecurityContext _context;

	private bool _disposedValue;

	public TicketCacheReader(string spn, long logonId = 0L, string package = "Kerberos")
	{
		_spn = spn;
		_context = new SspiSecurityContext(Credential.Current(), package, logonId);
	}

	public byte[] RequestToken()
	{
		if (_context.InitializeSecurityContext(_spn, out var clientRequest) == ContextStatus.Error)
		{
			throw new Win32Exception(Marshal.GetLastWin32Error());
		}
		return clientRequest;
	}

	protected virtual void Dispose(bool disposing)
	{
		if (!_disposedValue)
		{
			if (disposing)
			{
				_context.Dispose();
			}
			_disposedValue = true;
		}
	}

	public void Dispose()
	{
		Dispose(disposing: true);
		GC.SuppressFinalize(this);
	}
}
