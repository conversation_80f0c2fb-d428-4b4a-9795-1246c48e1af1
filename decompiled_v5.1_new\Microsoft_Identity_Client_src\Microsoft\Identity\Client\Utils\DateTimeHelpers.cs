using System;
using System.Globalization;
using Microsoft.Identity.Client.Core;

namespace Microsoft.Identity.Client.Utils;

internal static class DateTimeHelpers
{
	public static DateTimeOffset UnixTimestampToDateTime(double unixTimestamp)
	{
		DateTime dateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc).AddSeconds(unixTimestamp).ToUniversalTime();
		return dateTime;
	}

	public static DateTimeOffset? UnixTimestampToDateTimeOrNull(double unixTimestamp)
	{
		if (unixTimestamp == 0.0)
		{
			return null;
		}
		DateTime dateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc).AddSeconds(unixTimestamp).ToUniversalTime();
		return dateTime;
	}

	public static string DateTimeToUnixTimestamp(DateTimeOffset dateTimeOffset)
	{
		DateTime dateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
		return ((long)dateTimeOffset.Subtract(dateTime).TotalSeconds).ToString(CultureInfo.InvariantCulture);
	}

	public static long CurrDateTimeInUnixTimestamp()
	{
		DateTime value = new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc);
		return (long)DateTime.UtcNow.Subtract(value).TotalSeconds;
	}

	public static long GetDurationFromWindowsTimestamp(string windowsTimestampInFuture, ILoggerAdapter logger)
	{
		if (string.IsNullOrEmpty(windowsTimestampInFuture))
		{
			return 0L;
		}
		if (!ulong.TryParse(windowsTimestampInFuture, out var result) || result <= 11644473600L || result == ulong.MaxValue)
		{
			logger.Warning("Invalid Universal time " + windowsTimestampInFuture);
			return 0L;
		}
		return (long)(result - 11644473600L) - CurrDateTimeInUnixTimestamp();
	}

	public static long GetDurationFromNowInSeconds(string unixTimestampInFuture)
	{
		if (string.IsNullOrEmpty(unixTimestampInFuture))
		{
			return 0L;
		}
		return long.Parse(unixTimestampInFuture, CultureInfo.InvariantCulture) - CurrDateTimeInUnixTimestamp();
	}

	public static DateTimeOffset? DateTimeOffsetFromDuration(long? duration)
	{
		if (duration.HasValue)
		{
			return DateTimeOffsetFromDuration(duration.Value);
		}
		return null;
	}

	public static DateTimeOffset DateTimeOffsetFromDuration(long duration)
	{
		return DateTime.UtcNow + TimeSpan.FromSeconds(duration);
	}
}
