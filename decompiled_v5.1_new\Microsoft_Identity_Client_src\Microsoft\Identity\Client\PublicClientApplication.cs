using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Security;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Executors;
using Microsoft.Identity.Client.Internal.Broker;
using Microsoft.Identity.Client.PlatformsCommon.Factories;
using Microsoft.Identity.Client.WsTrust;

namespace Microsoft.Identity.Client;

public sealed class PublicClientApplication : ClientApplicationBase, IPublicClientApplication, IClientApplicationBase, IApplicationBase, IByRefreshToken
{
	private const string CurrentOSAccountDescriptor = "current_os_account";

	private static readonly IAccount s_currentOsAccount = new Account("current_os_account", null, null);

	public static IAccount OperatingSystemAccount => s_currentOsAccount;

	public bool IsSystemWebViewAvailable => base.ServiceBundle.PlatformProxy.GetWebUiFactory(base.ServiceBundle.Config).IsSystemWebViewAvailable;

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use PublicClientApplicationBuilder instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public PublicClientApplication(string clientId)
		: this(clientId, "https://login.microsoftonline.com/common/")
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use PublicClientApplicationBuilder instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public PublicClientApplication(string clientId, string authority)
		: base(PublicClientApplicationBuilder.Create(clientId).WithRedirectUri(PlatformProxyFactory.CreatePlatformProxy(null).GetDefaultRedirectUri(clientId)).WithAuthority(new Uri(authority))
			.BuildConfiguration())
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[Obsolete("Use AcquireTokenInteractive instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public Task<AuthenticationResult> AcquireTokenAsync(IEnumerable<string> scopes)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[Obsolete("Use AcquireTokenInteractive instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public Task<AuthenticationResult> AcquireTokenAsync(IEnumerable<string> scopes, string loginHint)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenInteractive instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenAsync(IEnumerable<string> scopes, IAccount account)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenInteractive instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenAsync(IEnumerable<string> scopes, string loginHint, Prompt prompt, string extraQueryParameters)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenInteractive instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenAsync(IEnumerable<string> scopes, IAccount account, Prompt prompt, string extraQueryParameters)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenInteractive instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenAsync(IEnumerable<string> scopes, string loginHint, Prompt prompt, string extraQueryParameters, IEnumerable<string> extraScopesToConsent, string authority)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenInteractive instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenAsync(IEnumerable<string> scopes, IAccount account, Prompt prompt, string extraQueryParameters, IEnumerable<string> extraScopesToConsent, string authority)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenInteractive instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenAsync(IEnumerable<string> scopes, UIParent parent)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenInteractive instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenAsync(IEnumerable<string> scopes, string loginHint, UIParent parent)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenInteractive instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenAsync(IEnumerable<string> scopes, IAccount account, UIParent parent)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenInteractive instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenAsync(IEnumerable<string> scopes, string loginHint, Prompt prompt, string extraQueryParameters, UIParent parent)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenInteractive instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenAsync(IEnumerable<string> scopes, IAccount account, Prompt prompt, string extraQueryParameters, UIParent parent)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenInteractive instead. See https://aka.ms/msal-net-3-breaking-changes. ")]
	public Task<AuthenticationResult> AcquireTokenAsync(IEnumerable<string> scopes, string loginHint, Prompt prompt, string extraQueryParameters, IEnumerable<string> extraScopesToConsent, string authority, UIParent parent)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenInteractive instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenAsync(IEnumerable<string> scopes, IAccount account, Prompt prompt, string extraQueryParameters, IEnumerable<string> extraScopesToConsent, string authority, UIParent parent)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[Obsolete("Use AcquireTokenByUsernamePassword instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public Task<AuthenticationResult> AcquireTokenByUsernamePasswordAsync(IEnumerable<string> scopes, string username, SecureString securePassword)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenWithDeviceCode instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenWithDeviceCodeAsync(IEnumerable<string> scopes, Func<DeviceCodeResult, Task> deviceCodeResultCallback)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenWithDeviceCode instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenWithDeviceCodeAsync(IEnumerable<string> scopes, string extraQueryParameters, Func<DeviceCodeResult, Task> deviceCodeResultCallback)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenWithDeviceCode instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenWithDeviceCodeAsync(IEnumerable<string> scopes, Func<DeviceCodeResult, Task> deviceCodeResultCallback, CancellationToken cancellationToken)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenWithDeviceCode instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenWithDeviceCodeAsync(IEnumerable<string> scopes, string extraQueryParameters, Func<DeviceCodeResult, Task> deviceCodeResultCallback, CancellationToken cancellationToken)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenByRefreshToken instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	Task<AuthenticationResult> IByRefreshToken.AcquireTokenByRefreshTokenAsync(IEnumerable<string> scopes, string refreshToken)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenByIntegratedWindowsAuth instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenByIntegratedWindowsAuthAsync(IEnumerable<string> scopes)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenByIntegratedWindowsAuth instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenByIntegratedWindowsAuthAsync(IEnumerable<string> scopes, string username)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use PublicClientApplicationBuilder instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public PublicClientApplication(string clientId, string authority, TokenCache userTokenCache)
		: this(PublicClientApplicationBuilder.Create(clientId).WithAuthority(new Uri(authority)).BuildConfiguration())
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	internal PublicClientApplication(ApplicationConfiguration configuration)
		: base(configuration)
	{
	}

	internal static bool IsOperatingSystemAccount(IAccount account)
	{
		return string.Equals(account?.HomeAccountId?.Identifier, "current_os_account", StringComparison.Ordinal);
	}

	public bool IsEmbeddedWebViewAvailable()
	{
		return base.ServiceBundle.PlatformProxy.GetWebUiFactory(base.ServiceBundle.Config).IsEmbeddedWebViewAvailable;
	}

	public bool IsUserInteractive()
	{
		return base.ServiceBundle.PlatformProxy.GetWebUiFactory(base.ServiceBundle.Config).IsUserInteractive;
	}

	public bool IsBrokerAvailable()
	{
		return base.ServiceBundle.PlatformProxy.CreateBroker(base.ServiceBundle.Config, null).IsBrokerInstalledAndInvokable((base.ServiceBundle.Config.Authority?.AuthorityInfo?.AuthorityType).GetValueOrDefault());
	}

	[CLSCompliant(false)]
	public AcquireTokenInteractiveParameterBuilder AcquireTokenInteractive(IEnumerable<string> scopes)
	{
		return AcquireTokenInteractiveParameterBuilder.Create(ClientExecutorFactory.CreatePublicClientExecutor(this), scopes).WithParentActivityOrWindowFunc(base.ServiceBundle.Config.ParentActivityOrWindowFunc);
	}

	public AcquireTokenWithDeviceCodeParameterBuilder AcquireTokenWithDeviceCode(IEnumerable<string> scopes, Func<DeviceCodeResult, Task> deviceCodeResultCallback)
	{
		return AcquireTokenWithDeviceCodeParameterBuilder.Create(ClientExecutorFactory.CreatePublicClientExecutor(this), scopes, deviceCodeResultCallback);
	}

	AcquireTokenByRefreshTokenParameterBuilder IByRefreshToken.AcquireTokenByRefreshToken(IEnumerable<string> scopes, string refreshToken)
	{
		return AcquireTokenByRefreshTokenParameterBuilder.Create(ClientExecutorFactory.CreateClientApplicationBaseExecutor(this), scopes, refreshToken);
	}

	public AcquireTokenByIntegratedWindowsAuthParameterBuilder AcquireTokenByIntegratedWindowsAuth(IEnumerable<string> scopes)
	{
		return AcquireTokenByIntegratedWindowsAuthParameterBuilder.Create(ClientExecutorFactory.CreatePublicClientExecutor(this), scopes);
	}

	[Obsolete("Using SecureString is not recommended. Use AcquireTokenByUsernamePassword(IEnumerable<string> scopes, string username, string password) instead.", false)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public AcquireTokenByUsernamePasswordParameterBuilder AcquireTokenByUsernamePassword(IEnumerable<string> scopes, string username, SecureString password)
	{
		return AcquireTokenByUsernamePasswordParameterBuilder.Create(ClientExecutorFactory.CreatePublicClientExecutor(this), scopes, username, new string(password.PasswordToCharArray()));
	}

	public AcquireTokenByUsernamePasswordParameterBuilder AcquireTokenByUsernamePassword(IEnumerable<string> scopes, string username, string password)
	{
		return AcquireTokenByUsernamePasswordParameterBuilder.Create(ClientExecutorFactory.CreatePublicClientExecutor(this), scopes, username, password);
	}

	public bool IsProofOfPossessionSupportedByClient()
	{
		if (base.ServiceBundle.Config.IsBrokerEnabled)
		{
			IBroker broker = base.ServiceBundle.PlatformProxy.CreateBroker(base.ServiceBundle.Config, null);
			if (broker.IsBrokerInstalledAndInvokable(base.ServiceBundle.Config.Authority.AuthorityInfo.AuthorityType))
			{
				return broker.IsPopSupported;
			}
		}
		return false;
	}
}
