using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.IdentityModel.Json;
using Microsoft.IdentityModel.Json.Linq;
using Microsoft.IdentityModel.Logging;
using Microsoft.IdentityModel.Tokens;

namespace Microsoft.IdentityModel.JsonWebTokens;

public class JsonWebTokenHandler : TokenHandler
{
	private IDictionary<string, string> _inboundClaimTypeMap;

	private const string _namespace = "http://schemas.xmlsoap.org/ws/2005/05/identity/claimproperties";

	private static string _shortClaimType = "http://schemas.xmlsoap.org/ws/2005/05/identity/claimproperties/ShortTypeName";

	private bool _mapInboundClaims = DefaultMapInboundClaims;

	public static IDictionary<string, string> DefaultInboundClaimTypeMap = new Dictionary<string, string>(ClaimTypeMapping.InboundClaimTypeMap);

	public static bool DefaultMapInboundClaims = false;

	public const string Base64UrlEncodedUnsignedJWSHeader = "eyJhbGciOiJub25lIn0";

	public Type TokenType => typeof(JsonWebToken);

	public static string ShortClaimTypeProperty
	{
		get
		{
			return _shortClaimType;
		}
		set
		{
			if (string.IsNullOrWhiteSpace(value))
			{
				throw LogHelper.LogArgumentNullException("value");
			}
			_shortClaimType = value;
		}
	}

	public bool MapInboundClaims
	{
		get
		{
			return _mapInboundClaims;
		}
		set
		{
			if (!_mapInboundClaims && value && _inboundClaimTypeMap.Count == 0)
			{
				_inboundClaimTypeMap = new Dictionary<string, string>(DefaultInboundClaimTypeMap);
			}
			_mapInboundClaims = value;
		}
	}

	public IDictionary<string, string> InboundClaimTypeMap
	{
		get
		{
			return _inboundClaimTypeMap;
		}
		set
		{
			_inboundClaimTypeMap = value ?? throw LogHelper.LogArgumentNullException("value");
		}
	}

	public virtual bool CanValidateToken => true;

	public JsonWebTokenHandler()
	{
		if (_mapInboundClaims)
		{
			_inboundClaimTypeMap = new Dictionary<string, string>(DefaultInboundClaimTypeMap);
		}
		else
		{
			_inboundClaimTypeMap = new Dictionary<string, string>();
		}
	}

	internal static IDictionary<string, object> AddCtyClaimDefaultValue(IDictionary<string, object> additionalClaims, bool setDefaultCtyClaim)
	{
		if (!setDefaultCtyClaim)
		{
			return additionalClaims;
		}
		object value;
		if (additionalClaims == null)
		{
			additionalClaims = new Dictionary<string, object> { { "cty", "JWT" } };
		}
		else if (!additionalClaims.TryGetValue("cty", out value))
		{
			additionalClaims.Add("cty", "JWT");
		}
		return additionalClaims;
	}

	public virtual bool CanReadToken(string token)
	{
		if (string.IsNullOrWhiteSpace(token))
		{
			return false;
		}
		if (token.Length > MaximumTokenSizeInBytes)
		{
			LogHelper.LogInformation("IDX10209: Token has length: '{0}' which is larger than the MaximumTokenSizeInBytes: '{1}'.", LogHelper.MarkAsNonPII(token.Length), LogHelper.MarkAsNonPII(MaximumTokenSizeInBytes));
			return false;
		}
		string[] array = token.Split(new char[1] { '.' }, 6);
		if (array.Length == 3)
		{
			return JwtTokenUtilities.RegexJws.IsMatch(token);
		}
		if (array.Length == 5)
		{
			return JwtTokenUtilities.RegexJwe.IsMatch(token);
		}
		LogHelper.LogInformation("IDX14107: Token string does not match the token formats: JWE (header.encryptedKey.iv.ciphertext.tag) or JWS (header.payload.signature)");
		return false;
	}

	private static JObject CreateDefaultJWEHeader(EncryptingCredentials encryptingCredentials, string compressionAlgorithm, string tokenType)
	{
		JObject jObject = new JObject();
		jObject.Add("alg", encryptingCredentials.Alg);
		jObject.Add("enc", encryptingCredentials.Enc);
		if (!string.IsNullOrEmpty(encryptingCredentials.Key.KeyId))
		{
			jObject.Add("kid", encryptingCredentials.Key.KeyId);
		}
		if (!string.IsNullOrEmpty(compressionAlgorithm))
		{
			jObject.Add("zip", compressionAlgorithm);
		}
		if (string.IsNullOrEmpty(tokenType))
		{
			jObject.Add("typ", "JWT");
		}
		else
		{
			jObject.Add("typ", tokenType);
		}
		return jObject;
	}

	private static JObject CreateDefaultJWSHeader(SigningCredentials signingCredentials, string tokenType)
	{
		JObject jObject = null;
		if (signingCredentials == null)
		{
			jObject = new JObject { { "alg", "none" } };
		}
		else
		{
			jObject = new JObject { { "alg", signingCredentials.Algorithm } };
			if (signingCredentials.Key.KeyId != null)
			{
				jObject.Add("kid", signingCredentials.Key.KeyId);
			}
			if (signingCredentials.Key is X509SecurityKey x509SecurityKey)
			{
				jObject["x5t"] = x509SecurityKey.X5t;
			}
		}
		if (string.IsNullOrEmpty(tokenType))
		{
			jObject.Add("typ", "JWT");
		}
		else
		{
			jObject.Add("typ", tokenType);
		}
		return jObject;
	}

	public virtual string CreateToken(string payload)
	{
		if (string.IsNullOrEmpty(payload))
		{
			throw LogHelper.LogArgumentNullException("payload");
		}
		return CreateTokenPrivate(payload, null, null, null, null, null, null);
	}

	public virtual string CreateToken(string payload, IDictionary<string, object> additionalHeaderClaims)
	{
		if (string.IsNullOrEmpty(payload))
		{
			throw LogHelper.LogArgumentNullException("payload");
		}
		if (additionalHeaderClaims == null)
		{
			throw LogHelper.LogArgumentNullException("additionalHeaderClaims");
		}
		return CreateTokenPrivate(payload, null, null, null, additionalHeaderClaims, null, null);
	}

	public virtual string CreateToken(string payload, SigningCredentials signingCredentials)
	{
		if (string.IsNullOrEmpty(payload))
		{
			throw LogHelper.LogArgumentNullException("payload");
		}
		if (signingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("signingCredentials");
		}
		return CreateTokenPrivate(payload, signingCredentials, null, null, null, null, null);
	}

	public virtual string CreateToken(string payload, SigningCredentials signingCredentials, IDictionary<string, object> additionalHeaderClaims)
	{
		if (string.IsNullOrEmpty(payload))
		{
			throw LogHelper.LogArgumentNullException("payload");
		}
		if (signingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("signingCredentials");
		}
		if (additionalHeaderClaims == null)
		{
			throw LogHelper.LogArgumentNullException("additionalHeaderClaims");
		}
		return CreateTokenPrivate(payload, signingCredentials, null, null, additionalHeaderClaims, null, null);
	}

	public virtual string CreateToken(SecurityTokenDescriptor tokenDescriptor)
	{
		if (tokenDescriptor == null)
		{
			throw LogHelper.LogArgumentNullException("tokenDescriptor");
		}
		if ((tokenDescriptor.Subject == null || !tokenDescriptor.Subject.Claims.Any()) && (tokenDescriptor.Claims == null || !tokenDescriptor.Claims.Any()))
		{
			LogHelper.LogWarning("IDX14114: Both '{0}.{1}' and '{0}.{2}' are null or empty.", LogHelper.MarkAsNonPII("SecurityTokenDescriptor"), LogHelper.MarkAsNonPII("Subject"), LogHelper.MarkAsNonPII("Claims"));
		}
		JObject jObject = ((tokenDescriptor.Subject == null) ? new JObject() : JObject.FromObject(TokenUtilities.CreateDictionaryFromClaims(tokenDescriptor.Subject.Claims)));
		if (tokenDescriptor.Claims != null && tokenDescriptor.Claims.Count > 0)
		{
			jObject.Merge(JObject.FromObject(tokenDescriptor.Claims), new JsonMergeSettings
			{
				MergeArrayHandling = MergeArrayHandling.Replace
			});
		}
		if (tokenDescriptor.Audience != null)
		{
			if (jObject.ContainsKey("aud"))
			{
				LogHelper.LogInformation(LogHelper.FormatInvariant("IDX14113: A duplicate value for 'SecurityTokenDescriptor.{0}' exists in 'SecurityTokenDescriptor.Claims'. \nThe value of 'SecurityTokenDescriptor.{0}' is used.", LogHelper.MarkAsNonPII("Audience")));
			}
			jObject["aud"] = tokenDescriptor.Audience;
		}
		if (tokenDescriptor.Expires.HasValue)
		{
			if (jObject.ContainsKey("exp"))
			{
				LogHelper.LogInformation(LogHelper.FormatInvariant("IDX14113: A duplicate value for 'SecurityTokenDescriptor.{0}' exists in 'SecurityTokenDescriptor.Claims'. \nThe value of 'SecurityTokenDescriptor.{0}' is used.", LogHelper.MarkAsNonPII("Expires")));
			}
			jObject["exp"] = EpochTime.GetIntDate(tokenDescriptor.Expires.Value);
		}
		if (tokenDescriptor.Issuer != null)
		{
			if (jObject.ContainsKey("iss"))
			{
				LogHelper.LogInformation(LogHelper.FormatInvariant("IDX14113: A duplicate value for 'SecurityTokenDescriptor.{0}' exists in 'SecurityTokenDescriptor.Claims'. \nThe value of 'SecurityTokenDescriptor.{0}' is used.", LogHelper.MarkAsNonPII("Issuer")));
			}
			jObject["iss"] = tokenDescriptor.Issuer;
		}
		if (tokenDescriptor.IssuedAt.HasValue)
		{
			if (jObject.ContainsKey("iat"))
			{
				LogHelper.LogInformation(LogHelper.FormatInvariant("IDX14113: A duplicate value for 'SecurityTokenDescriptor.{0}' exists in 'SecurityTokenDescriptor.Claims'. \nThe value of 'SecurityTokenDescriptor.{0}' is used.", LogHelper.MarkAsNonPII("IssuedAt")));
			}
			jObject["iat"] = EpochTime.GetIntDate(tokenDescriptor.IssuedAt.Value);
		}
		if (tokenDescriptor.NotBefore.HasValue)
		{
			if (jObject.ContainsKey("nbf"))
			{
				LogHelper.LogInformation(LogHelper.FormatInvariant("IDX14113: A duplicate value for 'SecurityTokenDescriptor.{0}' exists in 'SecurityTokenDescriptor.Claims'. \nThe value of 'SecurityTokenDescriptor.{0}' is used.", LogHelper.MarkAsNonPII("NotBefore")));
			}
			jObject["nbf"] = EpochTime.GetIntDate(tokenDescriptor.NotBefore.Value);
		}
		return CreateTokenPrivate(jObject.ToString(Formatting.None), tokenDescriptor.SigningCredentials, tokenDescriptor.EncryptingCredentials, tokenDescriptor.CompressionAlgorithm, tokenDescriptor.AdditionalHeaderClaims, tokenDescriptor.AdditionalInnerHeaderClaims, tokenDescriptor.TokenType);
	}

	public virtual string CreateToken(string payload, EncryptingCredentials encryptingCredentials)
	{
		if (string.IsNullOrEmpty(payload))
		{
			throw LogHelper.LogArgumentNullException("payload");
		}
		if (encryptingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("encryptingCredentials");
		}
		return CreateTokenPrivate(payload, null, encryptingCredentials, null, null, null, null);
	}

	public virtual string CreateToken(string payload, EncryptingCredentials encryptingCredentials, IDictionary<string, object> additionalHeaderClaims)
	{
		if (string.IsNullOrEmpty(payload))
		{
			throw LogHelper.LogArgumentNullException("payload");
		}
		if (encryptingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("encryptingCredentials");
		}
		if (additionalHeaderClaims == null)
		{
			throw LogHelper.LogArgumentNullException("additionalHeaderClaims");
		}
		return CreateTokenPrivate(payload, null, encryptingCredentials, null, additionalHeaderClaims, null, null);
	}

	public virtual string CreateToken(string payload, SigningCredentials signingCredentials, EncryptingCredentials encryptingCredentials)
	{
		if (string.IsNullOrEmpty(payload))
		{
			throw LogHelper.LogArgumentNullException("payload");
		}
		if (signingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("signingCredentials");
		}
		if (encryptingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("encryptingCredentials");
		}
		return CreateTokenPrivate(payload, signingCredentials, encryptingCredentials, null, null, null, null);
	}

	public virtual string CreateToken(string payload, SigningCredentials signingCredentials, EncryptingCredentials encryptingCredentials, IDictionary<string, object> additionalHeaderClaims)
	{
		if (string.IsNullOrEmpty(payload))
		{
			throw LogHelper.LogArgumentNullException("payload");
		}
		if (signingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("signingCredentials");
		}
		if (encryptingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("encryptingCredentials");
		}
		if (additionalHeaderClaims == null)
		{
			throw LogHelper.LogArgumentNullException("additionalHeaderClaims");
		}
		return CreateTokenPrivate(payload, signingCredentials, encryptingCredentials, null, additionalHeaderClaims, null, null);
	}

	public virtual string CreateToken(string payload, EncryptingCredentials encryptingCredentials, string compressionAlgorithm)
	{
		if (string.IsNullOrEmpty(payload))
		{
			throw LogHelper.LogArgumentNullException("payload");
		}
		if (encryptingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("encryptingCredentials");
		}
		if (string.IsNullOrEmpty(compressionAlgorithm))
		{
			throw LogHelper.LogArgumentNullException("compressionAlgorithm");
		}
		return CreateTokenPrivate(payload, null, encryptingCredentials, compressionAlgorithm, null, null, null);
	}

	public virtual string CreateToken(string payload, SigningCredentials signingCredentials, EncryptingCredentials encryptingCredentials, string compressionAlgorithm)
	{
		if (string.IsNullOrEmpty(payload))
		{
			throw LogHelper.LogArgumentNullException("payload");
		}
		if (signingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("signingCredentials");
		}
		if (encryptingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("encryptingCredentials");
		}
		if (string.IsNullOrEmpty(compressionAlgorithm))
		{
			throw LogHelper.LogArgumentNullException("compressionAlgorithm");
		}
		return CreateTokenPrivate(payload, signingCredentials, encryptingCredentials, compressionAlgorithm, null, null, null);
	}

	public virtual string CreateToken(string payload, SigningCredentials signingCredentials, EncryptingCredentials encryptingCredentials, string compressionAlgorithm, IDictionary<string, object> additionalHeaderClaims, IDictionary<string, object> additionalInnerHeaderClaims)
	{
		if (string.IsNullOrEmpty(payload))
		{
			throw LogHelper.LogArgumentNullException("payload");
		}
		if (signingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("signingCredentials");
		}
		if (encryptingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("encryptingCredentials");
		}
		if (string.IsNullOrEmpty(compressionAlgorithm))
		{
			throw LogHelper.LogArgumentNullException("compressionAlgorithm");
		}
		if (additionalHeaderClaims == null)
		{
			throw LogHelper.LogArgumentNullException("additionalHeaderClaims");
		}
		if (additionalInnerHeaderClaims == null)
		{
			throw LogHelper.LogArgumentNullException("additionalInnerHeaderClaims");
		}
		return CreateTokenPrivate(payload, signingCredentials, encryptingCredentials, compressionAlgorithm, additionalHeaderClaims, additionalInnerHeaderClaims, null);
	}

	public virtual string CreateToken(string payload, SigningCredentials signingCredentials, EncryptingCredentials encryptingCredentials, string compressionAlgorithm, IDictionary<string, object> additionalHeaderClaims)
	{
		if (string.IsNullOrEmpty(payload))
		{
			throw LogHelper.LogArgumentNullException("payload");
		}
		if (signingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("signingCredentials");
		}
		if (encryptingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("encryptingCredentials");
		}
		if (string.IsNullOrEmpty(compressionAlgorithm))
		{
			throw LogHelper.LogArgumentNullException("compressionAlgorithm");
		}
		if (additionalHeaderClaims == null)
		{
			throw LogHelper.LogArgumentNullException("additionalHeaderClaims");
		}
		return CreateTokenPrivate(payload, signingCredentials, encryptingCredentials, compressionAlgorithm, additionalHeaderClaims, null, null);
	}

	private string CreateTokenPrivate(string payload, SigningCredentials signingCredentials, EncryptingCredentials encryptingCredentials, string compressionAlgorithm, IDictionary<string, object> additionalHeaderClaims, IDictionary<string, object> additionalInnerHeaderClaims, string tokenType)
	{
		if (additionalHeaderClaims != null && additionalHeaderClaims.Count > 0 && additionalHeaderClaims.Keys.Intersect<string>(JwtTokenUtilities.DefaultHeaderParameters, StringComparer.OrdinalIgnoreCase).Any())
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenException(LogHelper.FormatInvariant("IDX14116: '{0}' cannot contain the following claims: '{1}'. These values are added by default (if necessary) during security token creation.", LogHelper.MarkAsNonPII("additionalHeaderClaims"), LogHelper.MarkAsNonPII(string.Join(", ", JwtTokenUtilities.DefaultHeaderParameters)))));
		}
		if (additionalInnerHeaderClaims != null && additionalInnerHeaderClaims.Count > 0 && additionalInnerHeaderClaims.Keys.Intersect<string>(JwtTokenUtilities.DefaultHeaderParameters, StringComparer.OrdinalIgnoreCase).Any())
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenException(LogHelper.FormatInvariant("IDX14116: '{0}' cannot contain the following claims: '{1}'. These values are added by default (if necessary) during security token creation.", "additionalInnerHeaderClaims", string.Join(", ", JwtTokenUtilities.DefaultHeaderParameters))));
		}
		JObject jObject = CreateDefaultJWSHeader(signingCredentials, tokenType);
		if (encryptingCredentials == null && additionalHeaderClaims != null && additionalHeaderClaims.Count > 0)
		{
			jObject.Merge(JObject.FromObject(additionalHeaderClaims));
		}
		if (additionalInnerHeaderClaims != null && additionalInnerHeaderClaims.Count > 0)
		{
			jObject.Merge(JObject.FromObject(additionalInnerHeaderClaims));
		}
		string text = Base64UrlEncoder.Encode(Encoding.UTF8.GetBytes(jObject.ToString(Formatting.None)));
		JObject jObject2 = null;
		try
		{
			if (base.SetDefaultTimesOnTokenCreation)
			{
				jObject2 = JObject.Parse(payload);
				if (jObject2 != null)
				{
					long intDate = EpochTime.GetIntDate(DateTime.UtcNow);
					if (!jObject2.TryGetValue("exp", out JToken value))
					{
						jObject2.Add("exp", intDate + base.TokenLifetimeInMinutes * 60);
					}
					if (!jObject2.TryGetValue("iat", out value))
					{
						jObject2.Add("iat", intDate);
					}
					if (!jObject2.TryGetValue("nbf", out value))
					{
						jObject2.Add("nbf", intDate);
					}
				}
			}
		}
		catch (Exception innerException)
		{
			LogHelper.LogExceptionMessage(new SecurityTokenException("IDX14307: JWE header is missing.", innerException));
		}
		payload = ((jObject2 != null) ? jObject2.ToString(Formatting.None) : payload);
		string text2 = Base64UrlEncoder.Encode(Encoding.UTF8.GetBytes(payload));
		string text3 = text + "." + text2;
		string text4 = ((signingCredentials == null) ? string.Empty : JwtTokenUtilities.CreateEncodedSignature(text3, signingCredentials));
		if (encryptingCredentials != null)
		{
			additionalHeaderClaims = AddCtyClaimDefaultValue(additionalHeaderClaims, encryptingCredentials.SetDefaultCtyClaim);
			return EncryptTokenPrivate(text3 + "." + text4, encryptingCredentials, compressionAlgorithm, additionalHeaderClaims, tokenType);
		}
		return text3 + "." + text4;
	}

	private static byte[] CompressToken(string token, string compressionAlgorithm)
	{
		if (token == null)
		{
			throw LogHelper.LogArgumentNullException("token");
		}
		if (string.IsNullOrEmpty(compressionAlgorithm))
		{
			throw LogHelper.LogArgumentNullException("compressionAlgorithm");
		}
		if (!CompressionProviderFactory.Default.IsSupportedAlgorithm(compressionAlgorithm))
		{
			throw LogHelper.LogExceptionMessage(new NotSupportedException(LogHelper.FormatInvariant("IDX10682: Compression algorithm '{0}' is not supported.", LogHelper.MarkAsNonPII(compressionAlgorithm))));
		}
		return CompressionProviderFactory.Default.CreateCompressionProvider(compressionAlgorithm).Compress(Encoding.UTF8.GetBytes(token)) ?? throw LogHelper.LogExceptionMessage(new InvalidOperationException(LogHelper.FormatInvariant("IDX10680: Failed to compress using algorithm '{0}'.", LogHelper.MarkAsNonPII(compressionAlgorithm))));
	}

	private static StringComparison GetStringComparisonRuleIf509(SecurityKey securityKey)
	{
		if (!(securityKey is X509SecurityKey))
		{
			return StringComparison.Ordinal;
		}
		return StringComparison.OrdinalIgnoreCase;
	}

	private static StringComparison GetStringComparisonRuleIf509OrECDsa(SecurityKey securityKey)
	{
		if (!(securityKey is X509SecurityKey) && !(securityKey is ECDsaSecurityKey))
		{
			return StringComparison.Ordinal;
		}
		return StringComparison.OrdinalIgnoreCase;
	}

	protected virtual ClaimsIdentity CreateClaimsIdentity(JsonWebToken jwtToken, TokenValidationParameters validationParameters)
	{
		if (jwtToken == null)
		{
			throw LogHelper.LogArgumentNullException("jwtToken");
		}
		return CreateClaimsIdentityPrivate(jwtToken, validationParameters, GetActualIssuer(jwtToken));
	}

	protected virtual ClaimsIdentity CreateClaimsIdentity(JsonWebToken jwtToken, TokenValidationParameters validationParameters, string issuer)
	{
		if (jwtToken == null)
		{
			throw LogHelper.LogArgumentNullException("jwtToken");
		}
		if (string.IsNullOrWhiteSpace(issuer))
		{
			issuer = GetActualIssuer(jwtToken);
		}
		if (MapInboundClaims)
		{
			return CreateClaimsIdentityWithMapping(jwtToken, validationParameters, issuer);
		}
		return CreateClaimsIdentityPrivate(jwtToken, validationParameters, issuer);
	}

	private ClaimsIdentity CreateClaimsIdentityWithMapping(JsonWebToken jwtToken, TokenValidationParameters validationParameters, string issuer)
	{
		if (validationParameters == null)
		{
			throw LogHelper.LogArgumentNullException("validationParameters");
		}
		ClaimsIdentity claimsIdentity = validationParameters.CreateClaimsIdentity(jwtToken, issuer);
		foreach (Claim claim2 in jwtToken.Claims)
		{
			string value;
			bool flag = _inboundClaimTypeMap.TryGetValue(claim2.Type, out value);
			if (!flag)
			{
				value = claim2.Type;
			}
			if (value == "http://schemas.xmlsoap.org/ws/2009/09/identity/claims/actor")
			{
				if (claimsIdentity.Actor != null)
				{
					throw LogHelper.LogExceptionMessage(new InvalidOperationException(LogHelper.FormatInvariant("IDX14112: Only a single 'Actor' is supported. Found second claim of type: '{0}'", LogHelper.MarkAsNonPII("actort"), claim2.Value)));
				}
				if (CanReadToken(claim2.Value))
				{
					JsonWebToken jwtToken2 = ReadToken(claim2.Value) as JsonWebToken;
					claimsIdentity.Actor = CreateClaimsIdentity(jwtToken2, validationParameters);
				}
			}
			if (flag)
			{
				Claim claim = new Claim(value, claim2.Value, claim2.ValueType, issuer, issuer, claimsIdentity);
				if (claim2.Properties.Count > 0)
				{
					foreach (KeyValuePair<string, string> property in claim2.Properties)
					{
						claim.Properties[property.Key] = property.Value;
					}
				}
				claim.Properties[ShortClaimTypeProperty] = claim2.Type;
				claimsIdentity.AddClaim(claim);
			}
			else
			{
				claimsIdentity.AddClaim(claim2);
			}
		}
		return claimsIdentity;
	}

	internal override ClaimsIdentity CreateClaimsIdentityInternal(SecurityToken securityToken, TokenValidationParameters tokenValidationParameters, string issuer)
	{
		return CreateClaimsIdentity(securityToken as JsonWebToken, tokenValidationParameters, issuer);
	}

	private static string GetActualIssuer(JsonWebToken jwtToken)
	{
		string text = jwtToken.Issuer;
		if (string.IsNullOrWhiteSpace(text))
		{
			LogHelper.LogVerbose("IDX10244: Issuer is null or empty. Using runtime default for creating claims '{0}'.", "LOCAL AUTHORITY");
			text = "LOCAL AUTHORITY";
		}
		return text;
	}

	private ClaimsIdentity CreateClaimsIdentityPrivate(JsonWebToken jwtToken, TokenValidationParameters validationParameters, string issuer)
	{
		if (validationParameters == null)
		{
			throw LogHelper.LogArgumentNullException("validationParameters");
		}
		ClaimsIdentity claimsIdentity = validationParameters.CreateClaimsIdentity(jwtToken, issuer);
		foreach (Claim claim2 in jwtToken.Claims)
		{
			string type = claim2.Type;
			if (type == "http://schemas.xmlsoap.org/ws/2009/09/identity/claims/actor")
			{
				if (claimsIdentity.Actor != null)
				{
					throw LogHelper.LogExceptionMessage(new InvalidOperationException(LogHelper.FormatInvariant("IDX14112: Only a single 'Actor' is supported. Found second claim of type: '{0}'", LogHelper.MarkAsNonPII("actort"), claim2.Value)));
				}
				if (CanReadToken(claim2.Value))
				{
					JsonWebToken jwtToken2 = ReadToken(claim2.Value) as JsonWebToken;
					claimsIdentity.Actor = CreateClaimsIdentity(jwtToken2, validationParameters, issuer);
				}
			}
			if (claim2.Properties.Count == 0)
			{
				claimsIdentity.AddClaim(new Claim(type, claim2.Value, claim2.ValueType, issuer, issuer, claimsIdentity));
				continue;
			}
			Claim claim = new Claim(type, claim2.Value, claim2.ValueType, issuer, issuer, claimsIdentity);
			foreach (KeyValuePair<string, string> property in claim2.Properties)
			{
				claim.Properties[property.Key] = property.Value;
			}
			claimsIdentity.AddClaim(claim);
		}
		return claimsIdentity;
	}

	public string DecryptToken(JsonWebToken jwtToken, TokenValidationParameters validationParameters)
	{
		return DecryptToken(jwtToken, validationParameters, null);
	}

	private string DecryptToken(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
	{
		if (jwtToken == null)
		{
			throw LogHelper.LogArgumentNullException("jwtToken");
		}
		if (validationParameters == null)
		{
			throw LogHelper.LogArgumentNullException("validationParameters");
		}
		if (string.IsNullOrEmpty(jwtToken.Enc))
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenException(LogHelper.FormatInvariant("IDX10612: Decryption failed. Header.Enc is null or empty, it must be specified.")));
		}
		IEnumerable<SecurityKey> contentEncryptionKeys = GetContentEncryptionKeys(jwtToken, validationParameters, configuration);
		return JwtTokenUtilities.DecryptJwtToken(jwtToken, validationParameters, new JwtTokenDecryptionParameters
		{
			DecompressionFunction = JwtTokenUtilities.DecompressToken,
			Keys = contentEncryptionKeys,
			MaximumDeflateSize = MaximumTokenSizeInBytes
		});
	}

	public string EncryptToken(string innerJwt, EncryptingCredentials encryptingCredentials)
	{
		if (string.IsNullOrEmpty(innerJwt))
		{
			throw LogHelper.LogArgumentNullException("innerJwt");
		}
		if (encryptingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("encryptingCredentials");
		}
		return EncryptTokenPrivate(innerJwt, encryptingCredentials, null, null, null);
	}

	public string EncryptToken(string innerJwt, EncryptingCredentials encryptingCredentials, IDictionary<string, object> additionalHeaderClaims)
	{
		if (string.IsNullOrEmpty(innerJwt))
		{
			throw LogHelper.LogArgumentNullException("innerJwt");
		}
		if (encryptingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("encryptingCredentials");
		}
		if (additionalHeaderClaims == null)
		{
			throw LogHelper.LogArgumentNullException("additionalHeaderClaims");
		}
		return EncryptTokenPrivate(innerJwt, encryptingCredentials, null, additionalHeaderClaims, null);
	}

	public string EncryptToken(string innerJwt, EncryptingCredentials encryptingCredentials, string algorithm)
	{
		if (string.IsNullOrEmpty(innerJwt))
		{
			throw LogHelper.LogArgumentNullException("innerJwt");
		}
		if (encryptingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("encryptingCredentials");
		}
		if (string.IsNullOrEmpty(algorithm))
		{
			throw LogHelper.LogArgumentNullException("algorithm");
		}
		return EncryptTokenPrivate(innerJwt, encryptingCredentials, algorithm, null, null);
	}

	public string EncryptToken(string innerJwt, EncryptingCredentials encryptingCredentials, string algorithm, IDictionary<string, object> additionalHeaderClaims)
	{
		if (string.IsNullOrEmpty(innerJwt))
		{
			throw LogHelper.LogArgumentNullException("innerJwt");
		}
		if (encryptingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("encryptingCredentials");
		}
		if (string.IsNullOrEmpty(algorithm))
		{
			throw LogHelper.LogArgumentNullException("algorithm");
		}
		if (additionalHeaderClaims == null)
		{
			throw LogHelper.LogArgumentNullException("additionalHeaderClaims");
		}
		return EncryptTokenPrivate(innerJwt, encryptingCredentials, algorithm, additionalHeaderClaims, null);
	}

	private static string EncryptTokenPrivate(string innerJwt, EncryptingCredentials encryptingCredentials, string compressionAlgorithm, IDictionary<string, object> additionalHeaderClaims, string tokenType)
	{
		CryptoProviderFactory cryptoProviderFactory = encryptingCredentials.CryptoProviderFactory ?? encryptingCredentials.Key.CryptoProviderFactory;
		if (cryptoProviderFactory == null)
		{
			throw LogHelper.LogExceptionMessage(new ArgumentException("IDX10620: Unable to obtain a CryptoProviderFactory, both EncryptingCredentials.CryptoProviderFactory and EncryptingCredentials.Key.CrypoProviderFactory are null."));
		}
		byte[] wrappedKey = null;
		SecurityKey securityKey = JwtTokenUtilities.GetSecurityKey(encryptingCredentials, cryptoProviderFactory, additionalHeaderClaims, out wrappedKey);
		using AuthenticatedEncryptionProvider authenticatedEncryptionProvider = cryptoProviderFactory.CreateAuthenticatedEncryptionProvider(securityKey, encryptingCredentials.Enc);
		if (authenticatedEncryptionProvider == null)
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenEncryptionFailedException("IDX14103: Failed to create the token encryption provider."));
		}
		JObject jObject = CreateDefaultJWEHeader(encryptingCredentials, compressionAlgorithm, tokenType);
		if (additionalHeaderClaims != null)
		{
			jObject.Merge(JObject.FromObject(additionalHeaderClaims));
		}
		byte[] plaintext;
		if (!string.IsNullOrEmpty(compressionAlgorithm))
		{
			try
			{
				plaintext = CompressToken(innerJwt, compressionAlgorithm);
			}
			catch (Exception inner)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenCompressionFailedException(LogHelper.FormatInvariant("IDX10680: Failed to compress using algorithm '{0}'.", LogHelper.MarkAsNonPII(compressionAlgorithm)), inner));
			}
		}
		else
		{
			plaintext = Encoding.UTF8.GetBytes(innerJwt);
		}
		try
		{
			string text = Base64UrlEncoder.Encode(Encoding.UTF8.GetBytes(jObject.ToString(Formatting.None)));
			AuthenticatedEncryptionResult authenticatedEncryptionResult = authenticatedEncryptionProvider.Encrypt(plaintext, Encoding.ASCII.GetBytes(text));
			return "dir".Equals(encryptingCredentials.Alg) ? string.Join(".", text, string.Empty, Base64UrlEncoder.Encode(authenticatedEncryptionResult.IV), Base64UrlEncoder.Encode(authenticatedEncryptionResult.Ciphertext), Base64UrlEncoder.Encode(authenticatedEncryptionResult.AuthenticationTag)) : string.Join(".", text, Base64UrlEncoder.Encode(wrappedKey), Base64UrlEncoder.Encode(authenticatedEncryptionResult.IV), Base64UrlEncoder.Encode(authenticatedEncryptionResult.Ciphertext), Base64UrlEncoder.Encode(authenticatedEncryptionResult.AuthenticationTag));
		}
		catch (Exception innerException)
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenEncryptionFailedException(LogHelper.FormatInvariant("IDX10616: Encryption failed. EncryptionProvider failed for: Algorithm: '{0}', SecurityKey: '{1}'. See inner exception.", LogHelper.MarkAsNonPII(encryptingCredentials.Enc), encryptingCredentials.Key), innerException));
		}
	}

	private static SecurityKey ResolveTokenDecryptionKeyFromConfig(JsonWebToken jwtToken, BaseConfiguration configuration)
	{
		if (jwtToken == null)
		{
			throw LogHelper.LogArgumentNullException("jwtToken");
		}
		if (!string.IsNullOrEmpty(jwtToken.Kid) && configuration.TokenDecryptionKeys != null)
		{
			foreach (SecurityKey tokenDecryptionKey in configuration.TokenDecryptionKeys)
			{
				if (tokenDecryptionKey != null && string.Equals(tokenDecryptionKey.KeyId, jwtToken.Kid, GetStringComparisonRuleIf509OrECDsa(tokenDecryptionKey)))
				{
					return tokenDecryptionKey;
				}
			}
		}
		if (!string.IsNullOrEmpty(jwtToken.X5t) && configuration.TokenDecryptionKeys != null)
		{
			foreach (SecurityKey tokenDecryptionKey2 in configuration.TokenDecryptionKeys)
			{
				if (tokenDecryptionKey2 != null && string.Equals(tokenDecryptionKey2.KeyId, jwtToken.X5t, GetStringComparisonRuleIf509(tokenDecryptionKey2)))
				{
					return tokenDecryptionKey2;
				}
				if (tokenDecryptionKey2 is X509SecurityKey x509SecurityKey && string.Equals(x509SecurityKey.X5t, jwtToken.X5t, StringComparison.OrdinalIgnoreCase))
				{
					return tokenDecryptionKey2;
				}
			}
		}
		return null;
	}

	internal IEnumerable<SecurityKey> GetContentEncryptionKeys(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
	{
		IEnumerable<SecurityKey> enumerable = null;
		if (validationParameters.TokenDecryptionKeyResolver != null)
		{
			enumerable = validationParameters.TokenDecryptionKeyResolver(jwtToken.EncodedToken, jwtToken, jwtToken.Kid, validationParameters);
		}
		else
		{
			SecurityKey securityKey = ResolveTokenDecryptionKey(jwtToken.EncodedToken, jwtToken, validationParameters);
			if (securityKey != null)
			{
				LogHelper.LogInformation("IDX10904: Token decryption key : '{0}' found in TokenValidationParameters.", securityKey);
			}
			else if (configuration != null)
			{
				securityKey = ResolveTokenDecryptionKeyFromConfig(jwtToken, configuration);
				if (securityKey != null)
				{
					LogHelper.LogInformation("IDX10905: Token decryption key : '{0}' found in Configuration/Metadata.", securityKey);
				}
			}
			if (securityKey != null)
			{
				enumerable = new List<SecurityKey> { securityKey };
			}
		}
		if (enumerable == null)
		{
			enumerable = JwtTokenUtilities.GetAllDecryptionKeys(validationParameters);
			if (configuration != null)
			{
				IEnumerable<SecurityKey> enumerable2;
				if (enumerable != null)
				{
					enumerable2 = enumerable.Concat(configuration.TokenDecryptionKeys);
				}
				else
				{
					IEnumerable<SecurityKey> tokenDecryptionKeys = configuration.TokenDecryptionKeys;
					enumerable2 = tokenDecryptionKeys;
				}
				enumerable = enumerable2;
			}
		}
		if (jwtToken.Alg.Equals("dir", StringComparison.Ordinal) || jwtToken.Alg.Equals("ECDH-ES", StringComparison.Ordinal))
		{
			return enumerable;
		}
		List<SecurityKey> list = new List<SecurityKey>();
		StringBuilder stringBuilder = new StringBuilder();
		StringBuilder stringBuilder2 = new StringBuilder();
		foreach (SecurityKey item in enumerable)
		{
			try
			{
				if (SupportedAlgorithms.EcdsaWrapAlgorithms.Contains(jwtToken.Alg))
				{
					EcdhKeyExchangeProvider ecdhKeyExchangeProvider = new EcdhKeyExchangeProvider(item as ECDsaSecurityKey, validationParameters.TokenDecryptionKey as ECDsaSecurityKey, jwtToken.Alg, jwtToken.Enc);
					jwtToken.TryGetHeaderValue<string>("apu", out var value);
					jwtToken.TryGetHeaderValue<string>("apv", out var value2);
					SecurityKey key = ecdhKeyExchangeProvider.GenerateKdf(value, value2);
					byte[] key2 = item.CryptoProviderFactory.CreateKeyWrapProviderForUnwrap(key, ecdhKeyExchangeProvider.GetEncryptionAlgorithm()).UnwrapKey(Base64UrlEncoder.DecodeBytes(jwtToken.EncryptedKey));
					list.Add(new SymmetricSecurityKey(key2));
				}
				else if (item.CryptoProviderFactory.IsSupportedAlgorithm(jwtToken.Alg, item))
				{
					byte[] key3 = item.CryptoProviderFactory.CreateKeyWrapProviderForUnwrap(item, jwtToken.Alg).UnwrapKey(jwtToken.EncryptedKeyBytes);
					list.Add(new SymmetricSecurityKey(key3));
				}
			}
			catch (Exception ex)
			{
				stringBuilder.AppendLine(ex.ToString());
			}
			stringBuilder2.AppendLine(item.ToString());
		}
		if (list.Count > 0 && stringBuilder.Length == 0)
		{
			return list;
		}
		throw LogHelper.LogExceptionMessage(new SecurityTokenKeyWrapException(LogHelper.FormatInvariant("IDX10618: Key unwrap failed using decryption Keys: '{0}'.\nExceptions caught:\n '{1}'.\ntoken: '{2}'.", stringBuilder2, stringBuilder, jwtToken)));
	}

	protected virtual SecurityKey ResolveTokenDecryptionKey(string token, JsonWebToken jwtToken, TokenValidationParameters validationParameters)
	{
		if (jwtToken == null)
		{
			throw LogHelper.LogArgumentNullException("jwtToken");
		}
		if (validationParameters == null)
		{
			throw LogHelper.LogArgumentNullException("validationParameters");
		}
		StringComparison stringComparisonRuleIf509OrECDsa = GetStringComparisonRuleIf509OrECDsa(validationParameters.TokenDecryptionKey);
		if (!string.IsNullOrEmpty(jwtToken.Kid))
		{
			if (validationParameters.TokenDecryptionKey != null && string.Equals(validationParameters.TokenDecryptionKey.KeyId, jwtToken.Kid, stringComparisonRuleIf509OrECDsa))
			{
				return validationParameters.TokenDecryptionKey;
			}
			if (validationParameters.TokenDecryptionKeys != null)
			{
				foreach (SecurityKey tokenDecryptionKey in validationParameters.TokenDecryptionKeys)
				{
					if (tokenDecryptionKey != null && string.Equals(tokenDecryptionKey.KeyId, jwtToken.Kid, GetStringComparisonRuleIf509OrECDsa(tokenDecryptionKey)))
					{
						return tokenDecryptionKey;
					}
				}
			}
		}
		if (!string.IsNullOrEmpty(jwtToken.X5t))
		{
			if (validationParameters.TokenDecryptionKey != null)
			{
				if (string.Equals(validationParameters.TokenDecryptionKey.KeyId, jwtToken.X5t, stringComparisonRuleIf509OrECDsa))
				{
					return validationParameters.TokenDecryptionKey;
				}
				if (validationParameters.TokenDecryptionKey is X509SecurityKey x509SecurityKey && string.Equals(x509SecurityKey.X5t, jwtToken.X5t, StringComparison.OrdinalIgnoreCase))
				{
					return validationParameters.TokenDecryptionKey;
				}
			}
			if (validationParameters.TokenDecryptionKeys != null)
			{
				foreach (SecurityKey tokenDecryptionKey2 in validationParameters.TokenDecryptionKeys)
				{
					if (tokenDecryptionKey2 != null && string.Equals(tokenDecryptionKey2.KeyId, jwtToken.X5t, GetStringComparisonRuleIf509(tokenDecryptionKey2)))
					{
						return tokenDecryptionKey2;
					}
					if (tokenDecryptionKey2 is X509SecurityKey x509SecurityKey2 && string.Equals(x509SecurityKey2.X5t, jwtToken.X5t, StringComparison.OrdinalIgnoreCase))
					{
						return tokenDecryptionKey2;
					}
				}
			}
		}
		return null;
	}

	public virtual JsonWebToken ReadJsonWebToken(string token)
	{
		if (string.IsNullOrEmpty(token))
		{
			throw LogHelper.LogArgumentNullException("token");
		}
		if (token.Length > MaximumTokenSizeInBytes)
		{
			throw LogHelper.LogExceptionMessage(new ArgumentException(LogHelper.FormatInvariant("IDX10209: Token has length: '{0}' which is larger than the MaximumTokenSizeInBytes: '{1}'.", LogHelper.MarkAsNonPII(token.Length), LogHelper.MarkAsNonPII(MaximumTokenSizeInBytes))));
		}
		return new JsonWebToken(token);
	}

	public override SecurityToken ReadToken(string token)
	{
		return ReadJsonWebToken(token);
	}

	public virtual TokenValidationResult ValidateToken(string token, TokenValidationParameters validationParameters)
	{
		return ValidateTokenAsync(token, validationParameters).ConfigureAwait(continueOnCapturedContext: false).GetAwaiter().GetResult();
	}

	public override async Task<TokenValidationResult> ValidateTokenAsync(string token, TokenValidationParameters validationParameters)
	{
		if (string.IsNullOrEmpty(token))
		{
			return new TokenValidationResult
			{
				Exception = LogHelper.LogArgumentNullException("token"),
				IsValid = false
			};
		}
		if (validationParameters == null)
		{
			return new TokenValidationResult
			{
				Exception = LogHelper.LogArgumentNullException("validationParameters"),
				IsValid = false
			};
		}
		if (token.Length > MaximumTokenSizeInBytes)
		{
			TokenValidationResult tokenValidationResult = new TokenValidationResult();
			tokenValidationResult.Exception = LogHelper.LogExceptionMessage(new ArgumentException(LogHelper.FormatInvariant("IDX10209: Token has length: '{0}' which is larger than the MaximumTokenSizeInBytes: '{1}'.", LogHelper.MarkAsNonPII(token.Length), LogHelper.MarkAsNonPII(MaximumTokenSizeInBytes))));
			tokenValidationResult.IsValid = false;
			return tokenValidationResult;
		}
		try
		{
			TokenValidationResult tokenValidationResult2 = ReadToken(token, validationParameters);
			if (tokenValidationResult2.IsValid)
			{
				return await ValidateTokenAsync(tokenValidationResult2.SecurityToken, validationParameters).ConfigureAwait(continueOnCapturedContext: false);
			}
			return tokenValidationResult2;
		}
		catch (Exception exception)
		{
			return new TokenValidationResult
			{
				Exception = exception,
				IsValid = false
			};
		}
	}

	public override async Task<TokenValidationResult> ValidateTokenAsync(SecurityToken token, TokenValidationParameters validationParameters)
	{
		if (token == null)
		{
			throw LogHelper.LogArgumentNullException("token");
		}
		if (validationParameters == null)
		{
			return new TokenValidationResult
			{
				Exception = LogHelper.LogArgumentNullException("validationParameters"),
				IsValid = false
			};
		}
		if (!(token is JsonWebToken jsonWebToken))
		{
			return new TokenValidationResult
			{
				Exception = LogHelper.LogExceptionMessage(new SecurityTokenMalformedException("IDX14100: JWT is not well formed, there are no dots (.).\nThe token needs to be in JWS or JWE Compact Serialization Format. (JWS): 'EncodedHeader.EndcodedPayload.EncodedSignature'. (JWE): 'EncodedProtectedHeader.EncodedEncryptedKey.EncodedInitializationVector.EncodedCiphertext.EncodedAuthenticationTag'.")),
				IsValid = false
			};
		}
		try
		{
			return await ValidateTokenAsync(jsonWebToken, validationParameters).ConfigureAwait(continueOnCapturedContext: false);
		}
		catch (Exception exception)
		{
			return new TokenValidationResult
			{
				Exception = exception,
				IsValid = false
			};
		}
	}

	private static TokenValidationResult ReadToken(string token, TokenValidationParameters validationParameters)
	{
		JsonWebToken jsonWebToken = null;
		if (validationParameters.TokenReader != null)
		{
			SecurityToken securityToken = validationParameters.TokenReader(token, validationParameters);
			if (securityToken == null)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenMalformedException(LogHelper.FormatInvariant("IDX10510: Token validation failed. The user defined 'Delegate' set on TokenValidationParameters.TokenReader returned null when reading token: '{0}'.", LogHelper.MarkAsSecurityArtifact(token, JwtTokenUtilities.SafeLogJwtToken))));
			}
			jsonWebToken = securityToken as JsonWebToken;
			if (jsonWebToken == null)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenMalformedException(LogHelper.FormatInvariant("IDX10509: Token validation failed. The user defined 'Delegate' set on TokenValidationParameters.TokenReader did not return a '{0}', but returned a '{1}' when reading token: '{2}'.", typeof(JsonWebToken), securityToken.GetType(), LogHelper.MarkAsSecurityArtifact(token, JwtTokenUtilities.SafeLogJwtToken))));
			}
		}
		else
		{
			try
			{
				jsonWebToken = new JsonWebToken(token);
			}
			catch (Exception innerException)
			{
				return new TokenValidationResult
				{
					Exception = LogHelper.LogExceptionMessage(new SecurityTokenMalformedException("IDX14100: JWT is not well formed, there are no dots (.).\nThe token needs to be in JWS or JWE Compact Serialization Format. (JWS): 'EncodedHeader.EndcodedPayload.EncodedSignature'. (JWE): 'EncodedProtectedHeader.EncodedEncryptedKey.EncodedInitializationVector.EncodedCiphertext.EncodedAuthenticationTag'.", innerException)),
					IsValid = false
				};
			}
		}
		return new TokenValidationResult
		{
			SecurityToken = jsonWebToken,
			IsValid = true
		};
	}

	private async Task<TokenValidationResult> ValidateTokenAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters)
	{
		BaseConfiguration currentConfiguration = null;
		if (validationParameters.ConfigurationManager != null)
		{
			try
			{
				currentConfiguration = await validationParameters.ConfigurationManager.GetBaseConfigurationAsync(CancellationToken.None).ConfigureAwait(continueOnCapturedContext: false);
			}
			catch (Exception ex)
			{
				LogHelper.LogWarning(LogHelper.FormatInvariant("IDX10261: Unable to retrieve configuration from authority: '{0}'. \nProceeding with token validation in case the relevant properties have been set manually on the TokenValidationParameters. Exception caught: \n {1}. See https://aka.ms/validate-using-configuration-manager for additional information.", validationParameters.ConfigurationManager.MetadataAddress, ex.ToString()));
			}
		}
		TokenValidationResult tokenValidationResult = await ValidateTokenAsync(jsonWebToken, validationParameters, currentConfiguration).ConfigureAwait(continueOnCapturedContext: false);
		if (validationParameters.ConfigurationManager != null)
		{
			if (tokenValidationResult.IsValid)
			{
				if (currentConfiguration != null)
				{
					validationParameters.ConfigurationManager.LastKnownGoodConfiguration = currentConfiguration;
				}
				return tokenValidationResult;
			}
			if (TokenUtilities.IsRecoverableException(tokenValidationResult.Exception))
			{
				if (currentConfiguration != null)
				{
					validationParameters.ConfigurationManager.RequestRefresh();
					validationParameters.RefreshBeforeValidation = true;
					BaseConfiguration lastConfig = currentConfiguration;
					currentConfiguration = await validationParameters.ConfigurationManager.GetBaseConfigurationAsync(CancellationToken.None).ConfigureAwait(continueOnCapturedContext: false);
					if (lastConfig != currentConfiguration)
					{
						tokenValidationResult = await ValidateTokenAsync(jsonWebToken, validationParameters, currentConfiguration).ConfigureAwait(continueOnCapturedContext: false);
						if (tokenValidationResult.IsValid)
						{
							validationParameters.ConfigurationManager.LastKnownGoodConfiguration = currentConfiguration;
							return tokenValidationResult;
						}
					}
				}
				if (validationParameters.ConfigurationManager.UseLastKnownGoodConfiguration)
				{
					validationParameters.RefreshBeforeValidation = false;
					validationParameters.ValidateWithLKG = true;
					Exception recoverableException = tokenValidationResult.Exception;
					foreach (BaseConfiguration validLkgConfiguration in validationParameters.ConfigurationManager.GetValidLkgConfigurations())
					{
						if (!validLkgConfiguration.Equals(currentConfiguration) && TokenUtilities.IsRecoverableConfiguration(jsonWebToken.Kid, currentConfiguration, validLkgConfiguration, recoverableException))
						{
							tokenValidationResult = await ValidateTokenAsync(jsonWebToken, validationParameters, validLkgConfiguration).ConfigureAwait(continueOnCapturedContext: false);
							if (tokenValidationResult.IsValid)
							{
								return tokenValidationResult;
							}
						}
					}
				}
			}
		}
		return tokenValidationResult;
	}

	private async Task<TokenValidationResult> ValidateTokenAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
	{
		if (jsonWebToken.IsEncrypted)
		{
			return await ValidateJWEAsync(jsonWebToken, validationParameters, configuration).ConfigureAwait(continueOnCapturedContext: false);
		}
		return await ValidateJWSAsync(jsonWebToken, validationParameters, configuration).ConfigureAwait(continueOnCapturedContext: false);
	}

	private async Task<TokenValidationResult> ValidateJWSAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
	{
		_ = 2;
		try
		{
			if (validationParameters.TransformBeforeSignatureValidation != null)
			{
				jsonWebToken = validationParameters.TransformBeforeSignatureValidation(jsonWebToken, validationParameters) as JsonWebToken;
			}
			TokenValidationResult tokenValidationResult;
			if (validationParameters.SignatureValidator != null || validationParameters.SignatureValidatorUsingConfiguration != null)
			{
				JsonWebToken validatedToken = ValidateSignatureUsingDelegates(jsonWebToken, validationParameters, configuration);
				tokenValidationResult = await ValidateTokenPayloadAsync(validatedToken, validationParameters, configuration).ConfigureAwait(continueOnCapturedContext: false);
				Validators.ValidateIssuerSecurityKey(validatedToken.SigningKey, validatedToken, validationParameters, configuration);
			}
			else if (validationParameters.ValidateSignatureLast)
			{
				tokenValidationResult = await ValidateTokenPayloadAsync(jsonWebToken, validationParameters, configuration).ConfigureAwait(continueOnCapturedContext: false);
				if (tokenValidationResult.IsValid)
				{
					tokenValidationResult.SecurityToken = ValidateSignatureAndIssuerSecurityKey(jsonWebToken, validationParameters, configuration);
				}
			}
			else
			{
				JsonWebToken jsonWebToken2 = ValidateSignatureAndIssuerSecurityKey(jsonWebToken, validationParameters, configuration);
				tokenValidationResult = await ValidateTokenPayloadAsync(jsonWebToken2, validationParameters, configuration).ConfigureAwait(continueOnCapturedContext: false);
			}
			return tokenValidationResult;
		}
		catch (Exception exception)
		{
			return new TokenValidationResult
			{
				Exception = exception,
				IsValid = false,
				TokenOnFailedValidation = (validationParameters.IncludeTokenOnFailedValidation ? jsonWebToken : null)
			};
		}
	}

	private async Task<TokenValidationResult> ValidateJWEAsync(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
	{
		try
		{
			TokenValidationResult tokenValidationResult = ReadToken(DecryptToken(jwtToken, validationParameters, configuration), validationParameters);
			if (!tokenValidationResult.IsValid)
			{
				return tokenValidationResult;
			}
			tokenValidationResult = await ValidateJWSAsync(tokenValidationResult.SecurityToken as JsonWebToken, validationParameters, configuration).ConfigureAwait(continueOnCapturedContext: false);
			if (!tokenValidationResult.IsValid)
			{
				return tokenValidationResult;
			}
			jwtToken.InnerToken = tokenValidationResult.SecurityToken as JsonWebToken;
			jwtToken.Payload = (tokenValidationResult.SecurityToken as JsonWebToken).Payload;
			return new TokenValidationResult
			{
				SecurityToken = jwtToken,
				ClaimsIdentity = tokenValidationResult.ClaimsIdentity,
				IsValid = true,
				TokenType = tokenValidationResult.TokenType
			};
		}
		catch (Exception exception)
		{
			return new TokenValidationResult
			{
				Exception = exception,
				IsValid = false,
				TokenOnFailedValidation = (validationParameters.IncludeTokenOnFailedValidation ? jwtToken : null)
			};
		}
	}

	private static JsonWebToken ValidateSignatureUsingDelegates(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
	{
		if (validationParameters.SignatureValidatorUsingConfiguration != null)
		{
			SecurityToken securityToken = validationParameters.SignatureValidatorUsingConfiguration(jsonWebToken.EncodedToken, validationParameters, configuration);
			if (securityToken == null)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10505: Signature validation failed. The user defined 'Delegate' specified on TokenValidationParameters returned null when validating token: '{0}'.", jsonWebToken)));
			}
			if (!(securityToken is JsonWebToken result))
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10506: Signature validation failed. The user defined 'Delegate' specified on TokenValidationParameters did not return a '{0}', but returned a '{1}' when validating token: '{2}'.", LogHelper.MarkAsNonPII(typeof(JsonWebToken)), LogHelper.MarkAsNonPII(securityToken.GetType()), jsonWebToken)));
			}
			return result;
		}
		if (validationParameters.SignatureValidator != null)
		{
			SecurityToken securityToken2 = validationParameters.SignatureValidator(jsonWebToken.EncodedToken, validationParameters);
			if (securityToken2 == null)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10505: Signature validation failed. The user defined 'Delegate' specified on TokenValidationParameters returned null when validating token: '{0}'.", jsonWebToken)));
			}
			if (!(securityToken2 is JsonWebToken result2))
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10506: Signature validation failed. The user defined 'Delegate' specified on TokenValidationParameters did not return a '{0}', but returned a '{1}' when validating token: '{2}'.", LogHelper.MarkAsNonPII(typeof(JsonWebToken)), LogHelper.MarkAsNonPII(securityToken2.GetType()), jsonWebToken)));
			}
			return result2;
		}
		throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10505: Signature validation failed. The user defined 'Delegate' specified on TokenValidationParameters returned null when validating token: '{0}'.", jsonWebToken)));
	}

	private static JsonWebToken ValidateSignatureAndIssuerSecurityKey(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
	{
		JsonWebToken jsonWebToken2 = ValidateSignature(jsonWebToken, validationParameters, configuration);
		Validators.ValidateIssuerSecurityKey(jsonWebToken2.SigningKey, jsonWebToken, validationParameters, configuration);
		return jsonWebToken2;
	}

	private async Task<TokenValidationResult> ValidateTokenPayloadAsync(JsonWebToken jsonWebToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
	{
		DateTime? expires = (jsonWebToken.HasPayloadClaim("exp") ? new DateTime?(jsonWebToken.ValidTo) : ((DateTime?)null));
		Validators.ValidateLifetime(jsonWebToken.HasPayloadClaim("nbf") ? new DateTime?(jsonWebToken.ValidFrom) : ((DateTime?)null), expires, jsonWebToken, validationParameters);
		Validators.ValidateAudience(jsonWebToken.Audiences, jsonWebToken, validationParameters);
		string issuer = await Validators.ValidateIssuerAsync(jsonWebToken.Issuer, jsonWebToken, validationParameters, configuration).ConfigureAwait(continueOnCapturedContext: false);
		Validators.ValidateTokenReplay(expires, jsonWebToken.EncodedToken, validationParameters);
		if (validationParameters.ValidateActor && !string.IsNullOrWhiteSpace(jsonWebToken.Actor))
		{
			TokenValidationResult tokenValidationResult = await ValidateTokenAsync(jsonWebToken.Actor, validationParameters.ActorValidationParameters ?? validationParameters).ConfigureAwait(continueOnCapturedContext: false);
			if (!tokenValidationResult.IsValid)
			{
				return tokenValidationResult;
			}
		}
		string tokenType = Validators.ValidateTokenType(jsonWebToken.Typ, jsonWebToken, validationParameters);
		return new TokenValidationResult(jsonWebToken, this, validationParameters.Clone(), issuer)
		{
			IsValid = true,
			TokenType = tokenType
		};
	}

	private static JsonWebToken ValidateSignature(JsonWebToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
	{
		bool flag = false;
		IEnumerable<SecurityKey> enumerable = null;
		if (!jwtToken.IsSigned)
		{
			if (validationParameters.RequireSignedTokens)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10504: Unable to validate signature, token does not have a signature: '{0}'.", jwtToken)));
			}
			return jwtToken;
		}
		if (validationParameters.IssuerSigningKeyResolverUsingConfiguration != null)
		{
			enumerable = validationParameters.IssuerSigningKeyResolverUsingConfiguration(jwtToken.EncodedToken, jwtToken, jwtToken.Kid, validationParameters, configuration);
		}
		else if (validationParameters.IssuerSigningKeyResolver != null)
		{
			enumerable = validationParameters.IssuerSigningKeyResolver(jwtToken.EncodedToken, jwtToken, jwtToken.Kid, validationParameters);
		}
		else
		{
			SecurityKey securityKey = JwtTokenUtilities.ResolveTokenSigningKey(jwtToken.Kid, jwtToken.X5t, validationParameters, configuration);
			if (securityKey != null)
			{
				flag = true;
				enumerable = new List<SecurityKey> { securityKey };
			}
		}
		if (validationParameters.TryAllIssuerSigningKeys && enumerable.IsNullOrEmpty())
		{
			enumerable = TokenUtilities.GetAllSigningKeys(validationParameters, configuration);
		}
		StringBuilder stringBuilder = new StringBuilder();
		StringBuilder stringBuilder2 = new StringBuilder();
		bool flag2 = !string.IsNullOrEmpty(jwtToken.Kid);
		if (enumerable != null)
		{
			foreach (SecurityKey item in enumerable)
			{
				try
				{
					if (ValidateSignature(jwtToken, item, validationParameters))
					{
						LogHelper.LogInformation("IDX10242: Security token: '{0}' has a valid signature.", jwtToken);
						jwtToken.SigningKey = item;
						return jwtToken;
					}
				}
				catch (Exception ex)
				{
					stringBuilder.AppendLine(ex.ToString());
				}
				if (item != null)
				{
					stringBuilder2.Append(item.ToString()).Append(" , KeyId: ").AppendLine(item.KeyId);
					if (flag2 && !flag && item.KeyId != null)
					{
						flag = jwtToken.Kid.Equals(item.KeyId, (item is X509SecurityKey) ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal);
					}
				}
			}
		}
		IEnumerable<SecurityKey> allSigningKeys = TokenUtilities.GetAllSigningKeys(validationParameters);
		IEnumerable<SecurityKey> allSigningKeys2 = TokenUtilities.GetAllSigningKeys(configuration);
		int num = allSigningKeys.Count();
		int num2 = allSigningKeys2.Count();
		if (flag2)
		{
			if (flag)
			{
				string arg = (allSigningKeys.Any((SecurityKey x) => x.KeyId.Equals(jwtToken.Kid)) ? "TokenValidationParameters" : "Configuration");
				throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10511: Signature validation failed. Keys tried: '{0}'. \nNumber of keys in TokenValidationParameters: '{1}'. \nNumber of keys in Configuration: '{2}'. \nMatched key was in '{3}'. \nkid: '{4}'. \nExceptions caught:\n '{5}'.\ntoken: '{6}'. See https://aka.ms/IDX10511 for details.", stringBuilder2, LogHelper.MarkAsNonPII(num), LogHelper.MarkAsNonPII(num2), LogHelper.MarkAsNonPII(arg), LogHelper.MarkAsNonPII(jwtToken.Kid), stringBuilder, jwtToken)));
			}
			Claim value;
			DateTime? expires = (jwtToken.TryGetClaim("exp", out value) ? new DateTime?(jwtToken.ValidTo) : ((DateTime?)null));
			DateTime? notBefore = (jwtToken.TryGetClaim("nbf", out value) ? new DateTime?(jwtToken.ValidFrom) : ((DateTime?)null));
			if (!validationParameters.ValidateSignatureLast)
			{
				InternalValidators.ValidateAfterSignatureFailed(jwtToken, notBefore, expires, jwtToken.Audiences, validationParameters, configuration);
			}
		}
		if (stringBuilder2.Length > 0)
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenSignatureKeyNotFoundException(LogHelper.FormatInvariant("IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '{0}'. Number of keys in TokenValidationParameters: '{1}'. \nNumber of keys in Configuration: '{2}'. \nExceptions caught:\n '{3}'.\ntoken: '{4}'. See https://aka.ms/IDX10503 for details.", stringBuilder2, LogHelper.MarkAsNonPII(num), LogHelper.MarkAsNonPII(num2), stringBuilder, jwtToken)));
		}
		throw LogHelper.LogExceptionMessage(new SecurityTokenSignatureKeyNotFoundException("IDX10500: Signature validation failed. No security keys were provided to validate the signature."));
	}

	internal static bool ValidateSignature(byte[] encodedBytes, byte[] signature, SecurityKey key, string algorithm, SecurityToken securityToken, TokenValidationParameters validationParameters)
	{
		CryptoProviderFactory cryptoProviderFactory = validationParameters.CryptoProviderFactory ?? key.CryptoProviderFactory;
		if (!cryptoProviderFactory.IsSupportedAlgorithm(algorithm, key))
		{
			LogHelper.LogInformation("IDX14000: Signature validation of this JWT is not supported for: Algorithm: '{0}', SecurityKey: '{1}'.", LogHelper.MarkAsNonPII(algorithm), key);
			return false;
		}
		Validators.ValidateAlgorithm(algorithm, key, securityToken, validationParameters);
		SignatureProvider signatureProvider = cryptoProviderFactory.CreateForVerifying(key, algorithm);
		if (signatureProvider == null)
		{
			throw LogHelper.LogExceptionMessage(new InvalidOperationException(LogHelper.FormatInvariant("IDX10636: CryptoProviderFactory.CreateForVerifying returned null for key: '{0}', signatureAlgorithm: '{1}'.", (key == null) ? "Null" : key.ToString(), LogHelper.MarkAsNonPII(algorithm))));
		}
		try
		{
			return signatureProvider.Verify(encodedBytes, signature);
		}
		finally
		{
			cryptoProviderFactory.ReleaseSignatureProvider(signatureProvider);
		}
	}

	internal static bool IsSignatureValid(byte[] signatureBytes, int signatureBytesLength, SignatureProvider signatureProvider, byte[] dataToVerify, int dataToVerifyLength)
	{
		if (signatureProvider is SymmetricSignatureProvider)
		{
			return signatureProvider.Verify(dataToVerify, 0, dataToVerifyLength, signatureBytes, 0, signatureBytesLength);
		}
		if (signatureBytes.Length == signatureBytesLength)
		{
			return signatureProvider.Verify(dataToVerify, 0, dataToVerifyLength, signatureBytes, 0, signatureBytesLength);
		}
		byte[] array = new byte[signatureBytesLength];
		Array.Copy(signatureBytes, 0, array, 0, signatureBytesLength);
		return signatureProvider.Verify(dataToVerify, 0, dataToVerifyLength, array, 0, signatureBytesLength);
	}

	internal static bool ValidateSignature(byte[] bytes, int len, string stringWithSignature, int signatureStartIndex, SignatureProvider signatureProvider)
	{
		return Base64UrlEncoding.Decode(stringWithSignature, signatureStartIndex + 1, stringWithSignature.Length - signatureStartIndex - 1, signatureProvider, bytes, len, IsSignatureValid);
	}

	internal static bool ValidateSignature(JsonWebToken jsonWebToken, SecurityKey key, TokenValidationParameters validationParameters)
	{
		CryptoProviderFactory cryptoProviderFactory = validationParameters.CryptoProviderFactory ?? key.CryptoProviderFactory;
		if (!cryptoProviderFactory.IsSupportedAlgorithm(jsonWebToken.Alg, key))
		{
			LogHelper.LogInformation("IDX14000: Signature validation of this JWT is not supported for: Algorithm: '{0}', SecurityKey: '{1}'.", LogHelper.MarkAsNonPII(jsonWebToken.Alg), key);
			return false;
		}
		Validators.ValidateAlgorithm(jsonWebToken.Alg, key, jsonWebToken, validationParameters);
		SignatureProvider signatureProvider = cryptoProviderFactory.CreateForVerifying(key, jsonWebToken.Alg);
		try
		{
			if (signatureProvider == null)
			{
				throw LogHelper.LogExceptionMessage(new InvalidOperationException(LogHelper.FormatInvariant("IDX10636: CryptoProviderFactory.CreateForVerifying returned null for key: '{0}', signatureAlgorithm: '{1}'.", (key == null) ? "Null" : key.ToString(), LogHelper.MarkAsNonPII(jsonWebToken.Alg))));
			}
			return EncodingUtils.PerformEncodingDependentOperation(jsonWebToken.EncodedToken, 0, jsonWebToken.Dot2, Encoding.UTF8, jsonWebToken.EncodedToken, jsonWebToken.Dot2, signatureProvider, ValidateSignature);
		}
		finally
		{
			cryptoProviderFactory.ReleaseSignatureProvider(signatureProvider);
		}
	}
}
