using System;
using System.ComponentModel;
using System.Security.Cryptography.X509Certificates;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;

namespace Microsoft.Identity.Client;

[Obsolete("Use ConfidentialClientApplicationBuilder.WithCertificate instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
[EditorBrowsable(EditorBrowsableState.Never)]
public sealed class ClientAssertionCertificate
{
	[EditorBrowsable(EditorBrowsableState.Never)]
	public static int MinKeySizeInBits => 2048;

	[EditorBrowsable(EditorBrowsableState.Never)]
	public X509Certificate2 Certificate
	{
		get
		{
			throw MigrationHelper.CreateMsalNet3BreakingChangesException();
		}
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	internal string Thumbprint
	{
		get
		{
			throw MigrationHelper.CreateMsalNet3BreakingChangesException();
		}
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	public ClientAssertionCertificate(X509Certificate2 certificate)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	internal byte[] Sign(ICryptographyManager cryptographyManager, string message)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}
}
