namespace Microsoft.Identity.Client.Cache;

internal static class StorageJsonValues
{
	public const string AuthorityTypeAdfs = "ADFS";

	public const string AuthorityTypeMsa = "MSA";

	public const string AuthorityTypeMsSts = "MSSTS";

	public const string AuthorityTypeOther = "Other";

	public const string TokenTypeBearer = "Bearer";

	public const string CredentialTypeRefreshToken = "RefreshToken";

	public const string CredentialTypeAccessToken = "AccessToken";

	public const string CredentialTypeAccessTokenWithAuthScheme = "AccessToken_With_AuthScheme";

	public const string CredentialTypeIdToken = "IdToken";

	public const string AccountRootKey = "Account";

	public const string CredentialTypeOther = "Other";

	public const string AppMetadata = "AppMetadata";
}
