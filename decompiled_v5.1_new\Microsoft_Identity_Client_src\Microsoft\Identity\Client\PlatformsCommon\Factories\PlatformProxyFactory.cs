using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal.Logger;
using Microsoft.Identity.Client.Platforms.netcore;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;

namespace Microsoft.Identity.Client.PlatformsCommon.Factories;

internal static class PlatformProxyFactory
{
	public static IPlatformProxy CreatePlatformProxy(ILoggerAdapter logger)
	{
		return new NetCorePlatformProxy(logger ?? LoggerHelper.NullLogger);
	}
}
