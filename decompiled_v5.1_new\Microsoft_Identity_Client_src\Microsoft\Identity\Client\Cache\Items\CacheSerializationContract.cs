using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;

namespace Microsoft.Identity.Client.Cache.Items;

internal class CacheSerializationContract
{
	private static readonly JsonSerializerOptions NeverIgnoreJsonOptions = new JsonSerializerOptions
	{
		DefaultIgnoreCondition = JsonIgnoreCondition.Never
	};

	private static readonly IEnumerable<string> s_knownPropertyNames = new string[5] { "AccessToken", "RefreshToken", "IdToken", "Account", "AppMetadata" };

	public Dictionary<string, MsalAccessTokenCacheItem> AccessTokens { get; set; } = new Dictionary<string, MsalAccessTokenCacheItem>();

	public Dictionary<string, MsalRefreshTokenCacheItem> RefreshTokens { get; set; } = new Dictionary<string, MsalRefreshTokenCacheItem>();

	public Dictionary<string, MsalIdTokenCacheItem> IdTokens { get; set; } = new Dictionary<string, MsalIdTokenCacheItem>();

	public Dictionary<string, MsalAccountCacheItem> Accounts { get; set; } = new Dictionary<string, MsalAccountCacheItem>();

	public Dictionary<string, MsalAppMetadataCacheItem> AppMetadata { get; set; } = new Dictionary<string, MsalAppMetadataCacheItem>();

	public IDictionary<string, JsonNode> UnknownNodes { get; }

	public CacheSerializationContract(IDictionary<string, JsonNode> unknownNodes)
	{
		UnknownNodes = unknownNodes ?? new Dictionary<string, JsonNode>();
	}

	internal static CacheSerializationContract FromJsonString(string json)
	{
		JsonDocumentOptions documentOptions = new JsonDocumentOptions
		{
			AllowTrailingCommas = true
		};
		JsonObject jsonObject = JsonNode.Parse(json, null, documentOptions).AsObject();
		CacheSerializationContract cacheSerializationContract = new CacheSerializationContract(ExtractUnknownNodes(jsonObject));
		if (jsonObject.ContainsKey("AccessToken"))
		{
			foreach (JsonObject item in GetElement(jsonObject, "AccessToken"))
			{
				if (item != null)
				{
					MsalAccessTokenCacheItem msalAccessTokenCacheItem = MsalAccessTokenCacheItem.FromJObject(item);
					cacheSerializationContract.AccessTokens[msalAccessTokenCacheItem.CacheKey] = msalAccessTokenCacheItem;
				}
			}
		}
		if (jsonObject.ContainsKey("RefreshToken"))
		{
			foreach (JsonObject item2 in GetElement(jsonObject, "RefreshToken"))
			{
				if (item2 != null)
				{
					MsalRefreshTokenCacheItem msalRefreshTokenCacheItem = MsalRefreshTokenCacheItem.FromJObject(item2);
					cacheSerializationContract.RefreshTokens[msalRefreshTokenCacheItem.CacheKey] = msalRefreshTokenCacheItem;
				}
			}
		}
		if (jsonObject.ContainsKey("IdToken"))
		{
			foreach (JsonObject item3 in GetElement(jsonObject, "IdToken"))
			{
				if (item3 != null)
				{
					MsalIdTokenCacheItem msalIdTokenCacheItem = MsalIdTokenCacheItem.FromJObject(item3);
					cacheSerializationContract.IdTokens[msalIdTokenCacheItem.CacheKey] = msalIdTokenCacheItem;
				}
			}
		}
		if (jsonObject.ContainsKey("Account"))
		{
			foreach (JsonObject item4 in GetElement(jsonObject, "Account"))
			{
				if (item4 != null)
				{
					MsalAccountCacheItem msalAccountCacheItem = MsalAccountCacheItem.FromJObject(item4);
					cacheSerializationContract.Accounts[msalAccountCacheItem.CacheKey] = msalAccountCacheItem;
				}
			}
		}
		if (jsonObject.ContainsKey("AppMetadata"))
		{
			foreach (JsonObject item5 in GetElement(jsonObject, "AppMetadata"))
			{
				if (item5 != null)
				{
					MsalAppMetadataCacheItem msalAppMetadataCacheItem = MsalAppMetadataCacheItem.FromJObject(item5);
					cacheSerializationContract.AppMetadata[msalAppMetadataCacheItem.CacheKey] = msalAppMetadataCacheItem;
				}
			}
		}
		return cacheSerializationContract;
		static IEnumerable<JsonObject> GetElement(JsonObject root, string key)
		{
			foreach (KeyValuePair<string, JsonNode> item6 in root[key].AsObject())
			{
				yield return item6.Value as JsonObject;
			}
		}
	}

	private static IDictionary<string, JsonNode> ExtractUnknownNodes(JsonObject root)
	{
		return root.Where<KeyValuePair<string, JsonNode>>((KeyValuePair<string, JsonNode> kvp) => !s_knownPropertyNames.Any((string p) => string.Equals(kvp.Key, p, StringComparison.OrdinalIgnoreCase))).ToDictionary((KeyValuePair<string, JsonNode> kvp) => kvp.Key, (KeyValuePair<string, JsonNode> kvp) => kvp.Value);
	}

	internal string ToJsonString()
	{
		JsonObject jsonObject = new JsonObject();
		JsonObject jsonObject2 = new JsonObject();
		foreach (KeyValuePair<string, MsalAccessTokenCacheItem> accessToken in AccessTokens)
		{
			jsonObject2[accessToken.Key] = accessToken.Value.ToJObject();
		}
		jsonObject["AccessToken"] = jsonObject2;
		JsonObject jsonObject3 = new JsonObject();
		foreach (KeyValuePair<string, MsalRefreshTokenCacheItem> refreshToken in RefreshTokens)
		{
			jsonObject3[refreshToken.Key] = refreshToken.Value.ToJObject();
		}
		jsonObject["RefreshToken"] = jsonObject3;
		JsonObject jsonObject4 = new JsonObject();
		foreach (KeyValuePair<string, MsalIdTokenCacheItem> idToken in IdTokens)
		{
			jsonObject4[idToken.Key] = idToken.Value.ToJObject();
		}
		jsonObject["IdToken"] = jsonObject4;
		JsonObject jsonObject5 = new JsonObject();
		foreach (KeyValuePair<string, MsalAccountCacheItem> account in Accounts)
		{
			jsonObject5[account.Key] = account.Value.ToJObject();
		}
		jsonObject["Account"] = jsonObject5;
		JsonObject jsonObject6 = new JsonObject();
		foreach (KeyValuePair<string, MsalAppMetadataCacheItem> appMetadatum in AppMetadata)
		{
			jsonObject6[appMetadatum.Key] = appMetadatum.Value.ToJObject();
		}
		jsonObject["AppMetadata"] = jsonObject6;
		foreach (KeyValuePair<string, JsonNode> unknownNode in UnknownNodes)
		{
			jsonObject[unknownNode.Key] = ((unknownNode.Value != null) ? JsonNode.Parse(unknownNode.Value.ToJsonString()) : null);
		}
		return jsonObject.ToJsonString(NeverIgnoreJsonOptions);
	}
}
