using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.OAuth2;

namespace Microsoft.Identity.Client.Internal.Requests.Silent;

internal class SilentRequest : RequestBase
{
	private readonly AcquireTokenSilentParameters _silentParameters;

	private readonly ISilentAuthRequestStrategy _clientStrategy;

	private readonly Lazy<ISilentAuthRequestStrategy> _brokerStrategyLazy;

	private readonly ILoggerAdapter _logger;

	public SilentRequest(IServiceBundle serviceBundle, AuthenticationRequestParameters authenticationRequestParameters, AcquireTokenSilentParameters silentParameters, ISilentAuthRequestStrategy clientStrategyOverride = null, ISilentAuthRequestStrategy brokerStrategyOverride = null)
		: base(serviceBundle, authenticationRequestParameters, silentParameters)
	{
		SilentRequest request = this;
		_silentParameters = silentParameters;
		_brokerStrategyLazy = new Lazy<ISilentAuthRequestStrategy>(() => brokerStrategyOverride ?? new BrokerSilentStrategy(request, serviceBundle, authenticationRequestParameters, silentParameters, serviceBundle.PlatformProxy.CreateBroker(serviceBundle.Config, null)));
		_clientStrategy = clientStrategyOverride ?? new CacheSilentStrategy(this, serviceBundle, authenticationRequestParameters, silentParameters);
		_logger = authenticationRequestParameters.RequestContext.Logger;
	}

	protected override async Task<AuthenticationResult> ExecuteAsync(CancellationToken cancellationToken)
	{
		await UpdateRequestWithAccountAsync().ConfigureAwait(continueOnCapturedContext: false);
		bool isBrokerConfigured = base.AuthenticationRequestParameters.AppConfig.IsBrokerEnabled && base.ServiceBundle.PlatformProxy.CanBrokerSupportSilentAuth();
		try
		{
			if (base.AuthenticationRequestParameters.Account == null)
			{
				_logger.Verbose(() => "No account passed to AcquireTokenSilent. ");
				throw new MsalUiRequiredException("user_null", "No account or login hint was passed to the AcquireTokenSilent call. ", null, UiRequiredExceptionClassification.AcquireTokenSilentFailed);
			}
			if (isBrokerConfigured)
			{
				_logger.Info("Broker is configured and enabled, attempting to use broker instead.");
				AuthenticationResult authenticationResult = await _brokerStrategyLazy.Value.ExecuteAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
				if (authenticationResult != null)
				{
					_logger.Verbose(() => "Broker responded to silent request.");
					return authenticationResult;
				}
			}
			_logger.Verbose(() => "Attempting to acquire token using local cache.");
			return await _clientStrategy.ExecuteAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		}
		catch (MsalException ex)
		{
			_logger.Verbose(() => (!isBrokerConfigured) ? "Token cache could not satisfy silent request." : "Broker could not satisfy silent request.");
			throw ex;
		}
	}

	internal new Task<AuthenticationResult> CacheTokenResponseAndCreateAuthenticationResultAsync(MsalTokenResponse response)
	{
		return base.CacheTokenResponseAndCreateAuthenticationResultAsync(response);
	}

	internal Task<AuthenticationResult> ExecuteTestAsync(CancellationToken cancellationToken)
	{
		return ExecuteAsync(cancellationToken);
	}

	private async Task UpdateRequestWithAccountAsync()
	{
		IAccount account = await GetAccountFromParamsOrLoginHintAsync(_silentParameters.Account, _silentParameters.LoginHint).ConfigureAwait(continueOnCapturedContext: false);
		base.AuthenticationRequestParameters.Account = account;
		Authority initialAuthority = await Authority.CreateAuthorityForRequestAsync(base.AuthenticationRequestParameters.RequestContext, base.AuthenticationRequestParameters.AuthorityOverride, account).ConfigureAwait(continueOnCapturedContext: false);
		base.AuthenticationRequestParameters.AuthorityManager = new AuthorityManager(base.AuthenticationRequestParameters.RequestContext, initialAuthority);
	}

	private async Task<IAccount> GetSingleAccountForLoginHintAsync(string loginHint)
	{
		if (!string.IsNullOrEmpty(loginHint))
		{
			List<IAccount> list = (await base.CacheManager.GetAccountsAsync().ConfigureAwait(continueOnCapturedContext: false)).Where((IAccount a) => !string.IsNullOrWhiteSpace(a.Username) && a.Username.Equals(loginHint, StringComparison.OrdinalIgnoreCase)).ToList();
			if (((IReadOnlyCollection<IAccount>)list).Count == 0)
			{
				throw new MsalUiRequiredException("no_account_for_login_hint", "You are trying to acquire a token silently using a login hint. No account was found in the token cache having this login hint. ", null, UiRequiredExceptionClassification.AcquireTokenSilentFailed);
			}
			if (((IReadOnlyCollection<IAccount>)list).Count > 1)
			{
				throw new MsalUiRequiredException("multiple_accounts_for_login_hint", "You are trying to acquire a token silently using a login hint. Multiple accounts were found in the token cache having this login hint. Please choose an account manually an pass it in to AcquireTokenSilently. ", null, UiRequiredExceptionClassification.AcquireTokenSilentFailed);
			}
			return ((IReadOnlyList<IAccount>)list)[0];
		}
		return null;
	}

	private async Task<IAccount> GetAccountFromParamsOrLoginHintAsync(IAccount account, string loginHint)
	{
		if (account != null)
		{
			return account;
		}
		return await GetSingleAccountForLoginHintAsync(loginHint).ConfigureAwait(continueOnCapturedContext: false);
	}

	protected override void ValidateAccountIdentifiers(ClientInfo fromServer)
	{
		if (fromServer == null || base.AuthenticationRequestParameters?.Account?.HomeAccountId == null || PublicClientApplication.IsOperatingSystemAccount(base.AuthenticationRequestParameters?.Account) || (base.AuthenticationRequestParameters.AuthorityInfo.AuthorityType == AuthorityType.B2C && fromServer.UniqueTenantIdentifier.Equals(base.AuthenticationRequestParameters.Account.HomeAccountId.TenantId, StringComparison.OrdinalIgnoreCase)) || (fromServer.UniqueObjectIdentifier.Equals(base.AuthenticationRequestParameters.Account.HomeAccountId.ObjectId, StringComparison.OrdinalIgnoreCase) && fromServer.UniqueTenantIdentifier.Equals(base.AuthenticationRequestParameters.Account.HomeAccountId.TenantId, StringComparison.OrdinalIgnoreCase)))
		{
			return;
		}
		base.AuthenticationRequestParameters.RequestContext.Logger.Error("Returned user identifiers do not match the sent user identifier");
		base.AuthenticationRequestParameters.RequestContext.Logger.ErrorPii($"User identifier returned by AAD (uid:{fromServer.UniqueObjectIdentifier} utid:{fromServer.UniqueTenantIdentifier}) does not match the user identifier sent. (uid:{base.AuthenticationRequestParameters.Account.HomeAccountId.ObjectId} utid:{base.AuthenticationRequestParameters.Account.HomeAccountId.TenantId})", string.Empty);
		throw new MsalClientException("user_mismatch", "Returned user identifier does not match the sent user identifier when saving the token to the cache. ");
	}
}
