# Bibi项目V5.1文件导航索引

## 📁 核心目录结构
```
D:\Dev\bibi\decompiled_v5.1_new\
├── 源码项目目录/ (35个*_src目录)
├── 工具脚本/ (3个.ps1文件)
├── 技术文档/ (3个.md文件)
└── 配置文件/ (2个配置文件)
```

## 🔧 工具脚本
- `batch_decompile_microsoft_fixed.ps1` - Microsoft组件批量反编译
- `finish_remaining_dlls.ps1` - 剩余DLL批量处理
- `update_dependencies_fixed.ps1` - 依赖引用更新

## 📚 技术文档
- `批量反编译最终报告.md` - 完整的反编译执行报告
- `全面反编译分析报告_V2.md` - 系统架构和技术分析
- `API使用文档.md` - API接口使用指南
- `编译验证报告.md` - 编译问题分析和解决方案

## ⚙️ 配置文件
- `dependency_mapping.json` - DLL到NuGet包的映射关系
- `global.json` - .NET SDK版本配置

## 🗂️ 源码项目 (35个)
### 核心组件
- `API_src/` - 主程序Web API
- `BiliveDanmakuAgent_src/` - 弹幕监控引擎
- `Jint_src/` - JavaScript执行引擎

### Microsoft框架
- `Microsoft_Identity_Client_src/` - 身份认证客户端
- `Microsoft_Data_SqlClient_src/` - SQL Server连接
- `Microsoft_IdentityModel_*_src/` - 身份模型组件

### Azure组件
- `Azure_Core_src/` - Azure SDK核心
- `Azure_Identity_src/` - Azure身份认证

### 第三方库
- `Newtonsoft_Json_src/` - JSON序列化
- `NPOI_*_src/` - Office文档处理
- `SixLabors_*_src/` - 图像处理
- `BouncyCastle_Cryptography_src/` - 加密库

## 🚀 快速启动
1. **查看项目概述**: 阅读`批量反编译最终报告.md`
2. **了解技术架构**: 查看`全面反编译分析报告_V2.md`
3. **API开发**: 参考`API使用文档.md`
4. **编译问题**: 查看`编译验证报告.md`

## 📋 维护记录
- 最后更新: 2025年7月31日
- 清理内容: 删除5个重复脚本和文档
- 项目状态: 35个源码项目，技术文档完整