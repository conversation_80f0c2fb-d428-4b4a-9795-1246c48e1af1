using System;
using System.Security.Claims;
using Microsoft.Identity.Client.Cache.Items;

namespace Microsoft.Identity.Client;

public class TenantProfile
{
	private readonly MsalIdTokenCacheItem _msalIdTokenCacheItem;

	public string Oid => _msalIdTokenCacheItem?.IdToken.ObjectId;

	public string TenantId => _msalIdTokenCacheItem?.IdToken.TenantId;

	public ClaimsPrincipal ClaimsPrincipal => _msalIdTokenCacheItem?.IdToken.ClaimsPrincipal;

	public bool IsHomeTenant => string.Equals(AccountId.ParseFromString(_msalIdTokenCacheItem?.HomeAccountId).TenantId, _msalIdTokenCacheItem?.IdToken.TenantId, StringComparison.OrdinalIgnoreCase);

	internal TenantProfile(MsalIdTokenCacheItem msalIdTokenCacheItem)
	{
		_msalIdTokenCacheItem = msalIdTokenCacheItem;
	}
}
