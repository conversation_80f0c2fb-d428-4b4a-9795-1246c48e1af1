using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;

namespace SixLabors.ImageSharp.Drawing;

public static class PathExtensions
{
	public static IPathCollection Rotate(this IPathCollection path, float radians)
	{
		return path.Transform(Matrix3x2Extensions.CreateRotation(radians, RectangleF.Center(path.Bounds)));
	}

	public static IPathCollection RotateDegree(this IPathCollection shape, float degree)
	{
		return shape.Rotate(GeometryUtilities.DegreeToRadian(degree));
	}

	public static IPathCollection Translate(this IPathCollection path, PointF position)
	{
		return path.Transform(Matrix3x2.CreateTranslation(position));
	}

	public static IPathCollection Translate(this IPathCollection path, float x, float y)
	{
		return path.Translate(new PointF(x, y));
	}

	public static IPathCollection Scale(this IPathCollection path, float scaleX, float scaleY)
	{
		return path.Transform(Matrix3x2.CreateScale(scaleX, scaleY, RectangleF.Center(path.Bounds)));
	}

	public static IPathCollection Scale(this IPathCollection path, float scale)
	{
		return path.Transform(Matrix3x2.CreateScale(scale, RectangleF.Center(path.Bounds)));
	}

	public static IPath Rotate(this IPath path, float radians)
	{
		return path.Transform(Matrix3x2.CreateRotation(radians, RectangleF.Center(path.Bounds)));
	}

	public static IPath RotateDegree(this IPath shape, float degree)
	{
		return shape.Rotate(GeometryUtilities.DegreeToRadian(degree));
	}

	public static IPath Translate(this IPath path, PointF position)
	{
		return path.Transform(Matrix3x2.CreateTranslation(position));
	}

	public static IPath Translate(this IPath path, float x, float y)
	{
		return path.Translate(new Vector2(x, y));
	}

	public static IPath Scale(this IPath path, float scaleX, float scaleY)
	{
		return path.Transform(Matrix3x2.CreateScale(scaleX, scaleY, RectangleF.Center(path.Bounds)));
	}

	public static IPath Scale(this IPath path, float scale)
	{
		return path.Transform(Matrix3x2.CreateScale(scale, RectangleF.Center(path.Bounds)));
	}

	public static float ComputeLength(this IPath path)
	{
		float num = 0f;
		foreach (ISimplePath item in path.Flatten())
		{
			ReadOnlySpan<PointF> span = item.Points.Span;
			if (span.Length >= 2)
			{
				for (int i = 1; i < span.Length; i++)
				{
					num += Vector2.Distance(span[i - 1], span[i]);
				}
				if (item.IsClosed)
				{
					num += Vector2.Distance(span[0], span[span.Length - 1]);
				}
			}
		}
		return num;
	}

	internal static IPath Reverse(this IPath path)
	{
		IEnumerable<LinearLineSegment> segments = from p in path.Flatten()
			select new LinearLineSegment(p.Points.ToArray().Reverse().ToArray());
		bool flag = false;
		if (path is ISimplePath simplePath)
		{
			flag = simplePath.IsClosed;
		}
		if (!flag)
		{
			return new Path(segments);
		}
		return new Polygon(segments);
	}
}
