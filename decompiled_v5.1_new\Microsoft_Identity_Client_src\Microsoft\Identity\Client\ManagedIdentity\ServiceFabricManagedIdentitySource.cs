using System;
using System.Globalization;
using System.Net.Http;
using Microsoft.Identity.Client.AppConfig;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.ManagedIdentity;

internal class ServiceFabricManagedIdentitySource : AbstractManagedIdentity
{
	private const string ServiceFabricMsiApiVersion = "2019-07-01-preview";

	private readonly Uri _endpoint;

	private readonly string _identityHeaderValue;

	public static AbstractManagedIdentity Create(RequestContext requestContext)
	{
		string identityEndpoint = EnvironmentVariables.IdentityEndpoint;
		requestContext.Logger.Info(() => "[Managed Identity] Service fabric managed identity is available.");
		if (!Uri.TryCreate(identityEndpoint, UriKind.Absolute, out Uri result))
		{
			string errorMessage = string.Format(CultureInfo.InvariantCulture, "[Managed Identity] The environment variable {0} contains an invalid Uri {1} in {2} managed identity source.", "IDENTITY_ENDPOINT", identityEndpoint, "Service Fabric");
			throw MsalServiceExceptionFactory.CreateManagedIdentityException("invalid_managed_identity_endpoint", errorMessage, null, ManagedIdentitySource.ServiceFabric, null);
		}
		requestContext.Logger.Verbose(() => "[Managed Identity] Creating Service Fabric managed identity. Endpoint URI: " + identityEndpoint);
		return new ServiceFabricManagedIdentitySource(requestContext, result, EnvironmentVariables.IdentityHeader);
	}

	private ServiceFabricManagedIdentitySource(RequestContext requestContext, Uri endpoint, string identityHeaderValue)
		: base(requestContext, ManagedIdentitySource.ServiceFabric)
	{
		_endpoint = endpoint;
		_identityHeaderValue = identityHeaderValue;
		if (requestContext.ServiceBundle.Config.ManagedIdentityId.IsUserAssigned)
		{
			requestContext.Logger.Warning("[Managed Identity] Service Fabric user assigned managed identity ClientId or ResourceId is not configurable at runtime.");
		}
	}

	protected override ManagedIdentityRequest CreateRequest(string resource)
	{
		ManagedIdentityRequest managedIdentityRequest = new ManagedIdentityRequest(HttpMethod.Get, _endpoint);
		managedIdentityRequest.Headers["secret"] = _identityHeaderValue;
		managedIdentityRequest.QueryParameters["api-version"] = "2019-07-01-preview";
		managedIdentityRequest.QueryParameters["resource"] = resource;
		switch (_requestContext.ServiceBundle.Config.ManagedIdentityId.IdType)
		{
		case ManagedIdentityIdType.ClientId:
			_requestContext.Logger.Info("[Managed Identity] Adding user assigned client id to the request.");
			managedIdentityRequest.QueryParameters["client_id"] = _requestContext.ServiceBundle.Config.ManagedIdentityId.UserAssignedId;
			break;
		case ManagedIdentityIdType.ResourceId:
			_requestContext.Logger.Info("[Managed Identity] Adding user assigned resource id to the request.");
			managedIdentityRequest.QueryParameters["mi_res_id"] = _requestContext.ServiceBundle.Config.ManagedIdentityId.UserAssignedId;
			break;
		case ManagedIdentityIdType.ObjectId:
			_requestContext.Logger.Info("[Managed Identity] Adding user assigned object id to the request.");
			managedIdentityRequest.QueryParameters["object_id"] = _requestContext.ServiceBundle.Config.ManagedIdentityId.UserAssignedId;
			break;
		}
		return managedIdentityRequest;
	}
}
