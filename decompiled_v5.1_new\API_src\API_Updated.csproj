<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <AssemblyName>API</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <LangVersion>12.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="6.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Quartz" Version="3.6.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.0" />
    <PackageReference Include="NPOI" Version="2.6.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="2.1.0" />
    <PackageReference Include="SixLabors.Fonts" Version="1.0.0" />
    <PackageReference Include="SixLabors.ImageSharp.Drawing" Version="1.0.0" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\BiliveDanmakuAgent_src\BiliveDanmakuAgent.csproj" />
    <ProjectReference Include="..\Jint_src\Jint.csproj" />
  </ItemGroup>
</Project>