using System.ClientModel.Internal;
using System.Diagnostics.CodeAnalysis;
using System.Runtime.CompilerServices;

namespace System.ClientModel.Primitives;

public static class ModelReaderWriter
{
	public static BinaryData Write<T>(T model, ModelReaderWriterOptions? options = null) where T : IPersistableModel<T>
	{
		if (model == null)
		{
			throw new ArgumentNullException("model");
		}
		if (options == null)
		{
			options = ModelReaderWriterOptions.Json;
		}
		if (IsJsonFormatRequested(model, options) && model is IJsonModel<T> model2)
		{
			using ModelWriter<T> modelWriter = new ModelWriter<T>(model2, options);
			return modelWriter.ToBinaryData();
		}
		return model.Write(options);
	}

	public static BinaryData Write(object model, ModelReaderWriterOptions? options = null)
	{
		if (model == null)
		{
			throw new ArgumentNullException("model");
		}
		if (options == null)
		{
			options = ModelReaderWriterOptions.Json;
		}
		if (!(model is IPersistableModel<object> persistableModel))
		{
			throw new InvalidOperationException(model.GetType().Name + " does not implement IPersistableModel");
		}
		if (IsJsonFormatRequested(persistableModel, options) && model is IJsonModel<object> model2)
		{
			using ModelWriter<object> modelWriter = new ModelWriter<object>(model2, options);
			return modelWriter.ToBinaryData();
		}
		return persistableModel.Write(options);
	}

	public static T? Read<[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.NonPublicConstructors)] T>(BinaryData data, ModelReaderWriterOptions? options = null) where T : IPersistableModel<T>
	{
		if (data == null)
		{
			throw new ArgumentNullException("data");
		}
		if (options == null)
		{
			options = ModelReaderWriterOptions.Json;
		}
		return GetInstance<T>().Create(data, options);
	}

	public static object? Read(BinaryData data, [DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.NonPublicConstructors)] Type returnType, ModelReaderWriterOptions? options = null)
	{
		if (data == null)
		{
			throw new ArgumentNullException("data");
		}
		if ((object)returnType == null)
		{
			throw new ArgumentNullException("returnType");
		}
		if (options == null)
		{
			options = ModelReaderWriterOptions.Json;
		}
		return GetInstance(returnType).Create(data, options);
	}

	private static IPersistableModel<object> GetInstance([DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.NonPublicConstructors)] Type returnType)
	{
		return (GetObjectInstance(returnType) as IPersistableModel<object>) ?? throw new InvalidOperationException(returnType.Name + " does not implement IPersistableModel");
	}

	private static IPersistableModel<T> GetInstance<[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.NonPublicConstructors)] T>() where T : IPersistableModel<T>
	{
		return (GetObjectInstance(typeof(T)) as IPersistableModel<T>) ?? throw new InvalidOperationException(typeof(T).Name + " does not implement IPersistableModel");
	}

	private static object GetObjectInstance([DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.NonPublicConstructors)] Type returnType)
	{
		PersistableModelProxyAttribute persistableModelProxyAttribute = Attribute.GetCustomAttribute(returnType, typeof(PersistableModelProxyAttribute), inherit: false) as PersistableModelProxyAttribute;
		Type type = ((persistableModelProxyAttribute == null) ? returnType : persistableModelProxyAttribute.ProxyType);
		if (returnType.IsAbstract && persistableModelProxyAttribute == null)
		{
			throw new InvalidOperationException($"{returnType.Name} must be decorated with {"PersistableModelProxyAttribute"} to be used with {"ModelReaderWriter"}");
		}
		return Activator.CreateInstance(type, nonPublic: true) ?? throw new InvalidOperationException("Unable to create instance of " + type.Name + ".");
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsJsonFormatRequested<T>(IPersistableModel<T> model, ModelReaderWriterOptions options)
	{
		if (!(options.Format == "J"))
		{
			if (options.Format == "W")
			{
				return model.GetFormatFromOptions(options) == "J";
			}
			return false;
		}
		return true;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsJsonFormatRequested(IPersistableModel<object> model, ModelReaderWriterOptions options)
	{
		return IsJsonFormatRequested<object>(model, options);
	}
}
