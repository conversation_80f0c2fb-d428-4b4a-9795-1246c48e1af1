using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Executors;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.AppConfig;
using Microsoft.Identity.Client.AuthScheme;
using Microsoft.Identity.Client.AuthScheme.PoP;
using Microsoft.Identity.Client.Internal.Broker;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;

namespace Microsoft.Identity.Client;

public sealed class AcquireTokenSilentParameterBuilder : AbstractClientAppBaseAcquireTokenParameterBuilder<AcquireTokenSilentParameterBuilder>
{
	private AcquireTokenSilentParameters Parameters { get; } = new AcquireTokenSilentParameters();

	internal AcquireTokenSilentParameterBuilder(IClientApplicationBaseExecutor clientApplicationBaseExecutor)
		: base(clientApplicationBaseExecutor)
	{
	}

	internal static AcquireTokenSilentParameterBuilder Create(IClientApplicationBaseExecutor clientApplicationBaseExecutor, IEnumerable<string> scopes, IAccount account)
	{
		return new AcquireTokenSilentParameterBuilder(clientApplicationBaseExecutor).WithScopes(scopes).WithAccount(account);
	}

	internal static AcquireTokenSilentParameterBuilder Create(IClientApplicationBaseExecutor clientApplicationBaseExecutor, IEnumerable<string> scopes, string loginHint)
	{
		return new AcquireTokenSilentParameterBuilder(clientApplicationBaseExecutor).WithScopes(scopes).WithLoginHint(loginHint);
	}

	private AcquireTokenSilentParameterBuilder WithAccount(IAccount account)
	{
		Parameters.Account = account;
		return this;
	}

	private AcquireTokenSilentParameterBuilder WithLoginHint(string loginHint)
	{
		Parameters.LoginHint = loginHint;
		return this;
	}

	public AcquireTokenSilentParameterBuilder WithForceRefresh(bool forceRefresh)
	{
		Parameters.ForceRefresh = forceRefresh;
		return this;
	}

	internal override Task<AuthenticationResult> ExecuteInternalAsync(CancellationToken cancellationToken)
	{
		return base.ClientApplicationBaseExecutor.ExecuteAsync(base.CommonParameters, Parameters, cancellationToken);
	}

	protected override void Validate()
	{
		base.Validate();
		if (!Parameters.SendX5C.HasValue)
		{
			Parameters.SendX5C = base.ServiceBundle.Config.SendX5C;
		}
		if (base.ServiceBundle.Config.Authority.AuthorityInfo.AuthorityType == AuthorityType.B2C && (base.CommonParameters.Scopes == null || base.CommonParameters.Scopes.All(string.IsNullOrWhiteSpace)))
		{
			throw new MsalUiRequiredException("scopes_required_client_credentials", "At least one scope needs to be requested for this authentication flow. ", null, UiRequiredExceptionClassification.AcquireTokenSilentFailed);
		}
	}

	internal override ApiEvent.ApiIds CalculateApiEventId()
	{
		return ApiEvent.ApiIds.AcquireTokenSilent;
	}

	public AcquireTokenSilentParameterBuilder WithSendX5C(bool withSendX5C)
	{
		Parameters.SendX5C = withSendX5C;
		return this;
	}

	public AcquireTokenSilentParameterBuilder WithProofOfPossession(PoPAuthenticationConfiguration popAuthenticationConfiguration)
	{
		ApplicationBase.GuardMobileFrameworks();
		ValidateUseOfExperimentalFeature("WithProofOfPossession");
		base.CommonParameters.PopAuthenticationConfiguration = popAuthenticationConfiguration ?? throw new ArgumentNullException("popAuthenticationConfiguration");
		base.CommonParameters.AuthenticationScheme = new PopAuthenticationScheme(base.CommonParameters.PopAuthenticationConfiguration, base.ServiceBundle);
		return this;
	}

	public AcquireTokenSilentParameterBuilder WithProofOfPossession(string nonce, HttpMethod httpMethod, Uri requestUri)
	{
		if (base.ServiceBundle.Config.IsConfidentialClient)
		{
			ValidateUseOfExperimentalFeature("WithProofOfPossession");
		}
		if (!base.ServiceBundle.Config.IsConfidentialClient && !base.ServiceBundle.Config.IsBrokerEnabled)
		{
			throw new MsalClientException("broker_required_for_pop", "The request has Proof-of-Possession configured but does not have broker enabled. Broker is required to use Proof-of-Possession on public clients. Use IPublicClientApplication.IsProofOfPossessionSupportedByClient to ensure Proof-of-Possession can be performed before using WithProofOfPossession.");
		}
		ApplicationBase.GuardMobileFrameworks();
		IBroker broker = base.ServiceBundle.PlatformProxy.CreateBroker(base.ServiceBundle.Config, null);
		if (base.ServiceBundle.Config.IsBrokerEnabled)
		{
			if (string.IsNullOrEmpty(nonce))
			{
				throw new ArgumentNullException("nonce");
			}
			if (!broker.IsPopSupported)
			{
				throw new MsalClientException("broker_does_not_support_pop", "The broker does not support Proof-of-Possession on the current platform.");
			}
		}
		PoPAuthenticationConfiguration poPAuthenticationConfiguration = new PoPAuthenticationConfiguration(requestUri ?? throw new ArgumentNullException("requestUri"));
		poPAuthenticationConfiguration.HttpMethod = httpMethod ?? throw new ArgumentNullException("httpMethod");
		poPAuthenticationConfiguration.Nonce = nonce;
		IAuthenticationScheme authenticationScheme;
		if (base.ServiceBundle.Config.IsBrokerEnabled)
		{
			poPAuthenticationConfiguration.SignHttpRequest = false;
			authenticationScheme = new PopBrokerAuthenticationScheme();
		}
		else
		{
			authenticationScheme = new PopAuthenticationScheme(poPAuthenticationConfiguration, base.ServiceBundle);
		}
		base.CommonParameters.PopAuthenticationConfiguration = poPAuthenticationConfiguration;
		base.CommonParameters.AuthenticationScheme = authenticationScheme;
		return this;
	}
}
