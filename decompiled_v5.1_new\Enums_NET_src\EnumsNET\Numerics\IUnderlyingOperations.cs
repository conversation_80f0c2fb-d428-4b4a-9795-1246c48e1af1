using System;
using System.Globalization;

namespace EnumsNET.Numerics;

internal interface IUnderlyingOperations<T> where T : struct, IComparable<T>, IEquatable<T>
{
	T One { get; }

	bool LessThan(T left, T right);

	T And(T left, T right);

	T Or(T left, T right);

	T Xor(T left, T right);

	T Not(T value);

	T LeftShift(T value, int amount);

	T Subtract(T left, T right);

	T Create(long value);

	bool IsInValueRange(long value);

	bool IsInValueRange(ulong value);

	bool TryParseNumber(ReadOnlySpan<char> s, NumberStyles style, IFormatProvider provider, out T result);

	bool TryParseNative(ReadOnlySpan<char> s, out T result);

	string ToHexadecimalString(T value);

	string ToDecimalString(T value);

	bool TryFormat(T value, Span<char> destination, out int charsWritten);

	bool TryToHexadecimalString(T value, Span<char> destination, out int charsWritten);

	bool TryToDecimalString(T value, Span<char> destination, out int charsWritten);

	int BitCount(T value);

	bool InRange(T value, T minValue, T maxValue);
}
