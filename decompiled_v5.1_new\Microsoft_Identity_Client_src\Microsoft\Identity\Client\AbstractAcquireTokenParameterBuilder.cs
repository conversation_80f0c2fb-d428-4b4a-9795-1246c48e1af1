using System;
using System.Collections.Generic;
using System.ComponentModel;
using Microsoft.Identity.Client.AuthScheme;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

public abstract class AbstractAcquireTokenParameterBuilder<T> : BaseAbstractAcquireTokenParameterBuilder<T> where T : BaseAbstractAcquireTokenParameterBuilder<T>
{
	protected AbstractAcquireTokenParameterBuilder()
	{
	}

	internal AbstractAcquireTokenParameterBuilder(IServiceBundle serviceBundle)
		: base(serviceBundle)
	{
	}

	protected T WithScopes(IEnumerable<string> scopes)
	{
		base.CommonParameters.Scopes = scopes;
		return this as T;
	}

	public T WithExtraQueryParameters(Dictionary<string, string> extraQueryParameters)
	{
		base.CommonParameters.ExtraQueryParameters = extraQueryParameters ?? new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
		return this as T;
	}

	public T WithClaims(string claims)
	{
		base.CommonParameters.Claims = claims;
		return this as T;
	}

	public T WithExtraQueryParameters(string extraQueryParameters)
	{
		if (!string.IsNullOrWhiteSpace(extraQueryParameters))
		{
			return WithExtraQueryParameters(CoreHelpers.ParseKeyValueList(extraQueryParameters, '&', urlDecode: true, null));
		}
		return this as T;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This API has been deprecated. You can override the tenant ID in the request using WithTenantId. See https://aka.ms/msal-net-authority-override ")]
	public T WithAuthority(string authorityUri, bool validateAuthority = true)
	{
		if (string.IsNullOrWhiteSpace(authorityUri))
		{
			throw new ArgumentNullException("authorityUri");
		}
		base.CommonParameters.AuthorityOverride = AuthorityInfo.FromAuthorityUri(authorityUri, validateAuthority);
		return this as T;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This API has been deprecated. You can override the tenant ID in the request using WithTenantId. See https://aka.ms/msal-net-authority-override ")]
	public T WithAuthority(string cloudInstanceUri, Guid tenantId, bool validateAuthority = true)
	{
		if (string.IsNullOrWhiteSpace(cloudInstanceUri))
		{
			throw new ArgumentNullException("cloudInstanceUri");
		}
		base.CommonParameters.AuthorityOverride = AuthorityInfo.FromAadAuthority(cloudInstanceUri, tenantId, validateAuthority);
		return this as T;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This API has been deprecated. You can override the tenant ID in the request using WithTenantId. See https://aka.ms/msal-net-authority-override ")]
	public T WithAuthority(string cloudInstanceUri, string tenant, bool validateAuthority = true)
	{
		if (string.IsNullOrWhiteSpace(cloudInstanceUri))
		{
			throw new ArgumentNullException("cloudInstanceUri");
		}
		base.CommonParameters.AuthorityOverride = AuthorityInfo.FromAadAuthority(cloudInstanceUri, tenant, validateAuthority);
		return this as T;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This API has been deprecated. You can override the tenant ID in the request using WithTenantId. See https://aka.ms/msal-net-authority-override ")]
	public T WithAuthority(AzureCloudInstance azureCloudInstance, Guid tenantId, bool validateAuthority = true)
	{
		base.CommonParameters.AuthorityOverride = AuthorityInfo.FromAadAuthority(azureCloudInstance, tenantId, validateAuthority);
		return this as T;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This API has been deprecated. You can override the tenant ID in the request using WithTenantId. See https://aka.ms/msal-net-authority-override ")]
	public T WithAuthority(AzureCloudInstance azureCloudInstance, string tenant, bool validateAuthority = true)
	{
		base.CommonParameters.AuthorityOverride = AuthorityInfo.FromAadAuthority(azureCloudInstance, tenant, validateAuthority);
		return this as T;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This API has been deprecated. You can override the tenant ID in the request using WithTenantId. See https://aka.ms/msal-net-authority-override ")]
	public T WithAuthority(AzureCloudInstance azureCloudInstance, AadAuthorityAudience authorityAudience, bool validateAuthority = true)
	{
		base.CommonParameters.AuthorityOverride = AuthorityInfo.FromAadAuthority(azureCloudInstance, authorityAudience, validateAuthority);
		return this as T;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This API has been deprecated. You can override the tenant ID in the request using WithTenantId. See https://aka.ms/msal-net-authority-override ")]
	public T WithAuthority(AadAuthorityAudience authorityAudience, bool validateAuthority = true)
	{
		base.CommonParameters.AuthorityOverride = AuthorityInfo.FromAadAuthority(authorityAudience, validateAuthority);
		return this as T;
	}

	public T WithTenantId(string tenantId)
	{
		if (string.IsNullOrEmpty(tenantId))
		{
			throw new ArgumentNullException("tenantId");
		}
		Authority authority = AuthorityInfo.AuthorityInfoHelper.CreateAuthorityWithTenant(base.ServiceBundle.Config.Authority, tenantId, forceSpecifiedTenant: true);
		base.CommonParameters.AuthorityOverride = authority.AuthorityInfo;
		return this as T;
	}

	public T WithTenantIdFromAuthority(Uri authorityUri)
	{
		if (authorityUri == null)
		{
			throw new ArgumentNullException("authorityUri");
		}
		Authority authority = Authority.CreateAuthority(AuthorityInfo.FromAuthorityUri(authorityUri.ToString(), validateAuthority: false));
		return WithTenantId(authority.TenantId);
	}

	public T WithAdfsAuthority(string authorityUri, bool validateAuthority = true)
	{
		if (string.IsNullOrWhiteSpace(authorityUri))
		{
			throw new ArgumentNullException("authorityUri");
		}
		base.CommonParameters.AuthorityOverride = new AuthorityInfo(AuthorityType.Adfs, authorityUri, validateAuthority);
		return this as T;
	}

	public T WithB2CAuthority(string authorityUri)
	{
		if (string.IsNullOrWhiteSpace(authorityUri))
		{
			throw new ArgumentNullException("authorityUri");
		}
		base.CommonParameters.AuthorityOverride = new AuthorityInfo(AuthorityType.B2C, authorityUri, validateAuthority: false);
		return this as T;
	}

	internal T WithAuthenticationScheme(IAuthenticationScheme scheme)
	{
		base.CommonParameters.AuthenticationScheme = scheme ?? throw new ArgumentNullException("scheme");
		return this as T;
	}
}
