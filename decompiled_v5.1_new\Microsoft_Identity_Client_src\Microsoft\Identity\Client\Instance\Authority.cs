using System;
using System.Diagnostics;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.Instance;

[DebuggerDisplay("{AuthorityInfo.CanonicalAuthority}")]
internal abstract class Authority
{
	public AuthorityInfo AuthorityInfo { get; }

	internal abstract string TenantId { get; }

	protected Authority(AuthorityInfo authorityInfo)
	{
		if (authorityInfo == null)
		{
			throw new ArgumentNullException("authorityInfo");
		}
		AuthorityInfo = new AuthorityInfo(authorityInfo);
	}

	public static Task<Authority> CreateAuthorityForRequestAsync(RequestContext requestContext, AuthorityInfo requestAuthorityInfo, IAccount account = null)
	{
		return AuthorityInfo.AuthorityInfoHelper.CreateAuthorityForRequestAsync(requestContext, requestAuthorityInfo, account);
	}

	public static Authority CreateAuthority(string authority, bool validateAuthority = false)
	{
		return AuthorityInfo.FromAuthorityUri(authority, validateAuthority).CreateAuthority();
	}

	public static Authority CreateAuthority(AuthorityInfo authorityInfo)
	{
		return authorityInfo.CreateAuthority();
	}

	internal static Authority CreateAuthorityWithTenant(AuthorityInfo authorityInfo, string tenantId)
	{
		Authority authority = CreateAuthority(authorityInfo);
		if (string.IsNullOrEmpty(tenantId))
		{
			return authority;
		}
		string tenantedAuthority = authority.GetTenantedAuthority(tenantId, forceSpecifiedTenant: false);
		return CreateAuthority(new AuthorityInfo(authority.AuthorityInfo.AuthorityType, tenantedAuthority, authority.AuthorityInfo.ValidateAuthority));
	}

	internal static Authority CreateAuthorityWithEnvironment(AuthorityInfo authorityInfo, string environment)
	{
		if (!authorityInfo.IsInstanceDiscoverySupported)
		{
			return CreateAuthority(authorityInfo);
		}
		return CreateAuthority(new UriBuilder(authorityInfo.CanonicalAuthority)
		{
			Host = environment
		}.Uri.AbsoluteUri, authorityInfo.ValidateAuthority);
	}

	internal abstract string GetTenantedAuthority(string tenantId, bool forceSpecifiedTenant);

	internal abstract Task<string> GetTokenEndpointAsync(RequestContext requestContext);

	internal abstract Task<string> GetAuthorizationEndpointAsync(RequestContext requestContext);

	internal abstract Task<string> GetDeviceCodeEndpointAsync(RequestContext requestContext);

	internal static string GetEnvironment(string authority)
	{
		return new Uri(authority).Host;
	}
}
