using System;
using System.Diagnostics;
using System.Threading.Tasks;

namespace Microsoft.Identity.Client.Utils;

internal static class StopwatchService
{
	internal static readonly Stopwatch Watch = Stopwatch.StartNew();

	internal static long CurrentElapsedMilliseconds => Watch.ElapsedMilliseconds;

	internal static MeasureDurationResult MeasureCodeBlock(Action codeBlock)
	{
		if (codeBlock == null)
		{
			throw new ArgumentNullException("codeBlock");
		}
		long elapsedTicks = Watch.ElapsedTicks;
		codeBlock();
		return new MeasureDurationResult(Watch.ElapsedTicks - elapsedTicks);
	}

	internal static async Task<MeasureDurationResult> MeasureCodeBlockAsync(Func<Task> codeBlock)
	{
		if (codeBlock == null)
		{
			throw new ArgumentNullException("codeBlock");
		}
		long startTicks = Watch.ElapsedTicks;
		await codeBlock().ConfigureAwait(continueOnCapturedContext: false);
		return new MeasureDurationResult(Watch.ElapsedTicks - startTicks);
	}

	internal static async Task<MeasureDurationResult<TResult>> MeasureCodeBlockAsync<TResult>(Func<Task<TResult>> codeBlock)
	{
		if (codeBlock == null)
		{
			throw new ArgumentNullException("codeBlock");
		}
		long startTicks = Watch.ElapsedTicks;
		return new MeasureDurationResult<TResult>(await codeBlock().ConfigureAwait(continueOnCapturedContext: false), Watch.ElapsedTicks - startTicks);
	}

	internal static async Task<MeasureDurationResult> MeasureAsync(this Task task)
	{
		if (task == null)
		{
			throw new ArgumentNullException("task");
		}
		long startTicks = Watch.ElapsedTicks;
		await task.ConfigureAwait(continueOnCapturedContext: false);
		return new MeasureDurationResult(Watch.ElapsedTicks - startTicks);
	}

	internal static async Task<MeasureDurationResult<TResult>> MeasureAsync<TResult>(this Task<TResult> task)
	{
		if (task == null)
		{
			throw new ArgumentNullException("task");
		}
		long startTicks = Watch.ElapsedTicks;
		return new MeasureDurationResult<TResult>(await task.ConfigureAwait(continueOnCapturedContext: true), Watch.ElapsedTicks - startTicks);
	}
}
