namespace Newtonsoft.Json.Bson;

internal class BsonEmpty : Newtonsoft.Json.Bson.BsonToken
{
	public static readonly Newtonsoft.Json.Bson.BsonToken Null = new Newtonsoft.Json.Bson.BsonEmpty(Newtonsoft.Json.Bson.BsonType.Null);

	public static readonly Newtonsoft.Json.Bson.BsonToken Undefined = new Newtonsoft.Json.Bson.BsonEmpty(Newtonsoft.Json.Bson.BsonType.Undefined);

	public override Newtonsoft.Json.Bson.BsonType Type { get; }

	private BsonEmpty(Newtonsoft.Json.Bson.BsonType type)
	{
		Type = type;
	}
}
