using System.Runtime.InteropServices;

namespace Microsoft.Identity.Client.PlatformsCommon.Shared;

internal static class Win32VersionApi
{
	[StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
	private struct OSVERSIONINFOEXW
	{
		public int dwOSVersionInfoSize;

		public int dwMajorVersion;

		public int dwMinorVersion;

		public int dwBuildNumber;

		public int dwPlatformId;

		[MarshalAs(UnmanagedType.ByValTStr, SizeConst = 128)]
		public string scZSDVersion;

		public ushort wServicePackMajor;

		public ushort wServicePackMinor;

		public short wSuiteMask;

		public byte wProductType;

		public byte wReserved;
	}

	private const byte VER_NT_WORKSTATION = 1;

	private const byte VER_NT_DOMAIN_CONTROLLER = 2;

	private const byte VER_NT_SERVER = 3;

	private const byte NT_STATUS_SUCCESS = 0;

	private const int WamSupportedWindows10BuildNumber = 15063;

	private const int Windows2019BuildNumber = 17763;

	[DllImport("ntdll.dll", CharSet = CharSet.Unicode, ExactSpelling = true, SetLastError = true)]
	private static extern int RtlGetVersion(ref OSVERSIONINFOEXW versionInformation);

	public static bool IsWamSupportedOs()
	{
		try
		{
			OSVERSIONINFOEXW versionInformation = new OSVERSIONINFOEXW
			{
				dwOSVersionInfoSize = Marshal.SizeOf<OSVERSIONINFOEXW>()
			};
			if (RtlGetVersion(ref versionInformation) == 0)
			{
				switch (versionInformation.wProductType)
				{
				case 1:
					if (versionInformation.dwMajorVersion == 10)
					{
						if (versionInformation.dwBuildNumber >= 15063)
						{
							return true;
						}
						return false;
					}
					return false;
				case 2:
				case 3:
					if (versionInformation.dwMajorVersion == 10)
					{
						if (versionInformation.dwBuildNumber >= 17763)
						{
							return true;
						}
						return false;
					}
					return false;
				default:
					return false;
				}
			}
			return false;
		}
		catch
		{
			return false;
		}
	}
}
