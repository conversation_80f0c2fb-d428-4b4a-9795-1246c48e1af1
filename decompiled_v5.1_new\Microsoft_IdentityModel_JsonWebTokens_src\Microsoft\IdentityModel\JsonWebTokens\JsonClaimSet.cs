using System;
using System.Collections.Generic;
using System.Globalization;
using System.Security.Claims;
using System.Text.Json;
using Microsoft.IdentityModel.Json.Linq;
using Microsoft.IdentityModel.Logging;
using Microsoft.IdentityModel.Tokens;

namespace Microsoft.IdentityModel.JsonWebTokens;

internal class JsonClaimSet
{
	private IList<Claim> _claims;

	private readonly object _claimsLock = new object();

	internal Dictionary<string, JsonElement> Elements { get; } = new Dictionary<string, JsonElement>();

	internal JsonClaimSet(JsonDocument jsonDocument)
	{
		foreach (JsonProperty item in jsonDocument.RootElement.EnumerateObject())
		{
			Elements[item.Name] = item.Value.Clone();
		}
	}

	internal JsonClaimSet(byte[] jsonBytes)
		: this(JsonDocument.Parse((ReadOnlyMemory<byte>)jsonBytes, default(JsonDocumentOptions)))
	{
	}

	internal JsonClaimSet(string json)
		: this(JsonDocument.Parse(json))
	{
	}

	internal IList<Claim> Claims(string issuer)
	{
		if (_claims == null)
		{
			lock (_claimsLock)
			{
				if (_claims == null)
				{
					_claims = CreateClaims(issuer);
				}
			}
		}
		return _claims;
	}

	internal IList<Claim> CreateClaims(string issuer)
	{
		IList<Claim> list = new List<Claim>();
		foreach (KeyValuePair<string, JsonElement> element in Elements)
		{
			if (element.Value.ValueKind == JsonValueKind.Array)
			{
				foreach (JsonElement item in element.Value.EnumerateArray())
				{
					Claim claim = CreateClaimFromJsonElement(element.Key, issuer, item);
					if (claim != null)
					{
						list.Add(claim);
					}
				}
			}
			else
			{
				Claim claim2 = CreateClaimFromJsonElement(element.Key, issuer, element.Value);
				if (claim2 != null)
				{
					list.Add(claim2);
				}
			}
		}
		return list;
	}

	private static Claim CreateClaimFromJsonElement(string key, string issuer, JsonElement jsonElement)
	{
		if (jsonElement.ValueKind == JsonValueKind.String)
		{
			try
			{
				if (jsonElement.TryGetDateTime(out var value))
				{
					return new Claim(key, value.ToUniversalTime().ToString("o", CultureInfo.InvariantCulture), "http://www.w3.org/2001/XMLSchema#dateTime", issuer, issuer);
				}
				return new Claim(key, jsonElement.ToString(), "http://www.w3.org/2001/XMLSchema#string", issuer, issuer);
			}
			catch (IndexOutOfRangeException)
			{
				return new Claim(key, jsonElement.ToString(), "http://www.w3.org/2001/XMLSchema#string", issuer, issuer);
			}
		}
		if (jsonElement.ValueKind == JsonValueKind.Null)
		{
			return new Claim(key, string.Empty, "JSON_NULL", issuer, issuer);
		}
		if (jsonElement.ValueKind == JsonValueKind.Object)
		{
			return new Claim(key, jsonElement.ToString(), "JSON", issuer, issuer);
		}
		if (jsonElement.ValueKind == JsonValueKind.False)
		{
			return new Claim(key, "false", "http://www.w3.org/2001/XMLSchema#boolean", issuer, issuer);
		}
		if (jsonElement.ValueKind == JsonValueKind.True)
		{
			return new Claim(key, "true", "http://www.w3.org/2001/XMLSchema#boolean", issuer, issuer);
		}
		if (jsonElement.ValueKind == JsonValueKind.Number)
		{
			if (jsonElement.TryGetInt16(out var _))
			{
				return new Claim(key, jsonElement.ToString(), "http://www.w3.org/2001/XMLSchema#integer", issuer, issuer);
			}
			if (jsonElement.TryGetInt32(out var _))
			{
				return new Claim(key, jsonElement.ToString(), "http://www.w3.org/2001/XMLSchema#integer", issuer, issuer);
			}
			if (jsonElement.TryGetInt64(out var _))
			{
				return new Claim(key, jsonElement.ToString(), "http://www.w3.org/2001/XMLSchema#integer64", issuer, issuer);
			}
			if (jsonElement.TryGetDecimal(out var _))
			{
				return new Claim(key, jsonElement.ToString(), "http://www.w3.org/2001/XMLSchema#double", issuer, issuer);
			}
			if (jsonElement.TryGetDouble(out var _))
			{
				return new Claim(key, jsonElement.ToString(), "http://www.w3.org/2001/XMLSchema#double", issuer, issuer);
			}
			if (jsonElement.TryGetUInt32(out var _))
			{
				return new Claim(key, jsonElement.ToString(), "http://www.w3.org/2001/XMLSchema#uinteger32", issuer, issuer);
			}
			if (jsonElement.TryGetUInt64(out var _))
			{
				return new Claim(key, jsonElement.ToString(), "http://www.w3.org/2001/XMLSchema#uinteger64", issuer, issuer);
			}
		}
		else if (jsonElement.ValueKind == JsonValueKind.Array)
		{
			return new Claim(key, jsonElement.ToString(), "JSON_ARRAY", issuer, issuer);
		}
		return null;
	}

	private static object CreateObjectFromJsonElement(JsonElement jsonElement)
	{
		if (jsonElement.ValueKind == JsonValueKind.Array)
		{
			int num = 0;
			foreach (JsonElement item in jsonElement.EnumerateArray())
			{
				_ = item;
				num++;
			}
			object[] array = new object[num];
			int num2 = 0;
			{
				foreach (JsonElement item2 in jsonElement.EnumerateArray())
				{
					array[num2++] = CreateObjectFromJsonElement(item2);
				}
				return array;
			}
		}
		if (jsonElement.ValueKind == JsonValueKind.String)
		{
			if (DateTime.TryParse(jsonElement.GetString(), CultureInfo.InvariantCulture, DateTimeStyles.RoundtripKind, out var result))
			{
				return result;
			}
			return jsonElement.GetString();
		}
		if (jsonElement.ValueKind == JsonValueKind.Null)
		{
			return null;
		}
		if (jsonElement.ValueKind == JsonValueKind.Object)
		{
			return jsonElement.ToString();
		}
		if (jsonElement.ValueKind == JsonValueKind.False)
		{
			return false;
		}
		if (jsonElement.ValueKind == JsonValueKind.True)
		{
			return true;
		}
		if (jsonElement.ValueKind == JsonValueKind.Number)
		{
			if (jsonElement.TryGetInt64(out var value))
			{
				return value;
			}
			if (jsonElement.TryGetInt32(out var value2))
			{
				return value2;
			}
			if (jsonElement.TryGetDecimal(out var value3))
			{
				return value3;
			}
			if (jsonElement.TryGetDouble(out var value4))
			{
				return value4;
			}
			if (jsonElement.TryGetUInt32(out var value5))
			{
				return value5;
			}
			if (jsonElement.TryGetUInt64(out var value6))
			{
				return value6;
			}
		}
		return jsonElement.GetString();
	}

	internal Claim GetClaim(string key, string issuer)
	{
		if (key == null)
		{
			throw new ArgumentNullException("key");
		}
		if (!Elements.TryGetValue(key, out var value))
		{
			throw LogHelper.LogExceptionMessage(new ArgumentException(LogHelper.FormatInvariant("IDX14304: Claim with name '{0}' does not exist in the payload.", key)));
		}
		return CreateClaimFromJsonElement(key, issuer, value);
	}

	internal static string GetClaimValueType(object obj)
	{
		if (obj == null)
		{
			return "JSON_NULL";
		}
		Type type = obj.GetType();
		if (type == typeof(string))
		{
			return "http://www.w3.org/2001/XMLSchema#string";
		}
		if (type == typeof(int))
		{
			return "http://www.w3.org/2001/XMLSchema#integer";
		}
		if (type == typeof(bool))
		{
			return "http://www.w3.org/2001/XMLSchema#boolean";
		}
		if (type == typeof(double))
		{
			return "http://www.w3.org/2001/XMLSchema#double";
		}
		if (type == typeof(long))
		{
			long num = (long)obj;
			if (num >= int.MinValue && num <= int.MaxValue)
			{
				return "http://www.w3.org/2001/XMLSchema#integer";
			}
			return "http://www.w3.org/2001/XMLSchema#integer64";
		}
		if (type == typeof(DateTime))
		{
			return "http://www.w3.org/2001/XMLSchema#dateTime";
		}
		return type.ToString();
	}

	internal string GetStringValue(string key)
	{
		if (Elements.TryGetValue(key, out var value) && value.ValueKind == JsonValueKind.String)
		{
			return value.GetString();
		}
		return string.Empty;
	}

	internal DateTime GetDateTime(string key)
	{
		if (!Elements.TryGetValue(key, out var value))
		{
			return DateTime.MinValue;
		}
		return EpochTime.DateTime(Convert.ToInt64(Math.Truncate(Convert.ToDouble(ParseTimeValue(key, value), CultureInfo.InvariantCulture))));
	}

	internal T GetValue<T>(string key)
	{
		bool found;
		return GetValue<T>(key, throwEx: true, out found);
	}

	internal T GetValue<T>(string key, bool throwEx, out bool found)
	{
		found = Elements.TryGetValue(key, out var value);
		if (!found)
		{
			if (throwEx)
			{
				throw LogHelper.LogExceptionMessage(new ArgumentException(LogHelper.FormatInvariant("IDX14304: Claim with name '{0}' does not exist in the payload.", key)));
			}
			return default(T);
		}
		if (typeof(T) == typeof(JsonElement))
		{
			return (T)(object)value;
		}
		try
		{
			if (value.ValueKind == JsonValueKind.Null)
			{
				if (typeof(T) == typeof(object) || typeof(T).IsClass || Nullable.GetUnderlyingType(typeof(T)) != null)
				{
					return (T)(object)null;
				}
				found = false;
				return default(T);
			}
			if (typeof(T) == typeof(JObject))
			{
				return (T)(object)JObject.Parse(value.ToString());
			}
			if (typeof(T) == typeof(JArray))
			{
				return (T)(object)JArray.Parse(value.ToString());
			}
			if (typeof(T) == typeof(object))
			{
				return (T)CreateObjectFromJsonElement(value);
			}
			if (typeof(T) == typeof(object[]))
			{
				if (value.ValueKind == JsonValueKind.Array)
				{
					int num = 0;
					foreach (JsonElement item in value.EnumerateArray())
					{
						_ = item;
						num++;
					}
					object[] array = new object[num];
					int num2 = 0;
					foreach (JsonElement item2 in value.EnumerateArray())
					{
						array[num2++] = CreateObjectFromJsonElement(item2);
					}
					return (T)(object)array;
				}
				return (T)(object)new object[1] { CreateObjectFromJsonElement(value) };
			}
			if (typeof(T) == typeof(string))
			{
				return (T)(object)value.ToString();
			}
			if (value.ValueKind == JsonValueKind.String)
			{
				if (typeof(T) == typeof(long) && long.TryParse(value.ToString(), out var result))
				{
					return (T)(object)result;
				}
				if (typeof(T) == typeof(int) && int.TryParse(value.ToString(), out var result2))
				{
					return (T)(object)result2;
				}
				if (typeof(T) == typeof(DateTime))
				{
					if (DateTime.TryParse(value.GetString(), CultureInfo.InvariantCulture, DateTimeStyles.RoundtripKind, out var result3))
					{
						return (T)(object)result3;
					}
					return JsonSerializer.Deserialize<T>(value.GetRawText());
				}
				if (typeof(T) == typeof(double) && double.TryParse(value.ToString(), out var result4))
				{
					return (T)(object)result4;
				}
				if (typeof(T) == typeof(float) && float.TryParse(value.ToString(), out var result5))
				{
					return (T)(object)result5;
				}
			}
			return JsonSerializer.Deserialize<T>(value.GetRawText());
		}
		catch (Exception innerException)
		{
			found = false;
			if (throwEx)
			{
				throw LogHelper.LogExceptionMessage(new ArgumentException(LogHelper.FormatInvariant("IDX14305: Unable to convert the '{0}' json property to the following type: '{1}'. Property type was: '{2}'. Value: '{3}'.", key, typeof(T), value.ValueKind, value.GetRawText()), innerException));
			}
		}
		return default(T);
	}

	internal bool TryGetClaim(string key, string issuer, out Claim claim)
	{
		if (!Elements.TryGetValue(key, out var value))
		{
			claim = null;
			return false;
		}
		claim = CreateClaimFromJsonElement(key, issuer, value);
		return true;
	}

	internal bool TryGetValue<T>(string key, out T value)
	{
		value = GetValue<T>(key, throwEx: false, out var found);
		return found;
	}

	internal bool HasClaim(string claimName)
	{
		JsonElement value;
		return Elements.TryGetValue(claimName, out value);
	}

	private static long ParseTimeValue(string claimName, JsonElement jsonElement)
	{
		if (jsonElement.ValueKind == JsonValueKind.Number)
		{
			if (jsonElement.TryGetInt64(out var value))
			{
				return value;
			}
			if (jsonElement.TryGetDouble(out var value2))
			{
				return (long)value2;
			}
			if (jsonElement.TryGetInt32(out var value3))
			{
				return value3;
			}
			if (jsonElement.TryGetDecimal(out var value4))
			{
				return (long)value4;
			}
		}
		if (jsonElement.ValueKind == JsonValueKind.String)
		{
			string s = jsonElement.GetString();
			if (long.TryParse(s, out var result))
			{
				return result;
			}
			if (float.TryParse(s, out var result2))
			{
				return (long)result2;
			}
			if (double.TryParse(s, out var result3))
			{
				return (long)result3;
			}
		}
		throw LogHelper.LogExceptionMessage(new FormatException(LogHelper.FormatInvariant("IDX14300: Could not parse '{0}' : '{1}' as a '{2}'.", claimName, jsonElement.ToString(), typeof(long))));
	}
}
