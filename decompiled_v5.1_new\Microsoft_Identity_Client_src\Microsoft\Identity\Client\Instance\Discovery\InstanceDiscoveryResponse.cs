using System.Text.Json.Serialization;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.Platforms.net6;

namespace Microsoft.Identity.Client.Instance.Discovery;

[JsonObject]
[Preserve(AllMembers = true)]
internal sealed class InstanceDiscoveryResponse : OAuth2ResponseBase
{
	[JsonPropertyName("tenant_discovery_endpoint")]
	public string TenantDiscoveryEndpoint { get; set; }

	[JsonPropertyName("metadata")]
	public InstanceDiscoveryMetadataEntry[] Metadata { get; set; }
}
