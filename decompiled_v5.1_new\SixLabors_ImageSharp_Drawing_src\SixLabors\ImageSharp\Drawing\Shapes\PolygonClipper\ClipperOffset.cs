using System;

namespace SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper;

internal class ClipperOffset
{
	private readonly PolygonOffsetter polygonClipperOffset;

	public ClipperOffset(float meterLimit = 2f, float arcTolerance = 0.25f)
	{
		polygonClipperOffset = new PolygonOffsetter(meterLimit, arcTolerance);
	}

	public ComplexPolygon Execute(float width)
	{
		PathsF pathsF = new PathsF();
		polygonClipperOffset.Execute(width, pathsF);
		Polygon[] array = new Polygon[pathsF.Count];
		for (int i = 0; i < pathsF.Count; i++)
		{
			PathF pathF = pathsF[i];
			PointF[] array2 = new PointF[pathF.Count];
			for (int j = 0; j < pathF.Count; j++)
			{
				array2[j] = pathF[j];
			}
			array[i] = new Polygon(array2);
		}
		IPath[] paths = array;
		return new ComplexPolygon(paths);
	}

	public void AddPath(ReadOnlySpan<PointF> pathPoints, JointStyle jointStyle, EndCapStyle endCapStyle)
	{
		PathF pathF = new PathF(pathPoints.Length);
		for (int i = 0; i < pathPoints.Length; i++)
		{
			pathF.Add(pathPoints[i]);
		}
		polygonClipperOffset.AddPath(pathF, jointStyle, endCapStyle);
	}

	public void AddPath(IPath path, JointStyle jointStyle, EndCapStyle endCapStyle)
	{
		SixLabors.Guard.NotNull(path, "path");
		foreach (ISimplePath item in path.Flatten())
		{
			AddPath(item, jointStyle, endCapStyle);
		}
	}

	private void AddPath(ISimplePath path, JointStyle jointStyle, EndCapStyle endCapStyle)
	{
		ReadOnlySpan<PointF> span = path.Points.Span;
		AddPath(span, jointStyle, path.IsClosed ? EndCapStyle.Joined : endCapStyle);
	}
}
