﻿<linker>
  <assembly fullname="System.Security.Cryptography.ProtectedData" feature="System.Resources.UseSystemResourceKeys" featurevalue="true">
    <!-- System.Resources.UseSystemResourceKeys removes resource strings and instead uses the resource key as the exception message -->
    <resource name="FxResources.System.Security.Cryptography.ProtectedData.SR.resources" action="remove" />
    <type fullname="System.SR">
      <method signature="System.Boolean UsingResourceKeys()" body="stub" value="true" />
    </type>
  </assembly>
</linker>