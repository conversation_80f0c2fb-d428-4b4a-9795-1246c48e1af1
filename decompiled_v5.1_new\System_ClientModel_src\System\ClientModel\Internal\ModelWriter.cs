using System.Buffers;
using System.ClientModel.Primitives;
using System.IO;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace System.ClientModel.Internal;

internal class ModelWriter : ModelWriter<object>
{
	public ModelWriter(IJsonModel<object> model, ModelReaderWriterOptions options)
		: base(model, options)
	{
	}
}
internal class ModelWriter<T> : IDisposable
{
	private sealed class SequenceBuilder : IBufferWriter<byte>, IDisposable
	{
		private struct Buffer
		{
			public byte[] Array;

			public int Written;
		}

		private volatile Buffer[] _buffers;

		private volatile int _count;

		private readonly int _segmentSize;

		private readonly object _lock = new object();

		public SequenceBuilder(int segmentSize = 16384)
		{
			_segmentSize = segmentSize;
			_buffers = Array.Empty<Buffer>();
		}

		public void Advance(int bytesWritten)
		{
			ref Buffer reference = ref _buffers[_count - 1];
			reference.Written += bytesWritten;
			if (reference.Written > reference.Array.Length)
			{
				throw new ArgumentOutOfRangeException("bytesWritten");
			}
		}

		public Memory<byte> GetMemory(int sizeHint = 0)
		{
			if (sizeHint < 256)
			{
				sizeHint = 256;
			}
			int sizeToRent = ((sizeHint > _segmentSize) ? sizeHint : _segmentSize);
			if (_buffers.Length == 0)
			{
				ExpandBuffers(sizeToRent);
			}
			ref Buffer reference = ref _buffers[_count - 1];
			Memory<byte> result = reference.Array.AsMemory(reference.Written);
			if (result.Length >= sizeHint)
			{
				return result;
			}
			ExpandBuffers(sizeToRent);
			return _buffers[_count - 1].Array;
		}

		private void ExpandBuffers(int sizeToRent)
		{
			lock (_lock)
			{
				int num = ((_count == 0) ? 1 : (_count * 2));
				Buffer[] array = new Buffer[num];
				if (_count > 0)
				{
					_buffers.CopyTo(array, 0);
				}
				_buffers = array;
				_buffers[_count].Array = ArrayPool<byte>.Shared.Rent(sizeToRent);
				_count = ((num == 1) ? num : (_count + 1));
			}
		}

		public Span<byte> GetSpan(int sizeHint = 0)
		{
			return GetMemory(sizeHint).Span;
		}

		public void Dispose()
		{
			int count;
			Buffer[] buffers;
			lock (_lock)
			{
				count = _count;
				buffers = _buffers;
				_count = 0;
				_buffers = Array.Empty<Buffer>();
			}
			for (int i = 0; i < count; i++)
			{
				ArrayPool<byte>.Shared.Return(buffers[i].Array);
			}
		}

		public bool TryComputeLength(out long length)
		{
			length = 0L;
			for (int i = 0; i < _count; i++)
			{
				length += _buffers[i].Written;
			}
			return true;
		}

		public void CopyTo(Stream stream, CancellationToken cancellation)
		{
			for (int i = 0; i < _count; i++)
			{
				cancellation.ThrowIfCancellationRequested();
				Buffer buffer = _buffers[i];
				stream.Write(buffer.Array, 0, buffer.Written);
			}
		}

		public async Task CopyToAsync(Stream stream, CancellationToken cancellation)
		{
			for (int i = 0; i < _count; i++)
			{
				cancellation.ThrowIfCancellationRequested();
				Buffer buffer = _buffers[i];
				await stream.WriteAsync(buffer.Array, 0, buffer.Written, cancellation).ConfigureAwait(continueOnCapturedContext: false);
			}
		}
	}

	private readonly IJsonModel<T> _model;

	private readonly ModelReaderWriterOptions _options;

	private readonly object _writeLock = new object();

	private readonly object _readLock = new object();

	private volatile SequenceBuilder? _sequenceBuilder;

	private volatile bool _isDisposed;

	private volatile int _readCount;

	private ManualResetEvent? _readersFinished;

	private ManualResetEvent ReadersFinished => _readersFinished ?? (_readersFinished = new ManualResetEvent(initialState: true));

	public ModelWriter(IJsonModel<T> model, ModelReaderWriterOptions options)
	{
		_model = model;
		_options = options;
	}

	private SequenceBuilder GetSequenceBuilder()
	{
		if (_sequenceBuilder == null)
		{
			lock (_writeLock)
			{
				if (_isDisposed)
				{
					throw new ObjectDisposedException("ModelWriter");
				}
				if (_sequenceBuilder == null)
				{
					SequenceBuilder sequenceBuilder = new SequenceBuilder();
					using Utf8JsonWriter utf8JsonWriter = new Utf8JsonWriter(sequenceBuilder);
					_model.Write(utf8JsonWriter, _options);
					utf8JsonWriter.Flush();
					_sequenceBuilder = sequenceBuilder;
				}
			}
		}
		return _sequenceBuilder;
	}

	internal void CopyTo(Stream stream, CancellationToken cancellation)
	{
		SequenceBuilder sequenceBuilder = GetSequenceBuilder();
		IncrementRead();
		try
		{
			sequenceBuilder.CopyTo(stream, cancellation);
		}
		finally
		{
			DecrementRead();
		}
	}

	internal bool TryComputeLength(out long length)
	{
		SequenceBuilder sequenceBuilder = GetSequenceBuilder();
		IncrementRead();
		try
		{
			return sequenceBuilder.TryComputeLength(out length);
		}
		finally
		{
			DecrementRead();
		}
	}

	internal async Task CopyToAsync(Stream stream, CancellationToken cancellation)
	{
		SequenceBuilder sequenceBuilder = GetSequenceBuilder();
		IncrementRead();
		try
		{
			await sequenceBuilder.CopyToAsync(stream, cancellation).ConfigureAwait(continueOnCapturedContext: false);
		}
		finally
		{
			DecrementRead();
		}
	}

	public BinaryData ToBinaryData()
	{
		SequenceBuilder sequenceBuilder = GetSequenceBuilder();
		IncrementRead();
		try
		{
			sequenceBuilder.TryComputeLength(out var length);
			if (length > int.MaxValue)
			{
				throw new InvalidOperationException($"Length of serialized model is too long.  Value was {length} max is {int.MaxValue}");
			}
			using MemoryStream memoryStream = new MemoryStream((int)length);
			sequenceBuilder.CopyTo(memoryStream, default(CancellationToken));
			return new BinaryData(memoryStream.GetBuffer().AsMemory(0, (int)memoryStream.Position));
		}
		finally
		{
			DecrementRead();
		}
	}

	public void Dispose()
	{
		if (_isDisposed)
		{
			return;
		}
		lock (_writeLock)
		{
			if (!_isDisposed)
			{
				_isDisposed = true;
				if (_readersFinished == null || _readersFinished.WaitOne())
				{
					_sequenceBuilder?.Dispose();
				}
				_sequenceBuilder = null;
				_readersFinished?.Dispose();
				_readersFinished = null;
			}
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void IncrementRead()
	{
		if (_isDisposed)
		{
			throw new ObjectDisposedException("ModelWriter");
		}
		lock (_readLock)
		{
			_readCount++;
			ReadersFinished.Reset();
		}
		if (_isDisposed)
		{
			DecrementRead();
			throw new ObjectDisposedException("ModelWriter");
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void DecrementRead()
	{
		lock (_readLock)
		{
			_readCount--;
			if (_readCount == 0)
			{
				ReadersFinished.Set();
			}
		}
	}
}
