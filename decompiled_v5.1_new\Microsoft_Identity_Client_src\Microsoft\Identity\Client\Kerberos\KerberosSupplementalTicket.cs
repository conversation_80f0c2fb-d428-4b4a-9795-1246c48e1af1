using System.Text.Json.Serialization;

namespace Microsoft.Identity.Client.Kerberos;

public class KerberosSupplementalTicket
{
	[Json<PERSON>ropertyName("clientKey")]
	public string ClientKey { get; set; }

	[JsonPropertyName("keyType")]
	public KerberosKeyTypes KeyType { get; set; }

	[Json<PERSON>ropertyName("messageBuffer")]
	public string KerberosMessageBuffer { get; set; }

	[JsonPropertyName("error")]
	public string ErrorMessage { get; set; }

	[JsonPropertyName("realm")]
	public string Realm { get; set; }

	[JsonPropertyName("sn")]
	public string ServicePrincipalName { get; set; }

	[JsonPropertyName("cn")]
	public string ClientName { get; set; }

	public KerberosSupplementalTicket()
	{
	}

	public KerberosSupplementalTicket(string errorMessage)
	{
		ErrorMessage = errorMessage;
	}

	public override string ToString()
	{
		return $"[ Realm: {Realm}, sp: {ServicePrincipalName}, cn: {ClientName}, KeyType: {KeyType} ]";
	}
}
