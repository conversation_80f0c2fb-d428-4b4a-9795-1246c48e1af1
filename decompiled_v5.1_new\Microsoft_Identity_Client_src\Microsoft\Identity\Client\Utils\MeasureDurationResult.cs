using System.Diagnostics;

namespace Microsoft.Identity.Client.Utils;

internal struct MeasureDurationResult<TResult>
{
	private const int TicksPerMicrosecond = 10;

	private static readonly double s_tickFrequency = 10000000.0 / (double)Stopwatch.Frequency;

	public TResult Result { get; }

	public long Milliseconds { get; }

	public long Microseconds { get; }

	public long Ticks { get; }

	public MeasureDurationResult(TResult result, long ticks)
	{
		Result = result;
		Milliseconds = (long)((double)ticks / s_tickFrequency / 10000.0);
		Microseconds = (long)((double)ticks * s_tickFrequency / 10.0 % 1000.0);
		Ticks = ticks;
	}
}
internal struct MeasureDurationResult
{
	private const int TicksPerMicrosecond = 10;

	private static readonly double s_tickFrequency = 10000000.0 / (double)Stopwatch.Frequency;

	public long Milliseconds { get; }

	public long Microseconds { get; }

	public long Ticks { get; }

	public MeasureDurationResult(long ticks)
	{
		Milliseconds = (long)((double)ticks * s_tickFrequency / 10000.0 % 1000.0);
		Microseconds = (long)((double)ticks * s_tickFrequency / 10.0 % 1000.0);
		Ticks = ticks;
	}
}
