using System.Collections.Generic;
using Microsoft.Identity.Client.Kerberos;

namespace Microsoft.Identity.Client;

public abstract class ApplicationOptions : BaseApplicationOptions
{
	public string ClientId { get; set; }

	public string TenantId { get; set; }

	public AadAuthorityAudience AadAuthorityAudience { get; set; }

	public string Instance { get; set; }

	public AzureCloudInstance AzureCloudInstance { get; set; }

	public string RedirectUri { get; set; }

	public string ClientName { get; set; }

	public string ClientVersion { get; set; }

	public IEnumerable<string> ClientCapabilities { get; set; }

	public bool LegacyCacheCompatibilityEnabled { get; set; } = true;

	public string KerberosServicePrincipalName { get; set; } = string.Empty;

	public KerberosTicketContainer TicketContainer { get; set; }
}
