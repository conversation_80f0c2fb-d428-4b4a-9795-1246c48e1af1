using System;
using System.Collections.Generic;
using System.Text.Json.Nodes;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Cache;

internal class TokenCacheJsonSerializer : ITokenCacheSerializable
{
	private readonly ITokenCacheAccessor _accessor;

	public TokenCacheJsonSerializer(ITokenCacheAccessor accessor)
	{
		_accessor = accessor;
	}

	public byte[] Serialize(IDictionary<string, JsonNode> unknownNodes)
	{
		CacheSerializationContract cacheSerializationContract = new CacheSerializationContract(unknownNodes);
		foreach (MsalAccessTokenCacheItem allAccessToken in _accessor.GetAllAccessTokens())
		{
			cacheSerializationContract.AccessTokens[allAccessToken.CacheKey] = allAccessToken;
		}
		foreach (MsalRefreshTokenCacheItem allRefreshToken in _accessor.GetAllRefreshTokens())
		{
			cacheSerializationContract.RefreshTokens[allRefreshToken.CacheKey] = allRefreshToken;
		}
		foreach (MsalIdTokenCacheItem allIdToken in _accessor.GetAllIdTokens())
		{
			cacheSerializationContract.IdTokens[allIdToken.CacheKey] = allIdToken;
		}
		foreach (MsalAccountCacheItem allAccount in _accessor.GetAllAccounts())
		{
			cacheSerializationContract.Accounts[allAccount.CacheKey] = allAccount;
		}
		foreach (MsalAppMetadataCacheItem allAppMetadatum in _accessor.GetAllAppMetadata())
		{
			cacheSerializationContract.AppMetadata[allAppMetadatum.CacheKey] = allAppMetadatum;
		}
		return cacheSerializationContract.ToJsonString().ToByteArray();
	}

	public IDictionary<string, JsonNode> Deserialize(byte[] bytes, bool clearExistingCacheData)
	{
		string text = CoreHelpers.ByteArrayToString(bytes);
		CacheSerializationContract cacheSerializationContract;
		try
		{
			cacheSerializationContract = CacheSerializationContract.FromJsonString(text);
		}
		catch (Exception ex)
		{
			string arg = ((text.Length > 5) ? text.Substring(0, 5) : text);
			throw new MsalClientException("json_parse_failed", $"MSAL deserialization failed to parse the cache contents. First characters of the cache string: {arg} \r\nPossible cause: token cache encryption is used via Microsoft.Identity.Web.TokenCache and decryption fails, for example. \r\n Full details of inner exception: {ex} ", ex);
		}
		if (clearExistingCacheData)
		{
			_accessor.Clear();
		}
		if (cacheSerializationContract.AccessTokens != null)
		{
			foreach (MsalAccessTokenCacheItem value in cacheSerializationContract.AccessTokens.Values)
			{
				_accessor.SaveAccessToken(value);
			}
		}
		if (cacheSerializationContract.RefreshTokens != null)
		{
			foreach (MsalRefreshTokenCacheItem value2 in cacheSerializationContract.RefreshTokens.Values)
			{
				_accessor.SaveRefreshToken(value2);
			}
		}
		if (cacheSerializationContract.IdTokens != null)
		{
			foreach (MsalIdTokenCacheItem value3 in cacheSerializationContract.IdTokens.Values)
			{
				_accessor.SaveIdToken(value3);
			}
		}
		if (cacheSerializationContract.Accounts != null)
		{
			foreach (MsalAccountCacheItem value4 in cacheSerializationContract.Accounts.Values)
			{
				_accessor.SaveAccount(value4);
			}
		}
		if (cacheSerializationContract.AppMetadata != null)
		{
			foreach (MsalAppMetadataCacheItem value5 in cacheSerializationContract.AppMetadata.Values)
			{
				_accessor.SaveAppMetadata(value5);
			}
		}
		return cacheSerializationContract.UnknownNodes;
	}
}
