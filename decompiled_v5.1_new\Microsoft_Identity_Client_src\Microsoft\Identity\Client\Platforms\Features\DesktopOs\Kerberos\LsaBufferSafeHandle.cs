using System;
using System.Runtime.InteropServices;

namespace Microsoft.Identity.Client.Platforms.Features.DesktopOs.Kerberos;

internal class LsaBufferSafeHandle : SafeHandle
{
	public override bool IsInvalid => handle == IntPtr.Zero;

	public LsaBufferSafeHandle()
		: base(IntPtr.Zero, ownsHandle: true)
	{
	}

	protected override bool ReleaseHandle()
	{
		NativeMethods.LsaThrowIfError(NativeMethods.LsaFreeReturnBuffer(handle));
		handle = IntPtr.Zero;
		return true;
	}
}
