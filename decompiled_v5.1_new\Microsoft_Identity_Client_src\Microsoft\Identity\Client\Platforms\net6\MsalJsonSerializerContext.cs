using System;
using System.CodeDom.Compiler;
using System.Collections.Generic;
using System.Net;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Json.Serialization.Metadata;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Instance.Discovery;
using Microsoft.Identity.Client.Instance.Oidc;
using Microsoft.Identity.Client.Instance.Validation;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Kerberos;
using Microsoft.Identity.Client.ManagedIdentity;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.Region;
using Microsoft.Identity.Client.WsTrust;

namespace Microsoft.Identity.Client.Platforms.net6;

[JsonSerializable(typeof(KerberosSupplementalTicket))]
[JsonSerializable(typeof(InstanceDiscoveryResponse))]
[JsonSerializable(typeof(LocalImdsErrorResponse))]
[JsonSerializable(typeof(AdalResultWrapper))]
[JsonSerializable(typeof(List<KeyValuePair<string, IEnumerable<string>>>))]
[JsonSerializable(typeof(ClientInfo))]
[JsonSerializable(typeof(OAuth2ResponseBase))]
[JsonSerializable(typeof(MsalTokenResponse))]
[JsonSerializable(typeof(UserRealmDiscoveryResponse))]
[JsonSerializable(typeof(DeviceCodeResponse))]
[JsonSerializable(typeof(AdfsWebFingerResponse))]
[JsonSerializable(typeof(DeviceAuthHeader))]
[JsonSerializable(typeof(DeviceAuthPayload))]
[JsonSerializable(typeof(ManagedIdentityResponse))]
[JsonSerializable(typeof(ManagedIdentityErrorResponse))]
[JsonSerializable(typeof(OidcMetadata))]
[JsonSourceGenerationOptions]
[GeneratedCode("System.Text.Json.SourceGeneration", "6.0.10.26714")]
internal class MsalJsonSerializerContext : JsonSerializerContext
{
	private static MsalJsonSerializerContext s_customContext;

	private JsonTypeInfo<string>? _String;

	private JsonTypeInfo<KerberosKeyTypes>? _KerberosKeyTypes;

	private JsonTypeInfo<KerberosSupplementalTicket>? _KerberosSupplementalTicket;

	private JsonTypeInfo<string[]>? _StringArray;

	private JsonTypeInfo<InstanceDiscoveryMetadataEntry>? _InstanceDiscoveryMetadataEntry;

	private JsonTypeInfo<InstanceDiscoveryMetadataEntry[]>? _InstanceDiscoveryMetadataEntryArray;

	private JsonTypeInfo<InstanceDiscoveryResponse>? _InstanceDiscoveryResponse;

	private JsonTypeInfo<List<string>>? _ListString;

	private JsonTypeInfo<LocalImdsErrorResponse>? _LocalImdsErrorResponse;

	private JsonTypeInfo<DateTimeOffset>? _DateTimeOffset;

	private JsonTypeInfo<DateTimeOffset?>? _NullableDateTimeOffset;

	private JsonTypeInfo<Uri>? _Uri;

	private JsonTypeInfo<AdalUserInfo>? _AdalUserInfo;

	private JsonTypeInfo<AdalResult>? _AdalResult;

	private JsonTypeInfo<bool>? _Boolean;

	private JsonTypeInfo<AdalResultWrapper>? _AdalResultWrapper;

	private JsonTypeInfo<IEnumerable<string>>? _IEnumerableString;

	private JsonTypeInfo<int>? _Int32;

	private JsonTypeInfo<KeyValuePair<string, IEnumerable<string>>>? _KeyValuePairStringIEnumerableString;

	private JsonTypeInfo<List<KeyValuePair<string, IEnumerable<string>>>>? _ListKeyValuePairStringIEnumerableString;

	private JsonTypeInfo<ClientInfo>? _ClientInfo;

	private JsonTypeInfo<OAuth2ResponseBase>? _OAuth2ResponseBase;

	private JsonTypeInfo<JsonElement>? _JsonElement;

	private JsonTypeInfo<Dictionary<string, JsonElement>>? _DictionaryStringJsonElement;

	private JsonTypeInfo<long>? _Int64;

	private JsonTypeInfo<long?>? _NullableInt64;

	private JsonTypeInfo<TokenSource>? _TokenSource;

	private JsonTypeInfo<HttpResponseHeaders>? _HttpResponseHeaders;

	private JsonTypeInfo<IDictionary<string, string>>? _IDictionaryStringString;

	private JsonTypeInfo<HttpStatusCode>? _HttpStatusCode;

	private JsonTypeInfo<HttpResponse>? _HttpResponse;

	private JsonTypeInfo<MsalTokenResponse>? _MsalTokenResponse;

	private JsonTypeInfo<UserRealmDiscoveryResponse>? _UserRealmDiscoveryResponse;

	private JsonTypeInfo<DeviceCodeResponse>? _DeviceCodeResponse;

	private JsonTypeInfo<LinksList>? _LinksList;

	private JsonTypeInfo<List<LinksList>>? _ListLinksList;

	private JsonTypeInfo<AdfsWebFingerResponse>? _AdfsWebFingerResponse;

	private JsonTypeInfo<IList<string>>? _IListString;

	private JsonTypeInfo<DeviceAuthHeader>? _DeviceAuthHeader;

	private JsonTypeInfo<Lazy<long>>? _LazyInt64;

	private JsonTypeInfo<DeviceAuthPayload>? _DeviceAuthPayload;

	private JsonTypeInfo<ManagedIdentityResponse>? _ManagedIdentityResponse;

	private JsonTypeInfo<ManagedIdentityErrorResponse>? _ManagedIdentityErrorResponse;

	private JsonTypeInfo<OidcMetadata>? _OidcMetadata;

	private static MsalJsonSerializerContext? s_defaultContext;

	private static readonly JsonEncodedText PropName_clientKey = JsonEncodedText.Encode("clientKey");

	private static readonly JsonEncodedText PropName_keyType = JsonEncodedText.Encode("keyType");

	private static readonly JsonEncodedText PropName_messageBuffer = JsonEncodedText.Encode("messageBuffer");

	private static readonly JsonEncodedText PropName_error = JsonEncodedText.Encode("error");

	private static readonly JsonEncodedText PropName_realm = JsonEncodedText.Encode("realm");

	private static readonly JsonEncodedText PropName_sn = JsonEncodedText.Encode("sn");

	private static readonly JsonEncodedText PropName_cn = JsonEncodedText.Encode("cn");

	private static readonly JsonEncodedText PropName_tenant_discovery_endpoint = JsonEncodedText.Encode("tenant_discovery_endpoint");

	private static readonly JsonEncodedText PropName_metadata = JsonEncodedText.Encode("metadata");

	private static readonly JsonEncodedText PropName_suberror = JsonEncodedText.Encode("suberror");

	private static readonly JsonEncodedText PropName_error_description = JsonEncodedText.Encode("error_description");

	private static readonly JsonEncodedText PropName_error_codes = JsonEncodedText.Encode("error_codes");

	private static readonly JsonEncodedText PropName_correlation_id = JsonEncodedText.Encode("correlation_id");

	private static readonly JsonEncodedText PropName_claims = JsonEncodedText.Encode("claims");

	private static readonly JsonEncodedText PropName_preferred_network = JsonEncodedText.Encode("preferred_network");

	private static readonly JsonEncodedText PropName_preferred_cache = JsonEncodedText.Encode("preferred_cache");

	private static readonly JsonEncodedText PropName_aliases = JsonEncodedText.Encode("aliases");

	private static readonly JsonEncodedText EncodedPropName_6E65776573742D76657273696F6E73 = JsonEncodedText.Encode("newest-versions");

	private static readonly JsonEncodedText PropName_Result = JsonEncodedText.Encode("Result");

	private static readonly JsonEncodedText PropName_RawClientInfo = JsonEncodedText.Encode("RawClientInfo");

	private static readonly JsonEncodedText PropName_RefreshToken = JsonEncodedText.Encode("RefreshToken");

	private static readonly JsonEncodedText PropName_UserAssertionHash = JsonEncodedText.Encode("UserAssertionHash");

	private static readonly JsonEncodedText PropName_UserInfo = JsonEncodedText.Encode("UserInfo");

	private static readonly JsonEncodedText PropName_UniqueId = JsonEncodedText.Encode("UniqueId");

	private static readonly JsonEncodedText PropName_DisplayableId = JsonEncodedText.Encode("DisplayableId");

	private static readonly JsonEncodedText PropName_GivenName = JsonEncodedText.Encode("GivenName");

	private static readonly JsonEncodedText PropName_FamilyName = JsonEncodedText.Encode("FamilyName");

	private static readonly JsonEncodedText PropName_PasswordExpiresOn = JsonEncodedText.Encode("PasswordExpiresOn");

	private static readonly JsonEncodedText PropName_PasswordChangeUrl = JsonEncodedText.Encode("PasswordChangeUrl");

	private static readonly JsonEncodedText PropName_IdentityProvider = JsonEncodedText.Encode("IdentityProvider");

	private static readonly JsonEncodedText PropName_Key = JsonEncodedText.Encode("Key");

	private static readonly JsonEncodedText PropName_Value = JsonEncodedText.Encode("Value");

	private static readonly JsonEncodedText PropName_uid = JsonEncodedText.Encode("uid");

	private static readonly JsonEncodedText PropName_utid = JsonEncodedText.Encode("utid");

	private static readonly JsonEncodedText PropName_Headers = JsonEncodedText.Encode("Headers");

	private static readonly JsonEncodedText PropName_HeadersAsDictionary = JsonEncodedText.Encode("HeadersAsDictionary");

	private static readonly JsonEncodedText PropName_StatusCode = JsonEncodedText.Encode("StatusCode");

	private static readonly JsonEncodedText PropName_UserAgent = JsonEncodedText.Encode("UserAgent");

	private static readonly JsonEncodedText PropName_Body = JsonEncodedText.Encode("Body");

	private static readonly JsonEncodedText PropName_ver = JsonEncodedText.Encode("ver");

	private static readonly JsonEncodedText PropName_account_type = JsonEncodedText.Encode("account_type");

	private static readonly JsonEncodedText PropName_federation_protocol = JsonEncodedText.Encode("federation_protocol");

	private static readonly JsonEncodedText PropName_federation_metadata_url = JsonEncodedText.Encode("federation_metadata_url");

	private static readonly JsonEncodedText PropName_federation_active_auth_url = JsonEncodedText.Encode("federation_active_auth_url");

	private static readonly JsonEncodedText PropName_cloud_audience_urn = JsonEncodedText.Encode("cloud_audience_urn");

	private static readonly JsonEncodedText PropName_domain_name = JsonEncodedText.Encode("domain_name");

	private static readonly JsonEncodedText PropName_IsFederated = JsonEncodedText.Encode("IsFederated");

	private static readonly JsonEncodedText PropName_IsManaged = JsonEncodedText.Encode("IsManaged");

	private static readonly JsonEncodedText PropName_user_code = JsonEncodedText.Encode("user_code");

	private static readonly JsonEncodedText PropName_device_code = JsonEncodedText.Encode("device_code");

	private static readonly JsonEncodedText PropName_verification_url = JsonEncodedText.Encode("verification_url");

	private static readonly JsonEncodedText PropName_verification_uri = JsonEncodedText.Encode("verification_uri");

	private static readonly JsonEncodedText PropName_expires_in = JsonEncodedText.Encode("expires_in");

	private static readonly JsonEncodedText PropName_interval = JsonEncodedText.Encode("interval");

	private static readonly JsonEncodedText PropName_message = JsonEncodedText.Encode("message");

	private static readonly JsonEncodedText PropName_subject = JsonEncodedText.Encode("subject");

	private static readonly JsonEncodedText PropName_links = JsonEncodedText.Encode("links");

	private static readonly JsonEncodedText PropName_rel = JsonEncodedText.Encode("rel");

	private static readonly JsonEncodedText PropName_href = JsonEncodedText.Encode("href");

	private static readonly JsonEncodedText PropName_x5c = JsonEncodedText.Encode("x5c");

	private static readonly JsonEncodedText PropName_typ = JsonEncodedText.Encode("typ");

	private static readonly JsonEncodedText PropName_alg = JsonEncodedText.Encode("alg");

	private static readonly JsonEncodedText PropName_iat = JsonEncodedText.Encode("iat");

	private static readonly JsonEncodedText PropName_aud = JsonEncodedText.Encode("aud");

	private static readonly JsonEncodedText PropName_nonce = JsonEncodedText.Encode("nonce");

	private static readonly JsonEncodedText PropName_IsValueCreated = JsonEncodedText.Encode("IsValueCreated");

	private static readonly JsonEncodedText PropName_access_token = JsonEncodedText.Encode("access_token");

	private static readonly JsonEncodedText PropName_expires_on = JsonEncodedText.Encode("expires_on");

	private static readonly JsonEncodedText PropName_resource = JsonEncodedText.Encode("resource");

	private static readonly JsonEncodedText PropName_token_type = JsonEncodedText.Encode("token_type");

	private static readonly JsonEncodedText PropName_client_id = JsonEncodedText.Encode("client_id");

	private static readonly JsonEncodedText PropName_correlationId = JsonEncodedText.Encode("correlationId");

	private static readonly JsonEncodedText PropName_token_endpoint = JsonEncodedText.Encode("token_endpoint");

	private static readonly JsonEncodedText PropName_authorization_endpoint = JsonEncodedText.Encode("authorization_endpoint");

	public static MsalJsonSerializerContext Custom => s_customContext ?? (s_customContext = new MsalJsonSerializerContext(new JsonSerializerOptions
	{
		NumberHandling = JsonNumberHandling.AllowReadingFromString,
		AllowTrailingCommas = true,
		Converters = { (JsonConverter)new JsonStringConverter() }
	}));

	public JsonTypeInfo<string> String
	{
		get
		{
			if (_String == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(string))) != null)
				{
					_String = JsonMetadataServices.CreateValueInfo<string>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					_String = JsonMetadataServices.CreateValueInfo<string>(base.Options, JsonMetadataServices.StringConverter);
				}
			}
			return _String;
		}
	}

	public JsonTypeInfo<KerberosKeyTypes> KerberosKeyTypes
	{
		get
		{
			if (_KerberosKeyTypes == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(KerberosKeyTypes))) != null)
				{
					_KerberosKeyTypes = JsonMetadataServices.CreateValueInfo<KerberosKeyTypes>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					_KerberosKeyTypes = JsonMetadataServices.CreateValueInfo<KerberosKeyTypes>(base.Options, JsonMetadataServices.GetEnumConverter<KerberosKeyTypes>(base.Options));
				}
			}
			return _KerberosKeyTypes;
		}
	}

	public JsonTypeInfo<KerberosSupplementalTicket> KerberosSupplementalTicket
	{
		get
		{
			if (_KerberosSupplementalTicket == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(KerberosSupplementalTicket))) != null)
				{
					_KerberosSupplementalTicket = JsonMetadataServices.CreateValueInfo<KerberosSupplementalTicket>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<KerberosSupplementalTicket> objectInfo = new JsonObjectInfoValues<KerberosSupplementalTicket>
					{
						ObjectCreator = () => new KerberosSupplementalTicket(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = KerberosSupplementalTicketPropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = KerberosSupplementalTicketSerializeHandler
					};
					_KerberosSupplementalTicket = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _KerberosSupplementalTicket;
		}
	}

	public JsonTypeInfo<string[]> StringArray
	{
		get
		{
			if (_StringArray == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(string[]))) != null)
				{
					_StringArray = JsonMetadataServices.CreateValueInfo<string[]>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonCollectionInfoValues<string[]> collectionInfo = new JsonCollectionInfoValues<string[]>
					{
						ObjectCreator = null,
						KeyInfo = null,
						ElementInfo = String,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = StringArraySerializeHandler
					};
					_StringArray = JsonMetadataServices.CreateArrayInfo(base.Options, collectionInfo);
				}
			}
			return _StringArray;
		}
	}

	public JsonTypeInfo<InstanceDiscoveryMetadataEntry> InstanceDiscoveryMetadataEntry
	{
		get
		{
			if (_InstanceDiscoveryMetadataEntry == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(InstanceDiscoveryMetadataEntry))) != null)
				{
					_InstanceDiscoveryMetadataEntry = JsonMetadataServices.CreateValueInfo<InstanceDiscoveryMetadataEntry>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<InstanceDiscoveryMetadataEntry> objectInfo = new JsonObjectInfoValues<InstanceDiscoveryMetadataEntry>
					{
						ObjectCreator = () => new InstanceDiscoveryMetadataEntry(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = InstanceDiscoveryMetadataEntryPropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = InstanceDiscoveryMetadataEntrySerializeHandler
					};
					_InstanceDiscoveryMetadataEntry = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _InstanceDiscoveryMetadataEntry;
		}
	}

	public JsonTypeInfo<InstanceDiscoveryMetadataEntry[]> InstanceDiscoveryMetadataEntryArray
	{
		get
		{
			if (_InstanceDiscoveryMetadataEntryArray == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(InstanceDiscoveryMetadataEntry[]))) != null)
				{
					_InstanceDiscoveryMetadataEntryArray = JsonMetadataServices.CreateValueInfo<InstanceDiscoveryMetadataEntry[]>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonCollectionInfoValues<InstanceDiscoveryMetadataEntry[]> collectionInfo = new JsonCollectionInfoValues<InstanceDiscoveryMetadataEntry[]>
					{
						ObjectCreator = null,
						KeyInfo = null,
						ElementInfo = InstanceDiscoveryMetadataEntry,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = InstanceDiscoveryMetadataEntryArraySerializeHandler
					};
					_InstanceDiscoveryMetadataEntryArray = JsonMetadataServices.CreateArrayInfo(base.Options, collectionInfo);
				}
			}
			return _InstanceDiscoveryMetadataEntryArray;
		}
	}

	public JsonTypeInfo<InstanceDiscoveryResponse> InstanceDiscoveryResponse
	{
		get
		{
			if (_InstanceDiscoveryResponse == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(InstanceDiscoveryResponse))) != null)
				{
					_InstanceDiscoveryResponse = JsonMetadataServices.CreateValueInfo<InstanceDiscoveryResponse>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<InstanceDiscoveryResponse> objectInfo = new JsonObjectInfoValues<InstanceDiscoveryResponse>
					{
						ObjectCreator = () => new InstanceDiscoveryResponse(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = InstanceDiscoveryResponsePropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = InstanceDiscoveryResponseSerializeHandler
					};
					_InstanceDiscoveryResponse = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _InstanceDiscoveryResponse;
		}
	}

	public JsonTypeInfo<List<string>> ListString
	{
		get
		{
			if (_ListString == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(List<string>))) != null)
				{
					_ListString = JsonMetadataServices.CreateValueInfo<List<string>>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonCollectionInfoValues<List<string>> collectionInfo = new JsonCollectionInfoValues<List<string>>
					{
						ObjectCreator = () => new List<string>(),
						KeyInfo = null,
						ElementInfo = String,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = ListStringSerializeHandler
					};
					_ListString = JsonMetadataServices.CreateListInfo<List<string>, string>(base.Options, collectionInfo);
				}
			}
			return _ListString;
		}
	}

	public JsonTypeInfo<LocalImdsErrorResponse> LocalImdsErrorResponse
	{
		get
		{
			if (_LocalImdsErrorResponse == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(LocalImdsErrorResponse))) != null)
				{
					_LocalImdsErrorResponse = JsonMetadataServices.CreateValueInfo<LocalImdsErrorResponse>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<LocalImdsErrorResponse> objectInfo = new JsonObjectInfoValues<LocalImdsErrorResponse>
					{
						ObjectCreator = () => new LocalImdsErrorResponse(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = LocalImdsErrorResponsePropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = LocalImdsErrorResponseSerializeHandler
					};
					_LocalImdsErrorResponse = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _LocalImdsErrorResponse;
		}
	}

	public JsonTypeInfo<DateTimeOffset> DateTimeOffset
	{
		get
		{
			if (_DateTimeOffset == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(DateTimeOffset))) != null)
				{
					_DateTimeOffset = JsonMetadataServices.CreateValueInfo<DateTimeOffset>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					_DateTimeOffset = JsonMetadataServices.CreateValueInfo<DateTimeOffset>(base.Options, JsonMetadataServices.DateTimeOffsetConverter);
				}
			}
			return _DateTimeOffset;
		}
	}

	public JsonTypeInfo<DateTimeOffset?> NullableDateTimeOffset
	{
		get
		{
			if (_NullableDateTimeOffset == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(DateTimeOffset?))) != null)
				{
					_NullableDateTimeOffset = JsonMetadataServices.CreateValueInfo<DateTimeOffset?>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					_NullableDateTimeOffset = JsonMetadataServices.CreateValueInfo<DateTimeOffset?>(base.Options, JsonMetadataServices.GetNullableConverter(DateTimeOffset));
				}
			}
			return _NullableDateTimeOffset;
		}
	}

	public JsonTypeInfo<Uri> Uri
	{
		get
		{
			if (_Uri == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(Uri))) != null)
				{
					_Uri = JsonMetadataServices.CreateValueInfo<Uri>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					_Uri = JsonMetadataServices.CreateValueInfo<Uri>(base.Options, JsonMetadataServices.UriConverter);
				}
			}
			return _Uri;
		}
	}

	public JsonTypeInfo<AdalUserInfo> AdalUserInfo
	{
		get
		{
			if (_AdalUserInfo == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(AdalUserInfo))) != null)
				{
					_AdalUserInfo = JsonMetadataServices.CreateValueInfo<AdalUserInfo>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<AdalUserInfo> objectInfo = new JsonObjectInfoValues<AdalUserInfo>
					{
						ObjectCreator = () => new AdalUserInfo(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = AdalUserInfoPropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = AdalUserInfoSerializeHandler
					};
					_AdalUserInfo = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _AdalUserInfo;
		}
	}

	public JsonTypeInfo<AdalResult> AdalResult
	{
		get
		{
			if (_AdalResult == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(AdalResult))) != null)
				{
					_AdalResult = JsonMetadataServices.CreateValueInfo<AdalResult>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<AdalResult> objectInfo = new JsonObjectInfoValues<AdalResult>
					{
						ObjectCreator = () => new AdalResult(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = AdalResultPropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = AdalResultSerializeHandler
					};
					_AdalResult = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _AdalResult;
		}
	}

	public JsonTypeInfo<bool> Boolean
	{
		get
		{
			if (_Boolean == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(bool))) != null)
				{
					_Boolean = JsonMetadataServices.CreateValueInfo<bool>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					_Boolean = JsonMetadataServices.CreateValueInfo<bool>(base.Options, JsonMetadataServices.BooleanConverter);
				}
			}
			return _Boolean;
		}
	}

	public JsonTypeInfo<AdalResultWrapper> AdalResultWrapper
	{
		get
		{
			if (_AdalResultWrapper == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(AdalResultWrapper))) != null)
				{
					_AdalResultWrapper = JsonMetadataServices.CreateValueInfo<AdalResultWrapper>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<AdalResultWrapper> objectInfo = new JsonObjectInfoValues<AdalResultWrapper>
					{
						ObjectCreator = () => new AdalResultWrapper(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = AdalResultWrapperPropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = AdalResultWrapperSerializeHandler
					};
					_AdalResultWrapper = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _AdalResultWrapper;
		}
	}

	public JsonTypeInfo<IEnumerable<string>> IEnumerableString
	{
		get
		{
			if (_IEnumerableString == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(IEnumerable<string>))) != null)
				{
					_IEnumerableString = JsonMetadataServices.CreateValueInfo<IEnumerable<string>>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonCollectionInfoValues<IEnumerable<string>> collectionInfo = new JsonCollectionInfoValues<IEnumerable<string>>
					{
						ObjectCreator = null,
						KeyInfo = null,
						ElementInfo = String,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = IEnumerableStringSerializeHandler
					};
					_IEnumerableString = JsonMetadataServices.CreateIEnumerableInfo<IEnumerable<string>, string>(base.Options, collectionInfo);
				}
			}
			return _IEnumerableString;
		}
	}

	public JsonTypeInfo<int> Int32
	{
		get
		{
			if (_Int32 == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(int))) != null)
				{
					_Int32 = JsonMetadataServices.CreateValueInfo<int>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					_Int32 = JsonMetadataServices.CreateValueInfo<int>(base.Options, JsonMetadataServices.Int32Converter);
				}
			}
			return _Int32;
		}
	}

	public JsonTypeInfo<KeyValuePair<string, IEnumerable<string>>> KeyValuePairStringIEnumerableString
	{
		get
		{
			if (_KeyValuePairStringIEnumerableString == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(KeyValuePair<string, IEnumerable<string>>))) != null)
				{
					_KeyValuePairStringIEnumerableString = JsonMetadataServices.CreateValueInfo<KeyValuePair<string, IEnumerable<string>>>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<KeyValuePair<string, IEnumerable<string>>> objectInfo = new JsonObjectInfoValues<KeyValuePair<string, IEnumerable<string>>>
					{
						ObjectCreator = null,
						ObjectWithParameterizedConstructorCreator = (object[] args) => new KeyValuePair<string, IEnumerable<string>>((string)args[0], (IEnumerable<string>)args[1]),
						PropertyMetadataInitializer = KeyValuePairStringIEnumerableStringPropInit,
						ConstructorParameterMetadataInitializer = KeyValuePairStringIEnumerableStringCtorParamInit,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = KeyValuePairStringIEnumerableStringSerializeHandler
					};
					_KeyValuePairStringIEnumerableString = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _KeyValuePairStringIEnumerableString;
		}
	}

	public JsonTypeInfo<List<KeyValuePair<string, IEnumerable<string>>>> ListKeyValuePairStringIEnumerableString
	{
		get
		{
			if (_ListKeyValuePairStringIEnumerableString == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(List<KeyValuePair<string, IEnumerable<string>>>))) != null)
				{
					_ListKeyValuePairStringIEnumerableString = JsonMetadataServices.CreateValueInfo<List<KeyValuePair<string, IEnumerable<string>>>>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonCollectionInfoValues<List<KeyValuePair<string, IEnumerable<string>>>> collectionInfo = new JsonCollectionInfoValues<List<KeyValuePair<string, IEnumerable<string>>>>
					{
						ObjectCreator = () => new List<KeyValuePair<string, IEnumerable<string>>>(),
						KeyInfo = null,
						ElementInfo = KeyValuePairStringIEnumerableString,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = ListKeyValuePairStringIEnumerableStringSerializeHandler
					};
					_ListKeyValuePairStringIEnumerableString = JsonMetadataServices.CreateListInfo<List<KeyValuePair<string, IEnumerable<string>>>, KeyValuePair<string, IEnumerable<string>>>(base.Options, collectionInfo);
				}
			}
			return _ListKeyValuePairStringIEnumerableString;
		}
	}

	public JsonTypeInfo<ClientInfo> ClientInfo
	{
		get
		{
			if (_ClientInfo == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(ClientInfo))) != null)
				{
					_ClientInfo = JsonMetadataServices.CreateValueInfo<ClientInfo>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<ClientInfo> objectInfo = new JsonObjectInfoValues<ClientInfo>
					{
						ObjectCreator = () => new ClientInfo(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = ClientInfoPropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = ClientInfoSerializeHandler
					};
					_ClientInfo = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _ClientInfo;
		}
	}

	public JsonTypeInfo<OAuth2ResponseBase> OAuth2ResponseBase
	{
		get
		{
			if (_OAuth2ResponseBase == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(OAuth2ResponseBase))) != null)
				{
					_OAuth2ResponseBase = JsonMetadataServices.CreateValueInfo<OAuth2ResponseBase>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<OAuth2ResponseBase> objectInfo = new JsonObjectInfoValues<OAuth2ResponseBase>
					{
						ObjectCreator = () => new OAuth2ResponseBase(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = OAuth2ResponseBasePropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = OAuth2ResponseBaseSerializeHandler
					};
					_OAuth2ResponseBase = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _OAuth2ResponseBase;
		}
	}

	public JsonTypeInfo<JsonElement> JsonElement
	{
		get
		{
			if (_JsonElement == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(JsonElement))) != null)
				{
					_JsonElement = JsonMetadataServices.CreateValueInfo<JsonElement>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					_JsonElement = JsonMetadataServices.CreateValueInfo<JsonElement>(base.Options, JsonMetadataServices.JsonElementConverter);
				}
			}
			return _JsonElement;
		}
	}

	public JsonTypeInfo<Dictionary<string, JsonElement>> DictionaryStringJsonElement
	{
		get
		{
			if (_DictionaryStringJsonElement == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(Dictionary<string, JsonElement>))) != null)
				{
					_DictionaryStringJsonElement = JsonMetadataServices.CreateValueInfo<Dictionary<string, JsonElement>>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonCollectionInfoValues<Dictionary<string, JsonElement>> collectionInfo = new JsonCollectionInfoValues<Dictionary<string, JsonElement>>
					{
						ObjectCreator = () => new Dictionary<string, JsonElement>(),
						KeyInfo = String,
						ElementInfo = JsonElement,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = DictionaryStringJsonElementSerializeHandler
					};
					_DictionaryStringJsonElement = JsonMetadataServices.CreateDictionaryInfo<Dictionary<string, JsonElement>, string, JsonElement>(base.Options, collectionInfo);
				}
			}
			return _DictionaryStringJsonElement;
		}
	}

	public JsonTypeInfo<long> Int64
	{
		get
		{
			if (_Int64 == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(long))) != null)
				{
					_Int64 = JsonMetadataServices.CreateValueInfo<long>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					_Int64 = JsonMetadataServices.CreateValueInfo<long>(base.Options, JsonMetadataServices.Int64Converter);
				}
			}
			return _Int64;
		}
	}

	public JsonTypeInfo<long?> NullableInt64
	{
		get
		{
			if (_NullableInt64 == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(long?))) != null)
				{
					_NullableInt64 = JsonMetadataServices.CreateValueInfo<long?>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					_NullableInt64 = JsonMetadataServices.CreateValueInfo<long?>(base.Options, JsonMetadataServices.GetNullableConverter(Int64));
				}
			}
			return _NullableInt64;
		}
	}

	public JsonTypeInfo<TokenSource> TokenSource
	{
		get
		{
			if (_TokenSource == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(TokenSource))) != null)
				{
					_TokenSource = JsonMetadataServices.CreateValueInfo<TokenSource>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					_TokenSource = JsonMetadataServices.CreateValueInfo<TokenSource>(base.Options, JsonMetadataServices.GetEnumConverter<TokenSource>(base.Options));
				}
			}
			return _TokenSource;
		}
	}

	public JsonTypeInfo<HttpResponseHeaders> HttpResponseHeaders
	{
		get
		{
			if (_HttpResponseHeaders == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(HttpResponseHeaders))) != null)
				{
					_HttpResponseHeaders = JsonMetadataServices.CreateValueInfo<HttpResponseHeaders>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonCollectionInfoValues<HttpResponseHeaders> collectionInfo = new JsonCollectionInfoValues<HttpResponseHeaders>
					{
						ObjectCreator = null,
						KeyInfo = null,
						ElementInfo = KeyValuePairStringIEnumerableString,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = HttpResponseHeadersSerializeHandler
					};
					_HttpResponseHeaders = JsonMetadataServices.CreateIEnumerableInfo<HttpResponseHeaders, KeyValuePair<string, IEnumerable<string>>>(base.Options, collectionInfo);
				}
			}
			return _HttpResponseHeaders;
		}
	}

	public JsonTypeInfo<IDictionary<string, string>> IDictionaryStringString
	{
		get
		{
			if (_IDictionaryStringString == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(IDictionary<string, string>))) != null)
				{
					_IDictionaryStringString = JsonMetadataServices.CreateValueInfo<IDictionary<string, string>>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonCollectionInfoValues<IDictionary<string, string>> collectionInfo = new JsonCollectionInfoValues<IDictionary<string, string>>
					{
						ObjectCreator = () => new Dictionary<string, string>(),
						KeyInfo = String,
						ElementInfo = String,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = IDictionaryStringStringSerializeHandler
					};
					_IDictionaryStringString = JsonMetadataServices.CreateIDictionaryInfo<IDictionary<string, string>, string, string>(base.Options, collectionInfo);
				}
			}
			return _IDictionaryStringString;
		}
	}

	public JsonTypeInfo<HttpStatusCode> HttpStatusCode
	{
		get
		{
			if (_HttpStatusCode == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(HttpStatusCode))) != null)
				{
					_HttpStatusCode = JsonMetadataServices.CreateValueInfo<HttpStatusCode>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					_HttpStatusCode = JsonMetadataServices.CreateValueInfo<HttpStatusCode>(base.Options, JsonMetadataServices.GetEnumConverter<HttpStatusCode>(base.Options));
				}
			}
			return _HttpStatusCode;
		}
	}

	public JsonTypeInfo<HttpResponse> HttpResponse
	{
		get
		{
			if (_HttpResponse == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(HttpResponse))) != null)
				{
					_HttpResponse = JsonMetadataServices.CreateValueInfo<HttpResponse>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<HttpResponse> objectInfo = new JsonObjectInfoValues<HttpResponse>
					{
						ObjectCreator = () => new HttpResponse(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = HttpResponsePropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = HttpResponseSerializeHandler
					};
					_HttpResponse = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _HttpResponse;
		}
	}

	public JsonTypeInfo<MsalTokenResponse> MsalTokenResponse
	{
		get
		{
			if (_MsalTokenResponse == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(MsalTokenResponse))) != null)
				{
					_MsalTokenResponse = JsonMetadataServices.CreateValueInfo<MsalTokenResponse>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<MsalTokenResponse> objectInfo = new JsonObjectInfoValues<MsalTokenResponse>
					{
						ObjectCreator = () => new MsalTokenResponse(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = MsalTokenResponsePropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = null
					};
					_MsalTokenResponse = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _MsalTokenResponse;
		}
	}

	public JsonTypeInfo<UserRealmDiscoveryResponse> UserRealmDiscoveryResponse
	{
		get
		{
			if (_UserRealmDiscoveryResponse == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(UserRealmDiscoveryResponse))) != null)
				{
					_UserRealmDiscoveryResponse = JsonMetadataServices.CreateValueInfo<UserRealmDiscoveryResponse>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<UserRealmDiscoveryResponse> objectInfo = new JsonObjectInfoValues<UserRealmDiscoveryResponse>
					{
						ObjectCreator = () => new UserRealmDiscoveryResponse(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = UserRealmDiscoveryResponsePropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = UserRealmDiscoveryResponseSerializeHandler
					};
					_UserRealmDiscoveryResponse = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _UserRealmDiscoveryResponse;
		}
	}

	public JsonTypeInfo<DeviceCodeResponse> DeviceCodeResponse
	{
		get
		{
			if (_DeviceCodeResponse == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(DeviceCodeResponse))) != null)
				{
					_DeviceCodeResponse = JsonMetadataServices.CreateValueInfo<DeviceCodeResponse>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<DeviceCodeResponse> objectInfo = new JsonObjectInfoValues<DeviceCodeResponse>
					{
						ObjectCreator = () => new DeviceCodeResponse(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = DeviceCodeResponsePropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = DeviceCodeResponseSerializeHandler
					};
					_DeviceCodeResponse = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _DeviceCodeResponse;
		}
	}

	public JsonTypeInfo<LinksList> LinksList
	{
		get
		{
			if (_LinksList == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(LinksList))) != null)
				{
					_LinksList = JsonMetadataServices.CreateValueInfo<LinksList>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<LinksList> objectInfo = new JsonObjectInfoValues<LinksList>
					{
						ObjectCreator = () => new LinksList(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = LinksListPropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = LinksListSerializeHandler
					};
					_LinksList = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _LinksList;
		}
	}

	public JsonTypeInfo<List<LinksList>> ListLinksList
	{
		get
		{
			if (_ListLinksList == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(List<LinksList>))) != null)
				{
					_ListLinksList = JsonMetadataServices.CreateValueInfo<List<LinksList>>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonCollectionInfoValues<List<LinksList>> collectionInfo = new JsonCollectionInfoValues<List<LinksList>>
					{
						ObjectCreator = () => new List<LinksList>(),
						KeyInfo = null,
						ElementInfo = LinksList,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = ListLinksListSerializeHandler
					};
					_ListLinksList = JsonMetadataServices.CreateListInfo<List<LinksList>, LinksList>(base.Options, collectionInfo);
				}
			}
			return _ListLinksList;
		}
	}

	public JsonTypeInfo<AdfsWebFingerResponse> AdfsWebFingerResponse
	{
		get
		{
			if (_AdfsWebFingerResponse == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(AdfsWebFingerResponse))) != null)
				{
					_AdfsWebFingerResponse = JsonMetadataServices.CreateValueInfo<AdfsWebFingerResponse>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<AdfsWebFingerResponse> objectInfo = new JsonObjectInfoValues<AdfsWebFingerResponse>
					{
						ObjectCreator = () => new AdfsWebFingerResponse(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = AdfsWebFingerResponsePropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = AdfsWebFingerResponseSerializeHandler
					};
					_AdfsWebFingerResponse = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _AdfsWebFingerResponse;
		}
	}

	public JsonTypeInfo<IList<string>> IListString
	{
		get
		{
			if (_IListString == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(IList<string>))) != null)
				{
					_IListString = JsonMetadataServices.CreateValueInfo<IList<string>>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonCollectionInfoValues<IList<string>> collectionInfo = new JsonCollectionInfoValues<IList<string>>
					{
						ObjectCreator = null,
						KeyInfo = null,
						ElementInfo = String,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = IListStringSerializeHandler
					};
					_IListString = JsonMetadataServices.CreateIListInfo<IList<string>, string>(base.Options, collectionInfo);
				}
			}
			return _IListString;
		}
	}

	public JsonTypeInfo<DeviceAuthHeader> DeviceAuthHeader
	{
		get
		{
			if (_DeviceAuthHeader == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(DeviceAuthHeader))) != null)
				{
					_DeviceAuthHeader = JsonMetadataServices.CreateValueInfo<DeviceAuthHeader>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<DeviceAuthHeader> objectInfo = new JsonObjectInfoValues<DeviceAuthHeader>
					{
						ObjectCreator = null,
						ObjectWithParameterizedConstructorCreator = (object[] args) => new DeviceAuthHeader((string)args[0]),
						PropertyMetadataInitializer = DeviceAuthHeaderPropInit,
						ConstructorParameterMetadataInitializer = DeviceAuthHeaderCtorParamInit,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = DeviceAuthHeaderSerializeHandler
					};
					_DeviceAuthHeader = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _DeviceAuthHeader;
		}
	}

	public JsonTypeInfo<Lazy<long>> LazyInt64
	{
		get
		{
			if (_LazyInt64 == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(Lazy<long>))) != null)
				{
					_LazyInt64 = JsonMetadataServices.CreateValueInfo<Lazy<long>>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<Lazy<long>> objectInfo = new JsonObjectInfoValues<Lazy<long>>
					{
						ObjectCreator = () => new Lazy<long>(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = LazyInt64PropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = LazyInt64SerializeHandler
					};
					_LazyInt64 = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _LazyInt64;
		}
	}

	public JsonTypeInfo<DeviceAuthPayload> DeviceAuthPayload
	{
		get
		{
			if (_DeviceAuthPayload == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(DeviceAuthPayload))) != null)
				{
					_DeviceAuthPayload = JsonMetadataServices.CreateValueInfo<DeviceAuthPayload>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<DeviceAuthPayload> objectInfo = new JsonObjectInfoValues<DeviceAuthPayload>
					{
						ObjectCreator = null,
						ObjectWithParameterizedConstructorCreator = (object[] args) => new DeviceAuthPayload((string)args[0], (string)args[1]),
						PropertyMetadataInitializer = DeviceAuthPayloadPropInit,
						ConstructorParameterMetadataInitializer = DeviceAuthPayloadCtorParamInit,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = DeviceAuthPayloadSerializeHandler
					};
					_DeviceAuthPayload = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _DeviceAuthPayload;
		}
	}

	public JsonTypeInfo<ManagedIdentityResponse> ManagedIdentityResponse
	{
		get
		{
			if (_ManagedIdentityResponse == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(ManagedIdentityResponse))) != null)
				{
					_ManagedIdentityResponse = JsonMetadataServices.CreateValueInfo<ManagedIdentityResponse>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<ManagedIdentityResponse> objectInfo = new JsonObjectInfoValues<ManagedIdentityResponse>
					{
						ObjectCreator = () => new ManagedIdentityResponse(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = ManagedIdentityResponsePropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = ManagedIdentityResponseSerializeHandler
					};
					_ManagedIdentityResponse = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _ManagedIdentityResponse;
		}
	}

	public JsonTypeInfo<ManagedIdentityErrorResponse> ManagedIdentityErrorResponse
	{
		get
		{
			if (_ManagedIdentityErrorResponse == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(ManagedIdentityErrorResponse))) != null)
				{
					_ManagedIdentityErrorResponse = JsonMetadataServices.CreateValueInfo<ManagedIdentityErrorResponse>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<ManagedIdentityErrorResponse> objectInfo = new JsonObjectInfoValues<ManagedIdentityErrorResponse>
					{
						ObjectCreator = () => new ManagedIdentityErrorResponse(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = ManagedIdentityErrorResponsePropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = ManagedIdentityErrorResponseSerializeHandler
					};
					_ManagedIdentityErrorResponse = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _ManagedIdentityErrorResponse;
		}
	}

	public JsonTypeInfo<OidcMetadata> OidcMetadata
	{
		get
		{
			if (_OidcMetadata == null)
			{
				JsonConverter runtimeProvidedCustomConverter;
				if (base.Options.Converters.Count > 0 && (runtimeProvidedCustomConverter = GetRuntimeProvidedCustomConverter(typeof(OidcMetadata))) != null)
				{
					_OidcMetadata = JsonMetadataServices.CreateValueInfo<OidcMetadata>(base.Options, runtimeProvidedCustomConverter);
				}
				else
				{
					JsonObjectInfoValues<OidcMetadata> objectInfo = new JsonObjectInfoValues<OidcMetadata>
					{
						ObjectCreator = () => new OidcMetadata(),
						ObjectWithParameterizedConstructorCreator = null,
						PropertyMetadataInitializer = OidcMetadataPropInit,
						ConstructorParameterMetadataInitializer = null,
						NumberHandling = JsonNumberHandling.Strict,
						SerializeHandler = OidcMetadataSerializeHandler
					};
					_OidcMetadata = JsonMetadataServices.CreateObjectInfo(base.Options, objectInfo);
				}
			}
			return _OidcMetadata;
		}
	}

	private static JsonSerializerOptions s_defaultOptions { get; } = new JsonSerializerOptions
	{
		DefaultIgnoreCondition = JsonIgnoreCondition.Never,
		IgnoreReadOnlyFields = false,
		IgnoreReadOnlyProperties = false,
		IncludeFields = false,
		WriteIndented = false
	};

	public static MsalJsonSerializerContext Default => s_defaultContext ?? (s_defaultContext = new MsalJsonSerializerContext(new JsonSerializerOptions(s_defaultOptions)));

	protected override JsonSerializerOptions? GeneratedSerializerOptions { get; } = s_defaultOptions;

	private static JsonPropertyInfo[] KerberosSupplementalTicketPropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[7];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(KerberosSupplementalTicket),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((KerberosSupplementalTicket)obj).ClientKey,
			Setter = delegate(object obj, string? value)
			{
				((KerberosSupplementalTicket)obj).ClientKey = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ClientKey",
			JsonPropertyName = "clientKey"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<KerberosKeyTypes> propertyInfo2 = new JsonPropertyInfoValues<KerberosKeyTypes>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(KerberosSupplementalTicket),
			PropertyTypeInfo = msalJsonSerializerContext.KerberosKeyTypes,
			Converter = null,
			Getter = (object obj) => ((KerberosSupplementalTicket)obj).KeyType,
			Setter = delegate(object obj, KerberosKeyTypes value)
			{
				((KerberosSupplementalTicket)obj).KeyType = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "KeyType",
			JsonPropertyName = "keyType"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string> propertyInfo3 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(KerberosSupplementalTicket),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((KerberosSupplementalTicket)obj).KerberosMessageBuffer,
			Setter = delegate(object obj, string? value)
			{
				((KerberosSupplementalTicket)obj).KerberosMessageBuffer = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "KerberosMessageBuffer",
			JsonPropertyName = "messageBuffer"
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		JsonPropertyInfoValues<string> propertyInfo4 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(KerberosSupplementalTicket),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((KerberosSupplementalTicket)obj).ErrorMessage,
			Setter = delegate(object obj, string? value)
			{
				((KerberosSupplementalTicket)obj).ErrorMessage = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ErrorMessage",
			JsonPropertyName = "error"
		};
		array[3] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo4);
		JsonPropertyInfoValues<string> propertyInfo5 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(KerberosSupplementalTicket),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((KerberosSupplementalTicket)obj).Realm,
			Setter = delegate(object obj, string? value)
			{
				((KerberosSupplementalTicket)obj).Realm = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Realm",
			JsonPropertyName = "realm"
		};
		array[4] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo5);
		JsonPropertyInfoValues<string> propertyInfo6 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(KerberosSupplementalTicket),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((KerberosSupplementalTicket)obj).ServicePrincipalName,
			Setter = delegate(object obj, string? value)
			{
				((KerberosSupplementalTicket)obj).ServicePrincipalName = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ServicePrincipalName",
			JsonPropertyName = "sn"
		};
		array[5] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo6);
		JsonPropertyInfoValues<string> propertyInfo7 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(KerberosSupplementalTicket),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((KerberosSupplementalTicket)obj).ClientName,
			Setter = delegate(object obj, string? value)
			{
				((KerberosSupplementalTicket)obj).ClientName = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ClientName",
			JsonPropertyName = "cn"
		};
		array[6] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo7);
		return array;
	}

	private static void KerberosSupplementalTicketSerializeHandler(Utf8JsonWriter writer, KerberosSupplementalTicket? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteString(PropName_clientKey, value.ClientKey);
		writer.WritePropertyName(PropName_keyType);
		JsonSerializer.Serialize(writer, value.KeyType, Default.KerberosKeyTypes);
		writer.WriteString(PropName_messageBuffer, value.KerberosMessageBuffer);
		writer.WriteString(PropName_error, value.ErrorMessage);
		writer.WriteString(PropName_realm, value.Realm);
		writer.WriteString(PropName_sn, value.ServicePrincipalName);
		writer.WriteString(PropName_cn, value.ClientName);
		writer.WriteEndObject();
	}

	private static void StringArraySerializeHandler(Utf8JsonWriter writer, string[]? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartArray();
		for (int i = 0; i < value.Length; i++)
		{
			writer.WriteStringValue(value[i]);
		}
		writer.WriteEndArray();
	}

	private static JsonPropertyInfo[] InstanceDiscoveryMetadataEntryPropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[3];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(InstanceDiscoveryMetadataEntry),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((InstanceDiscoveryMetadataEntry)obj).PreferredNetwork,
			Setter = delegate(object obj, string? value)
			{
				((InstanceDiscoveryMetadataEntry)obj).PreferredNetwork = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "PreferredNetwork",
			JsonPropertyName = "preferred_network"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<string> propertyInfo2 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(InstanceDiscoveryMetadataEntry),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((InstanceDiscoveryMetadataEntry)obj).PreferredCache,
			Setter = delegate(object obj, string? value)
			{
				((InstanceDiscoveryMetadataEntry)obj).PreferredCache = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "PreferredCache",
			JsonPropertyName = "preferred_cache"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string[]> propertyInfo3 = new JsonPropertyInfoValues<string[]>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(InstanceDiscoveryMetadataEntry),
			PropertyTypeInfo = msalJsonSerializerContext.StringArray,
			Converter = null,
			Getter = (object obj) => ((InstanceDiscoveryMetadataEntry)obj).Aliases,
			Setter = delegate(object obj, string[]? value)
			{
				((InstanceDiscoveryMetadataEntry)obj).Aliases = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Aliases",
			JsonPropertyName = "aliases"
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		return array;
	}

	private static void InstanceDiscoveryMetadataEntrySerializeHandler(Utf8JsonWriter writer, InstanceDiscoveryMetadataEntry? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteString(PropName_preferred_network, value.PreferredNetwork);
		writer.WriteString(PropName_preferred_cache, value.PreferredCache);
		writer.WritePropertyName(PropName_aliases);
		StringArraySerializeHandler(writer, value.Aliases);
		writer.WriteEndObject();
	}

	private static void InstanceDiscoveryMetadataEntryArraySerializeHandler(Utf8JsonWriter writer, InstanceDiscoveryMetadataEntry[]? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartArray();
		for (int i = 0; i < value.Length; i++)
		{
			InstanceDiscoveryMetadataEntrySerializeHandler(writer, value[i]);
		}
		writer.WriteEndArray();
	}

	private static JsonPropertyInfo[] InstanceDiscoveryResponsePropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[8];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(InstanceDiscoveryResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((InstanceDiscoveryResponse)obj).TenantDiscoveryEndpoint,
			Setter = delegate(object obj, string? value)
			{
				((InstanceDiscoveryResponse)obj).TenantDiscoveryEndpoint = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "TenantDiscoveryEndpoint",
			JsonPropertyName = "tenant_discovery_endpoint"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<InstanceDiscoveryMetadataEntry[]> propertyInfo2 = new JsonPropertyInfoValues<InstanceDiscoveryMetadataEntry[]>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(InstanceDiscoveryResponse),
			PropertyTypeInfo = msalJsonSerializerContext.InstanceDiscoveryMetadataEntryArray,
			Converter = null,
			Getter = (object obj) => ((InstanceDiscoveryResponse)obj).Metadata,
			Setter = delegate(object obj, InstanceDiscoveryMetadataEntry[]? value)
			{
				((InstanceDiscoveryResponse)obj).Metadata = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Metadata",
			JsonPropertyName = "metadata"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string> propertyInfo3 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).Error,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).Error = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Error",
			JsonPropertyName = "error"
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		JsonPropertyInfoValues<string> propertyInfo4 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).SubError,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).SubError = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "SubError",
			JsonPropertyName = "suberror"
		};
		array[3] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo4);
		JsonPropertyInfoValues<string> propertyInfo5 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).ErrorDescription,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).ErrorDescription = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ErrorDescription",
			JsonPropertyName = "error_description"
		};
		array[4] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo5);
		JsonPropertyInfoValues<string[]> propertyInfo6 = new JsonPropertyInfoValues<string[]>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.StringArray,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).ErrorCodes,
			Setter = delegate(object obj, string[]? value)
			{
				((OAuth2ResponseBase)obj).ErrorCodes = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ErrorCodes",
			JsonPropertyName = "error_codes"
		};
		array[5] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo6);
		JsonPropertyInfoValues<string> propertyInfo7 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).CorrelationId,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).CorrelationId = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "CorrelationId",
			JsonPropertyName = "correlation_id"
		};
		array[6] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo7);
		JsonPropertyInfoValues<string> propertyInfo8 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).Claims,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).Claims = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Claims",
			JsonPropertyName = "claims"
		};
		array[7] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo8);
		return array;
	}

	private static void InstanceDiscoveryResponseSerializeHandler(Utf8JsonWriter writer, InstanceDiscoveryResponse? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteString(PropName_tenant_discovery_endpoint, value.TenantDiscoveryEndpoint);
		writer.WritePropertyName(PropName_metadata);
		InstanceDiscoveryMetadataEntryArraySerializeHandler(writer, value.Metadata);
		writer.WriteString(PropName_error, value.Error);
		writer.WriteString(PropName_suberror, value.SubError);
		writer.WriteString(PropName_error_description, value.ErrorDescription);
		writer.WritePropertyName(PropName_error_codes);
		StringArraySerializeHandler(writer, value.ErrorCodes);
		writer.WriteString(PropName_correlation_id, value.CorrelationId);
		writer.WriteString(PropName_claims, value.Claims);
		writer.WriteEndObject();
	}

	private static void ListStringSerializeHandler(Utf8JsonWriter writer, List<string>? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartArray();
		for (int i = 0; i < value.Count; i++)
		{
			writer.WriteStringValue(value[i]);
		}
		writer.WriteEndArray();
	}

	private static JsonPropertyInfo[] LocalImdsErrorResponsePropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[2];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(LocalImdsErrorResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((LocalImdsErrorResponse)obj).Error,
			Setter = delegate(object obj, string? value)
			{
				((LocalImdsErrorResponse)obj).Error = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Error",
			JsonPropertyName = "error"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<List<string>> propertyInfo2 = new JsonPropertyInfoValues<List<string>>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(LocalImdsErrorResponse),
			PropertyTypeInfo = msalJsonSerializerContext.ListString,
			Converter = null,
			Getter = (object obj) => ((LocalImdsErrorResponse)obj).NewestVersions,
			Setter = delegate(object obj, List<string>? value)
			{
				((LocalImdsErrorResponse)obj).NewestVersions = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "NewestVersions",
			JsonPropertyName = "newest-versions"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		return array;
	}

	private static void LocalImdsErrorResponseSerializeHandler(Utf8JsonWriter writer, LocalImdsErrorResponse? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteString(PropName_error, value.Error);
		writer.WritePropertyName(EncodedPropName_6E65776573742D76657273696F6E73);
		ListStringSerializeHandler(writer, value.NewestVersions);
		writer.WriteEndObject();
	}

	private static JsonPropertyInfo[] AdalUserInfoPropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[7];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(AdalUserInfo),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((AdalUserInfo)obj).UniqueId,
			Setter = delegate(object obj, string? value)
			{
				((AdalUserInfo)obj).UniqueId = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = true,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "UniqueId",
			JsonPropertyName = null
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<string> propertyInfo2 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(AdalUserInfo),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((AdalUserInfo)obj).DisplayableId,
			Setter = delegate(object obj, string? value)
			{
				((AdalUserInfo)obj).DisplayableId = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = true,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "DisplayableId",
			JsonPropertyName = null
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string> propertyInfo3 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(AdalUserInfo),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((AdalUserInfo)obj).GivenName,
			Setter = delegate(object obj, string? value)
			{
				((AdalUserInfo)obj).GivenName = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = true,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "GivenName",
			JsonPropertyName = null
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		JsonPropertyInfoValues<string> propertyInfo4 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(AdalUserInfo),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((AdalUserInfo)obj).FamilyName,
			Setter = delegate(object obj, string? value)
			{
				((AdalUserInfo)obj).FamilyName = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = true,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "FamilyName",
			JsonPropertyName = null
		};
		array[3] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo4);
		JsonPropertyInfoValues<DateTimeOffset?> propertyInfo5 = new JsonPropertyInfoValues<DateTimeOffset?>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(AdalUserInfo),
			PropertyTypeInfo = msalJsonSerializerContext.NullableDateTimeOffset,
			Converter = null,
			Getter = (object obj) => ((AdalUserInfo)obj).PasswordExpiresOn,
			Setter = delegate(object obj, DateTimeOffset? value)
			{
				((AdalUserInfo)obj).PasswordExpiresOn = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = true,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "PasswordExpiresOn",
			JsonPropertyName = null
		};
		array[4] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo5);
		JsonPropertyInfoValues<Uri> propertyInfo6 = new JsonPropertyInfoValues<Uri>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(AdalUserInfo),
			PropertyTypeInfo = msalJsonSerializerContext.Uri,
			Converter = null,
			Getter = (object obj) => ((AdalUserInfo)obj).PasswordChangeUrl,
			Setter = delegate(object obj, Uri? value)
			{
				((AdalUserInfo)obj).PasswordChangeUrl = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = true,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "PasswordChangeUrl",
			JsonPropertyName = null
		};
		array[5] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo6);
		JsonPropertyInfoValues<string> propertyInfo7 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(AdalUserInfo),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((AdalUserInfo)obj).IdentityProvider,
			Setter = delegate(object obj, string? value)
			{
				((AdalUserInfo)obj).IdentityProvider = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = true,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "IdentityProvider",
			JsonPropertyName = null
		};
		array[6] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo7);
		return array;
	}

	private static void AdalUserInfoSerializeHandler(Utf8JsonWriter writer, AdalUserInfo? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteString(PropName_UniqueId, value.UniqueId);
		writer.WriteString(PropName_DisplayableId, value.DisplayableId);
		writer.WriteString(PropName_GivenName, value.GivenName);
		writer.WriteString(PropName_FamilyName, value.FamilyName);
		writer.WritePropertyName(PropName_PasswordExpiresOn);
		JsonSerializer.Serialize(writer, value.PasswordExpiresOn, Default.NullableDateTimeOffset);
		writer.WritePropertyName(PropName_PasswordChangeUrl);
		JsonSerializer.Serialize(writer, value.PasswordChangeUrl, Default.Uri);
		writer.WriteString(PropName_IdentityProvider, value.IdentityProvider);
		writer.WriteEndObject();
	}

	private static JsonPropertyInfo[] AdalResultPropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[1];
		JsonPropertyInfoValues<AdalUserInfo> propertyInfo = new JsonPropertyInfoValues<AdalUserInfo>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(AdalResult),
			PropertyTypeInfo = msalJsonSerializerContext.AdalUserInfo,
			Converter = null,
			Getter = (object obj) => ((AdalResult)obj).UserInfo,
			Setter = delegate(object obj, AdalUserInfo? value)
			{
				((AdalResult)obj).UserInfo = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = true,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "UserInfo",
			JsonPropertyName = null
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		return array;
	}

	private static void AdalResultSerializeHandler(Utf8JsonWriter writer, AdalResult? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WritePropertyName(PropName_UserInfo);
		AdalUserInfoSerializeHandler(writer, value.UserInfo);
		writer.WriteEndObject();
	}

	private static JsonPropertyInfo[] AdalResultWrapperPropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[6];
		JsonPropertyInfoValues<AdalResult> propertyInfo = new JsonPropertyInfoValues<AdalResult>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(AdalResultWrapper),
			PropertyTypeInfo = msalJsonSerializerContext.AdalResult,
			Converter = null,
			Getter = (object obj) => ((AdalResultWrapper)obj).Result,
			Setter = delegate(object obj, AdalResult? value)
			{
				((AdalResultWrapper)obj).Result = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Result",
			JsonPropertyName = null
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<string> propertyInfo2 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(AdalResultWrapper),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((AdalResultWrapper)obj).RawClientInfo,
			Setter = delegate(object obj, string? value)
			{
				((AdalResultWrapper)obj).RawClientInfo = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "RawClientInfo",
			JsonPropertyName = null
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string> propertyInfo3 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(AdalResultWrapper),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((AdalResultWrapper)obj).RefreshToken,
			Setter = delegate(object obj, string? value)
			{
				((AdalResultWrapper)obj).RefreshToken = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "RefreshToken",
			JsonPropertyName = null
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		JsonPropertyInfoValues<bool> propertyInfo4 = new JsonPropertyInfoValues<bool>
		{
			IsProperty = true,
			IsPublic = false,
			IsVirtual = false,
			DeclaringType = typeof(AdalResultWrapper),
			PropertyTypeInfo = msalJsonSerializerContext.Boolean,
			Converter = null,
			Getter = null,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "IsMultipleResourceRefreshToken",
			JsonPropertyName = null
		};
		array[3] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo4);
		JsonPropertyInfoValues<string> propertyInfo5 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = false,
			IsVirtual = false,
			DeclaringType = typeof(AdalResultWrapper),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = null,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ResourceInResponse",
			JsonPropertyName = null
		};
		array[4] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo5);
		JsonPropertyInfoValues<string> propertyInfo6 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(AdalResultWrapper),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((AdalResultWrapper)obj).UserAssertionHash,
			Setter = delegate(object obj, string? value)
			{
				((AdalResultWrapper)obj).UserAssertionHash = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "UserAssertionHash",
			JsonPropertyName = null
		};
		array[5] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo6);
		return array;
	}

	private static void AdalResultWrapperSerializeHandler(Utf8JsonWriter writer, AdalResultWrapper? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WritePropertyName(PropName_Result);
		AdalResultSerializeHandler(writer, value.Result);
		writer.WriteString(PropName_RawClientInfo, value.RawClientInfo);
		writer.WriteString(PropName_RefreshToken, value.RefreshToken);
		writer.WriteString(PropName_UserAssertionHash, value.UserAssertionHash);
		writer.WriteEndObject();
	}

	private static void IEnumerableStringSerializeHandler(Utf8JsonWriter writer, IEnumerable<string>? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartArray();
		foreach (string item in value)
		{
			writer.WriteStringValue(item);
		}
		writer.WriteEndArray();
	}

	private static JsonPropertyInfo[] KeyValuePairStringIEnumerableStringPropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[5];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(KeyValuePair<string, IEnumerable<string>>),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((KeyValuePair<string, IEnumerable<string>>)obj).Key,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Key",
			JsonPropertyName = null
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<IEnumerable<string>> propertyInfo2 = new JsonPropertyInfoValues<IEnumerable<string>>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(KeyValuePair<string, IEnumerable<string>>),
			PropertyTypeInfo = msalJsonSerializerContext.IEnumerableString,
			Converter = null,
			Getter = (object obj) => ((KeyValuePair<string, IEnumerable<string>>)obj).Value,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Value",
			JsonPropertyName = null
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string> propertyInfo3 = new JsonPropertyInfoValues<string>
		{
			IsProperty = false,
			IsPublic = false,
			IsVirtual = false,
			DeclaringType = typeof(KeyValuePair<string, IEnumerable<string>>),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = null,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "key",
			JsonPropertyName = null
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		JsonPropertyInfoValues<IEnumerable<string>> propertyInfo4 = new JsonPropertyInfoValues<IEnumerable<string>>
		{
			IsProperty = false,
			IsPublic = false,
			IsVirtual = false,
			DeclaringType = typeof(KeyValuePair<string, IEnumerable<string>>),
			PropertyTypeInfo = msalJsonSerializerContext.IEnumerableString,
			Converter = null,
			Getter = null,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "value",
			JsonPropertyName = null
		};
		array[3] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo4);
		JsonPropertyInfoValues<int> propertyInfo5 = new JsonPropertyInfoValues<int>
		{
			IsProperty = false,
			IsPublic = false,
			IsVirtual = false,
			DeclaringType = typeof(KeyValuePair<string, IEnumerable<string>>),
			PropertyTypeInfo = msalJsonSerializerContext.Int32,
			Converter = null,
			Getter = null,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "_dummyPrimitive",
			JsonPropertyName = null
		};
		array[4] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo5);
		return array;
	}

	private static void KeyValuePairStringIEnumerableStringSerializeHandler(Utf8JsonWriter writer, KeyValuePair<string, IEnumerable<string>> value)
	{
		writer.WriteStartObject();
		JsonEncodedText propName_Key = PropName_Key;
		KeyValuePair<string, IEnumerable<string>> keyValuePair = value;
		writer.WriteString(propName_Key, keyValuePair.Key);
		writer.WritePropertyName(PropName_Value);
		keyValuePair = value;
		IEnumerableStringSerializeHandler(writer, keyValuePair.Value);
		writer.WriteEndObject();
	}

	private static JsonParameterInfoValues[] KeyValuePairStringIEnumerableStringCtorParamInit()
	{
		return new JsonParameterInfoValues[2]
		{
			new JsonParameterInfoValues
			{
				Name = "key",
				ParameterType = typeof(string),
				Position = 0,
				HasDefaultValue = false,
				DefaultValue = null
			},
			new JsonParameterInfoValues
			{
				Name = "value",
				ParameterType = typeof(IEnumerable<string>),
				Position = 1,
				HasDefaultValue = false,
				DefaultValue = null
			}
		};
	}

	private static void ListKeyValuePairStringIEnumerableStringSerializeHandler(Utf8JsonWriter writer, List<KeyValuePair<string, IEnumerable<string>>>? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartArray();
		for (int i = 0; i < value.Count; i++)
		{
			KeyValuePairStringIEnumerableStringSerializeHandler(writer, value[i]);
		}
		writer.WriteEndArray();
	}

	private static JsonPropertyInfo[] ClientInfoPropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[2];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(ClientInfo),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((ClientInfo)obj).UniqueObjectIdentifier,
			Setter = delegate(object obj, string? value)
			{
				((ClientInfo)obj).UniqueObjectIdentifier = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "UniqueObjectIdentifier",
			JsonPropertyName = "uid"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<string> propertyInfo2 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(ClientInfo),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((ClientInfo)obj).UniqueTenantIdentifier,
			Setter = delegate(object obj, string? value)
			{
				((ClientInfo)obj).UniqueTenantIdentifier = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "UniqueTenantIdentifier",
			JsonPropertyName = "utid"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		return array;
	}

	private static void ClientInfoSerializeHandler(Utf8JsonWriter writer, ClientInfo? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteString(PropName_uid, value.UniqueObjectIdentifier);
		writer.WriteString(PropName_utid, value.UniqueTenantIdentifier);
		writer.WriteEndObject();
	}

	private static JsonPropertyInfo[] OAuth2ResponseBasePropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[6];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).Error,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).Error = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Error",
			JsonPropertyName = "error"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<string> propertyInfo2 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).SubError,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).SubError = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "SubError",
			JsonPropertyName = "suberror"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string> propertyInfo3 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).ErrorDescription,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).ErrorDescription = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ErrorDescription",
			JsonPropertyName = "error_description"
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		JsonPropertyInfoValues<string[]> propertyInfo4 = new JsonPropertyInfoValues<string[]>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.StringArray,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).ErrorCodes,
			Setter = delegate(object obj, string[]? value)
			{
				((OAuth2ResponseBase)obj).ErrorCodes = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ErrorCodes",
			JsonPropertyName = "error_codes"
		};
		array[3] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo4);
		JsonPropertyInfoValues<string> propertyInfo5 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).CorrelationId,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).CorrelationId = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "CorrelationId",
			JsonPropertyName = "correlation_id"
		};
		array[4] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo5);
		JsonPropertyInfoValues<string> propertyInfo6 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).Claims,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).Claims = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Claims",
			JsonPropertyName = "claims"
		};
		array[5] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo6);
		return array;
	}

	private static void OAuth2ResponseBaseSerializeHandler(Utf8JsonWriter writer, OAuth2ResponseBase? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteString(PropName_error, value.Error);
		writer.WriteString(PropName_suberror, value.SubError);
		writer.WriteString(PropName_error_description, value.ErrorDescription);
		writer.WritePropertyName(PropName_error_codes);
		StringArraySerializeHandler(writer, value.ErrorCodes);
		writer.WriteString(PropName_correlation_id, value.CorrelationId);
		writer.WriteString(PropName_claims, value.Claims);
		writer.WriteEndObject();
	}

	private static void DictionaryStringJsonElementSerializeHandler(Utf8JsonWriter writer, Dictionary<string, JsonElement>? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		foreach (KeyValuePair<string, JsonElement> item in value)
		{
			writer.WritePropertyName(item.Key);
			JsonSerializer.Serialize(writer, item.Value, Default.JsonElement);
		}
		writer.WriteEndObject();
	}

	private static void HttpResponseHeadersSerializeHandler(Utf8JsonWriter writer, HttpResponseHeaders? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartArray();
		foreach (KeyValuePair<string, IEnumerable<string>> item in value)
		{
			KeyValuePairStringIEnumerableStringSerializeHandler(writer, item);
		}
		writer.WriteEndArray();
	}

	private static void IDictionaryStringStringSerializeHandler(Utf8JsonWriter writer, IDictionary<string, string>? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		foreach (KeyValuePair<string, string> item in value)
		{
			writer.WriteString(item.Key, item.Value);
		}
		writer.WriteEndObject();
	}

	private static JsonPropertyInfo[] HttpResponsePropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[5];
		JsonPropertyInfoValues<HttpResponseHeaders> propertyInfo = new JsonPropertyInfoValues<HttpResponseHeaders>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(HttpResponse),
			PropertyTypeInfo = msalJsonSerializerContext.HttpResponseHeaders,
			Converter = null,
			Getter = (object obj) => ((HttpResponse)obj).Headers,
			Setter = delegate(object obj, HttpResponseHeaders? value)
			{
				((HttpResponse)obj).Headers = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Headers",
			JsonPropertyName = null
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<IDictionary<string, string>> propertyInfo2 = new JsonPropertyInfoValues<IDictionary<string, string>>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(HttpResponse),
			PropertyTypeInfo = msalJsonSerializerContext.IDictionaryStringString,
			Converter = null,
			Getter = (object obj) => ((HttpResponse)obj).HeadersAsDictionary,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "HeadersAsDictionary",
			JsonPropertyName = null
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<HttpStatusCode> propertyInfo3 = new JsonPropertyInfoValues<HttpStatusCode>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(HttpResponse),
			PropertyTypeInfo = msalJsonSerializerContext.HttpStatusCode,
			Converter = null,
			Getter = (object obj) => ((HttpResponse)obj).StatusCode,
			Setter = delegate(object obj, HttpStatusCode value)
			{
				((HttpResponse)obj).StatusCode = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "StatusCode",
			JsonPropertyName = null
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		JsonPropertyInfoValues<string> propertyInfo4 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(HttpResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((HttpResponse)obj).UserAgent,
			Setter = delegate(object obj, string? value)
			{
				((HttpResponse)obj).UserAgent = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "UserAgent",
			JsonPropertyName = null
		};
		array[3] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo4);
		JsonPropertyInfoValues<string> propertyInfo5 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(HttpResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((HttpResponse)obj).Body,
			Setter = delegate(object obj, string? value)
			{
				((HttpResponse)obj).Body = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Body",
			JsonPropertyName = null
		};
		array[4] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo5);
		return array;
	}

	private static void HttpResponseSerializeHandler(Utf8JsonWriter writer, HttpResponse? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WritePropertyName(PropName_Headers);
		HttpResponseHeadersSerializeHandler(writer, value.Headers);
		writer.WritePropertyName(PropName_HeadersAsDictionary);
		IDictionaryStringStringSerializeHandler(writer, value.HeadersAsDictionary);
		writer.WritePropertyName(PropName_StatusCode);
		JsonSerializer.Serialize(writer, value.StatusCode, Default.HttpStatusCode);
		writer.WriteString(PropName_UserAgent, value.UserAgent);
		writer.WriteString(PropName_Body, value.Body);
		writer.WriteEndObject();
	}

	private static JsonPropertyInfo[] MsalTokenResponsePropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[25];
		JsonPropertyInfoValues<Dictionary<string, JsonElement>> propertyInfo = new JsonPropertyInfoValues<Dictionary<string, JsonElement>>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.DictionaryStringJsonElement,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).ExtensionData,
			Setter = delegate(object obj, Dictionary<string, JsonElement>? value)
			{
				((MsalTokenResponse)obj).ExtensionData = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = true,
			NumberHandling = null,
			PropertyName = "ExtensionData",
			JsonPropertyName = null
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<string> propertyInfo2 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).TokenType,
			Setter = delegate(object obj, string? value)
			{
				((MsalTokenResponse)obj).TokenType = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "TokenType",
			JsonPropertyName = "token_type"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string> propertyInfo3 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).AccessToken,
			Setter = delegate(object obj, string? value)
			{
				((MsalTokenResponse)obj).AccessToken = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "AccessToken",
			JsonPropertyName = "access_token"
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		JsonPropertyInfoValues<string> propertyInfo4 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).RefreshToken,
			Setter = delegate(object obj, string? value)
			{
				((MsalTokenResponse)obj).RefreshToken = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "RefreshToken",
			JsonPropertyName = "refresh_token"
		};
		array[3] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo4);
		JsonPropertyInfoValues<string> propertyInfo5 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).Scope,
			Setter = delegate(object obj, string? value)
			{
				((MsalTokenResponse)obj).Scope = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Scope",
			JsonPropertyName = "scope"
		};
		array[4] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo5);
		JsonPropertyInfoValues<string> propertyInfo6 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).ClientInfo,
			Setter = delegate(object obj, string? value)
			{
				((MsalTokenResponse)obj).ClientInfo = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ClientInfo",
			JsonPropertyName = "client_info"
		};
		array[5] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo6);
		JsonPropertyInfoValues<string> propertyInfo7 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).IdToken,
			Setter = delegate(object obj, string? value)
			{
				((MsalTokenResponse)obj).IdToken = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "IdToken",
			JsonPropertyName = "id_token"
		};
		array[6] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo7);
		JsonPropertyInfoValues<long> propertyInfo8 = new JsonPropertyInfoValues<long>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.Int64,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).ExpiresIn,
			Setter = delegate(object obj, long value)
			{
				((MsalTokenResponse)obj).ExpiresIn = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = JsonNumberHandling.AllowReadingFromString,
			PropertyName = "ExpiresIn",
			JsonPropertyName = "expires_in"
		};
		array[7] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo8);
		JsonPropertyInfoValues<long> propertyInfo9 = new JsonPropertyInfoValues<long>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.Int64,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).ExtendedExpiresIn,
			Setter = delegate(object obj, long value)
			{
				((MsalTokenResponse)obj).ExtendedExpiresIn = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = JsonNumberHandling.AllowReadingFromString,
			PropertyName = "ExtendedExpiresIn",
			JsonPropertyName = "ext_expires_in"
		};
		array[8] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo9);
		JsonPropertyInfoValues<long?> propertyInfo10 = new JsonPropertyInfoValues<long?>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.NullableInt64,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).RefreshIn,
			Setter = delegate(object obj, long? value)
			{
				((MsalTokenResponse)obj).RefreshIn = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = JsonNumberHandling.AllowReadingFromString,
			PropertyName = "RefreshIn",
			JsonPropertyName = "refresh_in"
		};
		array[9] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo10);
		JsonPropertyInfoValues<string> propertyInfo11 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).FamilyId,
			Setter = delegate(object obj, string? value)
			{
				((MsalTokenResponse)obj).FamilyId = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "FamilyId",
			JsonPropertyName = "foci"
		};
		array[10] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo11);
		JsonPropertyInfoValues<string> propertyInfo12 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).SpaAuthCode,
			Setter = delegate(object obj, string? value)
			{
				((MsalTokenResponse)obj).SpaAuthCode = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "SpaAuthCode",
			JsonPropertyName = "spa_code"
		};
		array[11] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo12);
		JsonPropertyInfoValues<string> propertyInfo13 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).AuthorityUrl,
			Setter = delegate(object obj, string? value)
			{
				((MsalTokenResponse)obj).AuthorityUrl = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "AuthorityUrl",
			JsonPropertyName = "authority"
		};
		array[12] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo13);
		JsonPropertyInfoValues<string> propertyInfo14 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).TenantId,
			Setter = delegate(object obj, string? value)
			{
				((MsalTokenResponse)obj).TenantId = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "TenantId",
			JsonPropertyName = null
		};
		array[13] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo14);
		JsonPropertyInfoValues<string> propertyInfo15 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).Upn,
			Setter = delegate(object obj, string? value)
			{
				((MsalTokenResponse)obj).Upn = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Upn",
			JsonPropertyName = null
		};
		array[14] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo15);
		JsonPropertyInfoValues<string> propertyInfo16 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).AccountUserId,
			Setter = delegate(object obj, string? value)
			{
				((MsalTokenResponse)obj).AccountUserId = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "AccountUserId",
			JsonPropertyName = null
		};
		array[15] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo16);
		JsonPropertyInfoValues<string> propertyInfo17 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).WamAccountId,
			Setter = delegate(object obj, string? value)
			{
				((MsalTokenResponse)obj).WamAccountId = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "WamAccountId",
			JsonPropertyName = null
		};
		array[16] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo17);
		JsonPropertyInfoValues<TokenSource> propertyInfo18 = new JsonPropertyInfoValues<TokenSource>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.TokenSource,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).TokenSource,
			Setter = delegate(object obj, TokenSource value)
			{
				((MsalTokenResponse)obj).TokenSource = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "TokenSource",
			JsonPropertyName = null
		};
		array[17] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo18);
		JsonPropertyInfoValues<HttpResponse> propertyInfo19 = new JsonPropertyInfoValues<HttpResponse>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(MsalTokenResponse),
			PropertyTypeInfo = msalJsonSerializerContext.HttpResponse,
			Converter = null,
			Getter = (object obj) => ((MsalTokenResponse)obj).HttpResponse,
			Setter = delegate(object obj, HttpResponse? value)
			{
				((MsalTokenResponse)obj).HttpResponse = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "HttpResponse",
			JsonPropertyName = null
		};
		array[18] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo19);
		JsonPropertyInfoValues<string> propertyInfo20 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).Error,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).Error = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Error",
			JsonPropertyName = "error"
		};
		array[19] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo20);
		JsonPropertyInfoValues<string> propertyInfo21 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).SubError,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).SubError = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "SubError",
			JsonPropertyName = "suberror"
		};
		array[20] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo21);
		JsonPropertyInfoValues<string> propertyInfo22 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).ErrorDescription,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).ErrorDescription = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ErrorDescription",
			JsonPropertyName = "error_description"
		};
		array[21] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo22);
		JsonPropertyInfoValues<string[]> propertyInfo23 = new JsonPropertyInfoValues<string[]>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.StringArray,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).ErrorCodes,
			Setter = delegate(object obj, string[]? value)
			{
				((OAuth2ResponseBase)obj).ErrorCodes = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ErrorCodes",
			JsonPropertyName = "error_codes"
		};
		array[22] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo23);
		JsonPropertyInfoValues<string> propertyInfo24 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).CorrelationId,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).CorrelationId = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "CorrelationId",
			JsonPropertyName = "correlation_id"
		};
		array[23] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo24);
		JsonPropertyInfoValues<string> propertyInfo25 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).Claims,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).Claims = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Claims",
			JsonPropertyName = "claims"
		};
		array[24] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo25);
		return array;
	}

	private static JsonPropertyInfo[] UserRealmDiscoveryResponsePropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[9];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(UserRealmDiscoveryResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((UserRealmDiscoveryResponse)obj).Version,
			Setter = delegate(object obj, string? value)
			{
				((UserRealmDiscoveryResponse)obj).Version = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Version",
			JsonPropertyName = "ver"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<string> propertyInfo2 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(UserRealmDiscoveryResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((UserRealmDiscoveryResponse)obj).AccountType,
			Setter = delegate(object obj, string? value)
			{
				((UserRealmDiscoveryResponse)obj).AccountType = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "AccountType",
			JsonPropertyName = "account_type"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string> propertyInfo3 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(UserRealmDiscoveryResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((UserRealmDiscoveryResponse)obj).FederationProtocol,
			Setter = delegate(object obj, string? value)
			{
				((UserRealmDiscoveryResponse)obj).FederationProtocol = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "FederationProtocol",
			JsonPropertyName = "federation_protocol"
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		JsonPropertyInfoValues<string> propertyInfo4 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(UserRealmDiscoveryResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((UserRealmDiscoveryResponse)obj).FederationMetadataUrl,
			Setter = delegate(object obj, string? value)
			{
				((UserRealmDiscoveryResponse)obj).FederationMetadataUrl = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "FederationMetadataUrl",
			JsonPropertyName = "federation_metadata_url"
		};
		array[3] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo4);
		JsonPropertyInfoValues<string> propertyInfo5 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(UserRealmDiscoveryResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((UserRealmDiscoveryResponse)obj).FederationActiveAuthUrl,
			Setter = delegate(object obj, string? value)
			{
				((UserRealmDiscoveryResponse)obj).FederationActiveAuthUrl = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "FederationActiveAuthUrl",
			JsonPropertyName = "federation_active_auth_url"
		};
		array[4] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo5);
		JsonPropertyInfoValues<string> propertyInfo6 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(UserRealmDiscoveryResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((UserRealmDiscoveryResponse)obj).CloudAudienceUrn,
			Setter = delegate(object obj, string? value)
			{
				((UserRealmDiscoveryResponse)obj).CloudAudienceUrn = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "CloudAudienceUrn",
			JsonPropertyName = "cloud_audience_urn"
		};
		array[5] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo6);
		JsonPropertyInfoValues<string> propertyInfo7 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(UserRealmDiscoveryResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((UserRealmDiscoveryResponse)obj).DomainName,
			Setter = delegate(object obj, string? value)
			{
				((UserRealmDiscoveryResponse)obj).DomainName = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "DomainName",
			JsonPropertyName = "domain_name"
		};
		array[6] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo7);
		JsonPropertyInfoValues<bool> propertyInfo8 = new JsonPropertyInfoValues<bool>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(UserRealmDiscoveryResponse),
			PropertyTypeInfo = msalJsonSerializerContext.Boolean,
			Converter = null,
			Getter = (object obj) => ((UserRealmDiscoveryResponse)obj).IsFederated,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "IsFederated",
			JsonPropertyName = null
		};
		array[7] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo8);
		JsonPropertyInfoValues<bool> propertyInfo9 = new JsonPropertyInfoValues<bool>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(UserRealmDiscoveryResponse),
			PropertyTypeInfo = msalJsonSerializerContext.Boolean,
			Converter = null,
			Getter = (object obj) => ((UserRealmDiscoveryResponse)obj).IsManaged,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "IsManaged",
			JsonPropertyName = null
		};
		array[8] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo9);
		return array;
	}

	private static void UserRealmDiscoveryResponseSerializeHandler(Utf8JsonWriter writer, UserRealmDiscoveryResponse? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteString(PropName_ver, value.Version);
		writer.WriteString(PropName_account_type, value.AccountType);
		writer.WriteString(PropName_federation_protocol, value.FederationProtocol);
		writer.WriteString(PropName_federation_metadata_url, value.FederationMetadataUrl);
		writer.WriteString(PropName_federation_active_auth_url, value.FederationActiveAuthUrl);
		writer.WriteString(PropName_cloud_audience_urn, value.CloudAudienceUrn);
		writer.WriteString(PropName_domain_name, value.DomainName);
		writer.WriteBoolean(PropName_IsFederated, value.IsFederated);
		writer.WriteBoolean(PropName_IsManaged, value.IsManaged);
		writer.WriteEndObject();
	}

	private static JsonPropertyInfo[] DeviceCodeResponsePropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[13];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(DeviceCodeResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((DeviceCodeResponse)obj).UserCode,
			Setter = delegate(object obj, string? value)
			{
				((DeviceCodeResponse)obj).UserCode = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "UserCode",
			JsonPropertyName = "user_code"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<string> propertyInfo2 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(DeviceCodeResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((DeviceCodeResponse)obj).DeviceCode,
			Setter = delegate(object obj, string? value)
			{
				((DeviceCodeResponse)obj).DeviceCode = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "DeviceCode",
			JsonPropertyName = "device_code"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string> propertyInfo3 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(DeviceCodeResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((DeviceCodeResponse)obj).VerificationUrl,
			Setter = delegate(object obj, string? value)
			{
				((DeviceCodeResponse)obj).VerificationUrl = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "VerificationUrl",
			JsonPropertyName = "verification_url"
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		JsonPropertyInfoValues<string> propertyInfo4 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(DeviceCodeResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((DeviceCodeResponse)obj).VerificationUri,
			Setter = delegate(object obj, string? value)
			{
				((DeviceCodeResponse)obj).VerificationUri = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "VerificationUri",
			JsonPropertyName = "verification_uri"
		};
		array[3] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo4);
		JsonPropertyInfoValues<long> propertyInfo5 = new JsonPropertyInfoValues<long>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(DeviceCodeResponse),
			PropertyTypeInfo = msalJsonSerializerContext.Int64,
			Converter = null,
			Getter = (object obj) => ((DeviceCodeResponse)obj).ExpiresIn,
			Setter = delegate(object obj, long value)
			{
				((DeviceCodeResponse)obj).ExpiresIn = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = JsonNumberHandling.AllowReadingFromString,
			PropertyName = "ExpiresIn",
			JsonPropertyName = "expires_in"
		};
		array[4] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo5);
		JsonPropertyInfoValues<long> propertyInfo6 = new JsonPropertyInfoValues<long>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(DeviceCodeResponse),
			PropertyTypeInfo = msalJsonSerializerContext.Int64,
			Converter = null,
			Getter = (object obj) => ((DeviceCodeResponse)obj).Interval,
			Setter = delegate(object obj, long value)
			{
				((DeviceCodeResponse)obj).Interval = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = JsonNumberHandling.AllowReadingFromString,
			PropertyName = "Interval",
			JsonPropertyName = "interval"
		};
		array[5] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo6);
		JsonPropertyInfoValues<string> propertyInfo7 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(DeviceCodeResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((DeviceCodeResponse)obj).Message,
			Setter = delegate(object obj, string? value)
			{
				((DeviceCodeResponse)obj).Message = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Message",
			JsonPropertyName = "message"
		};
		array[6] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo7);
		JsonPropertyInfoValues<string> propertyInfo8 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).Error,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).Error = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Error",
			JsonPropertyName = "error"
		};
		array[7] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo8);
		JsonPropertyInfoValues<string> propertyInfo9 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).SubError,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).SubError = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "SubError",
			JsonPropertyName = "suberror"
		};
		array[8] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo9);
		JsonPropertyInfoValues<string> propertyInfo10 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).ErrorDescription,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).ErrorDescription = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ErrorDescription",
			JsonPropertyName = "error_description"
		};
		array[9] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo10);
		JsonPropertyInfoValues<string[]> propertyInfo11 = new JsonPropertyInfoValues<string[]>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.StringArray,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).ErrorCodes,
			Setter = delegate(object obj, string[]? value)
			{
				((OAuth2ResponseBase)obj).ErrorCodes = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ErrorCodes",
			JsonPropertyName = "error_codes"
		};
		array[10] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo11);
		JsonPropertyInfoValues<string> propertyInfo12 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).CorrelationId,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).CorrelationId = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "CorrelationId",
			JsonPropertyName = "correlation_id"
		};
		array[11] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo12);
		JsonPropertyInfoValues<string> propertyInfo13 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).Claims,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).Claims = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Claims",
			JsonPropertyName = "claims"
		};
		array[12] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo13);
		return array;
	}

	private static void DeviceCodeResponseSerializeHandler(Utf8JsonWriter writer, DeviceCodeResponse? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteString(PropName_user_code, value.UserCode);
		writer.WriteString(PropName_device_code, value.DeviceCode);
		writer.WriteString(PropName_verification_url, value.VerificationUrl);
		writer.WriteString(PropName_verification_uri, value.VerificationUri);
		writer.WriteNumber(PropName_expires_in, value.ExpiresIn);
		writer.WriteNumber(PropName_interval, value.Interval);
		writer.WriteString(PropName_message, value.Message);
		writer.WriteString(PropName_error, value.Error);
		writer.WriteString(PropName_suberror, value.SubError);
		writer.WriteString(PropName_error_description, value.ErrorDescription);
		writer.WritePropertyName(PropName_error_codes);
		StringArraySerializeHandler(writer, value.ErrorCodes);
		writer.WriteString(PropName_correlation_id, value.CorrelationId);
		writer.WriteString(PropName_claims, value.Claims);
		writer.WriteEndObject();
	}

	private static JsonPropertyInfo[] LinksListPropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[2];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(LinksList),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((LinksList)obj).Rel,
			Setter = delegate(object obj, string? value)
			{
				((LinksList)obj).Rel = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Rel",
			JsonPropertyName = "rel"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<string> propertyInfo2 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(LinksList),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((LinksList)obj).Href,
			Setter = delegate(object obj, string? value)
			{
				((LinksList)obj).Href = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Href",
			JsonPropertyName = "href"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		return array;
	}

	private static void LinksListSerializeHandler(Utf8JsonWriter writer, LinksList? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteString(PropName_rel, value.Rel);
		writer.WriteString(PropName_href, value.Href);
		writer.WriteEndObject();
	}

	private static void ListLinksListSerializeHandler(Utf8JsonWriter writer, List<LinksList>? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartArray();
		for (int i = 0; i < value.Count; i++)
		{
			LinksListSerializeHandler(writer, value[i]);
		}
		writer.WriteEndArray();
	}

	private static JsonPropertyInfo[] AdfsWebFingerResponsePropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[8];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(AdfsWebFingerResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((AdfsWebFingerResponse)obj).Subject,
			Setter = delegate(object obj, string? value)
			{
				((AdfsWebFingerResponse)obj).Subject = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Subject",
			JsonPropertyName = "subject"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<List<LinksList>> propertyInfo2 = new JsonPropertyInfoValues<List<LinksList>>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(AdfsWebFingerResponse),
			PropertyTypeInfo = msalJsonSerializerContext.ListLinksList,
			Converter = null,
			Getter = (object obj) => ((AdfsWebFingerResponse)obj).Links,
			Setter = delegate(object obj, List<LinksList>? value)
			{
				((AdfsWebFingerResponse)obj).Links = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Links",
			JsonPropertyName = "links"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string> propertyInfo3 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).Error,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).Error = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Error",
			JsonPropertyName = "error"
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		JsonPropertyInfoValues<string> propertyInfo4 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).SubError,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).SubError = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "SubError",
			JsonPropertyName = "suberror"
		};
		array[3] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo4);
		JsonPropertyInfoValues<string> propertyInfo5 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).ErrorDescription,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).ErrorDescription = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ErrorDescription",
			JsonPropertyName = "error_description"
		};
		array[4] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo5);
		JsonPropertyInfoValues<string[]> propertyInfo6 = new JsonPropertyInfoValues<string[]>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.StringArray,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).ErrorCodes,
			Setter = delegate(object obj, string[]? value)
			{
				((OAuth2ResponseBase)obj).ErrorCodes = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ErrorCodes",
			JsonPropertyName = "error_codes"
		};
		array[5] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo6);
		JsonPropertyInfoValues<string> propertyInfo7 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).CorrelationId,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).CorrelationId = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "CorrelationId",
			JsonPropertyName = "correlation_id"
		};
		array[6] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo7);
		JsonPropertyInfoValues<string> propertyInfo8 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OAuth2ResponseBase),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OAuth2ResponseBase)obj).Claims,
			Setter = delegate(object obj, string? value)
			{
				((OAuth2ResponseBase)obj).Claims = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Claims",
			JsonPropertyName = "claims"
		};
		array[7] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo8);
		return array;
	}

	private static void AdfsWebFingerResponseSerializeHandler(Utf8JsonWriter writer, AdfsWebFingerResponse? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteString(PropName_subject, value.Subject);
		writer.WritePropertyName(PropName_links);
		ListLinksListSerializeHandler(writer, value.Links);
		writer.WriteString(PropName_error, value.Error);
		writer.WriteString(PropName_suberror, value.SubError);
		writer.WriteString(PropName_error_description, value.ErrorDescription);
		writer.WritePropertyName(PropName_error_codes);
		StringArraySerializeHandler(writer, value.ErrorCodes);
		writer.WriteString(PropName_correlation_id, value.CorrelationId);
		writer.WriteString(PropName_claims, value.Claims);
		writer.WriteEndObject();
	}

	private static void IListStringSerializeHandler(Utf8JsonWriter writer, IList<string>? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartArray();
		for (int i = 0; i < value.Count; i++)
		{
			writer.WriteStringValue(value[i]);
		}
		writer.WriteEndArray();
	}

	private static JsonPropertyInfo[] DeviceAuthHeaderPropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[3];
		JsonPropertyInfoValues<IList<string>> propertyInfo = new JsonPropertyInfoValues<IList<string>>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(DeviceAuthHeader),
			PropertyTypeInfo = msalJsonSerializerContext.IListString,
			Converter = null,
			Getter = (object obj) => ((DeviceAuthHeader)obj).X5c,
			Setter = delegate(object obj, IList<string>? value)
			{
				((DeviceAuthHeader)obj).X5c = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "X5c",
			JsonPropertyName = "x5c"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<string> propertyInfo2 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(DeviceAuthHeader),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((DeviceAuthHeader)obj).Type,
			Setter = delegate(object obj, string? value)
			{
				((DeviceAuthHeader)obj).Type = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Type",
			JsonPropertyName = "typ"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string> propertyInfo3 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(DeviceAuthHeader),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((DeviceAuthHeader)obj).Alg,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Alg",
			JsonPropertyName = "alg"
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		return array;
	}

	private static void DeviceAuthHeaderSerializeHandler(Utf8JsonWriter writer, DeviceAuthHeader? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WritePropertyName(PropName_x5c);
		IListStringSerializeHandler(writer, value.X5c);
		writer.WriteString(PropName_typ, value.Type);
		writer.WriteString(PropName_alg, value.Alg);
		writer.WriteEndObject();
	}

	private static JsonParameterInfoValues[] DeviceAuthHeaderCtorParamInit()
	{
		return new JsonParameterInfoValues[1]
		{
			new JsonParameterInfoValues
			{
				Name = "base64EncodedCertificate",
				ParameterType = typeof(string),
				Position = 0,
				HasDefaultValue = false,
				DefaultValue = null
			}
		};
	}

	private static JsonPropertyInfo[] LazyInt64PropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[2];
		JsonPropertyInfoValues<bool> propertyInfo = new JsonPropertyInfoValues<bool>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(Lazy<long>),
			PropertyTypeInfo = msalJsonSerializerContext.Boolean,
			Converter = null,
			Getter = (object obj) => ((Lazy<long>)obj).IsValueCreated,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "IsValueCreated",
			JsonPropertyName = null
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<long> propertyInfo2 = new JsonPropertyInfoValues<long>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(Lazy<long>),
			PropertyTypeInfo = msalJsonSerializerContext.Int64,
			Converter = null,
			Getter = (object obj) => ((Lazy<long>)obj).Value,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Value",
			JsonPropertyName = null
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		return array;
	}

	private static void LazyInt64SerializeHandler(Utf8JsonWriter writer, Lazy<long>? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteBoolean(PropName_IsValueCreated, value.IsValueCreated);
		writer.WriteNumber(PropName_Value, value.Value);
		writer.WriteEndObject();
	}

	private static JsonPropertyInfo[] DeviceAuthPayloadPropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[4];
		JsonPropertyInfoValues<long> propertyInfo = new JsonPropertyInfoValues<long>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(DeviceAuthPayload),
			PropertyTypeInfo = msalJsonSerializerContext.Int64,
			Converter = null,
			Getter = (object obj) => ((DeviceAuthPayload)obj).Iat,
			Setter = delegate(object obj, long value)
			{
				((DeviceAuthPayload)obj).Iat = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = JsonNumberHandling.AllowReadingFromString,
			PropertyName = "Iat",
			JsonPropertyName = "iat"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<string> propertyInfo2 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(DeviceAuthPayload),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((DeviceAuthPayload)obj).Audience,
			Setter = delegate(object obj, string? value)
			{
				((DeviceAuthPayload)obj).Audience = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Audience",
			JsonPropertyName = "aud"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string> propertyInfo3 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(DeviceAuthPayload),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((DeviceAuthPayload)obj).Nonce,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Nonce",
			JsonPropertyName = "nonce"
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		JsonPropertyInfoValues<Lazy<long>> propertyInfo4 = new JsonPropertyInfoValues<Lazy<long>>
		{
			IsProperty = false,
			IsPublic = false,
			IsVirtual = false,
			DeclaringType = typeof(DeviceAuthPayload),
			PropertyTypeInfo = msalJsonSerializerContext.LazyInt64,
			Converter = null,
			Getter = null,
			Setter = null,
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "_defaultDeviceAuthJWTTimeSpan",
			JsonPropertyName = null
		};
		array[3] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo4);
		return array;
	}

	private static void DeviceAuthPayloadSerializeHandler(Utf8JsonWriter writer, DeviceAuthPayload? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteNumber(PropName_iat, value.Iat);
		writer.WriteString(PropName_aud, value.Audience);
		writer.WriteString(PropName_nonce, value.Nonce);
		writer.WriteEndObject();
	}

	private static JsonParameterInfoValues[] DeviceAuthPayloadCtorParamInit()
	{
		return new JsonParameterInfoValues[2]
		{
			new JsonParameterInfoValues
			{
				Name = "audience",
				ParameterType = typeof(string),
				Position = 0,
				HasDefaultValue = false,
				DefaultValue = null
			},
			new JsonParameterInfoValues
			{
				Name = "nonce",
				ParameterType = typeof(string),
				Position = 1,
				HasDefaultValue = false,
				DefaultValue = null
			}
		};
	}

	private static JsonPropertyInfo[] ManagedIdentityResponsePropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[5];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(ManagedIdentityResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((ManagedIdentityResponse)obj).AccessToken,
			Setter = delegate(object obj, string? value)
			{
				((ManagedIdentityResponse)obj).AccessToken = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "AccessToken",
			JsonPropertyName = "access_token"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<string> propertyInfo2 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(ManagedIdentityResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((ManagedIdentityResponse)obj).ExpiresOn,
			Setter = delegate(object obj, string? value)
			{
				((ManagedIdentityResponse)obj).ExpiresOn = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ExpiresOn",
			JsonPropertyName = "expires_on"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string> propertyInfo3 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(ManagedIdentityResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((ManagedIdentityResponse)obj).Resource,
			Setter = delegate(object obj, string? value)
			{
				((ManagedIdentityResponse)obj).Resource = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Resource",
			JsonPropertyName = "resource"
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		JsonPropertyInfoValues<string> propertyInfo4 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(ManagedIdentityResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((ManagedIdentityResponse)obj).TokenType,
			Setter = delegate(object obj, string? value)
			{
				((ManagedIdentityResponse)obj).TokenType = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "TokenType",
			JsonPropertyName = "token_type"
		};
		array[3] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo4);
		JsonPropertyInfoValues<string> propertyInfo5 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(ManagedIdentityResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((ManagedIdentityResponse)obj).ClientId,
			Setter = delegate(object obj, string? value)
			{
				((ManagedIdentityResponse)obj).ClientId = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ClientId",
			JsonPropertyName = "client_id"
		};
		array[4] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo5);
		return array;
	}

	private static void ManagedIdentityResponseSerializeHandler(Utf8JsonWriter writer, ManagedIdentityResponse? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteString(PropName_access_token, value.AccessToken);
		writer.WriteString(PropName_expires_on, value.ExpiresOn);
		writer.WriteString(PropName_resource, value.Resource);
		writer.WriteString(PropName_token_type, value.TokenType);
		writer.WriteString(PropName_client_id, value.ClientId);
		writer.WriteEndObject();
	}

	private static JsonPropertyInfo[] ManagedIdentityErrorResponsePropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[4];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(ManagedIdentityErrorResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((ManagedIdentityErrorResponse)obj).Message,
			Setter = delegate(object obj, string? value)
			{
				((ManagedIdentityErrorResponse)obj).Message = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Message",
			JsonPropertyName = "message"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<string> propertyInfo2 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(ManagedIdentityErrorResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((ManagedIdentityErrorResponse)obj).CorrelationId,
			Setter = delegate(object obj, string? value)
			{
				((ManagedIdentityErrorResponse)obj).CorrelationId = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "CorrelationId",
			JsonPropertyName = "correlationId"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		JsonPropertyInfoValues<string> propertyInfo3 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(ManagedIdentityErrorResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((ManagedIdentityErrorResponse)obj).Error,
			Setter = delegate(object obj, string? value)
			{
				((ManagedIdentityErrorResponse)obj).Error = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "Error",
			JsonPropertyName = "error"
		};
		array[2] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo3);
		JsonPropertyInfoValues<string> propertyInfo4 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(ManagedIdentityErrorResponse),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((ManagedIdentityErrorResponse)obj).ErrorDescription,
			Setter = delegate(object obj, string? value)
			{
				((ManagedIdentityErrorResponse)obj).ErrorDescription = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "ErrorDescription",
			JsonPropertyName = "error_description"
		};
		array[3] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo4);
		return array;
	}

	private static void ManagedIdentityErrorResponseSerializeHandler(Utf8JsonWriter writer, ManagedIdentityErrorResponse? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteString(PropName_message, value.Message);
		writer.WriteString(PropName_correlationId, value.CorrelationId);
		writer.WriteString(PropName_error, value.Error);
		writer.WriteString(PropName_error_description, value.ErrorDescription);
		writer.WriteEndObject();
	}

	private static JsonPropertyInfo[] OidcMetadataPropInit(JsonSerializerContext context)
	{
		MsalJsonSerializerContext msalJsonSerializerContext = (MsalJsonSerializerContext)context;
		JsonSerializerOptions options = context.Options;
		JsonPropertyInfo[] array = new JsonPropertyInfo[2];
		JsonPropertyInfoValues<string> propertyInfo = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OidcMetadata),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OidcMetadata)obj).TokenEndpoint,
			Setter = delegate(object obj, string? value)
			{
				((OidcMetadata)obj).TokenEndpoint = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "TokenEndpoint",
			JsonPropertyName = "token_endpoint"
		};
		array[0] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo);
		JsonPropertyInfoValues<string> propertyInfo2 = new JsonPropertyInfoValues<string>
		{
			IsProperty = true,
			IsPublic = true,
			IsVirtual = false,
			DeclaringType = typeof(OidcMetadata),
			PropertyTypeInfo = msalJsonSerializerContext.String,
			Converter = null,
			Getter = (object obj) => ((OidcMetadata)obj).AuthorizationEndpoint,
			Setter = delegate(object obj, string? value)
			{
				((OidcMetadata)obj).AuthorizationEndpoint = value;
			},
			IgnoreCondition = null,
			HasJsonInclude = false,
			IsExtensionData = false,
			NumberHandling = null,
			PropertyName = "AuthorizationEndpoint",
			JsonPropertyName = "authorization_endpoint"
		};
		array[1] = JsonMetadataServices.CreatePropertyInfo(options, propertyInfo2);
		return array;
	}

	private static void OidcMetadataSerializeHandler(Utf8JsonWriter writer, OidcMetadata? value)
	{
		if (value == null)
		{
			writer.WriteNullValue();
			return;
		}
		writer.WriteStartObject();
		writer.WriteString(PropName_token_endpoint, value.TokenEndpoint);
		writer.WriteString(PropName_authorization_endpoint, value.AuthorizationEndpoint);
		writer.WriteEndObject();
	}

	public MsalJsonSerializerContext()
		: base(null)
	{
	}

	public MsalJsonSerializerContext(JsonSerializerOptions options)
		: base(options)
	{
	}

	private JsonConverter? GetRuntimeProvidedCustomConverter(Type type)
	{
		IList<JsonConverter> converters = base.Options.Converters;
		for (int i = 0; i < converters.Count; i++)
		{
			JsonConverter jsonConverter = converters[i];
			if (!jsonConverter.CanConvert(type))
			{
				continue;
			}
			if (jsonConverter is JsonConverterFactory jsonConverterFactory)
			{
				jsonConverter = jsonConverterFactory.CreateConverter(type, base.Options);
				if (jsonConverter == null || jsonConverter is JsonConverterFactory)
				{
					throw new InvalidOperationException($"The converter '{jsonConverterFactory.GetType()}' cannot return null or a JsonConverterFactory instance.");
				}
			}
			return jsonConverter;
		}
		return null;
	}

	public override JsonTypeInfo GetTypeInfo(Type type)
	{
		if (type == typeof(KerberosSupplementalTicket))
		{
			return KerberosSupplementalTicket;
		}
		if (type == typeof(InstanceDiscoveryResponse))
		{
			return InstanceDiscoveryResponse;
		}
		if (type == typeof(LocalImdsErrorResponse))
		{
			return LocalImdsErrorResponse;
		}
		if (type == typeof(AdalResultWrapper))
		{
			return AdalResultWrapper;
		}
		if (type == typeof(List<KeyValuePair<string, IEnumerable<string>>>))
		{
			return ListKeyValuePairStringIEnumerableString;
		}
		if (type == typeof(ClientInfo))
		{
			return ClientInfo;
		}
		if (type == typeof(OAuth2ResponseBase))
		{
			return OAuth2ResponseBase;
		}
		if (type == typeof(MsalTokenResponse))
		{
			return MsalTokenResponse;
		}
		if (type == typeof(UserRealmDiscoveryResponse))
		{
			return UserRealmDiscoveryResponse;
		}
		if (type == typeof(DeviceCodeResponse))
		{
			return DeviceCodeResponse;
		}
		if (type == typeof(AdfsWebFingerResponse))
		{
			return AdfsWebFingerResponse;
		}
		if (type == typeof(DeviceAuthHeader))
		{
			return DeviceAuthHeader;
		}
		if (type == typeof(DeviceAuthPayload))
		{
			return DeviceAuthPayload;
		}
		if (type == typeof(ManagedIdentityResponse))
		{
			return ManagedIdentityResponse;
		}
		if (type == typeof(ManagedIdentityErrorResponse))
		{
			return ManagedIdentityErrorResponse;
		}
		if (type == typeof(OidcMetadata))
		{
			return OidcMetadata;
		}
		if (type == typeof(DateTimeOffset))
		{
			return DateTimeOffset;
		}
		if (type == typeof(string))
		{
			return String;
		}
		if (type == typeof(IEnumerable<string>))
		{
			return IEnumerableString;
		}
		if (type == typeof(Dictionary<string, JsonElement>))
		{
			return DictionaryStringJsonElement;
		}
		if (type == typeof(long))
		{
			return Int64;
		}
		return null;
	}
}
