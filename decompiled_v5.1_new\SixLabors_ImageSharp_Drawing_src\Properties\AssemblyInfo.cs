using System.Diagnostics;
using System.Reflection;
using System.Resources;
using System.Runtime.CompilerServices;
using System.Runtime.Versioning;
using System.Security;
using System.Security.Permissions;

[assembly: AssemblyMetadata("IsTrimmable", "True")]
[assembly: AssemblyCompany("Six Labors")]
[assembly: AssemblyConfiguration("Release")]
[assembly: AssemblyCopyright("Copyright © Six Labors")]
[assembly: AssemblyDescription("An extension to ImageSharp that allows the drawing of images, paths, and text.")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyInformationalVersion("2.1.4+b4a25da2660b4cc1c1023113bf5365537547fe2c")]
[assembly: AssemblyProduct("SixLabors.ImageSharp.Drawing")]
[assembly: AssemblyTitle("SixLabors.ImageSharp.Drawing")]
[assembly: AssemblyMetadata("RepositoryUrl", "https://github.com/SixLabors/ImageSharp.Drawing")]
[assembly: NeutralResourcesLanguage("en")]
[assembly: InternalsVisibleTo("DynamicProxyGenAssembly2, PublicKey=0024000004800000940000000602000000240000525341310004000001000100c547cac37abd99c8db225ef2f6c8a3602f3b3606cc9891605d02baa56104f4cfc0734aa39b93bf7852f7d9266654753cc297e7d2edfe0bac1cdcf9f717241550e0a7b191195b7667bb4f64bcb8e2121380fd1d9d46ad2d92d2d15605093924cceaf74c4861eff62abf69b9291ed0a340e113be11e6a7d3113e92484cf7045cc7")]
[assembly: InternalsVisibleTo("SixLabors.ImageSharp.Tests, PublicKey=00240000048000009400000006020000002400005253413100040000010001000147e6fe6766715eec6cfed61f1e7dcdbf69748a3e355c67e9d8dfd953acab1d5e012ba34b23308166fdc61ee1d0390d5f36d814a6091dd4b5ed9eda5a26afced924c683b4bfb4b3d64b0586a57eff9f02b1f84e3cb0ddd518bd1697f2c84dcbb97eb8bb5c7801be12112ed0ec86db934b0e9a5171e6bb1384b6d2f7d54dfa97")]
[assembly: InternalsVisibleTo("ImageSharp.Drawing.Benchmarks, PublicKey=00240000048000009400000006020000002400005253413100040000010001000147e6fe6766715eec6cfed61f1e7dcdbf69748a3e355c67e9d8dfd953acab1d5e012ba34b23308166fdc61ee1d0390d5f36d814a6091dd4b5ed9eda5a26afced924c683b4bfb4b3d64b0586a57eff9f02b1f84e3cb0ddd518bd1697f2c84dcbb97eb8bb5c7801be12112ed0ec86db934b0e9a5171e6bb1384b6d2f7d54dfa97")]
[assembly: AssemblyVersion("2.0.0.0")]
