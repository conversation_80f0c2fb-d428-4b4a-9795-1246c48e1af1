using System;
using System.IO;
using ICSharpCode.SharpZipLib.Zip;
using ICSharpCode.SharpZipLib.Zip.Compression.Streams;
using NPOI.Util;

namespace NPOI.OpenXml4Net.Util;

public class ZipSecureFile : ZipFile
{
	public class ThresholdInputStream : Stream
	{
		private long counter;

		private ThresholdInputStream cis;

		private Stream input;

		public override bool CanRead
		{
			get
			{
				throw new NotImplementedException();
			}
		}

		public override bool CanSeek
		{
			get
			{
				throw new NotImplementedException();
			}
		}

		public override bool CanWrite
		{
			get
			{
				throw new NotImplementedException();
			}
		}

		public override long Length
		{
			get
			{
				throw new NotImplementedException();
			}
		}

		public override long Position
		{
			get
			{
				throw new NotImplementedException();
			}
			set
			{
				throw new NotImplementedException();
			}
		}

		public ThresholdInputStream(Stream is1, ThresholdInputStream cis)
		{
			input = is1;
			this.cis = cis;
		}

		public int Read()
		{
			int num = input.ReadByte();
			if (num > -1)
			{
				Advance(1);
			}
			return num;
		}

		public override int Read(byte[] b, int off, int len)
		{
			int num = input.Read(b, off, len);
			if (num > -1)
			{
				Advance(num);
			}
			return num;
		}

		public long Skip(long n)
		{
			counter = 0L;
			return input.Seek(n, SeekOrigin.Current);
		}

		public void Reset()
		{
			counter = 0L;
			input.Seek(0L, SeekOrigin.Begin);
		}

		public void Advance(int advance)
		{
			counter += advance;
			if (counter < MAX_ENTRY_SIZE && (cis == null || (double)cis.counter / (double)counter >= MIN_INFLATE_RATIO))
			{
				return;
			}
			throw new IOException("Zip bomb detected! The file would exceed certain limits which usually indicate that the file is used to inflate memory usage and thus could pose a security risk. You can adjust these limits via setMinInflateRatio() and setMaxEntrySize() if you need to work with files which exceed these limits. Counter: " + counter + ", cis.counter: " + ((cis == null) ? 0 : cis.counter) + ", ratio: " + ((cis == null) ? 0.0 : ((double)cis.counter / (double)counter)) + "Limits: MIN_INFLATE_RATIO: " + MIN_INFLATE_RATIO + ", MAX_ENTRY_SIZE: " + MAX_ENTRY_SIZE);
		}

		public ZipEntry GetNextEntry()
		{
			if (!(input is ZipInputStream))
			{
				throw new NotSupportedException("underlying stream is not a ZipInputStream");
			}
			counter = 0L;
			return ((ZipInputStream)input).GetNextEntry();
		}

		public void CloseEntry()
		{
			if (!(input is ZipInputStream))
			{
				throw new NotSupportedException("underlying stream is not a ZipInputStream");
			}
			counter = 0L;
			((ZipInputStream)input).CloseEntry();
		}

		public void Unread(int b)
		{
			if (!(input is PushbackInputStream))
			{
				throw new NotSupportedException("underlying stream is not a PushbackInputStream");
			}
			if (--counter < 0)
			{
				counter = 0L;
			}
			((PushbackInputStream)input).Unread(b);
		}

		public void Unread(byte[] b, int off, int len)
		{
			if (!(input is PushbackInputStream))
			{
				throw new NotSupportedException("underlying stream is not a PushbackInputStream");
			}
			counter -= len;
			if (--counter < 0)
			{
				counter = 0L;
			}
			((PushbackInputStream)input).Unread(b, off, len);
		}

		public int Available()
		{
			return (int)(input.Length - input.Position);
		}

		public bool MarkSupported()
		{
			return true;
		}

		public void Mark(int readlimit)
		{
		}

		public override void Flush()
		{
			throw new NotImplementedException();
		}

		public override long Seek(long offset, SeekOrigin origin)
		{
			throw new NotImplementedException();
		}

		public override void SetLength(long value)
		{
			throw new NotImplementedException();
		}

		public override void Write(byte[] buffer, int offset, int count)
		{
			throw new NotImplementedException();
		}
	}

	private static double MIN_INFLATE_RATIO = 0.01;

	private static long MAX_ENTRY_SIZE = 4294967295L;

	public static void SetMinInflateRatio(double ratio)
	{
		MIN_INFLATE_RATIO = ratio;
	}

	public static double GetMinInflateRatio()
	{
		return MIN_INFLATE_RATIO;
	}

	public static void SetMaxEntrySize(long maxEntrySize)
	{
		if (maxEntrySize < 0 || maxEntrySize > uint.MaxValue)
		{
			throw new ArgumentException("Max entry size is bounded [0-4GB].");
		}
		MAX_ENTRY_SIZE = maxEntrySize;
	}

	public static long GetMaxEntrySize()
	{
		return MAX_ENTRY_SIZE;
	}

	public ZipSecureFile(FileStream file, int mode)
		: base(file)
	{
	}

	public ZipSecureFile(FileStream file)
		: base(file)
	{
	}

	public ZipSecureFile(string name)
		: base(name)
	{
	}

	public new Stream GetInputStream(ZipEntry entry)
	{
		return AddThreshold(base.GetInputStream(entry));
	}

	public static ThresholdInputStream AddThreshold(Stream zipIS)
	{
		ThresholdInputStream cis = null;
		if (zipIS is InflaterInputStream)
		{
			try
			{
				typeof(FilterInputStream).GetField("in");
			}
			catch (Exception)
			{
				cis = null;
			}
		}
		else
		{
			cis = null;
		}
		return new ThresholdInputStream(zipIS, cis);
	}
}
