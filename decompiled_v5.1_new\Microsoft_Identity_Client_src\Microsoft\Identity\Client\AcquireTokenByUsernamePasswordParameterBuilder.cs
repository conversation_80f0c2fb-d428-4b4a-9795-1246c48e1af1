using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Executors;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.AppConfig;
using Microsoft.Identity.Client.AuthScheme.PoP;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;

namespace Microsoft.Identity.Client;

public sealed class AcquireTokenByUsernamePasswordParameterBuilder : AbstractPublicClientAcquireTokenParameterBuilder<AcquireTokenByUsernamePasswordParameterBuilder>
{
	private AcquireTokenByUsernamePasswordParameters Parameters { get; } = new AcquireTokenByUsernamePasswordParameters();

	internal AcquireTokenByUsernamePasswordParameterBuilder(IPublicClientApplicationExecutor publicClientApplicationExecutor)
		: base(publicClientApplicationExecutor)
	{
	}

	internal static AcquireTokenByUsernamePasswordParameterBuilder Create(IPublicClientApplicationExecutor publicClientApplicationExecutor, IEnumerable<string> scopes, string username, string password)
	{
		return new AcquireTokenByUsernamePasswordParameterBuilder(publicClientApplicationExecutor).WithScopes(scopes).WithUsername(username).WithPassword(password);
	}

	public AcquireTokenByUsernamePasswordParameterBuilder WithFederationMetadata(string federationMetadata)
	{
		Parameters.FederationMetadata = federationMetadata;
		return this;
	}

	public AcquireTokenByUsernamePasswordParameterBuilder WithProofOfPossession(string nonce, HttpMethod httpMethod, Uri requestUri)
	{
		ApplicationBase.GuardMobileFrameworks();
		if (!base.ServiceBundle.Config.IsBrokerEnabled)
		{
			throw new MsalClientException("broker_required_for_pop", "The request has Proof-of-Possession configured but does not have broker enabled. Broker is required to use Proof-of-Possession on public clients. Use IPublicClientApplication.IsProofOfPossessionSupportedByClient to ensure Proof-of-Possession can be performed before using WithProofOfPossession.");
		}
		if (!base.ServiceBundle.PlatformProxy.CreateBroker(base.ServiceBundle.Config, null).IsPopSupported)
		{
			throw new MsalClientException("broker_does_not_support_pop", "The broker does not support Proof-of-Possession on the current platform.");
		}
		if (string.IsNullOrEmpty(nonce))
		{
			throw new ArgumentNullException("nonce");
		}
		PoPAuthenticationConfiguration poPAuthenticationConfiguration = new PoPAuthenticationConfiguration(requestUri);
		poPAuthenticationConfiguration.Nonce = nonce;
		poPAuthenticationConfiguration.HttpMethod = httpMethod;
		base.CommonParameters.PopAuthenticationConfiguration = poPAuthenticationConfiguration;
		base.CommonParameters.AuthenticationScheme = new PopBrokerAuthenticationScheme();
		return this;
	}

	private AcquireTokenByUsernamePasswordParameterBuilder WithUsername(string username)
	{
		Parameters.Username = username;
		return this;
	}

	private AcquireTokenByUsernamePasswordParameterBuilder WithPassword(string password)
	{
		Parameters.Password = password;
		return this;
	}

	internal override Task<AuthenticationResult> ExecuteInternalAsync(CancellationToken cancellationToken)
	{
		return base.PublicClientApplicationExecutor.ExecuteAsync(base.CommonParameters, Parameters, cancellationToken);
	}

	internal override ApiEvent.ApiIds CalculateApiEventId()
	{
		return ApiEvent.ApiIds.AcquireTokenByUsernamePassword;
	}
}
