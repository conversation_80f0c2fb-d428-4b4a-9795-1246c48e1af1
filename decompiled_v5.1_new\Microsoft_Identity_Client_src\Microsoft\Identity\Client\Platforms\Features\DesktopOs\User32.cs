using System;
using System.Runtime.InteropServices;

namespace Microsoft.Identity.Client.Platforms.Features.DesktopOs;

internal static class User32
{
	private const string LibraryName = "user32.dll";

	public const int UOI_FLAGS = 1;

	public const int WSF_VISIBLE = 1;

	[DllImport("user32.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	public static extern IntPtr GetProcessWindowStation();

	[DllImport("user32.dll", CallingConvention = CallingConvention.StdCall, SetLastError = true)]
	public unsafe static extern bool GetUserObjectInformation(IntPtr hObj, int nIndex, void* pvBuffer, uint nLength, ref uint lpnLengthNeeded);
}
