using System;
using System.Collections;
using System.Collections.Generic;
using EnumsNET.Numerics;

namespace EnumsNET;

internal sealed class NonGenericValuesContainer<TEnum, TUnderlying, TUnderlyingOperations> : IReadOnlyList<object>, IEnumerable<object>, IEnumerable, IReadOnlyCollection<object> where TEnum : struct, Enum where TUnderlying : struct, IComparable<TUnderlying>, IEquatable<TUnderlying>, IConvertible where TUnderlyingOperations : struct, IUnderlyingOperations<TUnderlying>
{
	private readonly ValuesContainer<TEnum, TUnderlying, TUnderlyingOperations> _container;

	public object this[int index] => _container[index];

	public int Count => _container.Count;

	public NonGenericValuesContainer(ValuesContainer<TEnum, TUnderlying, TUnderlyingOperations> container)
	{
		_container = container;
	}

	public IEnumerator<object> GetEnumerator()
	{
		foreach (TEnum item in _container)
		{
			yield return item;
		}
	}

	IEnumerator IEnumerable.GetEnumerator()
	{
		return GetEnumerator();
	}
}
