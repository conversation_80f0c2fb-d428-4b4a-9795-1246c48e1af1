using System;
using Microsoft.Identity.Client.AuthScheme;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Region;

namespace Microsoft.Identity.Client.TelemetryCore.Internal.Events;

internal class ApiEvent
{
	public enum ApiIds
	{
		None = 0,
		AcquireTokenByAuthorizationCode = 1000,
		AcquireTokenByRefreshToken = 1001,
		AcquireTokenByIntegratedWindowsAuth = 1002,
		AcquireTokenByUsernamePassword = 1003,
		AcquireTokenForClient = 1004,
		AcquireTokenInteractive = 1005,
		AcquireTokenOnBehalfOf = 1006,
		AcquireTokenSilent = 1007,
		AcquireTokenByDeviceCode = 1008,
		GetAuthorizationRequestUrl = 1009,
		GetAccounts = 1010,
		GetAccountById = 1011,
		GetAccountsByUserFlow = 1012,
		RemoveAccount = 1013,
		RemoveOboTokens = 1014,
		AcquireTokenForSystemAssignedManagedIdentity = 1015,
		AcquireTokenForUserAssignedManagedIdentity = 1016,
		InitiateLongRunningObo = 1017,
		AcquireTokenInLongRunningObo = 1018
	}

	private RegionAutodetectionSource? _regionAutodetectionSource;

	private RegionOutcome? _regionOutcome;

	private CacheRefreshReason? _cacheInfo;

	public Guid CorrelationId { get; set; }

	public ApiIds ApiId { get; set; }

	public string ApiIdString => ApiId.ToString("D");

	public string TokenEndpoint { get; set; }

	public bool IsAccessTokenCacheHit { get; set; }

	public string ApiErrorCode { get; set; }

	public string RegionUsed { get; set; }

	public RegionAutodetectionSource RegionAutodetectionSource
	{
		get
		{
			return _regionAutodetectionSource.GetValueOrDefault();
		}
		set
		{
			_regionAutodetectionSource = value;
		}
	}

	public string RegionAutodetectionSourceString
	{
		get
		{
			if (!_regionAutodetectionSource.HasValue)
			{
				return null;
			}
			return _regionAutodetectionSource.Value.ToString("D");
		}
	}

	public RegionOutcome RegionOutcome
	{
		get
		{
			return _regionOutcome.GetValueOrDefault();
		}
		set
		{
			_regionOutcome = value;
		}
	}

	public string RegionOutcomeString
	{
		get
		{
			if (!_regionOutcome.HasValue)
			{
				return null;
			}
			return _regionOutcome.Value.ToString("D");
		}
	}

	public string AutoDetectedRegion { get; internal set; }

	public string RegionDiscoveryFailureReason { get; set; }

	public bool IsTokenCacheSerialized { get; set; }

	public char IsTokenCacheSerializedString
	{
		get
		{
			if (!IsTokenCacheSerialized)
			{
				return '0';
			}
			return '1';
		}
	}

	public bool IsLegacyCacheEnabled { get; set; }

	public char IsLegacyCacheEnabledString
	{
		get
		{
			if (!IsLegacyCacheEnabled)
			{
				return '0';
			}
			return '1';
		}
	}

	public CacheRefreshReason CacheInfo
	{
		get
		{
			return _cacheInfo.GetValueOrDefault();
		}
		set
		{
			_cacheInfo = value;
		}
	}

	public string CacheInfoString
	{
		get
		{
			if (!_cacheInfo.HasValue)
			{
				return null;
			}
			return _cacheInfo.Value.ToString("D");
		}
	}

	public long DurationInHttpInMs { get; set; }

	public long DurationInCacheInMs { get; set; }

	public TokenType? TokenType { get; set; }

	public string TokenTypeString
	{
		get
		{
			if (!TokenType.HasValue)
			{
				return null;
			}
			return TokenType.Value.ToString("D");
		}
	}

	public AssertionType AssertionType { get; set; }

	public CacheLevel CacheLevel { get; set; }

	public string MsalRuntimeTelemetry { get; set; }

	public ApiEvent(Guid correlationId)
	{
		CorrelationId = correlationId;
	}

	public static bool IsLongRunningObo(ApiIds apiId)
	{
		if (apiId != ApiIds.InitiateLongRunningObo)
		{
			return apiId == ApiIds.AcquireTokenInLongRunningObo;
		}
		return true;
	}

	public static bool IsOnBehalfOfRequest(ApiIds apiId)
	{
		if (apiId != ApiIds.AcquireTokenOnBehalfOf)
		{
			return IsLongRunningObo(apiId);
		}
		return true;
	}
}
