using System;
using System.Runtime.InteropServices;

namespace Microsoft.Identity.Client.Platforms.Features.DesktopOs;

internal static class LibSystem
{
	private const string LibSystemLib = "/System/Library/Frameworks/System.framework/System";

	[DllImport("/System/Library/Frameworks/System.framework/System", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public static extern IntPtr dlopen(string name, int flags);

	[DllImport("/System/Library/Frameworks/System.framework/System", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public static extern IntPtr dlsym(IntPtr handle, string symbol);

	public static IntPtr GetGlobal(IntPtr handle, string symbol)
	{
		return Marshal.PtrToStructure<IntPtr>(dlsym(handle, symbol));
	}
}
