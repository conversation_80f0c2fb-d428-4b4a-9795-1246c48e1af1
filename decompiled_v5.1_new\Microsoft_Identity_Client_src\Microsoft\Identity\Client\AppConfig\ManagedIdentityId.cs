using System;

namespace Microsoft.Identity.Client.AppConfig;

public class ManagedIdentityId
{
	internal string UserAssignedId { get; private set; }

	internal ManagedIdentityIdType IdType { get; }

	internal bool IsUserAssigned { get; }

	public static ManagedIdentityId SystemAssigned { get; } = new ManagedIdentityId(ManagedIdentityIdType.SystemAssigned);

	private ManagedIdentityId(ManagedIdentityIdType idType)
	{
		IdType = idType;
		IsUserAssigned = idType != ManagedIdentityIdType.SystemAssigned;
	}

	public static ManagedIdentityId WithUserAssignedClientId(string clientId)
	{
		if (string.IsNullOrEmpty(clientId))
		{
			throw new ArgumentNullException(clientId);
		}
		return new ManagedIdentityId(ManagedIdentityIdType.ClientId)
		{
			UserAssignedId = clientId
		};
	}

	public static ManagedIdentityId WithUserAssignedResourceId(string resourceId)
	{
		if (string.IsNullOrEmpty(resourceId))
		{
			throw new ArgumentNullException(resourceId);
		}
		return new ManagedIdentityId(ManagedIdentityIdType.ResourceId)
		{
			UserAssignedId = resourceId
		};
	}

	public static ManagedIdentityId WithUserAssignedObjectId(string objectId)
	{
		if (string.IsNullOrEmpty(objectId))
		{
			throw new ArgumentNullException(objectId);
		}
		return new ManagedIdentityId(ManagedIdentityIdType.ObjectId)
		{
			UserAssignedId = objectId
		};
	}
}
