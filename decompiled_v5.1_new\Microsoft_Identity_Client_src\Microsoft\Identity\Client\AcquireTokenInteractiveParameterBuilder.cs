using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Executors;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.AppConfig;
using Microsoft.Identity.Client.AuthScheme.PoP;
using Microsoft.Identity.Client.Extensibility;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;

namespace Microsoft.Identity.Client;

public sealed class AcquireTokenInteractiveParameterBuilder : AbstractPublicClientAcquireTokenParameterBuilder<AcquireTokenInteractiveParameterBuilder>
{
	private AcquireTokenInteractiveParameters Parameters { get; } = new AcquireTokenInteractiveParameters();

	internal AcquireTokenInteractiveParameterBuilder(IPublicClientApplicationExecutor publicClientApplicationExecutor)
		: base(publicClientApplicationExecutor)
	{
	}

	internal void SetCustomWebUi(ICustomWebUi customWebUi)
	{
		Parameters.CustomWebUi = customWebUi;
	}

	internal static AcquireTokenInteractiveParameterBuilder Create(IPublicClientApplicationExecutor publicClientApplicationExecutor, IEnumerable<string> scopes)
	{
		return new AcquireTokenInteractiveParameterBuilder(publicClientApplicationExecutor).WithCurrentSynchronizationContext().WithScopes(scopes);
	}

	internal AcquireTokenInteractiveParameterBuilder WithCurrentSynchronizationContext()
	{
		Parameters.UiParent.SynchronizationContext = SynchronizationContext.Current;
		return this;
	}

	internal AcquireTokenInteractiveParameterBuilder WithParentActivityOrWindowFunc(Func<object> parentActivityOrWindowFunc)
	{
		if (parentActivityOrWindowFunc != null)
		{
			WithParentActivityOrWindow(parentActivityOrWindowFunc());
		}
		return this;
	}

	public AcquireTokenInteractiveParameterBuilder WithUseEmbeddedWebView(bool useEmbeddedWebView)
	{
		Parameters.UseEmbeddedWebView = (useEmbeddedWebView ? WebViewPreference.Embedded : WebViewPreference.System);
		return this;
	}

	public AcquireTokenInteractiveParameterBuilder WithSystemWebViewOptions(SystemWebViewOptions options)
	{
		SystemWebViewOptions.ValidatePlatformAvailability();
		Parameters.UiParent.SystemWebViewOptions = options;
		return this;
	}

	public AcquireTokenInteractiveParameterBuilder WithEmbeddedWebViewOptions(EmbeddedWebViewOptions options)
	{
		EmbeddedWebViewOptions.ValidatePlatformAvailability();
		Parameters.UiParent.EmbeddedWebviewOptions = options;
		return this;
	}

	public AcquireTokenInteractiveParameterBuilder WithLoginHint(string loginHint)
	{
		Parameters.LoginHint = loginHint;
		return this;
	}

	public AcquireTokenInteractiveParameterBuilder WithAccount(IAccount account)
	{
		Parameters.Account = account;
		return this;
	}

	public AcquireTokenInteractiveParameterBuilder WithExtraScopesToConsent(IEnumerable<string> extraScopesToConsent)
	{
		Parameters.ExtraScopesToConsent = extraScopesToConsent;
		return this;
	}

	public AcquireTokenInteractiveParameterBuilder WithPrompt(Prompt prompt)
	{
		Parameters.Prompt = prompt;
		return this;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	public AcquireTokenInteractiveParameterBuilder WithParentActivityOrWindow(object parent)
	{
		return WithParentObject(parent);
	}

	private AcquireTokenInteractiveParameterBuilder WithParentObject(object parent)
	{
		if (parent is IntPtr intPtr)
		{
			Parameters.UiParent.OwnerWindow = intPtr;
		}
		return this;
	}

	[CLSCompliant(false)]
	public AcquireTokenInteractiveParameterBuilder WithParentActivityOrWindow(IntPtr window)
	{
		return WithParentObject(window);
	}

	public AcquireTokenInteractiveParameterBuilder WithProofOfPossession(string nonce, HttpMethod httpMethod, Uri requestUri)
	{
		ApplicationBase.GuardMobileFrameworks();
		if (!base.ServiceBundle.Config.IsBrokerEnabled)
		{
			throw new MsalClientException("broker_required_for_pop", "The request has Proof-of-Possession configured but does not have broker enabled. Broker is required to use Proof-of-Possession on public clients. Use IPublicClientApplication.IsProofOfPossessionSupportedByClient to ensure Proof-of-Possession can be performed before using WithProofOfPossession.");
		}
		if (!base.ServiceBundle.PlatformProxy.CreateBroker(base.ServiceBundle.Config, null).IsPopSupported)
		{
			throw new MsalClientException("broker_does_not_support_pop", "The broker does not support Proof-of-Possession on the current platform.");
		}
		PoPAuthenticationConfiguration poPAuthenticationConfiguration = new PoPAuthenticationConfiguration(requestUri);
		if (string.IsNullOrEmpty(nonce))
		{
			throw new ArgumentNullException("nonce");
		}
		poPAuthenticationConfiguration.Nonce = nonce;
		poPAuthenticationConfiguration.HttpMethod = httpMethod;
		base.CommonParameters.PopAuthenticationConfiguration = poPAuthenticationConfiguration;
		base.CommonParameters.AuthenticationScheme = new PopBrokerAuthenticationScheme();
		return this;
	}

	protected override void Validate()
	{
		base.Validate();
		if (Parameters.UiParent.SystemWebViewOptions != null && Parameters.UseEmbeddedWebView == WebViewPreference.Embedded)
		{
			throw new MsalClientException("embedded_webview_not_compatible_default_browser", "You configured MSAL interactive authentication to use an embedded WebView and you also configured system WebView options. These are mutually exclusive. See https://aka.ms/msal-net-os-browser. ");
		}
		if (Parameters.UiParent.SystemWebViewOptions != null && Parameters.UseEmbeddedWebView == WebViewPreference.NotSpecified)
		{
			WithUseEmbeddedWebView(useEmbeddedWebView: false);
		}
		Parameters.LoginHint = ((!string.IsNullOrWhiteSpace(Parameters.LoginHint)) ? Parameters.LoginHint : Parameters.Account?.Username);
	}

	internal override Task<AuthenticationResult> ExecuteInternalAsync(CancellationToken cancellationToken)
	{
		return base.PublicClientApplicationExecutor.ExecuteAsync(base.CommonParameters, Parameters, cancellationToken);
	}

	internal override ApiEvent.ApiIds CalculateApiEventId()
	{
		return ApiEvent.ApiIds.AcquireTokenInteractive;
	}
}
