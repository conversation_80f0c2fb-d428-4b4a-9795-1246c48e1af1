using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using ExtendedNumerics.Properties;

namespace ExtendedNumerics.Helpers;

public static class BigIntegerHelper
{
	public static class FastFactorial
	{
		public static BigInteger Factorial(BigInteger value)
		{
			if (value == BigInteger.Zero || value == BigInteger.One)
			{
				return BigInteger.One;
			}
			return MultiplyRange(Two, value);
		}

		private static BigInteger MultiplyRange(BigInteger from, BigInteger to)
		{
			BigInteger bigInteger = to - from;
			if (bigInteger == BigInteger.One)
			{
				return from * to;
			}
			if (bigInteger == BigInteger.Zero)
			{
				return from;
			}
			BigInteger bigInteger2 = (from + to) / Two;
			return BigInteger.Multiply(MultiplyRange(from, bigInteger2), MultiplyRange(bigInteger2 + BigInteger.One, to));
		}
	}

	public static readonly BigInteger Two = 2;

	public static readonly BigInteger Ten = 10;

	public static BigInteger GCD(this IEnumerable<BigInteger> numbers)
	{
		return numbers.Aggregate(GCD);
	}

	public static BigInteger GCD(BigInteger value1, BigInteger value2)
	{
		BigInteger bigInteger = BigInteger.Abs(value1);
		BigInteger bigInteger2 = BigInteger.Abs(value2);
		while (bigInteger != BigInteger.Zero && bigInteger2 != BigInteger.Zero)
		{
			if (bigInteger > bigInteger2)
			{
				bigInteger %= bigInteger2;
			}
			else
			{
				bigInteger2 %= bigInteger;
			}
		}
		return BigInteger.Max(bigInteger, bigInteger2);
	}

	public static int GetLength(this BigInteger source)
	{
		int num = 0;
		BigInteger bigInteger = BigInteger.Abs(source);
		while (bigInteger > BigInteger.Zero)
		{
			bigInteger /= Ten;
			num++;
		}
		return num;
	}

	public static IEnumerable<BigInteger> GetRange(BigInteger min, BigInteger max)
	{
		while (min < max)
		{
			yield return min;
			++min;
		}
	}

	public static int GetSignifigantDigits(this BigInteger value)
	{
		if (value.IsZero)
		{
			return 0;
		}
		string text = value.ToString().TrimEnd('0');
		if (string.IsNullOrEmpty(text))
		{
			return 0;
		}
		if (value < BigInteger.Zero)
		{
			return text.Length - 1;
		}
		return text.Length;
	}

	public static bool IsCoprime(BigInteger value1, BigInteger value2)
	{
		return GCD(value1, value2) == BigInteger.One;
	}

	public static BigInteger LCM(IEnumerable<BigInteger> numbers)
	{
		return numbers.Aggregate(LCM);
	}

	public static BigInteger LCM(BigInteger num1, BigInteger num2)
	{
		BigInteger bigInteger = BigInteger.Abs(num1);
		BigInteger bigInteger2 = BigInteger.Abs(num2);
		return bigInteger * bigInteger2 / GCD(bigInteger, bigInteger2);
	}

	public static BigInteger NthRoot(this BigInteger value, int root, out BigInteger remainder)
	{
		if (root < 1)
		{
			throw new ArgumentException(LanguageResources.Arg_MustBeGreaterThanOrEqualToOne, "root");
		}
		if (value.Sign == -1)
		{
			throw new ArgumentException(LanguageResources.Arg_MustBeAPositiveInteger, "value");
		}
		if (value == BigInteger.One)
		{
			remainder = BigInteger.Zero;
			return BigInteger.One;
		}
		if (value == BigInteger.Zero)
		{
			remainder = BigInteger.Zero;
			return BigInteger.Zero;
		}
		if (root == 1)
		{
			remainder = BigInteger.Zero;
			return value;
		}
		BigInteger bigInteger = value;
		BigInteger bigInteger2 = BigInteger.Zero;
		do
		{
			BigInteger bigInteger3 = bigInteger + bigInteger2 >> 1;
			BigInteger bigInteger4 = BigInteger.Pow(bigInteger3, root);
			if (bigInteger4 > value)
			{
				bigInteger = bigInteger3;
			}
			if (bigInteger4 < value)
			{
				bigInteger2 = bigInteger3;
			}
			if (bigInteger4 == value)
			{
				bigInteger2 = bigInteger3;
				break;
			}
		}
		while (bigInteger2 != bigInteger - BigInteger.One);
		remainder = value - BigInteger.Pow(bigInteger2, root);
		return bigInteger2;
	}

	public static BigInteger Square(this BigInteger input)
	{
		return input * input;
	}

	public static BigInteger SquareRoot(this BigInteger input)
	{
		if (input.IsZero)
		{
			return BigInteger.Zero;
		}
		BigInteger bigInteger = BigInteger.Zero;
		BigInteger bigInteger2 = BigInteger.Zero;
		BigInteger bigInteger3 = BigInteger.Zero;
		BigInteger bigInteger4 = BigInteger.Abs(input);
		while (bigInteger4 > bigInteger3 + BigInteger.One)
		{
			bigInteger = bigInteger4 + bigInteger3 >> 1;
			bigInteger2 = bigInteger * bigInteger;
			if (input < bigInteger2)
			{
				bigInteger4 = bigInteger;
				continue;
			}
			if (!(input > bigInteger2))
			{
				break;
			}
			bigInteger3 = bigInteger;
		}
		if (!(input == bigInteger2))
		{
			return bigInteger3;
		}
		return bigInteger;
	}

	public static bool TryParseFraction(this string numberString, out BigDecimal? result)
	{
		result = null;
		if (string.IsNullOrWhiteSpace(numberString))
		{
			return false;
		}
		List<string> list = (from s in numberString.Split('/', StringSplitOptions.RemoveEmptyEntries)
			select s.Trim()).ToList();
		if (list.Count != 2)
		{
			return false;
		}
		try
		{
			BigDecimal dividend = BigDecimal.Parse(list[0]);
			BigDecimal divisor = BigDecimal.Parse(list[1]);
			result = BigDecimal.Divide(dividend, divisor);
			return true;
		}
		catch (Exception)
		{
			return false;
		}
	}
}
