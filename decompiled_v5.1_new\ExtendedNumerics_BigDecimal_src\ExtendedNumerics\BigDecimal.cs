using System;
using System.Globalization;
using System.Linq;
using System.Numerics;
using System.Runtime.CompilerServices;
using System.Text;
using ExtendedNumerics.Helpers;
using ExtendedNumerics.Properties;

namespace ExtendedNumerics;

public readonly record struct BigDecimal : IComparable, IComparable<BigDecimal>, IComparable<int>, IComparable<int?>, IComparable<decimal>, IComparable<double>, IComparable<float>
{
	public static BigDecimal Ten => 10;

	public static BigDecimal One => 1;

	public static BigDecimal Zero => 0;

	public static BigDecimal OneHalf => 0.5;

	public static BigDecimal MinusOne => -1;

	public static BigDecimal E { get; }

	public static BigDecimal Pi { get; }

	public static BigDecimal π { get; }

	private static BigInteger TenInt { get; }

	private static NumberFormatInfo BigDecimalNumberFormatInfo { get; }

	public static int Precision { get; set; }

	public static bool AlwaysTruncate { get; set; }

	public static bool AlwaysNormalize { get; set; }

	public int Sign => Mantissa.Sign;

	public int SignifigantDigits => GetSignifigantDigits(Mantissa);

	public int Length => GetSignifigantDigits(Mantissa) + Exponent;

	public int DecimalPlaces => PlacesRightOfDecimal(this);

	public BigInteger WholeValue => GetWholePart();

	private static BigDecimal MaxBigDecimalForDecimal => decimal.MaxValue;

	private static BigDecimal MaxBigDecimalForSingle => float.MaxValue;

	private static BigDecimal MaxBigDecimalForDouble => double.MaxValue;

	private static BigDecimal MaxBigDemicalForInt32 => int.MaxValue;

	private static BigDecimal MaxBigDemicalForUInt32 => uint.MaxValue;

	private const string NullString = "(null)";

	public readonly BigInteger Mantissa;

	public readonly int Exponent;

	private static double ExpChunk;

	private static BigDecimal ChunkSize;

	static BigDecimal()
	{
		TenInt = new BigInteger(10);
		BigDecimalNumberFormatInfo = CultureInfo.CurrentCulture.NumberFormat;
		Precision = 5000;
		AlwaysTruncate = false;
		AlwaysNormalize = true;
		ExpChunk = 2.0;
		ChunkSize = 1000000000;
		Pi = Parse("3.14159265358979323846264338327950288419716939937510582097494459230781640628620899862803482534211706798214808651328230664709384460955058223172535940812848111745028410270193852110555964462294895493038196", CultureInfo.InvariantCulture);
		π = Pi;
		E = Parse("2.71828182845904523536028747135266249775724709369995957496696762772407663035354759457138217852516642749193200305992181741359662904357290033429526059563073813232862794349076323382988075319525101901157383", CultureInfo.InvariantCulture);
	}

	private BigDecimal(Tuple<BigInteger, int> tuple)
	{
		Mantissa = tuple.Item1;
		Exponent = tuple.Item2;
	}

	public BigDecimal(BigInteger numerator, BigInteger denominator)
	{
		BigDecimal bigDecimal = Divide(new BigDecimal(numerator), new BigDecimal(denominator));
		Mantissa = bigDecimal.Mantissa;
		Exponent = bigDecimal.Exponent;
	}

	public BigDecimal(BigInteger mantissa, int exponent = 0)
	{
		Mantissa = mantissa;
		Exponent = exponent;
		if (AlwaysTruncate)
		{
			BigDecimal bigDecimal = Round(this, Precision);
			Mantissa = bigDecimal.Mantissa;
			Exponent = bigDecimal.Exponent;
		}
		if (AlwaysNormalize)
		{
			BigDecimal bigDecimal = Normalize(this);
			Mantissa = bigDecimal.Mantissa;
			Exponent = bigDecimal.Exponent;
		}
		if (Mantissa == 0L)
		{
			Exponent = 0;
		}
	}

	public BigDecimal(int value)
		: this(new BigInteger(value))
	{
	}

	public BigDecimal(float value)
	{
		if (float.IsInfinity(value))
		{
			throw new OverflowException(LanguageResources.Overflow_BigDecimal_Infinity);
		}
		if (float.IsNaN(value))
		{
			throw new NotFiniteNumberException(LanguageResources.NotFinite_NaN);
		}
		BigInteger mantissa = new BigInteger(value);
		int num = 0;
		float num2 = 1f;
		while (Math.Abs(value * num2 - float.Parse(mantissa.ToString())) > 0f)
		{
			num--;
			num2 *= 10f;
			mantissa = new BigInteger(value * num2);
		}
		Mantissa = mantissa;
		Exponent = num;
		if (AlwaysTruncate)
		{
			BigDecimal bigDecimal = Round(this, Precision);
			Mantissa = bigDecimal.Mantissa;
			Exponent = bigDecimal.Exponent;
		}
		if (AlwaysNormalize)
		{
			BigDecimal bigDecimal = Normalize(this);
			Mantissa = bigDecimal.Mantissa;
			Exponent = bigDecimal.Exponent;
		}
	}

	public BigDecimal(double value)
	{
		if (double.IsInfinity(value))
		{
			throw new OverflowException(LanguageResources.Overflow_BigDecimal_Infinity);
		}
		if (double.IsNaN(value))
		{
			throw new NotFiniteNumberException(LanguageResources.NotFinite_NaN);
		}
		BigInteger mantissa = new BigInteger(value);
		int num = 0;
		double num2 = 1.0;
		while (Math.Abs(value * num2 - double.Parse(mantissa.ToString())) > 0.0)
		{
			num--;
			num2 *= 10.0;
			mantissa = new BigInteger(value * num2);
		}
		Mantissa = mantissa;
		Exponent = num;
		if (AlwaysTruncate)
		{
			BigDecimal bigDecimal = Round(this, Precision);
			Mantissa = bigDecimal.Mantissa;
			Exponent = bigDecimal.Exponent;
		}
		if (AlwaysNormalize)
		{
			BigDecimal bigDecimal = Normalize(this);
			Mantissa = bigDecimal.Mantissa;
			Exponent = bigDecimal.Exponent;
		}
	}

	public BigDecimal(decimal value)
	{
		BigInteger mantissa = new BigInteger(value);
		int num = 0;
		decimal num2 = 1m;
		while (decimal.Parse(mantissa.ToString()) != value * num2)
		{
			num--;
			num2 *= 10m;
			mantissa = new BigInteger(value * num2);
		}
		Mantissa = mantissa;
		Exponent = num;
	}

	private static int GetSignifigantDigits(BigInteger value)
	{
		return value.GetSignifigantDigits();
	}

	public bool IsZero()
	{
		return Mantissa.IsZero;
	}

	public bool IsPositve()
	{
		if (!IsZero())
		{
			return !IsNegative();
		}
		return false;
	}

	public bool IsNegative()
	{
		return Mantissa.Sign < 0;
	}

	public int CompareTo(BigDecimal other)
	{
		if (!(this < other))
		{
			return (this > other) ? ((sbyte)1) : ((sbyte)0);
		}
		return -1;
	}

	public int CompareTo(decimal other)
	{
		if (!(this < other))
		{
			return (this > other) ? ((sbyte)1) : ((sbyte)0);
		}
		return -1;
	}

	public int CompareTo(double other)
	{
		if (!(this < other))
		{
			return (this > other) ? ((sbyte)1) : ((sbyte)0);
		}
		return -1;
	}

	public int CompareTo(float other)
	{
		if (!(this < other))
		{
			return (this > other) ? ((sbyte)1) : ((sbyte)0);
		}
		return -1;
	}

	public int CompareTo(int? other)
	{
		if (other.HasValue)
		{
			BigDecimal value = this;
			BigDecimal? bigDecimal = other;
			if (!(value > bigDecimal))
			{
				value = this;
				BigDecimal? bigDecimal2 = other;
				if (!(value < bigDecimal2))
				{
					return 0;
				}
				return -1;
			}
		}
		return 1;
	}

	public int CompareTo(int other)
	{
		if (!(this < other))
		{
			return (this > other) ? ((sbyte)1) : ((sbyte)0);
		}
		return -1;
	}

	int IComparable.CompareTo(object? obj)
	{
		if (obj == null)
		{
			return 1;
		}
		if (!(obj is BigDecimal))
		{
			throw new ArgumentException(string.Format(LanguageResources.Arg_MustBeOfType, "BigDecimal"), "obj");
		}
		return CompareTo((BigDecimal)obj);
	}

	public bool Equals(BigDecimal? other)
	{
		return Equals(this, other);
	}

	public static bool Equals(BigDecimal? left, BigDecimal? right)
	{
		if (!left.HasValue || !right.HasValue)
		{
			return false;
		}
		BigDecimal value = left.Value;
		BigDecimal value2 = right.Value;
		if (AlwaysTruncate)
		{
			value = Round(value, Precision);
			value2 = Round(value2, Precision);
		}
		if (AlwaysNormalize)
		{
			value = Normalize(value);
			value2 = Normalize(value2);
		}
		if (value.Mantissa.Equals(value2.Mantissa) && value.Exponent.Equals(value2.Exponent))
		{
			return value.Sign.Equals(value2.Sign);
		}
		return false;
	}

	public static BigDecimal Parse(double input)
	{
		return Parse(input.ToString(CultureInfo.CurrentCulture));
	}

	public static BigDecimal Parse(string input)
	{
		return Parse(input, BigDecimalNumberFormatInfo);
	}

	public static BigDecimal Parse(string input, IFormatProvider provider)
	{
		if (provider == null)
		{
			provider = BigDecimalNumberFormatInfo;
		}
		NumberFormatInfo instance = NumberFormatInfo.GetInstance(provider);
		input = input.Trim();
		if (string.IsNullOrEmpty(input))
		{
			return BigInteger.Zero;
		}
		int result = 0;
		bool flag = false;
		if (input.StartsWith(instance.NegativeSign, StringComparison.OrdinalIgnoreCase))
		{
			flag = true;
			input = input.TrimStart(new char[1] { instance.NegativeSign.Single() });
		}
		int num = input.LastIndexOf('E') + 1;
		if (num > 0 && int.TryParse(input.Substring(num), out result))
		{
			input = input.Substring(0, num - 1);
		}
		if (input.Contains(instance.NumberDecimalSeparator))
		{
			int num2 = input.IndexOf(instance.NumberDecimalSeparator, StringComparison.Ordinal);
			result += num2 + 1 - input.Length;
			input = input.Replace(instance.NumberDecimalSeparator, string.Empty);
		}
		BigInteger bigInteger = BigInteger.Parse(input, instance);
		if (flag)
		{
			bigInteger = BigInteger.Negate(bigInteger);
		}
		BigDecimal bigDecimal = new BigDecimal(new Tuple<BigInteger, int>(bigInteger, result));
		if (AlwaysTruncate)
		{
			bigDecimal = Round(bigDecimal, Precision);
		}
		if (AlwaysNormalize)
		{
			bigDecimal = Normalize(bigDecimal);
		}
		return bigDecimal;
	}

	public static bool TryParse(string input, out BigDecimal result)
	{
		return TryParse(input, BigDecimalNumberFormatInfo, out result);
	}

	public static bool TryParse(string input, IFormatProvider provider, out BigDecimal result)
	{
		try
		{
			BigDecimal bigDecimal = Parse(input, provider);
			result = bigDecimal;
			return true;
		}
		catch
		{
		}
		result = default(BigDecimal);
		return false;
	}

	public static int NumberOfDigits(BigInteger value)
	{
		return (int)Math.Ceiling(BigInteger.Log10(value * value.Sign));
	}

	public static BigDecimal Normalize(BigDecimal value)
	{
		if (value.Mantissa.IsZero)
		{
			if (value.Exponent != 0)
			{
				return new BigDecimal(new Tuple<BigInteger, int>(BigInteger.Zero, 0));
			}
			return value;
		}
		string text = value.Mantissa.ToString();
		int num = text.LastIndexOf('0', text.Length - 1);
		if (num < text.Length - 1)
		{
			return value;
		}
		char c = text[num];
		while (num > 0 && c == '0')
		{
			c = text[--num];
		}
		string value2 = text.Substring(0, num + 1);
		string text2 = text.Substring(num + 1);
		if (BigInteger.TryParse(value2, out var result))
		{
			return new BigDecimal(new Tuple<BigInteger, int>(result, value.Exponent + text2.Length));
		}
		return value;
	}

	public int GetDecimalIndex()
	{
		int num = Mantissa.GetLength();
		if (Mantissa.Sign < 0)
		{
			num++;
		}
		return num + Exponent;
	}

	public BigInteger GetWholePart()
	{
		string text = string.Empty;
		string[] array = ToString(BigDecimalNumberFormatInfo).Split(BigDecimalNumberFormatInfo.NumberDecimalSeparator.ToCharArray());
		if (array.Length != 0)
		{
			text = array[0];
		}
		if (text.IndexOf("E", StringComparison.Ordinal) > 0)
		{
			text = text.Split('E')[0];
		}
		return BigInteger.Parse(text);
	}

	public BigDecimal GetFractionalPart()
	{
		string text = string.Empty;
		string[] array = ToString().Split(BigDecimalNumberFormatInfo.NumberDecimalSeparator.ToCharArray());
		if (array.Length == 1)
		{
			return Zero;
		}
		if (array.Length == 2)
		{
			text = array[1];
		}
		BigInteger mantissa = BigInteger.Parse(text.TrimStart('0'));
		return new BigDecimal(mantissa, -text.Length);
	}

	public static implicit operator BigDecimal(BigInteger value)
	{
		return new BigDecimal(value);
	}

	public static implicit operator BigDecimal(byte value)
	{
		return new BigDecimal(new BigInteger(value));
	}

	public static implicit operator BigDecimal(sbyte value)
	{
		return new BigDecimal(new BigInteger(value));
	}

	public static implicit operator BigDecimal(uint value)
	{
		return new BigDecimal(new BigInteger(value));
	}

	public static implicit operator BigDecimal(int value)
	{
		return new BigDecimal(new BigInteger(value));
	}

	public static implicit operator BigDecimal(ushort value)
	{
		return new BigDecimal(new BigInteger(value));
	}

	public static implicit operator BigDecimal(short value)
	{
		return new BigDecimal(new BigInteger(value));
	}

	public static implicit operator BigDecimal(ulong value)
	{
		return new BigDecimal(new BigInteger(value));
	}

	public static implicit operator BigDecimal(long value)
	{
		return new BigDecimal(new BigInteger(value));
	}

	public static implicit operator BigDecimal(float value)
	{
		return new BigDecimal(value);
	}

	public static implicit operator BigDecimal(decimal value)
	{
		return new BigDecimal(value);
	}

	public static implicit operator BigDecimal(double value)
	{
		return Parse(value.ToString("G17", BigDecimalNumberFormatInfo));
	}

	public static explicit operator BigInteger(BigDecimal value)
	{
		BigDecimal bigDecimal = Floor(value);
		return bigDecimal.Mantissa * BigInteger.Pow(10, bigDecimal.Exponent);
	}

	public static explicit operator double(BigDecimal value)
	{
		if (value > MaxBigDecimalForDouble)
		{
			throw new OverflowException(LanguageResources.Overflow_Double);
		}
		return Convert.ToDouble(value.ToString());
	}

	public static explicit operator float(BigDecimal value)
	{
		if (value > MaxBigDecimalForSingle)
		{
			throw new OverflowException(LanguageResources.Overflow_Single);
		}
		return Convert.ToSingle(value.ToString());
	}

	public static explicit operator decimal(BigDecimal value)
	{
		if (value > MaxBigDecimalForDecimal)
		{
			throw new OverflowException(LanguageResources.Overflow_Decimal);
		}
		return Convert.ToDecimal(value.ToString());
	}

	public static explicit operator int(BigDecimal value)
	{
		if (value > MaxBigDemicalForInt32)
		{
			throw new OverflowException(LanguageResources.Overflow_Int32);
		}
		return Convert.ToInt32(value.ToString());
	}

	public static explicit operator uint(BigDecimal value)
	{
		if (value > MaxBigDemicalForUInt32)
		{
			throw new OverflowException(LanguageResources.Overflow_UInt32);
		}
		return Convert.ToUInt32(value.ToString());
	}

	public static BigDecimal operator %(BigDecimal left, BigDecimal right)
	{
		return Mod(left, right);
	}

	public static BigDecimal operator +(BigDecimal value)
	{
		return value;
	}

	public static BigDecimal operator -(BigDecimal value)
	{
		return Negate(value);
	}

	public static BigDecimal operator ++(BigDecimal value)
	{
		return Add(value, 1);
	}

	public static BigDecimal operator --(BigDecimal value)
	{
		return Subtract(value, 1);
	}

	public static BigDecimal operator +(BigDecimal left, BigDecimal right)
	{
		return Add(left, right);
	}

	public static BigDecimal operator -(BigDecimal left, BigDecimal right)
	{
		return Subtract(left, right);
	}

	public static BigDecimal operator *(BigDecimal left, BigDecimal right)
	{
		return Multiply(left, right);
	}

	public static BigDecimal operator /(BigDecimal dividend, BigDecimal divisor)
	{
		return Divide(dividend, divisor);
	}

	public static bool operator <(BigDecimal left, BigDecimal right)
	{
		if (left.Exponent <= right.Exponent)
		{
			return left.Mantissa < AlignExponent(right, left);
		}
		return AlignExponent(left, right) < right.Mantissa;
	}

	public static bool operator >(BigDecimal left, BigDecimal right)
	{
		if (left.Exponent <= right.Exponent)
		{
			return left.Mantissa > AlignExponent(right, left);
		}
		return AlignExponent(left, right) > right.Mantissa;
	}

	public static bool operator <=(BigDecimal left, BigDecimal right)
	{
		if (left.Exponent <= right.Exponent)
		{
			return left.Mantissa <= AlignExponent(right, left);
		}
		return AlignExponent(left, right) <= right.Mantissa;
	}

	public static bool operator >=(BigDecimal left, BigDecimal right)
	{
		if (left.Exponent <= right.Exponent)
		{
			return left.Mantissa >= AlignExponent(right, left);
		}
		return AlignExponent(left, right) >= right.Mantissa;
	}

	public static BigDecimal Min(BigDecimal left, BigDecimal right)
	{
		if (!(left <= right))
		{
			return right;
		}
		return left;
	}

	public static BigDecimal Max(BigDecimal left, BigDecimal right)
	{
		if (!(left >= right))
		{
			return right;
		}
		return left;
	}

	public static BigDecimal Negate(BigDecimal value)
	{
		return new BigDecimal(BigInteger.Negate(value.Mantissa), value.Exponent);
	}

	public static BigDecimal Add(BigDecimal left, BigDecimal right)
	{
		if (left.Exponent > right.Exponent)
		{
			return new BigDecimal(AlignExponent(left, right) + right.Mantissa, right.Exponent);
		}
		return new BigDecimal(AlignExponent(right, left) + left.Mantissa, left.Exponent);
	}

	public static BigDecimal Subtract(BigDecimal left, BigDecimal right)
	{
		return Add(left, Negate(right));
	}

	public static BigDecimal Multiply(BigDecimal left, BigDecimal right)
	{
		return new BigDecimal(left.Mantissa * right.Mantissa, left.Exponent + right.Exponent);
	}

	public static BigDecimal Mod(BigDecimal value, BigDecimal mod)
	{
		BigDecimal left = Floor(Divide(value, mod));
		return Subtract(value, Multiply(left, mod));
	}

	public static BigDecimal Divide(BigDecimal dividend, BigDecimal divisor)
	{
		if (divisor == Zero)
		{
			throw new DivideByZeroException("divisor");
		}
		int num = dividend.Exponent - divisor.Exponent;
		int num2 = 0;
		BigInteger remainder;
		BigInteger bigInteger = BigInteger.DivRem(dividend.Mantissa, divisor.Mantissa, out remainder);
		bool flag = true;
		BigInteger bigInteger2 = 0;
		while (remainder != 0L)
		{
			if (flag)
			{
				flag = false;
			}
			else if (remainder == bigInteger2)
			{
				if (GetSignifigantDigits(bigInteger) >= divisor.SignifigantDigits)
				{
					break;
				}
			}
			else if (GetSignifigantDigits(bigInteger) >= Precision)
			{
				break;
			}
			while (BigInteger.Abs(remainder) < BigInteger.Abs(divisor.Mantissa))
			{
				remainder *= (BigInteger)10;
				bigInteger *= (BigInteger)10;
				num2++;
			}
			bigInteger2 = remainder;
			bigInteger += BigInteger.DivRem(remainder, divisor.Mantissa, out remainder);
		}
		return new BigDecimal(bigInteger, num - num2);
	}

	public static BigDecimal Pow(BigDecimal @base, BigInteger exponent)
	{
		if (AlwaysTruncate)
		{
			return Pow_Precision(@base, exponent);
		}
		return Pow_Fast(@base, exponent);
	}

	private static BigDecimal Pow_Fast(BigDecimal @base, BigInteger exponent)
	{
		if (exponent.IsZero)
		{
			return One;
		}
		BigDecimal bigDecimal = @base;
		if (exponent.Sign < 0)
		{
			if (bigDecimal == Zero)
			{
				throw new NotSupportedException(LanguageResources.NotSupported_NegativePower);
			}
			bigDecimal = One / bigDecimal;
			exponent = BigInteger.Negate(exponent);
		}
		BigDecimal one = One;
		while (exponent > BigInteger.Zero)
		{
			if (exponent % 2 == 1L)
			{
				one *= bigDecimal;
				--exponent;
				if (exponent == 0L)
				{
					break;
				}
			}
			bigDecimal *= bigDecimal;
			exponent /= (BigInteger)2;
		}
		return one;
	}

	private static BigDecimal Pow_Precision(BigDecimal baseValue, BigInteger exponent)
	{
		if (exponent.IsZero)
		{
			return One;
		}
		if (exponent.Sign < 0)
		{
			if (baseValue == Zero)
			{
				throw new NotSupportedException(LanguageResources.NotSupported_NegativePower);
			}
			baseValue = One / baseValue;
			exponent = BigInteger.Negate(exponent);
		}
		BigDecimal result = baseValue;
		while (exponent > BigInteger.One)
		{
			result *= baseValue;
			--exponent;
		}
		return result;
	}

	public static BigDecimal Pow(double basis, double exponent)
	{
		BigDecimal one = One;
		while (Math.Abs(exponent) > ExpChunk)
		{
			double num = ((exponent > 0.0) ? ExpChunk : (0.0 - ExpChunk));
			one *= (BigDecimal)Math.Pow(basis, num);
			exponent -= num;
		}
		return one * Math.Pow(basis, exponent);
	}

	public static BigDecimal SquareRoot(BigDecimal input, int decimalPlaces)
	{
		return NthRoot(input, 2, decimalPlaces);
	}

	public static BigDecimal NthRoot(BigDecimal input, int root, int decimalPlaces)
	{
		BigInteger mantissa = input.Mantissa;
		int i = input.Exponent;
		int num = Math.Sign(i);
		if (num == 0)
		{
			num = 1;
		}
		int num2 = decimalPlaces * root - PlacesRightOfDecimal(input);
		if (num2 > 0)
		{
			mantissa *= BigInteger.Pow(10, num2);
			i -= num2;
		}
		for (; i % root != 0; i += num)
		{
			mantissa *= (BigInteger)10;
		}
		BigInteger remainder;
		BigInteger mantissa2 = mantissa.NthRoot(root, out remainder);
		int exponent = (i /= root);
		return new BigDecimal(mantissa2, exponent);
	}

	private static int PlacesLeftOfDecimal(BigDecimal input)
	{
		return BigInteger.Abs(input.Mantissa).GetLength() + input.Exponent;
	}

	private static int PlacesRightOfDecimal(BigDecimal input)
	{
		return BigInteger.Abs(input.Mantissa).GetLength() - PlacesLeftOfDecimal(input);
	}

	private static BigInteger AlignExponent(BigDecimal value, BigDecimal reference)
	{
		return value.Mantissa * BigInteger.Pow(TenInt, value.Exponent - reference.Exponent);
	}

	public static BigDecimal Abs(BigDecimal value)
	{
		if (value.IsNegative())
		{
			return Negate(value);
		}
		return new BigDecimal(value.Mantissa, value.Exponent);
	}

	public static BigDecimal Truncate(BigDecimal value)
	{
		return Floor(value);
	}

	public static BigInteger Round(BigDecimal value)
	{
		return Round(value, MidpointRounding.AwayFromZero);
	}

	public static BigInteger Round(BigDecimal value, MidpointRounding mode)
	{
		BigInteger wholeValue = value.WholeValue;
		BigDecimal fractionalPart = value.GetFractionalPart();
		BigInteger bigInteger = ((!value.IsNegative()) ? 1 : (-1));
		if (fractionalPart > OneHalf)
		{
			wholeValue += bigInteger;
		}
		else if (fractionalPart == OneHalf)
		{
			if (mode == MidpointRounding.AwayFromZero)
			{
				wholeValue += bigInteger;
			}
			else if (!wholeValue.IsEven)
			{
				wholeValue += bigInteger;
			}
		}
		return wholeValue;
	}

	public static BigDecimal Round(BigDecimal value, int precision)
	{
		if (precision < 0)
		{
			string text = value.WholeValue.ToString();
			int length = text.Length;
			if (Math.Abs(precision) >= length)
			{
				return Zero;
			}
			return Parse(text[..(length + precision)] + new string(Enumerable.Repeat('0', Math.Abs(precision)).ToArray()));
		}
		if (precision == 0)
		{
			return new BigDecimal(value.WholeValue);
		}
		BigInteger bigInteger = value.Mantissa;
		int num = value.Exponent;
		int num2 = Math.Sign(num);
		int num3 = PlacesRightOfDecimal(value);
		if (num3 > precision)
		{
			int num4 = num3 - precision;
			bigInteger = BigInteger.Divide(bigInteger, BigInteger.Pow(TenInt, num4));
			if (num2 != 0)
			{
				num -= num2 * num4;
			}
		}
		return new BigDecimal(new Tuple<BigInteger, int>(bigInteger, num));
	}

	public static BigDecimal Ceiling(BigDecimal value)
	{
		BigDecimal bigDecimal = value.WholeValue;
		if (bigDecimal != value.Mantissa && value >= 0)
		{
			bigDecimal += (BigDecimal)1;
		}
		return bigDecimal;
	}

	public static BigDecimal Floor(BigDecimal value)
	{
		BigDecimal bigDecimal = value.WholeValue;
		if (bigDecimal != value.Mantissa && value <= 0)
		{
			bigDecimal -= (BigDecimal)1;
		}
		return bigDecimal;
	}

	public static BigDecimal Sin(BigDecimal radians)
	{
		return Sin(radians, Precision);
	}

	public static BigDecimal Sin(BigDecimal radians, int precision)
	{
		int sign = radians.Sign;
		BigDecimal bigDecimal = Abs(radians);
		if (bigDecimal == 0)
		{
			return 0;
		}
		if (bigDecimal == TrigonometricHelper.HalfPi)
		{
			return sign;
		}
		if (bigDecimal > TrigonometricHelper.HalfPi)
		{
			bigDecimal = TrigonometricHelper.WrapInput(radians);
		}
		BigDecimal sumStart = 0;
		BigInteger counterStart = 1;
		BigInteger jump = 2;
		BigInteger multiplier = -1;
		bool factorialDenominator = true;
		return TrigonometricHelper.TaylorSeriesSum(bigDecimal, sumStart, counterStart, jump, multiplier, factorialDenominator, precision) * sign;
	}

	public static BigDecimal Cos(BigDecimal radians)
	{
		return Cos(radians, Precision);
	}

	public static BigDecimal Cos(BigDecimal radians, int precision)
	{
		return Sin(radians + TrigonometricHelper.HalfPi, precision);
	}

	public static BigDecimal Tan(BigDecimal radians)
	{
		return Tan(radians, Precision);
	}

	public static BigDecimal Tan(BigDecimal radians, int precision)
	{
		if (Normalize(Mod(radians, TrigonometricHelper.HalfPi)).IsZero())
		{
			throw new ArithmeticException(LanguageResources.Arithmetic_Trig_Undefined_Tan_PiOver2);
		}
		if (Normalize(Mod(radians, new BigDecimal(3) * TrigonometricHelper.HalfPi)).IsZero())
		{
			throw new ArithmeticException(LanguageResources.Arithmetic_Trig_Undefined_Tan_3PiOver2);
		}
		BigDecimal radians2 = Normalize(Mod(radians, Pi));
		BigDecimal bigDecimal = Sin(radians2, precision);
		BigDecimal bigDecimal2 = Cos(radians2, precision);
		return bigDecimal / bigDecimal2;
	}

	public static BigDecimal Cot(BigDecimal radians)
	{
		return Cot(radians, Precision);
	}

	public static BigDecimal Cot(BigDecimal radians, int precision)
	{
		if (radians.IsZero())
		{
			throw new ArithmeticException(LanguageResources.Arithmetic_Trig_Undefined_Cot_Zero);
		}
		BigDecimal radians2 = Normalize(Mod(radians, Pi));
		if (radians2.IsZero())
		{
			throw new ArithmeticException(LanguageResources.Arithmetic_Trig_Undefined_Cot_Pi);
		}
		BigDecimal bigDecimal = Cos(radians2, precision);
		BigDecimal bigDecimal2 = Sin(radians2, precision);
		return bigDecimal / bigDecimal2;
	}

	public static BigDecimal Sec(BigDecimal radians)
	{
		return Sec(radians, Precision);
	}

	public static BigDecimal Sec(BigDecimal radians, int precision)
	{
		if (TrigonometricHelper.ModOddHalfPi(radians) == One)
		{
			throw new ArithmeticException(LanguageResources.Arithmetic_Trig_Undefined_Sec_OddPiOver2);
		}
		BigDecimal bigDecimal = Cos(Normalize(Mod(radians, new BigDecimal(2) * Pi)), precision);
		return One / bigDecimal;
	}

	public static BigDecimal Csc(BigDecimal radians)
	{
		return Csc(radians, Precision);
	}

	public static BigDecimal Csc(BigDecimal radians, int precision)
	{
		if (radians.IsZero())
		{
			throw new ArithmeticException(LanguageResources.Arithmetic_Trig_Undefined_Csc_Zero);
		}
		if (Normalize(Mod(radians, Pi)).IsZero())
		{
			throw new ArithmeticException(LanguageResources.Arithmetic_Trig_Undefined_Csc_Pi);
		}
		BigDecimal bigDecimal = Sin(Normalize(Mod(radians, 2 * Pi)), precision);
		return One / bigDecimal;
	}

	public static BigDecimal Sinh(BigDecimal radians)
	{
		return Sinh(radians, Precision);
	}

	public static BigDecimal Sinh(BigDecimal radians, int precision)
	{
		BigDecimal sumStart = 0;
		BigInteger counterStart = 1;
		BigInteger jump = 2;
		BigInteger multiplier = 1;
		bool factorialDenominator = true;
		return TrigonometricHelper.TaylorSeriesSum(radians, sumStart, counterStart, jump, multiplier, factorialDenominator, precision);
	}

	public static BigDecimal Cosh(BigDecimal radians)
	{
		return Cosh(radians, Precision);
	}

	public static BigDecimal Cosh(BigDecimal radians, int precision)
	{
		BigDecimal sumStart = 1;
		BigInteger counterStart = 2;
		BigInteger jump = 2;
		BigInteger multiplier = 1;
		bool factorialDenominator = true;
		return TrigonometricHelper.TaylorSeriesSum(radians, sumStart, counterStart, jump, multiplier, factorialDenominator, precision);
	}

	public static BigDecimal Tanh(BigDecimal radians)
	{
		return Tanh(radians, Precision);
	}

	public static BigDecimal Tanh(BigDecimal radians, int precision)
	{
		BigDecimal bigDecimal = Sinh(radians, precision);
		BigDecimal bigDecimal2 = Cosh(radians, precision);
		return bigDecimal / bigDecimal2;
	}

	public static BigDecimal Coth(BigDecimal radians)
	{
		return Coth(radians, Precision);
	}

	public static BigDecimal Coth(BigDecimal radians, int precision)
	{
		BigDecimal bigDecimal = Cosh(radians, precision);
		BigDecimal bigDecimal2 = Sinh(radians, precision);
		return bigDecimal / bigDecimal2;
	}

	public static BigDecimal Sech(BigDecimal radians)
	{
		return Sech(radians, Precision);
	}

	public static BigDecimal Sech(BigDecimal radians, int precision)
	{
		BigDecimal bigDecimal = Cosh(radians, precision);
		return One / bigDecimal;
	}

	public static BigDecimal Csch(BigDecimal radians)
	{
		return Csch(radians, Precision);
	}

	public static BigDecimal Csch(BigDecimal radians, int precision)
	{
		BigDecimal radians2 = Normalize(radians);
		if (radians2.IsZero())
		{
			throw new ArithmeticException(LanguageResources.Arithmetic_Trig_Undefined_Csch_Zero);
		}
		BigDecimal bigDecimal = Sinh(radians2, precision);
		return One / bigDecimal;
	}

	public static BigDecimal Arcsin(BigDecimal radians)
	{
		return Arcsin(radians, Precision);
	}

	public static BigDecimal Arcsin(BigDecimal radians, int precision)
	{
		int sign = radians.Sign;
		BigDecimal bigDecimal = Mod(Abs(radians), One);
		BigDecimal bigDecimal2 = SquareRoot(One - bigDecimal * bigDecimal, precision);
		BigDecimal radians2 = bigDecimal / bigDecimal2;
		return sign * Arctan(radians2, precision);
	}

	public static BigDecimal Arccos(BigDecimal radians)
	{
		return Arccos(radians, Precision);
	}

	public static BigDecimal Arccos(BigDecimal radians, int precision)
	{
		int sign = radians.Sign;
		BigDecimal bigDecimal = Mod(Abs(radians), One);
		BigDecimal radians2 = SquareRoot(One - bigDecimal * bigDecimal, precision) / bigDecimal;
		if (sign == -1)
		{
			return Pi - Arctan(radians2, precision);
		}
		return Arctan(radians2, precision);
	}

	public static BigDecimal Arctan(BigDecimal radians)
	{
		return Arctan(radians, Precision);
	}

	public static BigDecimal Arctan(BigDecimal radians, int precision)
	{
		int sign = radians.Sign;
		BigDecimal bigDecimal = new BigDecimal(radians.Mantissa, radians.Exponent);
		bool flag = false;
		switch (sign)
		{
		case 0:
			return 0;
		case -1:
			flag = bigDecimal < -1;
			break;
		default:
			flag = bigDecimal > 1;
			break;
		}
		if (flag)
		{
			bigDecimal = One / bigDecimal;
		}
		bigDecimal = Abs(bigDecimal);
		BigDecimal sumStart = 0;
		BigInteger counterStart = 1;
		BigInteger jump = 2;
		BigInteger multiplier = -1;
		bool factorialDenominator = false;
		BigDecimal bigDecimal2 = TrigonometricHelper.TaylorSeriesSum(bigDecimal, sumStart, counterStart, jump, multiplier, factorialDenominator, precision);
		bigDecimal2 *= (BigDecimal)sign;
		if (flag)
		{
			bigDecimal2 = sign * TrigonometricHelper.HalfPi - bigDecimal2;
		}
		return bigDecimal2;
	}

	public static BigDecimal Arccot(BigDecimal radians)
	{
		return Arccot(radians, Precision);
	}

	public static BigDecimal Arccot(BigDecimal radians, int precision)
	{
		return TrigonometricHelper.HalfPi - Arctan(radians, precision);
	}

	public static BigDecimal Arccsc(BigDecimal radians)
	{
		return Arccsc(radians, Precision);
	}

	public static BigDecimal Arccsc(BigDecimal radians, int precision)
	{
		return Arcsin(One / radians, precision);
	}

	public static BigDecimal Exp(BigDecimal x)
	{
		return Exp(x, Precision);
	}

	public static BigDecimal Exp(BigDecimal x, int precision)
	{
		BigDecimal sumStart = 1;
		BigInteger counterStart = 1;
		BigInteger jump = 1;
		BigInteger multiplier = 1;
		bool factorialDenominator = true;
		return TrigonometricHelper.TaylorSeriesSum(x, sumStart, counterStart, jump, multiplier, factorialDenominator, precision);
	}

	public static BigDecimal Ln(BigDecimal argument)
	{
		return Ln(argument, Precision);
	}

	public static BigDecimal Ln(BigDecimal argument, int precision)
	{
		BigDecimal bigDecimal = new BigDecimal(argument.Mantissa, argument.Exponent);
		BigDecimal bigDecimal2 = 0;
		while (bigDecimal > ChunkSize)
		{
			BigInteger bigInteger = Round(NthRoot(bigDecimal, 3, precision));
			bigDecimal /= (BigDecimal)bigInteger;
			bigDecimal /= (BigDecimal)bigInteger;
			bigDecimal2 += Ln(bigInteger, precision);
			bigDecimal2 += Ln(bigInteger, precision);
		}
		return bigDecimal2 + LogNatural(bigDecimal, precision);
	}

	internal static BigDecimal LogNatural(BigDecimal argument, int precision)
	{
		BigDecimal precisionTarget = TrigonometricHelper.GetPrecisionTarget(precision);
		BigDecimal bigDecimal = new BigDecimal(2);
		BigDecimal bigDecimal2 = new BigDecimal(argument.Mantissa, argument.Exponent);
		bool flag = true;
		BigDecimal bigDecimal3 = 0;
		BigDecimal bigDecimal4 = 14;
		BigDecimal value = 1;
		int num = 0;
		do
		{
			BigDecimal bigDecimal5 = Exp(bigDecimal4);
			BigDecimal bigDecimal6 = bigDecimal2 - bigDecimal5;
			BigDecimal bigDecimal7 = bigDecimal2 + bigDecimal5;
			BigDecimal bigDecimal8 = bigDecimal * (bigDecimal6 / bigDecimal7);
			bigDecimal3 = bigDecimal4 + bigDecimal8;
			if (flag)
			{
				flag = false;
			}
			else
			{
				value = bigDecimal3 - bigDecimal4;
			}
			bigDecimal4 = bigDecimal3;
			num++;
		}
		while (Abs(value) > precisionTarget);
		return bigDecimal4;
	}

	public static BigDecimal LogN(int @base, BigDecimal argument, int precision)
	{
		return Ln(argument, precision) / Ln(@base, precision);
	}

	public static BigDecimal Log2(BigDecimal argument, int precision)
	{
		return LogN(2, argument, precision);
	}

	public static BigDecimal Log10(BigDecimal argument, int precision)
	{
		return LogN(10, argument, precision);
	}

	public override int GetHashCode()
	{
		return new Tuple<BigInteger, int>(Mantissa, Exponent).GetHashCode();
	}

	public override string ToString()
	{
		return ToString(BigDecimalNumberFormatInfo);
	}

	public string ToString(IFormatProvider provider)
	{
		return ToString(Mantissa, Exponent, provider);
	}

	private static string ToString(BigInteger mantissa, int exponent, IFormatProvider? provider = null)
	{
		if (provider == null)
		{
			provider = CultureInfo.CurrentCulture;
		}
		NumberFormatInfo instance = NumberFormatInfo.GetInstance(provider);
		bool num = mantissa.Sign == -1;
		bool num2 = Math.Sign(exponent) == -1;
		string text = BigInteger.Abs(mantissa).ToString();
		int num3 = Math.Abs(exponent);
		if (num2)
		{
			if (num3 > text.Length)
			{
				text = string.Concat(string.Concat(Enumerable.Repeat(count: Math.Abs(num3 - text.Length), element: instance.NativeDigits[0])), text);
				text = text.Insert(0, instance.NumberDecimalSeparator);
				text = text.Insert(0, instance.NativeDigits[0]);
			}
			else
			{
				int num4 = Math.Abs(num3 - text.Length);
				text = text.Insert(num4, instance.NumberDecimalSeparator);
				if (num4 == 0)
				{
					text = text.Insert(0, instance.NativeDigits[0]);
				}
			}
			text = text.TrimEnd('0');
			if (text.Last().ToString() == instance.NumberDecimalSeparator)
			{
				text = text.Substring(0, text.Length - 1);
			}
		}
		else
		{
			string text2 = string.Concat(Enumerable.Repeat(instance.NativeDigits[0], num3));
			text += text2;
		}
		if (num)
		{
			text = text.Insert(0, instance.NegativeSign);
		}
		return text;
	}

	private static string ToScientificENotation(BigDecimal bigDecimal)
	{
		string text = bigDecimal.Mantissa.ToString();
		int num = text.Length - bigDecimal.Exponent + 1;
		if (bigDecimal.Mantissa.Sign == -1)
		{
			num++;
		}
		return $"{text.Insert(num, BigDecimalNumberFormatInfo.NumberDecimalSeparator)}E{bigDecimal.Exponent:D}";
	}

	[CompilerGenerated]
	private bool PrintMembers(StringBuilder builder)
	{
		builder.Append("Mantissa = ");
		builder.Append(Mantissa.ToString());
		builder.Append(", Exponent = ");
		builder.Append(Exponent.ToString());
		builder.Append(", Sign = ");
		builder.Append(Sign.ToString());
		builder.Append(", SignifigantDigits = ");
		builder.Append(SignifigantDigits.ToString());
		builder.Append(", Length = ");
		builder.Append(Length.ToString());
		builder.Append(", DecimalPlaces = ");
		builder.Append(DecimalPlaces.ToString());
		builder.Append(", WholeValue = ");
		builder.Append(WholeValue.ToString());
		return true;
	}
}
