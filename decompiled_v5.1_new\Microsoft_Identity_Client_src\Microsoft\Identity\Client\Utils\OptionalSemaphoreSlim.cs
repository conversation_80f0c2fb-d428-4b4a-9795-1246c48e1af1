using System.Threading;
using System.Threading.Tasks;

namespace Microsoft.Identity.Client.Utils;

internal class OptionalSemaphoreSlim
{
	private readonly bool _useRealSemaphore;

	private int _noLockCurrentCount;

	private SemaphoreSlim _semaphoreSlim;

	public int CurrentCount
	{
		get
		{
			if (!_useRealSemaphore)
			{
				return _noLockCurrentCount;
			}
			return _semaphoreSlim.CurrentCount;
		}
	}

	public string GetCurrentCountLogMessage()
	{
		return $"Real semaphore: {_useRealSemaphore}. Count: {CurrentCount}";
	}

	public OptionalSemaphoreSlim(bool useRealSemaphore)
	{
		_useRealSemaphore = useRealSemaphore;
		if (_useRealSemaphore)
		{
			_semaphoreSlim = new SemaphoreSlim(1, 1);
		}
		_noLockCurrentCount = 1;
	}

	public void Release()
	{
		if (_useRealSemaphore)
		{
			_semaphoreSlim.Release();
		}
		else
		{
			Interlocked.Increment(ref _noLockCurrentCount);
		}
	}

	public Task WaitAsync(CancellationToken cancellationToken)
	{
		if (_useRealSemaphore)
		{
			return _semaphoreSlim.WaitAsync(cancellationToken);
		}
		Interlocked.Decrement(ref _noLockCurrentCount);
		return Task.FromResult(result: true);
	}

	public void Wait()
	{
		if (_useRealSemaphore)
		{
			_semaphoreSlim.Wait();
		}
		else
		{
			Interlocked.Decrement(ref _noLockCurrentCount);
		}
	}
}
