using System;
using System.Globalization;
using System.Threading.Tasks;
using System.Xml;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.WsTrust;

internal class CommonNonInteractiveHandler
{
	private readonly RequestContext _requestContext;

	private readonly IServiceBundle _serviceBundle;

	public CommonNonInteractiveHandler(RequestContext requestContext, IServiceBundle serviceBundle)
	{
		_requestContext = requestContext;
		_serviceBundle = serviceBundle;
	}

	public async Task<string> GetPlatformUserAsync()
	{
		string platformUsername = await _serviceBundle.PlatformProxy.GetUserPrincipalNameAsync().ConfigureAwait(continueOnCapturedContext: false);
		if (string.IsNullOrWhiteSpace(platformUsername))
		{
			_requestContext.Logger.Error("Could not find UPN for logged in user. ");
			throw new MsalClientException("unknown_user", "Could not identify the user logged into the OS. See http://aka.ms/msal-net-iwa for details. ");
		}
		_requestContext.Logger.InfoPii(() => "Logged in user detected with user name '" + platformUsername + "'", () => "Logged in user detected. ");
		return platformUsername;
	}

	public async Task<UserRealmDiscoveryResponse> QueryUserRealmDataAsync(string userRealmUriPrefix, string username)
	{
		UserRealmDiscoveryResponse userRealmResponse = await _serviceBundle.WsTrustWebRequestManager.GetUserRealmAsync(userRealmUriPrefix, username, _requestContext).ConfigureAwait(continueOnCapturedContext: false);
		if (string.Equals(userRealmResponse.DomainName, "live.com"))
		{
			throw new MsalClientException("ropc_not_supported_for_msa", "ROPC does not support MSA accounts. See https://aka.ms/msal-net-ropc for details. ");
		}
		_requestContext.Logger.InfoPii(() => $"User with user name '{username}' detected as '{userRealmResponse.AccountType}'. ", () => "User detected as '" + userRealmResponse.AccountType + "'. ");
		return userRealmResponse;
	}

	public async Task<WsTrustResponse> PerformWsTrustMexExchangeAsync(string federationMetadataUrl, string cloudAudienceUrn, UserAuthType userAuthType, string username, string password, string federationMetadataFilename)
	{
		MexDocument mexDocument;
		try
		{
			mexDocument = await _serviceBundle.WsTrustWebRequestManager.GetMexDocumentAsync(federationMetadataUrl, _requestContext, federationMetadataFilename).ConfigureAwait(continueOnCapturedContext: false);
		}
		catch (XmlException innerException)
		{
			throw new MsalClientException("parsing_ws_metadata_exchange_failed", "Parsing WS metadata exchange failed. ", innerException);
		}
		WsTrustEndpoint wsTrustEndpoint = ((userAuthType == UserAuthType.IntegratedAuth) ? mexDocument.GetWsTrustWindowsTransportEndpoint() : mexDocument.GetWsTrustUsernamePasswordEndpoint());
		if (wsTrustEndpoint == null)
		{
			throw new MsalClientException("wstrust_endpoint_not_found", "WS-Trust endpoint not found in metadata document. ");
		}
		_requestContext.Logger.VerbosePii(() => string.Format(CultureInfo.InvariantCulture, "WS-Trust endpoint '{0}' being used from MEX at '{1}'", wsTrustEndpoint.Uri, federationMetadataUrl), () => "Fetched and parsed MEX. ");
		WsTrustResponse wsTrustResponse = await GetWsTrustResponseAsync(userAuthType, cloudAudienceUrn, wsTrustEndpoint, username, password).ConfigureAwait(continueOnCapturedContext: false);
		_requestContext.Logger.Info(() => "Token of type '" + wsTrustResponse.TokenType + "' acquired from WS-Trust endpoint. ");
		return wsTrustResponse;
	}

	internal async Task<WsTrustResponse> GetWsTrustResponseAsync(UserAuthType userAuthType, string cloudAudienceUrn, WsTrustEndpoint endpoint, string username, string password)
	{
		string wsTrustRequest = ((userAuthType == UserAuthType.IntegratedAuth) ? endpoint.BuildTokenRequestMessageWindowsIntegratedAuth(cloudAudienceUrn) : endpoint.BuildTokenRequestMessageUsernamePassword(cloudAudienceUrn, username, password));
		try
		{
			WsTrustResponse wsTrustResponse = await _serviceBundle.WsTrustWebRequestManager.GetWsTrustResponseAsync(endpoint, wsTrustRequest, _requestContext).ConfigureAwait(continueOnCapturedContext: false);
			_requestContext.Logger.Info(() => "Token of type '" + wsTrustResponse.TokenType + "' acquired from WS-Trust endpoint. ");
			return wsTrustResponse;
		}
		catch (Exception ex) when (!(ex is MsalClientException))
		{
			throw new MsalClientException("parsing_wstrust_response_failed", "There was an error parsing the WS-Trust response from the endpoint. \nThis may occur if there are issues with your ADFS configuration. See https://aka.ms/msal-net-iwa-troubleshooting for more details.\nEnable logging to see more details. See https://aka.ms/msal-net-logging. Error Message: " + ex.Message, ex);
		}
	}
}
