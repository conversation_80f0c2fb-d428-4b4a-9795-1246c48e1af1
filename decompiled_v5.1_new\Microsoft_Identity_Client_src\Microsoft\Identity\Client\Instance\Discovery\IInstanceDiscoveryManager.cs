using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.Instance.Discovery;

internal interface IInstanceDiscoveryManager
{
	Task<InstanceDiscoveryMetadataEntry> GetMetadataEntryTryAvoidNetworkAsync(AuthorityInfo authorityinfo, IEnumerable<string> existingEnvironmentsInCache, RequestContext requestContext);

	Task<InstanceDiscoveryMetadataEntry> GetMetadataEntryAsync(AuthorityInfo authorityinfo, RequestContext requestContext, bool forceValidation = false);
}
