namespace EnumsNET.Numerics;

internal static class Number
{
	public static int BitCount(int v)
	{
		v -= (v >> 1) & 0x55555555;
		v = (v & 0x33333333) + ((v >> 2) & 0x33333333);
		return ((v + (v >> 4)) & 0xF0F0F0F) * 16843009 >> 24;
	}

	public static int BitCount(long v)
	{
		v -= (v >> 1) & 0x5555555555555555L;
		v = (v & 0x3333333333333333L) + ((v >> 2) & 0x3333333333333333L);
		return (int)(((v + (v >> 4)) & 0xF0F0F0F0F0F0F0FL) * 72340172838076673L >> 56);
	}
}
