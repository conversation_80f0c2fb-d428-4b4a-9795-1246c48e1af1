using System;
using System.Globalization;
using System.IO;
using System.Runtime.CompilerServices;
using System.Text;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.PlatformsCommon.Factories;
using Microsoft.IdentityModel.Abstractions;

namespace Microsoft.Identity.Client.Internal.Logger;

internal class LoggerHelper
{
	private static Lazy<string> s_msalVersionLazy = new Lazy<string>(MsalIdHelper.GetMsalVersion);

	private static Lazy<string> s_runtimeVersionLazy = new Lazy<string>(() => PlatformProxyFactory.CreatePlatformProxy(null).GetRuntimeVersion());

	private static readonly Lazy<ILoggerAdapter> s_nullLogger = new Lazy<ILoggerAdapter>(() => new NullLogger());

	private static Lazy<string> s_osLazy = new Lazy<string>(() => MsalIdHelper.GetMsalIdParameters(null).TryGetValue("x-client-OS", out var value) ? value : "Unknown OS");

	private static Lazy<string> s_skuLazy = new Lazy<string>(() => MsalIdHelper.GetMsalIdParameters(null).TryGetValue("x-client-SKU", out value) ? value : "Unknown SKU");

	public static ILoggerAdapter NullLogger => s_nullLogger.Value;

	public static string GetClientInfo(string clientName, string clientVersion)
	{
		if (!string.IsNullOrEmpty(clientName) && !"UnknownClient".Equals(clientName))
		{
			if (string.IsNullOrEmpty(clientVersion))
			{
				return " (" + clientName + ")";
			}
			return $" ({clientName}: {clientVersion})";
		}
		return string.Empty;
	}

	public static ILoggerAdapter CreateLogger(Guid correlationId, ApplicationConfiguration config)
	{
		if (config.IdentityLogger == null)
		{
			if (config.LoggingCallback == null)
			{
				return s_nullLogger.Value;
			}
			return CallbackIdentityLoggerAdapter.Create(correlationId, config);
		}
		return IdentityLoggerAdapter.Create(correlationId, config);
	}

	public static string FormatLogMessage(string message, bool piiEnabled, string correlationId, string clientInformation)
	{
		return string.Format(CultureInfo.InvariantCulture, "{0} MSAL {1} {2} {3} {4} [{5}{6}]{7} {8}", piiEnabled, s_msalVersionLazy.Value, s_skuLazy.Value, s_runtimeVersionLazy.Value, s_osLazy.Value, DateTime.UtcNow.ToString("u"), correlationId, clientInformation, message);
	}

	internal static string GetPiiScrubbedExceptionDetails(Exception ex)
	{
		if (ex == null)
		{
			return string.Empty;
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.AppendLine(string.Format(CultureInfo.InvariantCulture, "Exception type: {0}", ex.GetType()));
		if (ex is MsalException ex2)
		{
			StringBuilder stringBuilder2 = stringBuilder;
			StringBuilder stringBuilder3 = stringBuilder2;
			StringBuilder.AppendInterpolatedStringHandler handler = new StringBuilder.AppendInterpolatedStringHandler(13, 1, stringBuilder2);
			handler.AppendLiteral(", ErrorCode: ");
			handler.AppendFormatted(ex2.ErrorCode);
			stringBuilder3.AppendLine(ref handler);
		}
		if (ex is MsalServiceException ex3)
		{
			stringBuilder.AppendLine(string.Format(CultureInfo.InvariantCulture, "HTTP StatusCode {0}", ex3.StatusCode));
			StringBuilder stringBuilder2 = stringBuilder;
			StringBuilder stringBuilder4 = stringBuilder2;
			StringBuilder.AppendInterpolatedStringHandler handler = new StringBuilder.AppendInterpolatedStringHandler(14, 1, stringBuilder2);
			handler.AppendLiteral("CorrelationId ");
			handler.AppendFormatted(ex3.CorrelationId);
			stringBuilder4.AppendLine(ref handler);
			string[] errorCodes = ex3.ErrorCodes;
			if (errorCodes != null && errorCodes.Length > 0)
			{
				stringBuilder2 = stringBuilder;
				StringBuilder stringBuilder5 = stringBuilder2;
				handler = new StringBuilder.AppendInterpolatedStringHandler(36, 1, stringBuilder2);
				handler.AppendLiteral("Microsoft Entra ID Error Code AADSTS");
				handler.AppendFormatted(string.Join(" ", ex3.ErrorCodes));
				stringBuilder5.AppendLine(ref handler);
			}
		}
		if (ex.InnerException != null)
		{
			stringBuilder.AppendLine("---> Inner Exception Details");
			stringBuilder.AppendLine(GetPiiScrubbedExceptionDetails(ex.InnerException));
			stringBuilder.AppendLine("=== End of inner exception stack trace ===");
		}
		if (ex is MsalClaimsChallengeException)
		{
			stringBuilder.AppendLine("The returned error contains a claims challenge. For additional info on how to handle claims related to multifactor authentication, Conditional Access, and incremental consent, see https://aka.ms/msal-conditional-access-claims. If you are using the On-Behalf-Of flow, see https://aka.ms/msal-conditional-access-claims-obo for details.");
		}
		stringBuilder.AppendLine("To see full exception details, enable PII Logging. See https://aka.ms/msal-net-logging");
		if (ex.StackTrace != null)
		{
			stringBuilder.AppendLine(ex.StackTrace);
		}
		return stringBuilder.ToString();
	}

	public static DurationLogHelper LogBlockDuration(ILoggerAdapter logger, string measuredBlockName, LogLevel logLevel = LogLevel.Verbose)
	{
		return new DurationLogHelper(logger, measuredBlockName, logLevel);
	}

	public static DurationLogHelper LogMethodDuration(ILoggerAdapter logger, LogLevel logLevel = LogLevel.Verbose, [CallerMemberName] string methodName = null, [CallerFilePath] string filePath = null)
	{
		string text = ((!string.IsNullOrEmpty(filePath)) ? Path.GetFileNameWithoutExtension(filePath) : "");
		return new DurationLogHelper(logger, text + ":" + methodName, logLevel);
	}

	public static EventLogLevel GetEventLogLevel(LogLevel logLevel)
	{
		if (logLevel == LogLevel.Always)
		{
			return EventLogLevel.LogAlways;
		}
		return (EventLogLevel)(logLevel + 2);
	}

	public static string GetMessageToLog(string messageWithPii, string messageScrubbed, bool piiLoggingEnabled)
	{
		if (!(!string.IsNullOrWhiteSpace(messageWithPii) && piiLoggingEnabled))
		{
			return messageScrubbed;
		}
		return messageWithPii;
	}
}
