using System.Text;

namespace Microsoft.Identity.Client.Utils;

internal static class StringExtensions
{
	private static UTF8Encoding utf8Encoding = new UTF8Encoding();

	public static byte[] ToByteArray(this string stringInput)
	{
		return utf8Encoding.GetBytes(stringInput);
	}

	public static string NullIfEmpty(this string s)
	{
		if (!string.IsNullOrEmpty(s))
		{
			return s;
		}
		return null;
	}

	public static string NullIfWhiteSpace(this string s)
	{
		if (!string.IsNullOrWhiteSpace(s))
		{
			return s;
		}
		return null;
	}
}
