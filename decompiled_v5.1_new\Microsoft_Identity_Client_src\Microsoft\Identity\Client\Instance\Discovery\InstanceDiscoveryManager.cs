using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Region;

namespace Microsoft.Identity.Client.Instance.Discovery;

internal class InstanceDiscoveryManager : IInstanceDiscoveryManager
{
	private readonly IHttpManager _httpManager;

	private readonly IUserMetadataProvider _userMetadataProvider;

	private readonly IKnownMetadataProvider _knownMetadataProvider;

	private readonly INetworkCacheMetadataProvider _networkCacheMetadataProvider;

	private readonly INetworkMetadataProvider _networkMetadataProvider;

	private readonly IRegionDiscoveryProvider _regionDiscoveryProvider;

	public InstanceDiscoveryManager(IHttpManager httpManager, bool shouldClearCaches, InstanceDiscoveryResponse userProvidedInstanceDiscoveryResponse = null, Uri userProvidedInstanceDiscoveryUri = null)
		: this(httpManager, shouldClearCaches, (userProvidedInstanceDiscoveryResponse != null) ? new UserMetadataProvider(userProvidedInstanceDiscoveryResponse) : null, userProvidedInstanceDiscoveryUri)
	{
	}

	public InstanceDiscoveryManager(IHttpManager httpManager, bool shouldClearCaches, IUserMetadataProvider userMetadataProvider = null, Uri userProvidedInstanceDiscoveryUri = null, IKnownMetadataProvider knownMetadataProvider = null, INetworkCacheMetadataProvider networkCacheMetadataProvider = null, INetworkMetadataProvider networkMetadataProvider = null, IRegionDiscoveryProvider regionDiscoveryProvider = null)
	{
		_httpManager = httpManager ?? throw new ArgumentNullException("httpManager");
		_userMetadataProvider = userMetadataProvider;
		_knownMetadataProvider = knownMetadataProvider ?? new KnownMetadataProvider();
		_networkCacheMetadataProvider = networkCacheMetadataProvider ?? new NetworkCacheMetadataProvider();
		_networkMetadataProvider = networkMetadataProvider ?? new NetworkMetadataProvider(_httpManager, _networkCacheMetadataProvider, userProvidedInstanceDiscoveryUri);
		_regionDiscoveryProvider = regionDiscoveryProvider ?? new RegionDiscoveryProvider(_httpManager, shouldClearCaches);
		if (shouldClearCaches)
		{
			_networkCacheMetadataProvider.Clear();
		}
	}

	public async Task<InstanceDiscoveryMetadataEntry> GetMetadataEntryTryAvoidNetworkAsync(AuthorityInfo authorityInfo, IEnumerable<string> existingEnvironmentsInCache, RequestContext requestContext)
	{
		string environment = authorityInfo.Host;
		if (authorityInfo.IsInstanceDiscoverySupported)
		{
			InstanceDiscoveryMetadataEntry instanceDiscoveryMetadataEntry = _userMetadataProvider?.GetMetadataOrThrow(environment, requestContext.Logger);
			if (instanceDiscoveryMetadataEntry == null)
			{
				instanceDiscoveryMetadataEntry = await _regionDiscoveryProvider.GetMetadataAsync(authorityInfo.CanonicalAuthority, requestContext).ConfigureAwait(continueOnCapturedContext: false);
			}
			InstanceDiscoveryMetadataEntry instanceDiscoveryMetadataEntry2 = instanceDiscoveryMetadataEntry;
			if (instanceDiscoveryMetadataEntry2 == null && requestContext.ServiceBundle.Config.IsInstanceDiscoveryEnabled)
			{
				instanceDiscoveryMetadataEntry = _networkCacheMetadataProvider.GetMetadata(environment, requestContext.Logger);
				if (instanceDiscoveryMetadataEntry == null)
				{
					InstanceDiscoveryMetadataEntry instanceDiscoveryMetadataEntry3 = _knownMetadataProvider.GetMetadata(environment, existingEnvironmentsInCache, requestContext.Logger);
					if (instanceDiscoveryMetadataEntry3 == null)
					{
						instanceDiscoveryMetadataEntry3 = await GetMetadataEntryAsync(authorityInfo, requestContext).ConfigureAwait(continueOnCapturedContext: false);
					}
					instanceDiscoveryMetadataEntry = instanceDiscoveryMetadataEntry3;
				}
				instanceDiscoveryMetadataEntry2 = instanceDiscoveryMetadataEntry;
			}
			if (instanceDiscoveryMetadataEntry2 == null)
			{
				requestContext.Logger.Info(() => $"Skipping Instance discovery for {authorityInfo.AuthorityType} authority because it is not enabled.");
				instanceDiscoveryMetadataEntry2 = CreateEntryForSingleAuthority(authorityInfo.CanonicalAuthority);
			}
			return instanceDiscoveryMetadataEntry2;
		}
		requestContext.Logger.Info(() => $"Skipping Instance discovery for {authorityInfo.AuthorityType} authority because it is not supported.");
		return await GetMetadataEntryAsync(authorityInfo, requestContext).ConfigureAwait(continueOnCapturedContext: false);
	}

	public async Task<InstanceDiscoveryMetadataEntry> GetMetadataEntryAsync(AuthorityInfo authorityInfo, RequestContext requestContext, bool forceValidation = false)
	{
		Uri authorityUri = authorityInfo.CanonicalAuthority;
		string environment = authorityInfo.Host;
		if (authorityInfo.IsInstanceDiscoverySupported)
		{
			InstanceDiscoveryMetadataEntry entry = _userMetadataProvider?.GetMetadataOrThrow(environment, requestContext.Logger);
			if (entry == null && !requestContext.ServiceBundle.Config.IsInstanceDiscoveryEnabled)
			{
				entry = await _regionDiscoveryProvider.GetMetadataAsync(authorityUri, requestContext).ConfigureAwait(continueOnCapturedContext: false);
				if (entry == null)
				{
					requestContext.Logger.Info("[Instance Discovery] Skipping Instance discovery because it is disabled. ");
					return CreateEntryForSingleAuthority(authorityUri);
				}
			}
			if (entry == null && forceValidation)
			{
				await FetchNetworkMetadataOrFallbackAsync(requestContext, authorityUri).ConfigureAwait(continueOnCapturedContext: false);
			}
			requestContext.Logger.Info("[Instance Discovery] Instance discovery is enabled and will be performed");
			if (entry == null)
			{
				InstanceDiscoveryMetadataEntry instanceDiscoveryMetadataEntry = await _regionDiscoveryProvider.GetMetadataAsync(authorityUri, requestContext).ConfigureAwait(continueOnCapturedContext: false);
				if (instanceDiscoveryMetadataEntry == null)
				{
					instanceDiscoveryMetadataEntry = await FetchNetworkMetadataOrFallbackAsync(requestContext, authorityUri).ConfigureAwait(continueOnCapturedContext: false);
				}
				entry = instanceDiscoveryMetadataEntry;
			}
			if (entry == null)
			{
				string text = "[Instance Discovery] Instance metadata for this authority could neither be fetched nor found. MSAL will continue regardless. SSO might be broken if authority aliases exist. ";
				requestContext.Logger.WarningPii(text + "Authority: " + authorityInfo.CanonicalAuthority, text);
				entry = CreateEntryForSingleAuthority(authorityUri);
				_networkCacheMetadataProvider.AddMetadata(environment, entry);
			}
			return entry;
		}
		requestContext.Logger.Info("[Instance Discovery] Skipping Instance discovery for non-AAD authority. ");
		return CreateEntryForSingleAuthority(authorityUri);
	}

	private async Task<InstanceDiscoveryMetadataEntry> FetchNetworkMetadataOrFallbackAsync(RequestContext requestContext, Uri authorityUri)
	{
		try
		{
			return await _networkMetadataProvider.GetMetadataAsync(authorityUri, requestContext).ConfigureAwait(continueOnCapturedContext: false);
		}
		catch (MsalServiceException ex)
		{
			if (!requestContext.ServiceBundle.Config.Authority.AuthorityInfo.ValidateAuthority)
			{
				requestContext.Logger.Info("[Instance Discovery] Skipping Instance discovery as validate authority is set to false. ");
				return CreateEntryForSingleAuthority(authorityUri);
			}
			if (ex.ErrorCode == "invalid_instance")
			{
				requestContext.Logger.Error("[Instance Discovery] Instance discovery failed - invalid instance!");
				throw;
			}
			string text = "[Instance Discovery] Instance Discovery failed. Potential cause: no network connection or discovery endpoint is busy. See exception below. MSAL will continue without network instance metadata. ";
			requestContext.Logger.WarningPii(text + " Authority: " + authorityUri, text);
			requestContext.Logger.WarningPii(ex);
			return _knownMetadataProvider.GetMetadata(authorityUri.Host, Enumerable.Empty<string>(), requestContext.Logger);
		}
	}

	internal void AddTestValueToStaticProvider(string environment, InstanceDiscoveryMetadataEntry entry)
	{
		_networkCacheMetadataProvider.AddMetadata(environment, entry);
	}

	private static InstanceDiscoveryMetadataEntry CreateEntryForSingleAuthority(Uri authority)
	{
		InstanceDiscoveryMetadataEntry instanceDiscoveryMetadataEntry = new InstanceDiscoveryMetadataEntry();
		instanceDiscoveryMetadataEntry.Aliases = new string[1] { authority.Host };
		instanceDiscoveryMetadataEntry.PreferredCache = authority.Host;
		instanceDiscoveryMetadataEntry.PreferredNetwork = authority.Host;
		return instanceDiscoveryMetadataEntry;
	}
}
