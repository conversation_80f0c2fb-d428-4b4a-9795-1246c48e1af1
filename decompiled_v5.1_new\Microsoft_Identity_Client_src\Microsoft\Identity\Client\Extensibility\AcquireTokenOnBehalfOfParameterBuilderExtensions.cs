using System.ComponentModel;

namespace Microsoft.Identity.Client.Extensibility;

public static class AcquireTokenOnBehalfOfParameterBuilderExtensions
{
	[EditorBrowsable(EditorBrowsableState.Never)]
	public static AcquireTokenOnBehalfOfParameterBuilder WithSearchInCacheForLongRunningProcess(this AcquireTokenOnBehalfOfParameterBuilder builder, bool searchInCache = true)
	{
		builder.Parameters.SearchInCacheForLongRunningObo = searchInCache;
		return builder;
	}
}
