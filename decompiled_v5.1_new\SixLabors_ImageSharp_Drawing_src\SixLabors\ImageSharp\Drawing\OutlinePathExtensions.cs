using System;
using System.Collections.Generic;
using System.Numerics;
using SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper;

namespace SixLabors.ImageSharp.Drawing;

public static class OutlinePathExtensions
{
	private const float MiterOffsetDelta = 20f;

	private const JointStyle DefaultJointStyle = JointStyle.Square;

	private const EndCapStyle DefaultEndCapStyle = EndCapStyle.Butt;

	private static float CalculateScalingMatrix(float width, out Matrix3x2 scaleUpMartrix, out Matrix3x2 scaleDownMartrix)
	{
		scaleUpMartrix = Matrix3x2.Identity;
		scaleDownMartrix = Matrix3x2.Identity;
		if ((double)width < 0.5)
		{
			float scale = 1f / width;
			scaleUpMartrix = Matrix3x2.CreateScale(scale);
			scaleDownMartrix = Matrix3x2.CreateScale(width);
			width = 1f;
		}
		return width;
	}

	public static IPath GenerateOutline(this IPath path, float width)
	{
		return path.GenerateOutline(width, JointStyle.Square, EndCapStyle.Butt);
	}

	public static IPath GenerateOutline(this IPath path, float width, JointStyle jointStyle, EndCapStyle endCapStyle)
	{
		if (width <= 0f)
		{
			return Path.Empty;
		}
		width = CalculateScalingMatrix(width, out var scaleUpMartrix, out var scaleDownMartrix);
		ClipperOffset clipperOffset = new ClipperOffset(20f);
		clipperOffset.AddPath(path.Transform(scaleUpMartrix), jointStyle, endCapStyle);
		return clipperOffset.Execute(width).Transform(scaleDownMartrix);
	}

	public static IPath GenerateOutline(this IPath path, float width, ReadOnlySpan<float> pattern)
	{
		return path.GenerateOutline(width, pattern, startOff: false);
	}

	public static IPath GenerateOutline(this IPath path, float width, ReadOnlySpan<float> pattern, bool startOff)
	{
		return path.GenerateOutline(width, pattern, startOff, JointStyle.Square, EndCapStyle.Butt);
	}

	public static IPath GenerateOutline(this IPath path, float width, ReadOnlySpan<float> pattern, JointStyle jointStyle, EndCapStyle endCapStyle)
	{
		return path.GenerateOutline(width, pattern, startOff: false, jointStyle, endCapStyle);
	}

	public static IPath GenerateOutline(this IPath path, float width, ReadOnlySpan<float> pattern, bool startOff, JointStyle jointStyle, EndCapStyle endCapStyle)
	{
		if (width <= 0f)
		{
			return Path.Empty;
		}
		if (pattern.Length < 2)
		{
			return path.GenerateOutline(width, jointStyle, endCapStyle);
		}
		width = CalculateScalingMatrix(width, out var scaleUpMartrix, out var scaleDownMartrix);
		IEnumerable<ISimplePath> enumerable = path.Transform(scaleUpMartrix).Flatten();
		ClipperOffset clipperOffset = new ClipperOffset(20f);
		List<PointF> list = new List<PointF>();
		foreach (ISimplePath item in enumerable)
		{
			bool flag = !startOff;
			float num = pattern[0] * width;
			int num2 = 0;
			ReadOnlySpan<PointF> span = item.Points.Span;
			int num3 = span.Length;
			if (!item.IsClosed)
			{
				num3--;
			}
			int num4 = 0;
			Vector2 vector = span[0];
			while (num4 < num3)
			{
				int index = (num4 + 1) % span.Length;
				Vector2 vector2 = span[index];
				float num5 = Vector2.Distance(vector, vector2);
				if (num5 > num)
				{
					float num6 = num / num5;
					Vector2 vector3 = vector * (1f - num6) + vector2 * num6;
					list.Add(vector);
					list.Add(vector3);
					if (flag)
					{
						clipperOffset.AddPath(new ReadOnlySpan<PointF>(list.ToArray()), jointStyle, endCapStyle);
					}
					flag = !flag;
					list.Clear();
					vector = vector3;
					num2 = (num2 + 1) % pattern.Length;
					num = pattern[num2] * width;
				}
				else if (num5 <= num)
				{
					list.Add(vector);
					vector = vector2;
					num4++;
					num -= num5;
				}
			}
			if (list.Count > 0)
			{
				if (item.IsClosed)
				{
					list.Add(span[0]);
				}
				else
				{
					list.Add(span[span.Length - 1]);
				}
				if (flag)
				{
					clipperOffset.AddPath(new ReadOnlySpan<PointF>(list.ToArray()), jointStyle, endCapStyle);
				}
				list.Clear();
			}
		}
		return clipperOffset.Execute(width).Transform(scaleDownMartrix);
	}
}
