using Microsoft.IdentityModel.Abstractions;

namespace Microsoft.Identity.Client.TelemetryCore.TelemetryClient;

internal static class TelemetryClientExtensions
{
	internal static bool HasEnabledClients(this ITelemetryClient[] clients, string eventName)
	{
		for (int i = 0; i < clients.Length; i++)
		{
			if (clients[i].IsEnabled(eventName))
			{
				return true;
			}
		}
		return false;
	}

	internal static void TrackEvent(this ITelemetryClient[] clients, TelemetryEventDetails eventDetails)
	{
		foreach (ITelemetryClient telemetryClient in clients)
		{
			if (telemetryClient.IsEnabled(eventDetails.Name))
			{
				telemetryClient.TrackEvent(eventDetails);
			}
		}
	}
}
