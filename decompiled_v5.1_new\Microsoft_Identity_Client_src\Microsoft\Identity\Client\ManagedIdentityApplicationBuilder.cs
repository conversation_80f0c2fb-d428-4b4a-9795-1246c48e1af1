using System;
using Microsoft.Identity.Client.AppConfig;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.TelemetryCore.TelemetryClient;
using Microsoft.IdentityModel.Abstractions;

namespace Microsoft.Identity.Client;

public sealed class ManagedIdentityApplicationBuilder : BaseAbstractApplicationBuilder<ManagedIdentityApplicationBuilder>
{
	internal ManagedIdentityApplicationBuilder(ApplicationConfiguration configuration)
		: base(configuration)
	{
		ApplicationBase.GuardMobileFrameworks();
	}

	public static ManagedIdentityApplicationBuilder Create(ManagedIdentityId managedIdentityId)
	{
		ApplicationBase.GuardMobileFrameworks();
		return new ManagedIdentityApplicationBuilder(BuildConfiguration(managedIdentityId));
	}

	private static ApplicationConfiguration BuildConfiguration(ManagedIdentityId managedIdentityId)
	{
		if (managedIdentityId == null)
		{
			throw new ArgumentNullException("managedIdentityId");
		}
		return new ApplicationConfiguration(MsalClientType.ManagedIdentityClient)
		{
			ManagedIdentityId = managedIdentityId,
			CacheSynchronizationEnabled = false,
			AccessorOptions = CacheOptions.EnableSharedCacheOptions
		};
	}

	public ManagedIdentityApplicationBuilder WithTelemetryClient(params ITelemetryClient[] telemetryClients)
	{
		ValidateUseOfExperimentalFeature("ITelemetryClient");
		if (telemetryClients == null)
		{
			throw new ArgumentNullException("telemetryClients");
		}
		if (telemetryClients.Length != 0)
		{
			for (int i = 0; i < telemetryClients.Length; i++)
			{
				(telemetryClients[i] ?? throw new ArgumentNullException("telemetryClient")).Initialize();
			}
			base.Config.TelemetryClients = telemetryClients;
		}
		TelemetryClientLogMsalVersion();
		return this;
	}

	private void TelemetryClientLogMsalVersion()
	{
		if (base.Config.TelemetryClients.HasEnabledClients("config_update"))
		{
			MsalTelemetryEventDetails msalTelemetryEventDetails = new MsalTelemetryEventDetails("config_update");
			msalTelemetryEventDetails.SetProperty("MsalVersion", MsalIdHelper.GetMsalVersion());
			base.Config.TelemetryClients.TrackEvent(msalTelemetryEventDetails);
		}
	}

	internal ManagedIdentityApplicationBuilder WithAppTokenCacheInternalForTest(ITokenCacheInternal tokenCacheInternal)
	{
		base.Config.AppTokenCacheInternalForTest = tokenCacheInternal;
		return this;
	}

	public IManagedIdentityApplication Build()
	{
		return BuildConcrete();
	}

	internal ManagedIdentityApplication BuildConcrete()
	{
		DefaultConfiguration();
		return new ManagedIdentityApplication(BuildConfiguration());
	}

	private void DefaultConfiguration()
	{
		ComputeClientIdForCaching();
		base.Config.TenantId = "managed_identity";
		base.Config.RedirectUri = "https://replyUrlNotSet";
		base.Config.IsInstanceDiscoveryEnabled = false;
	}

	private void ComputeClientIdForCaching()
	{
		if (base.Config.ManagedIdentityId.IdType == ManagedIdentityIdType.SystemAssigned)
		{
			base.Config.ClientId = "system_assigned_managed_identity";
		}
		else
		{
			base.Config.ClientId = base.Config.ManagedIdentityId.UserAssignedId;
		}
	}
}
