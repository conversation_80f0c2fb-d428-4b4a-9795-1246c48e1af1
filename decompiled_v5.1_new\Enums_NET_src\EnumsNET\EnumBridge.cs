using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using EnumsNET.Numerics;

namespace EnumsNET;

internal sealed class EnumBridge<TEnum, TUnderlying, TUnderlyingOperations> : IEnumBridge<TUnderlying, TUnderlyingOperations>, IEnumBridge where TEnum : struct, Enum where TUnderlying : struct, IComparable<TUnderlying>, IEquatable<TUnderlying>, IConvertible where TUnderlyingOperations : struct, IUnderlyingOperations<TUnderlying>
{
	public object ToObjectUnchecked(TUnderlying value)
	{
		return UnsafeUtility.As<TUnderlying, TEnum>(ref value);
	}

	public bool CustomValidate(object customValidator, TUnderlying value)
	{
		return UnsafeUtility.As<IEnumValidatorAttribute<TEnum>>(customValidator).IsValid(UnsafeUtility.As<TUnderlying, TEnum>(ref value));
	}

	public EnumComparer CreateEnumComparer(EnumCache enumCache)
	{
		return new EnumComparer<TEnum>(enumCache);
	}

	public EnumMember CreateEnumMember(EnumMemberInternal member)
	{
		return new EnumMember<TEnum>(member);
	}

	public TUnderlying? IsEnum(object value)
	{
		if (!(value is TEnum source))
		{
			return null;
		}
		return UnsafeUtility.As<TEnum, TUnderlying>(ref source);
	}

	public IValuesContainer CreateValuesContainer(IEnumerable<EnumMemberInternal<TUnderlying, TUnderlyingOperations>> members, int count, bool cached)
	{
		return new ValuesContainer<TEnum, TUnderlying, TUnderlyingOperations>(members, count, cached);
	}

	public IReadOnlyList<EnumMember> CreateMembersContainer(IEnumerable<EnumMemberInternal> members, int count, bool cached)
	{
		return new MembersContainer<TEnum>(members, count, cached);
	}
}
