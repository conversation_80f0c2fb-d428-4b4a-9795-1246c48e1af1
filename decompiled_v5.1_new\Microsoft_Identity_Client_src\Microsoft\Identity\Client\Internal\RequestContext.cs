using System;
using System.Threading;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal.Logger;
using Microsoft.Identity.Client.TelemetryCore;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;

namespace Microsoft.Identity.Client.Internal;

internal class RequestContext
{
	public Guid CorrelationId { get; }

	public ILoggerAdapter Logger { get; }

	public IServiceBundle ServiceBundle { get; }

	public ApiEvent ApiEvent { get; set; }

	public CancellationToken UserCancellationToken { get; }

	public RequestContext(IServiceBundle serviceBundle, Guid correlationId, CancellationToken cancellationToken = default(CancellationToken))
	{
		ServiceBundle = serviceBundle ?? throw new ArgumentNullException("serviceBundle");
		Logger = LoggerHelper.CreateLogger(correlationId, ServiceBundle.Config);
		CorrelationId = correlationId;
		UserCancellationToken = cancellationToken;
	}

	public TelemetryHelper CreateTelemetryHelper(ApiEvent eventToStart)
	{
		return new TelemetryHelper(ServiceBundle.HttpTelemetryManager, eventToStart);
	}
}
