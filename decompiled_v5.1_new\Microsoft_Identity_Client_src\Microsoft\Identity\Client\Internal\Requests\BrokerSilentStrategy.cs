using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal.Broker;
using Microsoft.Identity.Client.Internal.Requests.Silent;
using Microsoft.Identity.Client.OAuth2;

namespace Microsoft.Identity.Client.Internal.Requests;

internal class BrokerSilentStrategy : ISilentAuthRequestStrategy
{
	internal AuthenticationRequestParameters _authenticationRequestParameters;

	protected IServiceBundle _serviceBundle;

	private readonly AcquireTokenSilentParameters _silentParameters;

	private readonly SilentRequest _silentRequest;

	private readonly ILoggerAdapter _logger;

	internal IBroker Broker { get; }

	public BrokerSilentStrategy(SilentRequest request, IServiceBundle serviceBundle, AuthenticationRequestParameters authenticationRequestParameters, AcquireTokenSilentParameters silentParameters, IBroker broker)
	{
		_authenticationRequestParameters = authenticationRequestParameters;
		_silentParameters = silentParameters;
		_serviceBundle = serviceBundle;
		_silentRequest = request;
		Broker = broker ?? throw new ArgumentNullException("broker");
		_logger = authenticationRequestParameters.RequestContext.Logger;
	}

	public async Task<AuthenticationResult> ExecuteAsync(CancellationToken cancellationToken)
	{
		if (!Broker.IsBrokerInstalledAndInvokable(_authenticationRequestParameters.AuthorityInfo.AuthorityType))
		{
			if (_authenticationRequestParameters.PopAuthenticationConfiguration != null)
			{
				throw new MsalClientException("broker_application_required", "MSAL cannot invoke the broker and it is required for Proof-of-Possession. WAM (Broker) may not be installed on the user's device or there was an error invoking the broker. Use IPublicClientApplication.IsProofOfPossessionSupportedByClient to ensure Proof-of-Possession can be performed before using WithProofOfPossession.Check logs for more details and see https://aka.ms/msal-net-pop. ");
			}
			_logger.Warning("Broker is not installed or authority type is incorrect. Cannot respond to silent request.");
			return null;
		}
		MsalTokenResponse msalTokenResponse = await SendTokenRequestToBrokerAsync().ConfigureAwait(continueOnCapturedContext: false);
		if (msalTokenResponse != null)
		{
			ValidateResponseFromBroker(msalTokenResponse);
			Metrics.IncrementTotalAccessTokensFromBroker();
			return await _silentRequest.CacheTokenResponseAndCreateAuthenticationResultAsync(msalTokenResponse).ConfigureAwait(continueOnCapturedContext: false);
		}
		return null;
	}

	private async Task<MsalTokenResponse> SendTokenRequestToBrokerAsync()
	{
		_authenticationRequestParameters.RequestContext.Logger.Info("Can invoke broker. Will attempt to acquire token with broker. ");
		return PublicClientApplication.IsOperatingSystemAccount(_authenticationRequestParameters.Account) ? (await Broker.AcquireTokenSilentDefaultUserAsync(_authenticationRequestParameters, _silentParameters).ConfigureAwait(continueOnCapturedContext: false)) : (await Broker.AcquireTokenSilentAsync(_authenticationRequestParameters, _silentParameters).ConfigureAwait(continueOnCapturedContext: false));
	}

	internal void ValidateResponseFromBroker(MsalTokenResponse msalTokenResponse)
	{
		_logger.Info("Checking MsalTokenResponse returned from broker. ");
		if (msalTokenResponse.AccessToken != null)
		{
			_logger.Info("Success. Response contains an access token. ");
			return;
		}
		if (msalTokenResponse.Error != null)
		{
			_logger.Info(() => LogMessages.ErrorReturnedInBrokerResponse(msalTokenResponse.Error));
			if (msalTokenResponse.Error == "no_tokens_found" || msalTokenResponse.Error == "no_account_found" || msalTokenResponse.Error == "Broker refresh token is invalid")
			{
				throw new MsalUiRequiredException(msalTokenResponse.Error, msalTokenResponse.ErrorDescription);
			}
			throw MsalServiceExceptionFactory.FromBrokerResponse(msalTokenResponse, "Broker response returned error: " + msalTokenResponse.ErrorDescription);
		}
		_logger.Info("Unknown error returned in broker response. ");
		throw new MsalServiceException("broker_response_returned_error", "Broker response returned an error which does not contain an error or error description. See https://aka.ms/msal-brokers for details. ", null);
	}
}
