# 批量更新项目依赖引用脚本

$mappingFile = "D:\Dev\bibi\decompiled_v5.1_new\dependency_mapping.json"
$baseDir = "D:\Dev\bibi\decompiled_v5.1_new"

# 读取映射配置
$mapping = Get-Content $mappingFile | ConvertFrom-Json

Write-Host "开始批量更新项目依赖引用..." -ForegroundColor Yellow

# 获取所有项目目录
$projectDirs = Get-ChildItem "$baseDir\*_src" -Directory

foreach ($projectDir in $projectDirs) {
    $csprojFile = Get-ChildItem $projectDir.FullName -Filter "*.csproj" | Select-Object -First 1
    
    if (-not $csprojFile) {
        Write-Host "跳过 $($projectDir.Name): 未找到.csproj文件" -ForegroundColor Yellow
        continue
    }
    
    Write-Host "处理项目: $($projectDir.Name)" -ForegroundColor Green
    
    # 读取项目文件内容
    $content = Get-Content $csprojFile.FullName -Raw
    
    # 简单的字符串替换方法
    $replacements = 0
    
    # 替换常见的依赖
    $commonMappings = @{
        'Newtonsoft.Json.dll' = 'Newtonsoft.Json" Version="13.0.3'
        'log4net.dll' = 'log4net" Version="2.0.15'
        'Microsoft.Data.SqlClient.dll' = 'Microsoft.Data.SqlClient" Version="5.1.0'
        'Azure.Core.dll' = 'Azure.Core" Version="1.35.0'
        'Quartz.dll' = 'Quartz" Version="3.6.0'
        'NPOI.Core.dll' = 'NPOI" Version="2.6.0'
        'SixLabors.ImageSharp.dll' = 'SixLabors.ImageSharp" Version="2.1.0'
        'Microsoft.Identity.Client.dll' = 'Microsoft.Identity.Client" Version="4.56.0'
    }
    
    foreach ($dll in $commonMappings.Keys) {
        $packageRef = $commonMappings[$dll]
        if ($content -match [regex]::Escape($dll)) {
            $oldPattern = "<Reference Include=`"[^`"]+`">\s*<HintPath>[^<]*$dll</HintPath>\s*</Reference>"
            $newRef = "<PackageReference Include=`"$packageRef`" />"
            
            if ($content -match $oldPattern) {
                $content = $content -replace $oldPattern, $newRef
                $replacements++
                Write-Host "  替换: $dll" -ForegroundColor Green
            }
        }
    }
    
    # 如果有替换，保存文件
    if ($replacements -gt 0) {
        Set-Content $csprojFile.FullName -Value $content -Encoding UTF8
        Write-Host "  已更新 $replacements 个依赖" -ForegroundColor Cyan
    }
}

Write-Host "依赖更新完成!" -ForegroundColor Yellow