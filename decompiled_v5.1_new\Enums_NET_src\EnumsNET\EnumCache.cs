using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;
using System.Text;
using System.Threading;
using EnumsNET.Numerics;
using EnumsNET.Utilities;

namespace EnumsNET;

internal abstract class EnumCache
{
	public readonly Type EnumType;

	public readonly Type UnderlyingType;

	public readonly TypeCode TypeCode;

	public readonly bool IsFlagEnum;

	private protected readonly bool _hasDuplicateValues;

	private IReadOnlyList<string>? _names;

	private IValuesContainer? _values;

	private IReadOnlyList<EnumMember>? _members;

	private EnumComparer? _enumComparer;

	internal EnumCache? Next;

	public EnumComparer EnumComparer
	{
		get
		{
			EnumComparer enumComparer = _enumComparer;
			return enumComparer ?? Interlocked.CompareExchange(ref _enumComparer, enumComparer = CreateEnumComparer(), null) ?? enumComparer;
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	protected static bool IsNumeric(ReadOnlySpan<char> value)
	{
		if (value.Length > 0)
		{
			char c;
			if ((uint)((c = value[0]) - 48) > 9u && c != '-')
			{
				return c == '+';
			}
			return true;
		}
		return false;
	}

	protected EnumCache(Type enumType, Type underlyingType, bool isFlagEnum, bool hasDuplicateValues)
	{
		EnumType = enumType;
		UnderlyingType = underlyingType;
		TypeCode = underlyingType.GetTypeCode();
		IsFlagEnum = isFlagEnum;
		_hasDuplicateValues = hasDuplicateValues;
	}

	public IReadOnlyList<EnumMember> GetMembers(EnumMemberSelection selection)
	{
		bool flag = selection switch
		{
			EnumMemberSelection.Distinct => !_hasDuplicateValues, 
			EnumMemberSelection.All => true, 
			_ => false, 
		};
		IReadOnlyList<EnumMember> readOnlyList;
		if (!flag || (readOnlyList = _members) == null)
		{
			readOnlyList = GetMembersInternal(selection, flag);
			if (flag)
			{
				readOnlyList = Interlocked.CompareExchange(ref _members, readOnlyList, null) ?? readOnlyList;
			}
		}
		return readOnlyList;
	}

	public IReadOnlyList<string> GetNames(EnumMemberSelection selection)
	{
		bool flag = selection switch
		{
			EnumMemberSelection.Distinct => !_hasDuplicateValues, 
			EnumMemberSelection.All => true, 
			_ => false, 
		};
		IReadOnlyList<string> readOnlyList;
		if (!flag || (readOnlyList = _names) == null)
		{
			readOnlyList = GetNamesInternal(selection, flag);
			if (flag)
			{
				readOnlyList = Interlocked.CompareExchange(ref _names, readOnlyList, null) ?? readOnlyList;
			}
		}
		return readOnlyList;
	}

	public IValuesContainer GetValues(EnumMemberSelection selection)
	{
		bool flag = selection switch
		{
			EnumMemberSelection.Distinct => !_hasDuplicateValues, 
			EnumMemberSelection.All => true, 
			_ => false, 
		};
		IValuesContainer valuesContainer;
		if (!flag || (valuesContainer = _values) == null)
		{
			valuesContainer = GetValuesInternal(selection, flag);
			if (flag)
			{
				valuesContainer = Interlocked.CompareExchange(ref _values, valuesContainer, null) ?? valuesContainer;
			}
		}
		return valuesContainer;
	}

	protected abstract EnumComparer CreateEnumComparer();

	public abstract string AsString(ref byte value);

	public abstract string AsString(ref byte value, string format);

	public abstract string? AsString(ref byte value, EnumFormat format);

	public abstract string? AsString(ref byte value, ValueCollection<EnumFormat> formats);

	public abstract string AsString(object value);

	public abstract string AsString(object value, string format);

	public abstract string? AsString(object value, EnumFormat format);

	public abstract string? AsString(object value, ValueCollection<EnumFormat> formats);

	public abstract void CombineFlags(ref byte value, ref byte otherFlags, ref byte result);

	public abstract void CombineFlags(ref byte flag0, ref byte flag1, ref byte flag2, ref byte result);

	public abstract void CombineFlags(ref byte flag0, ref byte flag1, ref byte flag2, ref byte flag3, ref byte result);

	public abstract void CombineFlags(ref byte flag0, ref byte flag1, ref byte flag2, ref byte flag3, ref byte flag4, ref byte result);

	public abstract object CombineFlags(IEnumerable<object?>? flags, bool isNullable);

	public abstract object CombineFlags(object value, object otherFlags);

	public abstract void CommonFlags(ref byte value, ref byte otherFlags, ref byte result);

	public abstract object CommonFlags(object value, object otherFlags);

	public abstract int CompareTo(ref byte value, ref byte other);

	public abstract int CompareTo(object value, object other);

	public abstract bool Equals(ref byte value, ref byte other);

	public new abstract bool Equals(object value, object other);

	public abstract string? FormatFlags(ref byte value, string? delimiter, ValueCollection<EnumFormat> formats);

	public abstract string? FormatFlags(object value, string? delimiter, ValueCollection<EnumFormat> formats);

	public abstract void GetAllFlags(ref byte result);

	public abstract object GetAllFlags();

	public abstract int GetFlagCount();

	public abstract int GetFlagCount(ref byte value);

	public abstract int GetFlagCount(object value);

	public abstract int GetFlagCount(ref byte value, ref byte otherFlags);

	public abstract int GetFlagCount(object value, object otherFlags);

	public abstract IReadOnlyList<EnumMember> GetFlagMembers(ref byte value);

	public abstract IReadOnlyList<EnumMember> GetFlagMembers(object value);

	public abstract IValuesContainer GetFlags(ref byte value);

	public abstract IReadOnlyList<object> GetFlags(object value);

	public abstract int GetHashCode(ref byte value);

	public abstract EnumMemberInternal? GetMember(ref byte value);

	public abstract EnumMemberInternal? GetMember(object value);

	public abstract EnumMember? GetMember(ReadOnlySpan<char> value, bool ignoreCase, ValueCollection<EnumFormat> formats);

	protected abstract IReadOnlyList<EnumMember> GetMembersInternal(EnumMemberSelection selection, bool cached);

	public abstract int GetMemberCount(EnumMemberSelection selection);

	protected abstract IReadOnlyList<string> GetNamesInternal(EnumMemberSelection selection, bool cached);

	public abstract object GetUnderlyingValue(ref byte value);

	public abstract object GetUnderlyingValue(object value);

	protected abstract IValuesContainer GetValuesInternal(EnumMemberSelection selection, bool cached);

	public abstract bool HasAllFlags(ref byte value);

	public abstract bool HasAllFlags(object value);

	public abstract bool HasAllFlags(ref byte value, ref byte otherFlags);

	public abstract bool HasAllFlags(object value, object otherFlags);

	public abstract bool HasAnyFlags(ref byte value);

	public abstract bool HasAnyFlags(object value);

	public abstract bool HasAnyFlags(ref byte value, ref byte otherFlags);

	public abstract bool HasAnyFlags(object value, object otherFlags);

	public abstract bool IsDefined(ref byte value);

	public abstract bool IsDefined(object value);

	public abstract bool IsValid(ref byte value, EnumValidation validation);

	public abstract bool IsValid(object value, EnumValidation validation);

	public abstract bool IsValidFlagCombination(ref byte value);

	public abstract bool IsValidFlagCombination(object value);

	public abstract void Parse(ReadOnlySpan<char> value, bool ignoreCase, ValueCollection<EnumFormat> formats, ref byte result);

	public abstract object Parse(ReadOnlySpan<char> value, bool ignoreCase, ValueCollection<EnumFormat> formats);

	public abstract void Parse(ReadOnlySpan<char> value, bool ignoreCase, ref byte result);

	public abstract object Parse(ReadOnlySpan<char> value, bool ignoreCase);

	public abstract void ParseFlags(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, ValueCollection<EnumFormat> formats, ref byte result);

	public abstract object ParseFlags(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, ValueCollection<EnumFormat> formats);

	public abstract void RemoveFlags(ref byte value, ref byte otherFlags, ref byte result);

	public abstract object RemoveFlags(object value, object otherFlags);

	public abstract byte ToByte(ref byte value);

	public abstract byte ToByte(object value);

	public abstract void ToggleFlags(ref byte value, ref byte result);

	public abstract object ToggleFlags(object value);

	public abstract void ToggleFlags(ref byte value, ref byte otherFlags, ref byte result);

	public abstract object ToggleFlags(object value, object otherFlags);

	public abstract short ToInt16(ref byte value);

	public abstract short ToInt16(object value);

	public abstract int ToInt32(ref byte value);

	public abstract int ToInt32(object value);

	public abstract long ToInt64(ref byte value);

	public abstract long ToInt64(object value);

	public abstract void ToObject(ulong value, EnumValidation validation, ref byte result);

	public abstract object ToObject(ulong value, EnumValidation validation);

	public abstract void ToObject(object value, EnumValidation validation, ref byte result);

	public abstract object ToObject(object value, EnumValidation validation);

	public abstract void ToObject(long value, EnumValidation validation, ref byte result);

	public abstract object ToObject(long value, EnumValidation validation);

	public abstract sbyte ToSByte(ref byte value);

	public abstract sbyte ToSByte(object value);

	public abstract ushort ToUInt16(ref byte value);

	public abstract ushort ToUInt16(object value);

	public abstract uint ToUInt32(ref byte value);

	public abstract uint ToUInt32(object value);

	public abstract ulong ToUInt64(ref byte value);

	public abstract ulong ToUInt64(object value);

	public abstract bool TryFormat(ref byte value, Span<char> destination, out int charsWritten);

	public abstract bool TryFormat(object value, Span<char> destination, out int charsWritten);

	public abstract bool TryFormat(ref byte value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> format);

	public abstract bool TryFormat(object value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> format);

	public abstract bool TryFormat(ref byte value, Span<char> destination, out int charsWritten, ValueCollection<EnumFormat> formats);

	public abstract bool TryFormat(object value, Span<char> destination, out int charsWritten, ValueCollection<EnumFormat> formats);

	public abstract bool TryFormatFlags(ref byte value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> delimiter, ValueCollection<EnumFormat> formats);

	public abstract bool TryFormatFlags(object value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> delimiter, ValueCollection<EnumFormat> formats);

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	internal static bool TryWriteNonNullableStringToSpan(string str, Span<char> destination, out int charsWritten)
	{
		bool flag = str.AsSpan().TryCopyTo(destination);
		charsWritten = (flag ? str.Length : 0);
		return flag;
	}

	public abstract bool TryParse(ReadOnlySpan<char> value, bool ignoreCase, ref byte result, ValueCollection<EnumFormat> formats);

	public abstract bool TryParse(ReadOnlySpan<char> value, bool ignoreCase, out object? result, ValueCollection<EnumFormat> formats);

	public abstract bool TryParse(ReadOnlySpan<char> value, bool ignoreCase, ref byte result);

	public abstract bool TryParse(ReadOnlySpan<char> value, bool ignoreCase, out object? result);

	public abstract bool TryParseFlags(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, ref byte result, ValueCollection<EnumFormat> formats);

	public abstract bool TryParseFlags(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out object? result, ValueCollection<EnumFormat> formats);

	public abstract bool TryToObject(ulong value, ref byte result, EnumValidation validation);

	public abstract bool TryToObject(ulong value, out object? result, EnumValidation validation);

	public abstract bool TryToObject(object? value, ref byte result, EnumValidation validation);

	public abstract bool TryToObject(object? value, out object? result, EnumValidation validation);

	public abstract bool TryToObject(long value, ref byte result, EnumValidation validation);

	public abstract bool TryToObject(long value, out object? result, EnumValidation validation);

	public abstract void Validate(ref byte value, string paramName, EnumValidation validation);

	public abstract object Validate(object value, string paramName, EnumValidation validation);
}
internal abstract class EnumCache<TUnderlying, TUnderlyingOperations> : EnumCache where TUnderlying : struct, IComparable<TUnderlying>, IEquatable<TUnderlying>, IConvertible where TUnderlyingOperations : struct, IUnderlyingOperations<TUnderlying>
{
	internal sealed class EnumMemberParser
	{
		private readonly struct Entry
		{
			public readonly int OrdinalNext;

			public readonly int OrdinalIgnoreCaseNext;

			public readonly string FormattedValue;

			public readonly EnumMemberInternal<TUnderlying, TUnderlyingOperations> Member;

			public Entry(int ordinalNext, int ordinalIgnoreCaseNext, string formattedValue, EnumMemberInternal<TUnderlying, TUnderlyingOperations> member)
			{
				OrdinalNext = ordinalNext;
				OrdinalIgnoreCaseNext = ordinalIgnoreCaseNext;
				FormattedValue = formattedValue;
				Member = member;
			}
		}

		private readonly int[] _ordinalBuckets;

		private readonly int[] _ordinalIgnoreCaseBuckets;

		private readonly Entry[] _entries;

		public EnumMemberParser(EnumFormat format, EnumCache<TUnderlying, TUnderlyingOperations> enumCache)
		{
			int num = enumCache._buckets.Length;
			int[] array = new int[num];
			int[] array2 = new int[num];
			EnumMemberInternal<TUnderlying, TUnderlyingOperations>[] members = enumCache._members;
			Entry[] array3 = new Entry[members.Length];
			for (int i = 0; i < members.Length; i++)
			{
				EnumMemberInternal<TUnderlying, TUnderlyingOperations> enumMemberInternal = members[i];
				string text = enumMemberInternal.AsString(format);
				if (text != null)
				{
					int hashCode = text.GetHashCode();
					ref int reference = ref array[hashCode & (num - 1)];
					int hashCode2 = StringComparer.OrdinalIgnoreCase.GetHashCode(text);
					ref int reference2 = ref array2[hashCode2 & (num - 1)];
					array3[i] = new Entry(reference - 1, reference2 - 1, text, enumMemberInternal);
					reference = i + 1;
					reference2 = i + 1;
				}
			}
			_ordinalBuckets = array;
			_ordinalIgnoreCaseBuckets = array2;
			_entries = array3;
		}

		[MethodImpl(MethodImplOptions.AggressiveInlining)]
		internal bool TryParse(ReadOnlySpan<char> formattedValue, bool ignoreCase, [NotNullWhen(true)] out EnumMemberInternal<TUnderlying, TUnderlyingOperations>? result)
		{
			Entry[] entries = _entries;
			if (ignoreCase)
			{
				int hashCode = string.GetHashCode(formattedValue, StringComparison.OrdinalIgnoreCase);
				for (int num = _ordinalIgnoreCaseBuckets[hashCode & (_ordinalIgnoreCaseBuckets.Length - 1)] - 1; num >= 0; num = entries[num].OrdinalIgnoreCaseNext)
				{
					if (MemoryExtensions.Equals(formattedValue, entries[num].FormattedValue, StringComparison.OrdinalIgnoreCase))
					{
						result = entries[num].Member;
						return true;
					}
				}
			}
			else
			{
				int hashCode2 = string.GetHashCode(formattedValue, StringComparison.Ordinal);
				for (int num2 = _ordinalBuckets[hashCode2 & (_ordinalBuckets.Length - 1)] - 1; num2 >= 0; num2 = entries[num2].OrdinalNext)
				{
					if (MemoryExtensions.Equals(formattedValue, entries[num2].FormattedValue, StringComparison.Ordinal))
					{
						result = entries[num2].Member;
						return true;
					}
				}
			}
			result = null;
			return false;
		}
	}

	internal readonly IEnumBridge<TUnderlying, TUnderlyingOperations> EnumBridge;

	private readonly bool _isContiguous;

	private readonly object? _customValidator;

	private readonly TUnderlying _allFlags;

	private protected readonly TUnderlying _maxDefined;

	private protected readonly TUnderlying _minDefined;

	private readonly EnumMemberInternal<TUnderlying, TUnderlyingOperations>?[] _buckets;

	private readonly EnumMemberInternal<TUnderlying, TUnderlyingOperations>[] _members;

	private readonly int _distinctCount;

	private EnumMemberParser?[] _enumMemberParsers = ArrayHelper.Empty<EnumMemberParser>();

	protected EnumCache(Type enumType, IEnumBridge<TUnderlying, TUnderlyingOperations> enumBridge, bool isFlagEnum, EnumMemberInternal<TUnderlying, TUnderlyingOperations>[] members, EnumMemberInternal<TUnderlying, TUnderlyingOperations>?[] buckets, TUnderlying allFlags, int distinctCount, bool isContiguous, object? customValidator)
		: base(enumType, typeof(TUnderlying), isFlagEnum, distinctCount != members.Length)
	{
		EnumBridge = enumBridge;
		_customValidator = customValidator;
		_members = members;
		_buckets = buckets;
		_allFlags = allFlags;
		_distinctCount = distinctCount;
		_isContiguous = isContiguous;
		if (members.Length != 0)
		{
			_maxDefined = members[^1].Value;
			_minDefined = members[0].Value;
		}
		for (int i = 0; i < members.Length; i++)
		{
			members[i].EnumCache = this;
		}
	}

	protected sealed override EnumComparer CreateEnumComparer()
	{
		return EnumBridge.CreateEnumComparer(this);
	}

	public sealed override int GetMemberCount(EnumMemberSelection selection)
	{
		switch (selection)
		{
		case EnumMemberSelection.All:
		case EnumMemberSelection.DisplayOrder:
			return _members.Length;
		case EnumMemberSelection.Flags:
		case EnumMemberSelection.Distinct | EnumMemberSelection.Flags:
		case EnumMemberSelection.Flags | EnumMemberSelection.DisplayOrder:
		case EnumMemberSelection.Distinct | EnumMemberSelection.Flags | EnumMemberSelection.DisplayOrder:
			return GetFlagCount();
		case EnumMemberSelection.Distinct:
		case EnumMemberSelection.Distinct | EnumMemberSelection.DisplayOrder:
			return _distinctCount;
		default:
			throw new ArgumentException("invalid value of " + selection.AsString() + " for EnumMemberSelection", "selection");
		}
	}

	protected sealed override IReadOnlyList<EnumMember> GetMembersInternal(EnumMemberSelection selection, bool cached)
	{
		return EnumBridge.CreateMembersContainer(GetMembersInternal(selection), GetMemberCount(selection), cached);
	}

	protected sealed override IReadOnlyList<string> GetNamesInternal(EnumMemberSelection selection, bool cached)
	{
		return new NamesContainer(GetMembersInternal(selection), GetMemberCount(selection), cached);
	}

	protected sealed override IValuesContainer GetValuesInternal(EnumMemberSelection selection, bool cached)
	{
		return EnumBridge.CreateValuesContainer(GetMembersInternal(selection), GetMemberCount(selection), cached);
	}

	private IEnumerable<EnumMemberInternal<TUnderlying, TUnderlyingOperations>> GetMembersInternal(EnumMemberSelection selection)
	{
		IEnumerable<EnumMemberInternal<TUnderlying, TUnderlyingOperations>> enumerable2;
		switch (selection)
		{
		case EnumMemberSelection.All:
		case EnumMemberSelection.DisplayOrder:
			enumerable2 = _members;
			break;
		case EnumMemberSelection.Flags:
		case EnumMemberSelection.Distinct | EnumMemberSelection.Flags:
		case EnumMemberSelection.Flags | EnumMemberSelection.DisplayOrder:
		case EnumMemberSelection.Distinct | EnumMemberSelection.Flags | EnumMemberSelection.DisplayOrder:
			enumerable2 = EnumerateFlagMembers(_allFlags);
			break;
		case EnumMemberSelection.Distinct:
		case EnumMemberSelection.Distinct | EnumMemberSelection.DisplayOrder:
		{
			IEnumerable<EnumMemberInternal<TUnderlying, TUnderlyingOperations>> enumerable;
			if (!_hasDuplicateValues)
			{
				IEnumerable<EnumMemberInternal<TUnderlying, TUnderlyingOperations>> members = _members;
				enumerable = members;
			}
			else
			{
				enumerable = _members.Distinct();
			}
			enumerable2 = enumerable;
			break;
		}
		default:
			throw new ArgumentException("invalid value of " + selection.AsString() + " for EnumMemberSelection", "selection");
		}
		if (!selection.HasAnyFlags(EnumMemberSelection.DisplayOrder))
		{
			return enumerable2;
		}
		return enumerable2.OrderBy((EnumMemberInternal<TUnderlying, TUnderlyingOperations> m) => m.Attributes.Get<DisplayAttribute>()?.GetOrder() ?? int.MaxValue);
	}

	public sealed override void ToObject(ulong value, EnumValidation validation, ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = ToObjectInternal(value, validation);
	}

	public sealed override object ToObject(ulong value, EnumValidation validation)
	{
		return EnumBridge.ToObjectUnchecked(ToObjectInternal(value, validation));
	}

	public sealed override void ToObject(object value, EnumValidation validation, ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = ToObjectInternal(value, validation);
	}

	public sealed override object ToObject(object value, EnumValidation validation)
	{
		return EnumBridge.ToObjectUnchecked(ToObjectInternal(value, validation));
	}

	public sealed override void ToObject(long value, EnumValidation validation, ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = ToObjectInternal(value, validation);
	}

	public sealed override object ToObject(long value, EnumValidation validation)
	{
		return EnumBridge.ToObjectUnchecked(ToObjectInternal(value, validation));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public TUnderlying ToObject(object value)
	{
		return EnumBridge.IsEnum(value) ?? ToObjectNoInlining(value);
	}

	[MethodImpl(MethodImplOptions.NoInlining)]
	public TUnderlying ToObjectNoInlining(object value)
	{
		if (value is TUnderlying)
		{
			return (TUnderlying)value;
		}
		Preconditions.NotNull(value, "value");
		Type type = value.GetType();
		return (Nullable.GetUnderlyingType(type) ?? type).GetTypeCode() switch
		{
			TypeCode.Boolean => ToObject(Convert.ToByte((bool)value)), 
			TypeCode.Char => ToObject((char)value), 
			TypeCode.SByte => ToObject((sbyte)value), 
			TypeCode.Byte => ToObject((byte)value), 
			TypeCode.Int16 => ToObject((short)value), 
			TypeCode.UInt16 => ToObject((ushort)value), 
			TypeCode.Int32 => ToObject((int)value), 
			TypeCode.UInt32 => ToObject((uint)value), 
			TypeCode.Int64 => ToObject((long)value), 
			TypeCode.UInt64 => ToObject((ulong)value), 
			TypeCode.String => ParseInternal((string)value, ignoreCase: false), 
			_ => throw new ArgumentException($"value is not type {EnumType}, SByte, Int16, Int32, Int64, Byte, UInt16, UInt32, UInt64, Boolean, Char, or String."), 
		};
	}

	public TUnderlying ToObjectInternal(object value, EnumValidation validation)
	{
		TUnderlying? val = EnumBridge.IsEnum(value);
		if (val.HasValue)
		{
			Validate(val.GetValueOrDefault(), "value", validation);
			return val.GetValueOrDefault();
		}
		if (value is TUnderlying val2)
		{
			Validate(val2, "value", validation);
			return val2;
		}
		Preconditions.NotNull(value, "value");
		Type type = value.GetType();
		switch ((Nullable.GetUnderlyingType(type) ?? type).GetTypeCode())
		{
		case TypeCode.Boolean:
			return ToObjectInternal(Convert.ToByte((bool)value), validation);
		case TypeCode.Char:
			return ToObjectInternal((char)value, validation);
		case TypeCode.SByte:
			return ToObjectInternal((sbyte)value, validation);
		case TypeCode.Byte:
			return ToObjectInternal((byte)value, validation);
		case TypeCode.Int16:
			return ToObjectInternal((short)value, validation);
		case TypeCode.UInt16:
			return ToObjectInternal((ushort)value, validation);
		case TypeCode.Int32:
			return ToObjectInternal((int)value, validation);
		case TypeCode.UInt32:
			return ToObjectInternal((uint)value, validation);
		case TypeCode.Int64:
			return ToObjectInternal((long)value, validation);
		case TypeCode.UInt64:
			return ToObjectInternal((ulong)value, validation);
		case TypeCode.String:
		{
			TUnderlying val3 = ParseInternal((string)value, ignoreCase: false);
			Validate(val3, "value", validation);
			return val3;
		}
		default:
			throw new ArgumentException($"value is not type {EnumType}, SByte, Int16, Int32, Int64, Byte, UInt16, UInt32, UInt64, Boolean, Char, or String.");
		}
	}

	public TUnderlying ToObject(long value)
	{
		TUnderlyingOperations val = default(TUnderlyingOperations);
		if (!val.IsInValueRange(value))
		{
			throw new OverflowException("value is outside the underlying type's value range");
		}
		return val.Create(value);
	}

	public TUnderlying ToObjectInternal(long value, EnumValidation validation)
	{
		TUnderlyingOperations val = default(TUnderlyingOperations);
		if (!val.IsInValueRange(value))
		{
			throw new OverflowException("value is outside the underlying type's value range");
		}
		TUnderlying val2 = val.Create(value);
		Validate(val2, "value", validation);
		return val2;
	}

	public TUnderlying ToObject(ulong value)
	{
		TUnderlyingOperations val = default(TUnderlyingOperations);
		if (!val.IsInValueRange(value))
		{
			throw new OverflowException("value is outside the underlying type's value range");
		}
		return val.Create((long)value);
	}

	public TUnderlying ToObjectInternal(ulong value, EnumValidation validation)
	{
		TUnderlyingOperations val = default(TUnderlyingOperations);
		if (!val.IsInValueRange(value))
		{
			throw new OverflowException("value is outside the underlying type's value range");
		}
		TUnderlying val2 = val.Create((long)value);
		Validate(val2, "value", validation);
		return val2;
	}

	public sealed override bool TryToObject(object? value, ref byte result, EnumValidation validation)
	{
		if (TryToObject(value, out var result2, validation))
		{
			UnsafeUtility.As<byte, TUnderlying>(ref result) = result2;
			return true;
		}
		return false;
	}

	public sealed override bool TryToObject(object? value, out object? result, EnumValidation validation)
	{
		if (TryToObject(value, out var result2, validation))
		{
			result = EnumBridge.ToObjectUnchecked(result2);
			return true;
		}
		result = null;
		return false;
	}

	public sealed override bool TryToObject(long value, ref byte result, EnumValidation validation)
	{
		if (TryToObject(value, out var result2, validation))
		{
			UnsafeUtility.As<byte, TUnderlying>(ref result) = result2;
			return true;
		}
		return false;
	}

	public sealed override bool TryToObject(long value, out object? result, EnumValidation validation)
	{
		if (TryToObject(value, out var result2, validation))
		{
			result = EnumBridge.ToObjectUnchecked(result2);
			return true;
		}
		result = null;
		return false;
	}

	public sealed override bool TryToObject(ulong value, ref byte result, EnumValidation validation)
	{
		if (TryToObject(value, out var result2, validation))
		{
			UnsafeUtility.As<byte, TUnderlying>(ref result) = result2;
			return true;
		}
		return false;
	}

	public sealed override bool TryToObject(ulong value, out object? result, EnumValidation validation)
	{
		if (TryToObject(value, out var result2, validation))
		{
			result = EnumBridge.ToObjectUnchecked(result2);
			return true;
		}
		result = null;
		return false;
	}

	public bool TryToObject(object? value, out TUnderlying result, EnumValidation validation)
	{
		if (value != null)
		{
			TUnderlying? val = EnumBridge.IsEnum(value);
			if (val.HasValue)
			{
				result = val.GetValueOrDefault();
				return IsValid(result, validation);
			}
			if (value is TUnderlying val2)
			{
				result = val2;
				return IsValid(val2, validation);
			}
			Type type = value.GetType();
			switch ((Nullable.GetUnderlyingType(type) ?? type).GetTypeCode())
			{
			case TypeCode.Boolean:
				return TryToObject(Convert.ToByte((bool)value), out result, validation);
			case TypeCode.Char:
				return TryToObject((char)value, out result, validation);
			case TypeCode.SByte:
				return TryToObject((sbyte)value, out result, validation);
			case TypeCode.Byte:
				return TryToObject((byte)value, out result, validation);
			case TypeCode.Int16:
				return TryToObject((short)value, out result, validation);
			case TypeCode.UInt16:
				return TryToObject((ushort)value, out result, validation);
			case TypeCode.Int32:
				return TryToObject((int)value, out result, validation);
			case TypeCode.UInt32:
				return TryToObject((uint)value, out result, validation);
			case TypeCode.Int64:
				return TryToObject((long)value, out result, validation);
			case TypeCode.UInt64:
				return TryToObject((ulong)value, out result, validation);
			case TypeCode.String:
				if (TryParse((string)value, ignoreCase: false, out result))
				{
					return IsValid(result, validation);
				}
				break;
			}
		}
		result = default(TUnderlying);
		return false;
	}

	public bool TryToObject(long value, out TUnderlying result, EnumValidation validation)
	{
		TUnderlyingOperations val = default(TUnderlyingOperations);
		if (val.IsInValueRange(value))
		{
			result = val.Create(value);
			return IsValid(result, validation);
		}
		result = default(TUnderlying);
		return false;
	}

	public bool TryToObject(ulong value, out TUnderlying result, EnumValidation validation)
	{
		TUnderlyingOperations val = default(TUnderlyingOperations);
		if (val.IsInValueRange(value))
		{
			result = val.Create((long)value);
			return IsValid(result, validation);
		}
		result = default(TUnderlying);
		return false;
	}

	public sealed override bool IsValid(ref byte value, EnumValidation validation)
	{
		return IsValid(UnsafeUtility.As<byte, TUnderlying>(ref value), validation);
	}

	public sealed override bool IsValid(object value, EnumValidation validation)
	{
		return IsValid(ToObject(value), validation);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public bool IsValid(TUnderlying value, EnumValidation validation)
	{
		return validation switch
		{
			EnumValidation.None => true, 
			EnumValidation.Default => (_customValidator != null) ? EnumBridge.CustomValidate(_customValidator, value) : ((IsFlagEnum && IsValidFlagCombination(value)) || IsDefined(value)), 
			EnumValidation.IsDefined => IsDefined(value), 
			EnumValidation.IsValidFlagCombination => IsValidFlagCombination(value), 
			_ => validation.Validate("validation") != validation, 
		};
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public bool IsDefined(TUnderlying value)
	{
		if (!_isContiguous)
		{
			return GetMember(value) != null;
		}
		return default(TUnderlyingOperations).InRange(value, _minDefined, _maxDefined);
	}

	public sealed override void Validate(ref byte value, string paramName, EnumValidation validation)
	{
		Validate(UnsafeUtility.As<byte, TUnderlying>(ref value), paramName, validation);
	}

	public sealed override object Validate(object value, string paramName, EnumValidation validation)
	{
		TUnderlying value2 = ToObject(value);
		Validate(value2, paramName, validation);
		return EnumBridge.ToObjectUnchecked(value2);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public void Validate(TUnderlying value, string paramName, EnumValidation validation)
	{
		if (!IsValid(value, validation))
		{
			throw new ArgumentException($"invalid value of {AsString(value)} for {EnumType}", paramName);
		}
	}

	public sealed override string AsString(ref byte value, string format)
	{
		return AsStringInternal(UnsafeUtility.As<byte, TUnderlying>(ref value), null, format);
	}

	public sealed override string AsString(object value, string format)
	{
		return AsStringInternal(ToObject(value), null, format);
	}

	public sealed override string? AsString(ref byte value, EnumFormat format)
	{
		return AsString(UnsafeUtility.As<byte, TUnderlying>(ref value), format);
	}

	public sealed override string? AsString(object value, EnumFormat format)
	{
		return AsString(ToObject(value), format);
	}

	public sealed override string? AsString(ref byte value, ValueCollection<EnumFormat> formats)
	{
		return AsStringInternal(UnsafeUtility.As<byte, TUnderlying>(ref value), null, formats);
	}

	public sealed override string? AsString(object value, ValueCollection<EnumFormat> formats)
	{
		return AsStringInternal(ToObject(value), null, formats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public string AsString(TUnderlying value)
	{
		EnumMemberInternal<TUnderlying, TUnderlyingOperations> member = GetMember(value);
		object obj;
		if (!IsFlagEnum)
		{
			obj = member?.Name;
			if (obj == null)
			{
				return value.ToString();
			}
		}
		else
		{
			obj = FormatFlagsInternal(value, member, null, Enums.DefaultFormats);
		}
		return (string)obj;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public string? AsString(TUnderlying value, EnumFormat format)
	{
		return format switch
		{
			EnumFormat.DecimalValue => default(TUnderlyingOperations).ToDecimalString(value), 
			EnumFormat.HexadecimalValue => default(TUnderlyingOperations).ToHexadecimalString(value), 
			EnumFormat.UnderlyingValue => value.ToString(), 
			EnumFormat.Name => GetMember(value)?.Name, 
			EnumFormat.Description => GetMember(value)?.Attributes.Get<DescriptionAttribute>()?.Description, 
			EnumFormat.EnumMemberValue => GetMember(value)?.Attributes.Get<EnumMemberAttribute>()?.Value, 
			EnumFormat.DisplayName => GetMember(value)?.Attributes.Get<DisplayAttribute>()?.GetName(), 
			_ => Enums.CustomEnumMemberFormat(GetMember(value)?.EnumMember, format.Validate("format")), 
		};
	}

	internal string AsStringInternal(TUnderlying value, EnumMemberInternal<TUnderlying, TUnderlyingOperations>? member, string format)
	{
		if (format.Length == 1)
		{
			switch (format[0])
			{
			case 'G':
			case 'g':
			{
				object obj;
				if (!IsFlagEnum)
				{
					obj = (member ?? GetMember(value))?.Name;
					if (obj == null)
					{
						return value.ToString();
					}
				}
				else
				{
					obj = FormatFlagsInternal(value, member ?? GetMember(value), null, Enums.DefaultFormats);
				}
				return (string)obj;
			}
			case 'F':
			case 'f':
				return FormatFlagsInternal(value, member ?? GetMember(value), null, Enums.DefaultFormats);
			case 'D':
			case 'd':
				return value.ToString();
			case 'X':
			case 'x':
				return default(TUnderlyingOperations).ToHexadecimalString(value);
			}
		}
		throw new FormatException("format string can be only \"G\", \"g\", \"X\", \"x\", \"F\", \"f\", \"D\" or \"d\".");
	}

	internal string? AsStringInternal(TUnderlying value, ref bool isInitialized, ref EnumMemberInternal<TUnderlying, TUnderlyingOperations>? member, EnumFormat format)
	{
		return format switch
		{
			EnumFormat.DecimalValue => default(TUnderlyingOperations).ToDecimalString(value), 
			EnumFormat.HexadecimalValue => default(TUnderlyingOperations).ToHexadecimalString(value), 
			EnumFormat.UnderlyingValue => value.ToString(), 
			EnumFormat.Name => TryInitializeMember(value, ref isInitialized, ref member)?.Name, 
			EnumFormat.Description => TryInitializeMember(value, ref isInitialized, ref member)?.Attributes.Get<DescriptionAttribute>()?.Description, 
			EnumFormat.EnumMemberValue => TryInitializeMember(value, ref isInitialized, ref member)?.Attributes.Get<EnumMemberAttribute>()?.Value, 
			EnumFormat.DisplayName => TryInitializeMember(value, ref isInitialized, ref member)?.Attributes.Get<DisplayAttribute>()?.GetName(), 
			_ => Enums.CustomEnumMemberFormat(TryInitializeMember(value, ref isInitialized, ref member)?.EnumMember, format.Validate("format")), 
		};
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private EnumMemberInternal<TUnderlying, TUnderlyingOperations>? TryInitializeMember(TUnderlying value, ref bool isInitialized, ref EnumMemberInternal<TUnderlying, TUnderlyingOperations>? member)
	{
		if (!isInitialized)
		{
			member = GetMember(value);
			isInitialized = true;
		}
		return member;
	}

	internal string? AsStringInternal(TUnderlying value, EnumMemberInternal<TUnderlying, TUnderlyingOperations>? member, ValueCollection<EnumFormat> formats)
	{
		bool isInitialized = member != null;
		ValueCollection<EnumFormat> enumerator = formats.GetEnumerator();
		while (enumerator.MoveNext())
		{
			EnumFormat current = enumerator.Current;
			string text = AsStringInternal(value, ref isInitialized, ref member, current);
			if (text != null)
			{
				return text;
			}
		}
		return null;
	}

	public sealed override bool TryFormat(ref byte value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> format)
	{
		return TryFormatInternal(UnsafeUtility.As<byte, TUnderlying>(ref value), null, destination, out charsWritten, format);
	}

	public sealed override bool TryFormat(object value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> format)
	{
		return TryFormatInternal(ToObject(value), null, destination, out charsWritten, format);
	}

	internal bool TryFormatInternal(TUnderlying value, EnumMemberInternal<TUnderlying, TUnderlyingOperations>? member, Span<char> destination, out int charsWritten, ReadOnlySpan<char> format)
	{
		if (format.Length == 1)
		{
			switch (format[0])
			{
			case 'G':
			case 'g':
				if (member == null)
				{
					member = GetMember(value);
				}
				if (IsFlagEnum)
				{
					return TryFormatFlagsInternal(value, member, destination, out charsWritten, default(ReadOnlySpan<char>), Enums.DefaultFormats);
				}
				if (member != null)
				{
					return EnumCache.TryWriteNonNullableStringToSpan(member.Name, destination, out charsWritten);
				}
				return default(TUnderlyingOperations).TryFormat(value, destination, out charsWritten);
			case 'F':
			case 'f':
				return TryFormatFlagsInternal(value, member ?? GetMember(value), destination, out charsWritten, default(ReadOnlySpan<char>), Enums.DefaultFormats);
			case 'D':
			case 'd':
				return default(TUnderlyingOperations).TryFormat(value, destination, out charsWritten);
			case 'X':
			case 'x':
				return default(TUnderlyingOperations).TryToHexadecimalString(value, destination, out charsWritten);
			}
		}
		throw new FormatException("format string can be only \"G\", \"g\", \"X\", \"x\", \"F\", \"f\", \"D\" or \"d\".");
	}

	public sealed override bool TryFormat(ref byte value, Span<char> destination, out int charsWritten, ValueCollection<EnumFormat> formats)
	{
		return TryFormatInternal(UnsafeUtility.As<byte, TUnderlying>(ref value), null, destination, out charsWritten, formats);
	}

	public sealed override bool TryFormat(object value, Span<char> destination, out int charsWritten, ValueCollection<EnumFormat> formats)
	{
		return TryFormatInternal(ToObject(value), null, destination, out charsWritten, formats);
	}

	internal bool TryFormatInternal(TUnderlying value, EnumMemberInternal<TUnderlying, TUnderlyingOperations>? member, Span<char> destination, out int charsWritten, ValueCollection<EnumFormat> formats)
	{
		bool isInitialized = member != null;
		ValueCollection<EnumFormat> enumerator = formats.GetEnumerator();
		while (enumerator.MoveNext())
		{
			EnumFormat current = enumerator.Current;
			bool? flag = TryFormatInternal(value, ref isInitialized, ref member, destination, out charsWritten, current);
			if (flag.HasValue)
			{
				return flag == true;
			}
		}
		charsWritten = 0;
		return true;
	}

	private bool? TryFormatInternal(TUnderlying value, ref bool isInitialized, ref EnumMemberInternal<TUnderlying, TUnderlyingOperations>? member, Span<char> destination, out int charsWritten, EnumFormat format)
	{
		return format switch
		{
			EnumFormat.DecimalValue => default(TUnderlyingOperations).TryToDecimalString(value, destination, out charsWritten), 
			EnumFormat.HexadecimalValue => default(TUnderlyingOperations).TryToHexadecimalString(value, destination, out charsWritten), 
			EnumFormat.UnderlyingValue => default(TUnderlyingOperations).TryFormat(value, destination, out charsWritten), 
			EnumFormat.Name => TryWriteStringToSpan(TryInitializeMember(value, ref isInitialized, ref member)?.Name, destination, out charsWritten), 
			EnumFormat.Description => TryWriteStringToSpan(TryInitializeMember(value, ref isInitialized, ref member)?.Attributes.Get<DescriptionAttribute>()?.Description, destination, out charsWritten), 
			EnumFormat.EnumMemberValue => TryWriteStringToSpan(TryInitializeMember(value, ref isInitialized, ref member)?.Attributes.Get<EnumMemberAttribute>()?.Value, destination, out charsWritten), 
			EnumFormat.DisplayName => TryWriteStringToSpan(TryInitializeMember(value, ref isInitialized, ref member)?.Attributes.Get<DisplayAttribute>()?.GetName(), destination, out charsWritten), 
			_ => TryWriteStringToSpan(Enums.CustomEnumMemberFormat(TryInitializeMember(value, ref isInitialized, ref member)?.EnumMember, format.Validate("format")), destination, out charsWritten), 
		};
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool? TryWriteStringToSpan(string? str, Span<char> destination, out int charsWritten)
	{
		if (str == null)
		{
			charsWritten = 0;
			return null;
		}
		return EnumCache.TryWriteNonNullableStringToSpan(str, destination, out charsWritten);
	}

	public sealed override object GetUnderlyingValue(object value)
	{
		return ToObject(value);
	}

	public sealed override object GetUnderlyingValue(ref byte value)
	{
		return UnsafeUtility.As<byte, TUnderlying>(ref value);
	}

	public sealed override int GetHashCode(ref byte value)
	{
		return UnsafeUtility.As<byte, TUnderlying>(ref value).GetHashCode();
	}

	public sealed override bool Equals(ref byte value, ref byte other)
	{
		return UnsafeUtility.As<byte, TUnderlying>(ref value).Equals(UnsafeUtility.As<byte, TUnderlying>(ref other));
	}

	public sealed override bool Equals(object value, object other)
	{
		return ToObject(value).Equals(ToObject(other));
	}

	public sealed override int CompareTo(ref byte value, ref byte other)
	{
		return UnsafeUtility.As<byte, TUnderlying>(ref value).CompareTo(UnsafeUtility.As<byte, TUnderlying>(ref other));
	}

	public sealed override int CompareTo(object value, object other)
	{
		return ToObject(value).CompareTo(ToObject(other));
	}

	public sealed override sbyte ToSByte(ref byte value)
	{
		return UnsafeUtility.As<byte, TUnderlying>(ref value).ToSByte(null);
	}

	public sealed override sbyte ToSByte(object value)
	{
		return ToObject(value).ToSByte(null);
	}

	public sealed override byte ToByte(ref byte value)
	{
		return UnsafeUtility.As<byte, TUnderlying>(ref value).ToByte(null);
	}

	public sealed override byte ToByte(object value)
	{
		return ToObject(value).ToByte(null);
	}

	public sealed override short ToInt16(ref byte value)
	{
		return UnsafeUtility.As<byte, TUnderlying>(ref value).ToInt16(null);
	}

	public sealed override short ToInt16(object value)
	{
		return ToObject(value).ToInt16(null);
	}

	public sealed override ushort ToUInt16(ref byte value)
	{
		return UnsafeUtility.As<byte, TUnderlying>(ref value).ToUInt16(null);
	}

	public sealed override ushort ToUInt16(object value)
	{
		return ToObject(value).ToUInt16(null);
	}

	public sealed override int ToInt32(ref byte value)
	{
		return UnsafeUtility.As<byte, TUnderlying>(ref value).ToInt32(null);
	}

	public sealed override int ToInt32(object value)
	{
		return ToObject(value).ToInt32(null);
	}

	public sealed override uint ToUInt32(ref byte value)
	{
		return UnsafeUtility.As<byte, TUnderlying>(ref value).ToUInt32(null);
	}

	public sealed override uint ToUInt32(object value)
	{
		return ToObject(value).ToUInt32(null);
	}

	public sealed override long ToInt64(ref byte value)
	{
		return UnsafeUtility.As<byte, TUnderlying>(ref value).ToInt64(null);
	}

	public sealed override long ToInt64(object value)
	{
		return ToObject(value).ToInt64(null);
	}

	public sealed override ulong ToUInt64(ref byte value)
	{
		return UnsafeUtility.As<byte, TUnderlying>(ref value).ToUInt64(null);
	}

	public sealed override ulong ToUInt64(object value)
	{
		return ToObject(value).ToUInt64(null);
	}

	public override EnumMemberInternal? GetMember(ref byte value)
	{
		return GetMember(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	public override EnumMemberInternal? GetMember(object value)
	{
		return GetMember(ToObject(value));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public EnumMemberInternal<TUnderlying, TUnderlyingOperations>? GetMember(TUnderlying value)
	{
		EnumMemberInternal<TUnderlying, TUnderlyingOperations> enumMemberInternal = _buckets[value.GetHashCode() & (_buckets.Length - 1)];
		while (enumMemberInternal != null && !enumMemberInternal.Value.Equals(value))
		{
			enumMemberInternal = enumMemberInternal.Next;
		}
		return enumMemberInternal;
	}

	public sealed override EnumMember? GetMember(ReadOnlySpan<char> value, bool ignoreCase, ValueCollection<EnumFormat> formats)
	{
		if (!TryParseInternal(value.Trim(), ignoreCase, out TUnderlying result, out EnumMemberInternal<TUnderlying, TUnderlyingOperations> member, formats))
		{
			return null;
		}
		return (member ?? GetMember(result))?.EnumMember;
	}

	public TUnderlying ParseInternal(ReadOnlySpan<char> value, bool ignoreCase)
	{
		if (IsFlagEnum)
		{
			return ParseFlagsInternal(value, ignoreCase, null, Enums.DefaultFormats);
		}
		value = value.Trim();
		if (TryParseInternal(value, ignoreCase, out var result))
		{
			return result;
		}
		if (EnumCache.IsNumeric(value))
		{
			throw new OverflowException("value is outside the underlying type's value range");
		}
		throw new ArgumentException($"string was not recognized as being a member of {EnumType}", "value");
	}

	public bool TryParse(ReadOnlySpan<char> value, bool ignoreCase, out TUnderlying result)
	{
		if (IsFlagEnum)
		{
			return TryParseFlags(value, ignoreCase, null, out result, Enums.DefaultFormats);
		}
		if (value != null)
		{
			value = value.Trim();
			return TryParseInternal(value, ignoreCase, out result);
		}
		result = default(TUnderlying);
		return false;
	}

	protected bool TryParseInternal(ReadOnlySpan<char> value, bool ignoreCase, out TUnderlying result, out EnumMemberInternal<TUnderlying, TUnderlyingOperations>? member, ValueCollection<EnumFormat> formats)
	{
		member = null;
		TUnderlyingOperations val = default(TUnderlyingOperations);
		ValueCollection<EnumFormat> enumerator = formats.GetEnumerator();
		while (enumerator.MoveNext())
		{
			EnumFormat current = enumerator.Current;
			switch (current)
			{
			case EnumFormat.DecimalValue:
				if (val.TryParseNumber(value, NumberStyles.AllowLeadingSign, CultureInfo.InvariantCulture, out result))
				{
					return true;
				}
				continue;
			case EnumFormat.HexadecimalValue:
				if (val.TryParseNumber(value, NumberStyles.AllowHexSpecifier, CultureInfo.InvariantCulture, out result))
				{
					return true;
				}
				continue;
			case EnumFormat.UnderlyingValue:
				if (val.TryParseNative(value, out result))
				{
					return true;
				}
				continue;
			}
			if (GetEnumMemberParser(current).TryParse(value, ignoreCase, out member))
			{
				result = member.Value;
				return true;
			}
		}
		result = default(TUnderlying);
		return false;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	protected bool TryParseInternal(ReadOnlySpan<char> value, bool ignoreCase, out TUnderlying result)
	{
		if (!EnumCache.IsNumeric(value) && GetEnumMemberParser(EnumFormat.Name).TryParse(value, ignoreCase, out EnumMemberInternal<TUnderlying, TUnderlyingOperations> result2))
		{
			result = result2.Value;
			return true;
		}
		return default(TUnderlyingOperations).TryParseNative(value, out result);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private EnumMemberParser GetEnumMemberParser(EnumFormat format)
	{
		int num = (int)(format - 3);
		EnumMemberParser[] enumMemberParsers = _enumMemberParsers;
		return (((uint)num < (uint)enumMemberParsers.Length) ? enumMemberParsers[num] : null) ?? AddEnumMemberParser(format, num, enumMemberParsers);
	}

	[MethodImpl(MethodImplOptions.NoInlining)]
	private EnumMemberParser AddEnumMemberParser(EnumFormat format, int index, EnumMemberParser?[] parsers)
	{
		format.Validate("format");
		EnumMemberParser enumMemberParser = new EnumMemberParser(format, this);
		EnumMemberParser[] array;
		do
		{
			array = parsers;
			parsers = new EnumMemberParser[Math.Max(array.Length, index + 1)];
			array.CopyTo(parsers, 0);
			parsers[index] = enumMemberParser;
		}
		while ((parsers = Interlocked.CompareExchange(ref _enumMemberParsers, parsers, array)) != array);
		return enumMemberParser;
	}

	public sealed override void GetAllFlags(ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = _allFlags;
	}

	public sealed override object GetAllFlags()
	{
		return EnumBridge.ToObjectUnchecked(_allFlags);
	}

	public sealed override bool IsValidFlagCombination(ref byte value)
	{
		return IsValidFlagCombination(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	public sealed override bool IsValidFlagCombination(object value)
	{
		return IsValidFlagCombination(ToObject(value));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public bool IsValidFlagCombination(TUnderlying value)
	{
		return default(TUnderlyingOperations).And(_allFlags, value).Equals(value);
	}

	public sealed override string? FormatFlags(ref byte value, string? delimiter, ValueCollection<EnumFormat> formats)
	{
		return FormatFlagsInternal(UnsafeUtility.As<byte, TUnderlying>(ref value), delimiter, formats);
	}

	public sealed override string? FormatFlags(object value, string? delimiter, ValueCollection<EnumFormat> formats)
	{
		return FormatFlagsInternal(ToObject(value), delimiter, formats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private string? FormatFlagsInternal(TUnderlying value, string? delimiter, ValueCollection<EnumFormat> formats)
	{
		return FormatFlagsInternal(value, GetMember(value), delimiter, formats);
	}

	internal string? FormatFlagsInternal(TUnderlying value, EnumMemberInternal<TUnderlying, TUnderlyingOperations>? member, string? delimiter, ValueCollection<EnumFormat> formats)
	{
		if (member != null || value.Equals(default(TUnderlying)) || !IsValidFlagCombination(value))
		{
			return AsStringInternal(value, member, formats);
		}
		if (string.IsNullOrEmpty(delimiter))
		{
			delimiter = ", ";
		}
		StringBuilder stringBuilder = new StringBuilder();
		TUnderlyingOperations val = default(TUnderlyingOperations);
		bool flag = val.LessThan(value, default(TUnderlying));
		TUnderlying val2 = val.One;
		while (flag ? (!val2.Equals(default(TUnderlying))) : (!val.LessThan(value, val2)))
		{
			if (HasAnyFlags(value, val2))
			{
				if (stringBuilder.Length > 0)
				{
					stringBuilder.Append(delimiter);
				}
				stringBuilder.Append(AsStringInternal(val2, null, formats));
			}
			val2 = val.LeftShift(val2, 1);
		}
		return stringBuilder.ToString();
	}

	public sealed override bool TryFormatFlags(ref byte value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> delimiter, ValueCollection<EnumFormat> formats)
	{
		return TryFormatFlags(UnsafeUtility.As<byte, TUnderlying>(ref value), destination, out charsWritten, delimiter, formats);
	}

	public sealed override bool TryFormatFlags(object value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> delimiter, ValueCollection<EnumFormat> formats)
	{
		return TryFormatFlags(ToObject(value), destination, out charsWritten, delimiter, formats);
	}

	private bool TryFormatFlags(TUnderlying value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> delimiter, ValueCollection<EnumFormat> formats)
	{
		return TryFormatFlagsInternal(value, GetMember(value), destination, out charsWritten, delimiter, formats);
	}

	internal bool TryFormatFlagsInternal(TUnderlying value, EnumMemberInternal<TUnderlying, TUnderlyingOperations>? member, Span<char> destination, out int charsWritten, ReadOnlySpan<char> delimiter, ValueCollection<EnumFormat> formats)
	{
		if (member != null || value.Equals(default(TUnderlying)) || !IsValidFlagCombination(value))
		{
			return TryFormatInternal(value, member, destination, out charsWritten, formats);
		}
		if (delimiter.Length == 0)
		{
			delimiter = ", ";
		}
		Span<char> span = stackalloc char[Math.Min(destination.Length, 256)];
		int num = Iterate(delimiter, span, destination.Length);
		if (num >= 0)
		{
			if (num <= span.Length)
			{
				Span<char> span2 = span;
				span2.Slice(0, num).CopyTo(destination);
			}
			else
			{
				Iterate(delimiter, destination, destination.Length);
			}
			charsWritten = num;
			return true;
		}
		charsWritten = 0;
		return false;
		int Iterate(ReadOnlySpan<char> readOnlySpan, Span<char> dest, int maxLength)
		{
			Span<char> span3 = dest;
			int num2 = 0;
			TUnderlyingOperations val = default(TUnderlyingOperations);
			bool flag = val.LessThan(value, default(TUnderlying));
			TUnderlying val2 = val.One;
			while (flag ? (!val2.Equals(default(TUnderlying))) : (!val.LessThan(value, val2)))
			{
				if (HasAnyFlags(value, val2))
				{
					Span<char> span4;
					if (num2 > 0)
					{
						if (num2 + readOnlySpan.Length > maxLength)
						{
							num2 = -1;
							break;
						}
						if (dest.Length < readOnlySpan.Length)
						{
							dest = span3;
						}
						readOnlySpan.CopyTo(dest);
						span4 = dest;
						int length = readOnlySpan.Length;
						dest = span4.Slice(length, span4.Length - length);
						num2 += readOnlySpan.Length;
					}
					if (TryFormatInternal(val2, null, dest, out var charsWritten2, formats))
					{
						span4 = dest;
						int length = charsWritten2;
						dest = span4.Slice(length, span4.Length - length);
						num2 += charsWritten2;
					}
					else
					{
						dest = span3;
						if (!TryFormatInternal(val2, null, dest, out charsWritten2, formats) || num2 + charsWritten2 > maxLength)
						{
							num2 = -1;
							break;
						}
						span4 = dest;
						int length = charsWritten2;
						dest = span4.Slice(length, span4.Length - length);
						num2 += charsWritten2;
					}
				}
				val2 = val.LeftShift(val2, 1);
			}
			return num2;
		}
	}

	public sealed override IReadOnlyList<object> GetFlags(object value)
	{
		return GetFlags(ToObject(value)).GetNonGenericContainer();
	}

	public sealed override IValuesContainer GetFlags(ref byte value)
	{
		return GetFlags(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public IValuesContainer GetFlags(TUnderlying value)
	{
		return EnumBridge.CreateValuesContainer(EnumerateFlagMembers(value), GetFlagCount(value), cached: false);
	}

	public sealed override IReadOnlyList<EnumMember> GetFlagMembers(ref byte value)
	{
		return GetFlagMembers(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	public sealed override IReadOnlyList<EnumMember> GetFlagMembers(object value)
	{
		return GetFlagMembers(ToObject(value));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public IReadOnlyList<EnumMember> GetFlagMembers(TUnderlying value)
	{
		return EnumBridge.CreateMembersContainer(EnumerateFlagMembers(value), GetFlagCount(value), cached: false);
	}

	private IEnumerable<EnumMemberInternal<TUnderlying, TUnderlyingOperations>> EnumerateFlagMembers(TUnderlying value)
	{
		TUnderlyingOperations operations = default(TUnderlyingOperations);
		TUnderlying validValue = operations.And(value, _allFlags);
		bool checkForZero = operations.LessThan(validValue, default(TUnderlying)) || operations.LessThan(operations.LeftShift(validValue, 1), validValue);
		TUnderlying currentValue = operations.One;
		while (checkForZero ? (!currentValue.Equals(default(TUnderlying))) : (!operations.LessThan(validValue, currentValue)))
		{
			if (HasAnyFlags(validValue, currentValue))
			{
				yield return GetMember(currentValue);
			}
			currentValue = operations.LeftShift(currentValue, 1);
		}
	}

	public sealed override int GetFlagCount()
	{
		return default(TUnderlyingOperations).BitCount(_allFlags);
	}

	public sealed override int GetFlagCount(ref byte value)
	{
		return GetFlagCount(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	public sealed override int GetFlagCount(object value)
	{
		return GetFlagCount(ToObject(value));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public int GetFlagCount(TUnderlying value)
	{
		TUnderlyingOperations val = default(TUnderlyingOperations);
		return val.BitCount(val.And(value, _allFlags));
	}

	public sealed override int GetFlagCount(ref byte value, ref byte otherFlags)
	{
		return GetFlagCount(UnsafeUtility.As<byte, TUnderlying>(ref value), UnsafeUtility.As<byte, TUnderlying>(ref otherFlags));
	}

	public sealed override int GetFlagCount(object value, object otherFlags)
	{
		return GetFlagCount(ToObject(value), ToObject(otherFlags));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public int GetFlagCount(TUnderlying value, TUnderlying otherFlags)
	{
		TUnderlyingOperations val = default(TUnderlyingOperations);
		return val.BitCount(val.And(val.And(value, otherFlags), _allFlags));
	}

	public sealed override bool HasAnyFlags(ref byte value)
	{
		return HasAnyFlags(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	public sealed override bool HasAnyFlags(object value)
	{
		return HasAnyFlags(ToObject(value));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public bool HasAnyFlags(TUnderlying value)
	{
		return !value.Equals(default(TUnderlying));
	}

	public sealed override bool HasAnyFlags(ref byte value, ref byte otherFlags)
	{
		return HasAnyFlags(UnsafeUtility.As<byte, TUnderlying>(ref value), UnsafeUtility.As<byte, TUnderlying>(ref otherFlags));
	}

	public sealed override bool HasAnyFlags(object value, object otherFlags)
	{
		return HasAnyFlags(ToObject(value), ToObject(otherFlags));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public bool HasAnyFlags(TUnderlying value, TUnderlying otherFlags)
	{
		return !default(TUnderlyingOperations).And(value, otherFlags).Equals(default(TUnderlying));
	}

	public sealed override bool HasAllFlags(ref byte value)
	{
		return HasAllFlags(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	public sealed override bool HasAllFlags(object value)
	{
		return HasAllFlags(ToObject(value));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public bool HasAllFlags(TUnderlying value)
	{
		return HasAllFlags(value, _allFlags);
	}

	public sealed override bool HasAllFlags(ref byte value, ref byte otherFlags)
	{
		return HasAllFlags(UnsafeUtility.As<byte, TUnderlying>(ref value), UnsafeUtility.As<byte, TUnderlying>(ref otherFlags));
	}

	public sealed override bool HasAllFlags(object value, object otherFlags)
	{
		return HasAllFlags(ToObject(value), ToObject(otherFlags));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public bool HasAllFlags(TUnderlying value, TUnderlying otherFlags)
	{
		return default(TUnderlyingOperations).And(value, otherFlags).Equals(otherFlags);
	}

	public sealed override void ToggleFlags(ref byte value, ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = ToggleFlags(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	public sealed override object ToggleFlags(object value)
	{
		return EnumBridge.ToObjectUnchecked(ToggleFlags(ToObject(value)));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public TUnderlying ToggleFlags(TUnderlying value)
	{
		return default(TUnderlyingOperations).Xor(value, _allFlags);
	}

	public sealed override void ToggleFlags(ref byte value, ref byte otherFlags, ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = ToggleFlags(UnsafeUtility.As<byte, TUnderlying>(ref value), UnsafeUtility.As<byte, TUnderlying>(ref otherFlags));
	}

	public sealed override object ToggleFlags(object value, object otherFlags)
	{
		return EnumBridge.ToObjectUnchecked(ToggleFlags(ToObject(value), ToObject(otherFlags)));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public TUnderlying ToggleFlags(TUnderlying value, TUnderlying otherFlags)
	{
		return default(TUnderlyingOperations).Xor(value, otherFlags);
	}

	public sealed override void CommonFlags(ref byte value, ref byte otherFlags, ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = CommonFlags(UnsafeUtility.As<byte, TUnderlying>(ref value), UnsafeUtility.As<byte, TUnderlying>(ref otherFlags));
	}

	public sealed override object CommonFlags(object value, object otherFlags)
	{
		return EnumBridge.ToObjectUnchecked(CommonFlags(ToObject(value), ToObject(otherFlags)));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public TUnderlying CommonFlags(TUnderlying value, TUnderlying otherFlags)
	{
		return default(TUnderlyingOperations).And(value, otherFlags);
	}

	public sealed override void CombineFlags(ref byte value, ref byte otherFlags, ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = default(TUnderlyingOperations).Or(UnsafeUtility.As<byte, TUnderlying>(ref value), UnsafeUtility.As<byte, TUnderlying>(ref otherFlags));
	}

	public sealed override object CombineFlags(object value, object otherFlags)
	{
		return EnumBridge.ToObjectUnchecked(default(TUnderlyingOperations).Or(ToObject(value), ToObject(otherFlags)));
	}

	public sealed override void CombineFlags(ref byte flag0, ref byte flag1, ref byte flag2, ref byte result)
	{
		ref TUnderlying reference = ref UnsafeUtility.As<byte, TUnderlying>(ref result);
		TUnderlyingOperations val = default(TUnderlyingOperations);
		reference = val.Or(val.Or(UnsafeUtility.As<byte, TUnderlying>(ref flag0), UnsafeUtility.As<byte, TUnderlying>(ref flag1)), UnsafeUtility.As<byte, TUnderlying>(ref flag2));
	}

	public sealed override void CombineFlags(ref byte flag0, ref byte flag1, ref byte flag2, ref byte flag3, ref byte result)
	{
		ref TUnderlying reference = ref UnsafeUtility.As<byte, TUnderlying>(ref result);
		TUnderlyingOperations val = default(TUnderlyingOperations);
		reference = val.Or(val.Or(val.Or(UnsafeUtility.As<byte, TUnderlying>(ref flag0), UnsafeUtility.As<byte, TUnderlying>(ref flag1)), UnsafeUtility.As<byte, TUnderlying>(ref flag2)), UnsafeUtility.As<byte, TUnderlying>(ref flag3));
	}

	public sealed override void CombineFlags(ref byte flag0, ref byte flag1, ref byte flag2, ref byte flag3, ref byte flag4, ref byte result)
	{
		ref TUnderlying reference = ref UnsafeUtility.As<byte, TUnderlying>(ref result);
		TUnderlyingOperations val = default(TUnderlyingOperations);
		reference = val.Or(val.Or(val.Or(val.Or(UnsafeUtility.As<byte, TUnderlying>(ref flag0), UnsafeUtility.As<byte, TUnderlying>(ref flag1)), UnsafeUtility.As<byte, TUnderlying>(ref flag2)), UnsafeUtility.As<byte, TUnderlying>(ref flag3)), UnsafeUtility.As<byte, TUnderlying>(ref flag4));
	}

	public sealed override object CombineFlags(IEnumerable<object?>? flags, bool isNullable)
	{
		TUnderlyingOperations val = default(TUnderlyingOperations);
		TUnderlying val2 = default(TUnderlying);
		if (flags != null)
		{
			foreach (object flag in flags)
			{
				if (!isNullable || flag != null)
				{
					val2 = val.Or(val2, ToObject(flag));
				}
			}
		}
		return EnumBridge.ToObjectUnchecked(val2);
	}

	public sealed override void RemoveFlags(ref byte value, ref byte otherFlags, ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = RemoveFlags(UnsafeUtility.As<byte, TUnderlying>(ref value), UnsafeUtility.As<byte, TUnderlying>(ref otherFlags));
	}

	public sealed override object RemoveFlags(object value, object otherFlags)
	{
		return EnumBridge.ToObjectUnchecked(RemoveFlags(ToObject(value), ToObject(otherFlags)));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public TUnderlying RemoveFlags(TUnderlying value, TUnderlying otherFlags)
	{
		TUnderlyingOperations val = default(TUnderlyingOperations);
		return val.And(value, val.Not(otherFlags));
	}

	public sealed override void ParseFlags(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, ValueCollection<EnumFormat> formats, ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = ParseFlagsInternal(value, ignoreCase, delimiter, formats);
	}

	public sealed override object ParseFlags(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, ValueCollection<EnumFormat> formats)
	{
		return EnumBridge.ToObjectUnchecked(ParseFlagsInternal(value, ignoreCase, delimiter, formats));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public TUnderlying ParseFlagsInternal(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, ValueCollection<EnumFormat> formats)
	{
		if (TryParseFlags(value, ignoreCase, delimiter, out var result, formats))
		{
			return result;
		}
		if (EnumCache.IsNumeric(value.TrimStart()))
		{
			throw new OverflowException("value is outside the underlying type's value range");
		}
		throw new ArgumentException("value is not a valid combination of flag enum values");
	}

	public sealed override bool TryParseFlags(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, ref byte result, ValueCollection<EnumFormat> formats)
	{
		if (TryParseFlags(value, ignoreCase, delimiter, out var result2, formats))
		{
			UnsafeUtility.As<byte, TUnderlying>(ref result) = result2;
			return true;
		}
		return false;
	}

	public sealed override bool TryParseFlags(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out object? result, ValueCollection<EnumFormat> formats)
	{
		if (TryParseFlags(value, ignoreCase, delimiter, out var result2, formats))
		{
			result = EnumBridge.ToObjectUnchecked(result2);
			return true;
		}
		result = null;
		return false;
	}

	public bool TryParseFlags(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out TUnderlying result, ValueCollection<EnumFormat> formats)
	{
		ReadOnlySpan<char> value2;
		if (string.IsNullOrEmpty(delimiter))
		{
			value2 = ",";
		}
		else
		{
			value2 = delimiter.AsSpan().Trim();
			if (value2.Length == 0)
			{
				value2 = delimiter;
			}
		}
		result = default(TUnderlying);
		TUnderlyingOperations val = default(TUnderlyingOperations);
		StringComparison comparisonType = (ignoreCase ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal);
		do
		{
			int num = value.IndexOf(value2, comparisonType);
			ReadOnlySpan<char> value3;
			if (num < 0)
			{
				value3 = value.Trim();
				value = default(ReadOnlySpan<char>);
			}
			else
			{
				value3 = value.Slice(0, num).Trim();
				value = value.Slice(num + value2.Length);
				if (value.Length == 0)
				{
					result = default(TUnderlying);
					return false;
				}
			}
			if (!TryParseInternal(value3, ignoreCase, out TUnderlying result2, out EnumMemberInternal<TUnderlying, TUnderlyingOperations> _, formats))
			{
				result = default(TUnderlying);
				return false;
			}
			result = val.Or(result, result2);
		}
		while (value.Length > 0);
		return true;
	}
}
