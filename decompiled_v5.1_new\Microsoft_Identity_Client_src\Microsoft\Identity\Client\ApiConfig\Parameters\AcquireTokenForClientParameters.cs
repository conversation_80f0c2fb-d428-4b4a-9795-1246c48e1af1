using System.Text;
using Microsoft.Identity.Client.Core;

namespace Microsoft.Identity.Client.ApiConfig.Parameters;

internal class AcquireTokenForClientParameters : AbstractAcquireTokenConfidentialClientParameters, IAcquireTokenParameters
{
	public bool ForceRefresh { get; set; }

	public void LogParameters(ILoggerAdapter logger)
	{
		if (logger.IsLoggingEnabled(LogLevel.Info))
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.AppendLine("=== AcquireTokenForClientParameters ===");
			stringBuilder.AppendLine("SendX5C: " + base.SendX5C);
			stringBuilder.AppendLine("ForceRefresh: " + ForceRefresh);
			logger.Info(stringBuilder.ToString());
		}
	}
}
