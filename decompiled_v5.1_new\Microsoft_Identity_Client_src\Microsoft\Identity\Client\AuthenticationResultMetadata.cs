using System;
using Microsoft.Identity.Client.Cache;

namespace Microsoft.Identity.Client;

public class AuthenticationResultMetadata
{
	public TokenSource TokenSource { get; }

	public string TokenEndpoint { get; set; }

	public long DurationTotalInMs { get; set; }

	public long DurationInCacheInMs { get; set; }

	public long DurationInHttpInMs { get; set; }

	public DateTimeOffset? RefreshOn { get; set; }

	public CacheRefreshReason CacheRefreshReason { get; set; }

	public CacheLevel CacheLevel { get; set; }

	public RegionDetails RegionDetails { get; set; }

	public string Telemetry { get; set; }

	public AuthenticationResultMetadata(TokenSource tokenSource)
	{
		TokenSource = tokenSource;
	}
}
