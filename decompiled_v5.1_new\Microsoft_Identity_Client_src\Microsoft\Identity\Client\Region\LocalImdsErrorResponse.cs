using System.Collections.Generic;
using System.Text.Json.Serialization;
using Microsoft.Identity.Client.Platforms.net6;

namespace Microsoft.Identity.Client.Region;

[JsonObject]
[Preserve(AllMembers = true)]
internal sealed class LocalImdsErrorResponse
{
	[JsonPropertyName("error")]
	public string Error { get; set; }

	[JsonPropertyName("newest-versions")]
	public List<string> NewestVersions { get; set; }
}
