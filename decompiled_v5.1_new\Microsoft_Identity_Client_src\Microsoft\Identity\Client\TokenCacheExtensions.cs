using Microsoft.Identity.Client.PlatformsCommon.Factories;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;

namespace Microsoft.Identity.Client;

public static class TokenCacheExtensions
{
	public static void SetCacheOptions(this ITokenCache tokenCache, CacheOptions options)
	{
		ValidatePlatform();
		TokenCache obj = (TokenCache)tokenCache;
		ITokenCacheInternal tokenCacheInternal = (ITokenCacheInternal)tokenCache;
		obj.ServiceBundle.Config.AccessorOptions = options;
		if (tokenCacheInternal.IsAppSubscribedToSerializationEvents())
		{
			throw new MsalClientException("static_cache_with_external_serialization", "You configured MSAL cache serialization at the same time with internal caching options. These are mutually exclusive. Use only one option. Web site and web api scenarios should rely on external cache serialization, as internal cache serialization cannot scale. See https://aka.ms/msal-net-token-cache-serialization .");
		}
		IPlatformProxy platformProxy = obj.ServiceBundle?.PlatformProxy ?? PlatformProxyFactory.CreatePlatformProxy(null);
		obj.Accessor = platformProxy.CreateTokenCacheAccessor(options, tokenCacheInternal.IsApplicationCache);
	}

	private static void ValidatePlatform()
	{
	}
}
