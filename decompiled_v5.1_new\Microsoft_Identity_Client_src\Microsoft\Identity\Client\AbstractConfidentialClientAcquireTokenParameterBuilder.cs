using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Executors;
using Microsoft.Identity.Client.AppConfig;
using Microsoft.Identity.Client.AuthScheme.PoP;

namespace Microsoft.Identity.Client;

public abstract class AbstractConfidentialClientAcquireTokenParameterBuilder<T> : AbstractAcquireTokenParameterBuilder<T> where T : AbstractAcquireTokenParameterBuilder<T>
{
	internal IConfidentialClientApplicationExecutor ConfidentialClientApplicationExecutor { get; }

	internal AbstractConfidentialClientAcquireTokenParameterBuilder(IConfidentialClientApplicationExecutor confidentialClientApplicationExecutor)
		: base(confidentialClientApplicationExecutor.ServiceBundle)
	{
		ApplicationBase.GuardMobileFrameworks();
		ConfidentialClientApplicationExecutor = confidentialClientApplicationExecutor;
	}

	internal abstract Task<AuthenticationResult> ExecuteInternalAsync(CancellationToken cancellationToken);

	public override Task<AuthenticationResult> ExecuteAsync(CancellationToken cancellationToken)
	{
		ApplicationBase.GuardMobileFrameworks();
		ValidateAndCalculateApiId();
		return ExecuteInternalAsync(cancellationToken);
	}

	protected override void Validate()
	{
		if (base.ServiceBundle?.Config.ClientCredential == null && base.CommonParameters.OnBeforeTokenRequestHandler == null && base.ServiceBundle?.Config.AppTokenProvider == null)
		{
			throw new MsalClientException("Client_Credentials_Required_In_Confidential_Client_Application", "One client credential type required either: ClientSecret, Certificate, ClientAssertion or AppTokenProvider must be defined when creating a Confidential Client. Only specify one. See https://aka.ms/msal-net-client-credentials. ");
		}
		base.Validate();
	}

	public T WithProofOfPossession(PoPAuthenticationConfiguration popAuthenticationConfiguration)
	{
		ValidateUseOfExperimentalFeature("WithProofOfPossession");
		base.CommonParameters.PopAuthenticationConfiguration = popAuthenticationConfiguration ?? throw new ArgumentNullException("popAuthenticationConfiguration");
		base.CommonParameters.AuthenticationScheme = new PopAuthenticationScheme(base.CommonParameters.PopAuthenticationConfiguration, base.ServiceBundle);
		return this as T;
	}
}
