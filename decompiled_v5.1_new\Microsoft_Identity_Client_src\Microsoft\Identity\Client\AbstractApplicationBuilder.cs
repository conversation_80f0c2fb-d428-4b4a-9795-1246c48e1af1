using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text.Json;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.Instance.Discovery;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

public abstract class AbstractApplicationBuilder<T> : BaseAbstractApplicationBuilder<T> where T : BaseAbstractApplicationBuilder<T>
{
	internal AbstractApplicationBuilder(ApplicationConfiguration configuration)
		: base(configuration)
	{
	}

	[Obsolete("This method name has a typo, please use WithInstanceDiscoveryMetadata instead", false)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public T WithInstanceDicoveryMetadata(string instanceDiscoveryJson)
	{
		if (string.IsNullOrEmpty(instanceDiscoveryJson))
		{
			throw new ArgumentNullException(instanceDiscoveryJson);
		}
		try
		{
			InstanceDiscoveryResponse customInstanceDiscoveryMetadata = JsonHelper.DeserializeFromJson<InstanceDiscoveryResponse>(instanceDiscoveryJson);
			base.Config.CustomInstanceDiscoveryMetadata = customInstanceDiscoveryMetadata;
			return this as T;
		}
		catch (JsonException innerException)
		{
			throw new MsalClientException("invalid-custom-instance-metadata", "The json containing instance metadata could not be parsed. See https://aka.ms/msal-net-custom-instance-metadata for details. ", innerException);
		}
	}

	public T WithInstanceDiscoveryMetadata(string instanceDiscoveryJson)
	{
		if (string.IsNullOrEmpty(instanceDiscoveryJson))
		{
			throw new ArgumentNullException(instanceDiscoveryJson);
		}
		try
		{
			InstanceDiscoveryResponse customInstanceDiscoveryMetadata = JsonHelper.DeserializeFromJson<InstanceDiscoveryResponse>(instanceDiscoveryJson);
			base.Config.CustomInstanceDiscoveryMetadata = customInstanceDiscoveryMetadata;
			return this as T;
		}
		catch (JsonException innerException)
		{
			throw new MsalClientException("invalid-custom-instance-metadata", "The json containing instance metadata could not be parsed. See https://aka.ms/msal-net-custom-instance-metadata for details. ", innerException);
		}
	}

	[Obsolete("This method name has a typo, please use WithInstanceDiscoveryMetadata instead", false)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public T WithInstanceDicoveryMetadata(Uri instanceDiscoveryUri)
	{
		base.Config.CustomInstanceDiscoveryMetadataUri = instanceDiscoveryUri ?? throw new ArgumentNullException("instanceDiscoveryUri");
		return this as T;
	}

	public T WithInstanceDiscoveryMetadata(Uri instanceDiscoveryUri)
	{
		base.Config.CustomInstanceDiscoveryMetadataUri = instanceDiscoveryUri ?? throw new ArgumentNullException("instanceDiscoveryUri");
		return this as T;
	}

	internal T WithPlatformProxy(IPlatformProxy platformProxy)
	{
		base.Config.PlatformProxy = platformProxy;
		return this as T;
	}

	public T WithCacheOptions(CacheOptions options)
	{
		base.Config.AccessorOptions = options;
		return this as T;
	}

	internal T WithUserTokenCacheInternalForTest(ITokenCacheInternal tokenCacheInternal)
	{
		base.Config.UserTokenCacheInternalForTest = tokenCacheInternal;
		return this as T;
	}

	public T WithLegacyCacheCompatibility(bool enableLegacyCacheCompatibility = true)
	{
		base.Config.LegacyCacheCompatibilityEnabled = enableLegacyCacheCompatibility;
		return this as T;
	}

	[Obsolete("Telemetry is sent automatically by MSAL.NET. See https://aka.ms/msal-net-telemetry.", false)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	internal T WithTelemetry(TelemetryCallback telemetryCallback)
	{
		return this as T;
	}

	public T WithClientId(string clientId)
	{
		base.Config.ClientId = clientId;
		return this as T;
	}

	public T WithRedirectUri(string redirectUri)
	{
		base.Config.RedirectUri = GetValueIfNotEmpty(base.Config.RedirectUri, redirectUri);
		return this as T;
	}

	public T WithTenantId(string tenantId)
	{
		base.Config.TenantId = GetValueIfNotEmpty(base.Config.TenantId, tenantId);
		return this as T;
	}

	public T WithClientName(string clientName)
	{
		base.Config.ClientName = GetValueIfNotEmpty(base.Config.ClientName, clientName);
		return this as T;
	}

	public T WithClientVersion(string clientVersion)
	{
		base.Config.ClientVersion = GetValueIfNotEmpty(base.Config.ClientVersion, clientVersion);
		return this as T;
	}

	protected T WithOptions(ApplicationOptions applicationOptions)
	{
		WithClientId(applicationOptions.ClientId);
		WithRedirectUri(applicationOptions.RedirectUri);
		WithTenantId(applicationOptions.TenantId);
		WithClientName(applicationOptions.ClientName);
		WithClientVersion(applicationOptions.ClientVersion);
		WithClientCapabilities(applicationOptions.ClientCapabilities);
		WithLegacyCacheCompatibility(applicationOptions.LegacyCacheCompatibilityEnabled);
		WithLogging(null, applicationOptions.LogLevel, applicationOptions.EnablePiiLogging, applicationOptions.IsDefaultPlatformLoggingEnabled);
		base.Config.Instance = applicationOptions.Instance;
		base.Config.AadAuthorityAudience = applicationOptions.AadAuthorityAudience;
		base.Config.AzureCloudInstance = applicationOptions.AzureCloudInstance;
		return this as T;
	}

	public T WithExtraQueryParameters(IDictionary<string, string> extraQueryParameters)
	{
		base.Config.ExtraQueryParameters = extraQueryParameters ?? new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
		return this as T;
	}

	public T WithExtraQueryParameters(string extraQueryParameters)
	{
		if (!string.IsNullOrWhiteSpace(extraQueryParameters))
		{
			return WithExtraQueryParameters(CoreHelpers.ParseKeyValueList(extraQueryParameters, '&', urlDecode: true, null));
		}
		return this as T;
	}

	public T WithClientCapabilities(IEnumerable<string> clientCapabilities)
	{
		if (clientCapabilities != null && clientCapabilities.Any())
		{
			base.Config.ClientCapabilities = clientCapabilities;
		}
		return this as T;
	}

	public T WithInstanceDiscovery(bool enableInstanceDiscovery)
	{
		base.Config.IsInstanceDiscoveryEnabled = enableInstanceDiscovery;
		return this as T;
	}

	[Obsolete("Telemetry is sent automatically by MSAL.NET. See https://aka.ms/msal-net-telemetry.", false)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public T WithTelemetry(ITelemetryConfig telemetryConfig)
	{
		return this as T;
	}

	internal virtual void Validate()
	{
		if (string.IsNullOrWhiteSpace(base.Config.ClientId))
		{
			throw new MsalClientException("no_client_id", "No ClientId was specified. ");
		}
		if (base.Config.CustomInstanceDiscoveryMetadata != null && base.Config.CustomInstanceDiscoveryMetadataUri != null)
		{
			throw new MsalClientException("custom_metadata_instance_or_uri", "You have configured your own instance metadata using both an Uri and a string. Only one is supported. See https://aka.ms/msal-net-custom-instance-metadata for more details. ");
		}
		if (base.Config.Authority.AuthorityInfo.ValidateAuthority && (base.Config.CustomInstanceDiscoveryMetadata != null || base.Config.CustomInstanceDiscoveryMetadataUri != null))
		{
			throw new MsalClientException("validate_authority_or_custom_instance_metadata", "You have configured custom instance metadata, but the validateAuthority flag is set to true. These are mutually exclusive. Set the validateAuthority flag to false. See https://aka.ms/msal-net-custom-instance-metadata for more details. ");
		}
	}

	internal override ApplicationConfiguration BuildConfiguration()
	{
		ResolveAuthority();
		Validate();
		return base.Config;
	}

	public T WithAuthority(Uri authorityUri, bool validateAuthority = true)
	{
		if (authorityUri == null)
		{
			throw new ArgumentNullException("authorityUri");
		}
		return WithAuthority(authorityUri.ToString(), validateAuthority);
	}

	public T WithAuthority(string authorityUri, bool validateAuthority = true)
	{
		if (string.IsNullOrWhiteSpace(authorityUri))
		{
			throw new ArgumentNullException(authorityUri);
		}
		base.Config.Authority = Authority.CreateAuthority(authorityUri, validateAuthority);
		return this as T;
	}

	public T WithAuthority(string cloudInstanceUri, Guid tenantId, bool validateAuthority = true)
	{
		WithAuthority(cloudInstanceUri, tenantId.ToString("D", CultureInfo.InvariantCulture), validateAuthority);
		return this as T;
	}

	public T WithAuthority(string cloudInstanceUri, string tenant, bool validateAuthority = true)
	{
		if (string.IsNullOrWhiteSpace(cloudInstanceUri))
		{
			throw new ArgumentNullException("cloudInstanceUri");
		}
		if (string.IsNullOrWhiteSpace(tenant))
		{
			throw new ArgumentNullException("tenant");
		}
		AuthorityInfo authorityInfo = AuthorityInfo.FromAadAuthority(cloudInstanceUri, tenant, validateAuthority);
		base.Config.Authority = new AadAuthority(authorityInfo);
		return this as T;
	}

	public T WithAuthority(AzureCloudInstance azureCloudInstance, Guid tenantId, bool validateAuthority = true)
	{
		WithAuthority(azureCloudInstance, tenantId.ToString("D", CultureInfo.InvariantCulture), validateAuthority);
		return this as T;
	}

	public T WithAuthority(AzureCloudInstance azureCloudInstance, string tenant, bool validateAuthority = true)
	{
		if (string.IsNullOrWhiteSpace(tenant))
		{
			throw new ArgumentNullException("tenant");
		}
		base.Config.AzureCloudInstance = azureCloudInstance;
		base.Config.TenantId = tenant;
		base.Config.ValidateAuthority = validateAuthority;
		return this as T;
	}

	public T WithAuthority(AzureCloudInstance azureCloudInstance, AadAuthorityAudience authorityAudience, bool validateAuthority = true)
	{
		base.Config.AzureCloudInstance = azureCloudInstance;
		base.Config.AadAuthorityAudience = authorityAudience;
		base.Config.ValidateAuthority = validateAuthority;
		return this as T;
	}

	public T WithAuthority(AadAuthorityAudience authorityAudience, bool validateAuthority = true)
	{
		base.Config.AadAuthorityAudience = authorityAudience;
		base.Config.ValidateAuthority = validateAuthority;
		return this as T;
	}

	public T WithAdfsAuthority(string authorityUri, bool validateAuthority = true)
	{
		AuthorityInfo authorityInfo = AuthorityInfo.FromAdfsAuthority(authorityUri, validateAuthority);
		base.Config.Authority = Authority.CreateAuthority(authorityInfo);
		return this as T;
	}

	public T WithB2CAuthority(string authorityUri)
	{
		AuthorityInfo authorityInfo = AuthorityInfo.FromB2CAuthority(authorityUri);
		base.Config.Authority = Authority.CreateAuthority(authorityInfo);
		return this as T;
	}

	private static string GetValueIfNotEmpty(string original, string value)
	{
		if (!string.IsNullOrWhiteSpace(value))
		{
			return value;
		}
		return original;
	}
}
