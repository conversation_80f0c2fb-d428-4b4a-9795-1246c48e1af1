using System.Collections.Generic;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Internal.Requests;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

internal static class TokenResponseHelper
{
	internal const string NullPreferredUsernameDisplayLabel = "Missing from the token response";

	public static string GetTenantId(IdToken idToken, AuthenticationRequestParameters requestParams)
	{
		return Authority.CreateAuthorityWithTenant(requestParams.Authority.AuthorityInfo, idToken?.TenantId).TenantId;
	}

	public static string GetUsernameFromIdToken(IdToken idToken)
	{
		if (idToken == null)
		{
			return "Missing from the token response";
		}
		return idToken.PreferredUsername.NullIfWhiteSpace() ?? idToken.Upn.NullIfWhiteSpace() ?? idToken.Email.NullIfWhiteSpace() ?? idToken.Name.NullIfWhiteSpace() ?? "Missing from the token response";
	}

	public static string GetHomeAccountId(AuthenticationRequestParameters requestParams, MsalTokenResponse response, IdToken idToken)
	{
		string obj = ((response.ClientInfo != null) ? ClientInfo.CreateFromJson(response.ClientInfo) : null)?.ToAccountIdentifier() ?? idToken?.Subject;
		if (obj == null)
		{
			requestParams.RequestContext.Logger.Info("Cannot determine home account ID - or id token or no client info and no subject ");
		}
		return obj;
	}

	public static Dictionary<string, string> GetWamAccountIds(AuthenticationRequestParameters requestParams, MsalTokenResponse response)
	{
		if (!string.IsNullOrEmpty(response.WamAccountId))
		{
			return new Dictionary<string, string> { 
			{
				requestParams.AppConfig.ClientId,
				response.WamAccountId
			} };
		}
		return new Dictionary<string, string>();
	}
}
