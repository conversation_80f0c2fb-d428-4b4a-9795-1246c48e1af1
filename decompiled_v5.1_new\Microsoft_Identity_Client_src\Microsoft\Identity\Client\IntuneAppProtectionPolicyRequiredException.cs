namespace Microsoft.Identity.Client;

public class IntuneAppProtectionPolicyRequiredException : MsalServiceException
{
	public string Upn { get; set; }

	public string AccountUserId { get; set; }

	public string TenantId { get; set; }

	public string AuthorityUrl { get; set; }

	public IntuneAppProtectionPolicyRequiredException(string errorCode, string errorMessage)
		: base(errorCode, errorMessage, null)
	{
	}
}
