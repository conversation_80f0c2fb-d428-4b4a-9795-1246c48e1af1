using System;
using System.Linq;
using Microsoft.IdentityModel.Logging;
using Microsoft.IdentityModel.Tokens;

namespace Microsoft.IdentityModel.Protocols.OpenIdConnect.Configuration;

public class OpenIdConnectConfigurationValidator : IConfigurationValidator<OpenIdConnectConfiguration>
{
	private int _minimumNumberOfKeys = 1;

	private const int DefaultMinimumNumberOfKeys = 1;

	public int MinimumNumberOfKeys
	{
		get
		{
			return _minimumNumberOfKeys;
		}
		set
		{
			if (value < 1)
			{
				throw LogHelper.LogExceptionMessage(new ArgumentOutOfRangeException("value", LogHelper.FormatInvariant("IDX21816: The number of signing keys must be greater or equal to '{0}'. Value: '{1}'.", LogHelper.MarkAsNonPII(1), LogHelper.MarkAsNonPII(value))));
			}
			_minimumNumberOfKeys = value;
		}
	}

	public ConfigurationValidationResult Validate(OpenIdConnectConfiguration openIdConnectConfiguration)
	{
		if (openIdConnectConfiguration == null)
		{
			throw new ArgumentNullException("openIdConnectConfiguration");
		}
		if (openIdConnectConfiguration.JsonWebKeySet == null || openIdConnectConfiguration.JsonWebKeySet.Keys.Count == 0)
		{
			return new ConfigurationValidationResult
			{
				ErrorMessage = "IDX21817: The OpenIdConnectConfiguration did not contain any JsonWebKeys. This is required to validate the configuration.",
				Succeeded = false
			};
		}
		int num = openIdConnectConfiguration.JsonWebKeySet.Keys.Where((JsonWebKey key) => key.ConvertedSecurityKey != null).Count();
		if (num < MinimumNumberOfKeys)
		{
			string text = string.Join("\n", from key in openIdConnectConfiguration.JsonWebKeySet.Keys
				where !string.IsNullOrEmpty(key.ConvertKeyInfo)
				select key.Kid.ToString() + ": " + key.ConvertKeyInfo);
			ConfigurationValidationResult configurationValidationResult = new ConfigurationValidationResult();
			configurationValidationResult.ErrorMessage = LogHelper.FormatInvariant("IDX21818: The OpenIdConnectConfiguration's valid signing keys cannot be less than {0}. Values: {1}. Invalid keys: {2}", LogHelper.MarkAsNonPII(MinimumNumberOfKeys), LogHelper.MarkAsNonPII(num), string.IsNullOrEmpty(text) ? "None" : text);
			configurationValidationResult.Succeeded = false;
			return configurationValidationResult;
		}
		return new ConfigurationValidationResult
		{
			Succeeded = true
		};
	}
}
