using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace EnumsNET.Utilities;

internal static class ArrayHelper
{
	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public static T[] Empty<T>()
	{
		return Array.Empty<T>();
	}

	public static T[] ToArray<T>(IEnumerable<T> items, int count)
	{
		T[] array = new T[count];
		int num = 0;
		foreach (T item in items)
		{
			array[num++] = item;
		}
		return array;
	}
}
