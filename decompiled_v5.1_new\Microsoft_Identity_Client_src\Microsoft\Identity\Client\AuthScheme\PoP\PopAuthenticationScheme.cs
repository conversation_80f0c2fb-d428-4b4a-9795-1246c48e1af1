using System;
using System.Collections.Generic;
using System.Globalization;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json.Nodes;
using Microsoft.Identity.Client.AppConfig;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.AuthScheme.PoP;

internal class PopAuthenticationScheme : IAuthenticationScheme
{
	private readonly PoPAuthenticationConfiguration _popAuthenticationConfiguration;

	private readonly IPoPCryptoProvider _popCryptoProvider;

	public TokenType TelemetryTokenType => TokenType.Pop;

	public string AuthorizationHeaderPrefix => "PoP";

	public string AccessTokenType => "pop";

	public string KeyId { get; }

	public PopAuthenticationScheme(PoPAuthenticationConfiguration popAuthenticationConfiguration, IServiceBundle serviceBundle)
	{
		if (serviceBundle == null)
		{
			throw new ArgumentNullException("serviceBundle");
		}
		_popAuthenticationConfiguration = popAuthenticationConfiguration ?? throw new ArgumentNullException("popAuthenticationConfiguration");
		_popCryptoProvider = _popAuthenticationConfiguration.PopCryptoProvider ?? serviceBundle.PlatformProxy.GetDefaultPoPCryptoProvider();
		byte[] inArray = ComputeThumbprint(_popCryptoProvider.CannonicalPublicKeyJwk);
		KeyId = Base64UrlHelpers.Encode(inArray);
	}

	public IReadOnlyDictionary<string, string> GetTokenRequestParams()
	{
		return new Dictionary<string, string>
		{
			{ "token_type", "pop" },
			{
				"req_cnf",
				ComputeReqCnf()
			}
		};
	}

	public string FormatAccessToken(MsalAccessTokenCacheItem msalAccessTokenCacheItem)
	{
		if (!_popAuthenticationConfiguration.SignHttpRequest)
		{
			return msalAccessTokenCacheItem.Secret;
		}
		JsonObject jsonObject = new JsonObject();
		jsonObject["alg"] = _popCryptoProvider.CryptographicAlgorithm;
		jsonObject["kid"] = KeyId;
		jsonObject["typ"] = "pop";
		JsonObject jsonObject2 = CreateBody(msalAccessTokenCacheItem);
		return CreateJWS(JsonHelper.JsonObjectToString(jsonObject2), JsonHelper.JsonObjectToString(jsonObject));
	}

	private JsonObject CreateBody(MsalAccessTokenCacheItem msalAccessTokenCacheItem)
	{
		JsonNode value = JsonNode.Parse(_popCryptoProvider.CannonicalPublicKeyJwk);
		JsonObject jsonObject = new JsonObject
		{
			["cnf"] = new JsonObject { ["jwk"] = value },
			["ts"] = DateTimeHelpers.CurrDateTimeInUnixTimestamp(),
			["at"] = msalAccessTokenCacheItem.Secret,
			["nonce"] = _popAuthenticationConfiguration.Nonce ?? CreateSimpleNonce()
		};
		if (_popAuthenticationConfiguration.HttpMethod != null)
		{
			jsonObject["m"] = _popAuthenticationConfiguration.HttpMethod?.ToString();
		}
		if (!string.IsNullOrEmpty(_popAuthenticationConfiguration.HttpHost))
		{
			jsonObject["u"] = _popAuthenticationConfiguration.HttpHost;
		}
		if (!string.IsNullOrEmpty(_popAuthenticationConfiguration.HttpPath))
		{
			jsonObject["p"] = _popAuthenticationConfiguration.HttpPath;
		}
		return jsonObject;
	}

	private static string CreateSimpleNonce()
	{
		return Guid.NewGuid().ToString("N", CultureInfo.InvariantCulture);
	}

	private string ComputeReqCnf()
	{
		return Base64UrlHelpers.Encode($"{{\"{"kid"}\":\"{KeyId}\"}}");
	}

	private static byte[] ComputeThumbprint(string canonicalJwk)
	{
		using SHA256 sHA = SHA256.Create();
		return sHA.ComputeHash(Encoding.UTF8.GetBytes(canonicalJwk));
	}

	private string CreateJWS(string payload, string header)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append(Base64UrlHelpers.Encode(Encoding.UTF8.GetBytes(header)));
		stringBuilder.Append('.');
		stringBuilder.Append(Base64UrlHelpers.Encode(payload));
		string s = stringBuilder.ToString();
		stringBuilder.Append('.');
		stringBuilder.Append(Base64UrlHelpers.Encode(_popCryptoProvider.Sign(Encoding.UTF8.GetBytes(s))));
		return stringBuilder.ToString();
	}
}
