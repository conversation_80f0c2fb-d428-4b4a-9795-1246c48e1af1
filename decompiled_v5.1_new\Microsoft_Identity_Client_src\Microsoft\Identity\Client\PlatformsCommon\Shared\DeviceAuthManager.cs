using System;
using System.Collections.Generic;
using System.Globalization;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.PlatformsCommon.Shared;

internal class DeviceAuthManager : IDeviceAuthManager
{
	private readonly ICryptographyManager _cryptographyManager;

	public DeviceAuthManager(ICryptographyManager cryptographyManager)
	{
		_cryptographyManager = cryptographyManager;
	}

	public bool TryCreateDeviceAuthChallengeResponse(HttpResponseHeaders responseHeaders, Uri endpointUri, out string responseHeader)
	{
		responseHeader = string.Empty;
		X509Certificate2 certificate = null;
		if (!DeviceAuthHelper.IsDeviceAuthChallenge(responseHeaders))
		{
			return false;
		}
		if (!DeviceAuthHelper.CanOSPerformPKeyAuth())
		{
			responseHeader = DeviceAuthHelper.GetBypassChallengeResponse(responseHeaders);
			return true;
		}
		IDictionary<string, string> dictionary = DeviceAuthHelper.ParseChallengeData(responseHeaders);
		if (!dictionary.TryGetValue("SubmitUrl", out var value))
		{
			value = endpointUri.AbsoluteUri;
		}
		try
		{
			certificate = FindCertificate(dictionary);
		}
		catch (MsalException ex)
		{
			if (ex.ErrorCode == "device_certificate_not_found")
			{
				responseHeader = DeviceAuthHelper.GetBypassChallengeResponse(responseHeaders);
				return true;
			}
		}
		string responseToSign = GetDeviceAuthJwtResponse(value, dictionary["nonce"], certificate).GetResponseToSign();
		FormatResponseHeader(_cryptographyManager.SignWithCertificate(responseToSign, certificate, RSASignaturePadding.Pkcs1), dictionary, responseToSign, out responseHeader);
		return true;
	}

	private static DeviceAuthJWTResponse GetDeviceAuthJwtResponse(string submitUrl, string nonce, X509Certificate2 certificate)
	{
		return new DeviceAuthJWTResponse(submitUrl, nonce, Convert.ToBase64String(certificate.GetRawCertData()));
	}

	private static void FormatResponseHeader(byte[] signedResponse, IDictionary<string, string> challengeData, string responseToSign, out string responseHeader)
	{
		string value = Base64UrlHelpers.Encode(signedResponse);
		responseHeader = $"PKeyAuth AuthToken=\"{responseToSign}.{value}\", Context=\"{challengeData["Context"]}\", Version=\"{challengeData["Version"]}\"";
	}

	private static X509Certificate2 FindCertificate(IDictionary<string, string> challengeData)
	{
		X509Store x509Store = new X509Store(StoreName.My, StoreLocation.CurrentUser);
		try
		{
			x509Store.Open(OpenFlags.ReadOnly);
			X509Certificate2Collection certificates = x509Store.Certificates;
			if (challengeData.ContainsKey("CertAuthorities"))
			{
				return FindCertificateByCertAuthorities(challengeData, certificates);
			}
			X509Certificate2Collection x509Certificate2Collection = certificates.Find(X509FindType.FindByThumbprint, challengeData["CertThumbprint"], validOnly: false);
			if (x509Certificate2Collection.Count == 0)
			{
				throw new MsalException("device_certificate_not_found", string.Format(CultureInfo.CurrentCulture, "Device Certificate was not found for {0}. ", "Cert thumbprint:" + challengeData["CertThumbprint"]));
			}
			return x509Certificate2Collection[0];
		}
		finally
		{
			x509Store.Close();
		}
	}

	private static X509Certificate2 FindCertificateByCertAuthorities(IDictionary<string, string> challengeData, X509Certificate2Collection certCollection)
	{
		X509Certificate2Collection x509Certificate2Collection = null;
		string[] array = challengeData["CertAuthorities"].Split(new string[1] { ";" }, StringSplitOptions.None);
		for (int i = 0; i < array.Length; i++)
		{
			string[] array2 = array[i].Split(new string[1] { "," }, StringSplitOptions.None);
			string text = array2[^1];
			for (int num = array2.Length - 2; num >= 0; num--)
			{
				text = text + " + " + array2[num].Trim();
			}
			x509Certificate2Collection = certCollection.Find(X509FindType.FindByIssuerDistinguishedName, text, validOnly: false);
			if (x509Certificate2Collection.Count > 0)
			{
				break;
			}
		}
		if (x509Certificate2Collection == null || x509Certificate2Collection.Count == 0)
		{
			throw new MsalException("device_certificate_not_found", string.Format(CultureInfo.CurrentCulture, "Device Certificate was not found for {0}. ", "Cert Authorities:" + challengeData["CertAuthorities"]));
		}
		return x509Certificate2Collection[0];
	}
}
