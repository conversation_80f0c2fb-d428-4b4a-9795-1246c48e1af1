using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

public class WwwAuthenticateParameters
{
	private static readonly ISet<string> s_knownAuthenticationSchemes = new HashSet<string>(new string[2] { "Bearer", "PoP" }, StringComparer.OrdinalIgnoreCase);

	[Obsolete("The client apps should know which App ID URI it requests scopes for.", true)]
	public string Resource { get; set; }

	[Obsolete("The client apps should know which scopes to request for.", true)]
	public IEnumerable<string> Scopes { get; set; }

	public string Authority { get; set; }

	public string Claims { get; set; }

	public string Error { get; set; }

	public string AuthenticationScheme { get; private set; }

	public string Nonce { get; private set; }

	public string this[string key] => RawParameters[key];

	internal IDictionary<string, string> RawParameters { get; private set; }

	public string GetTenantId()
	{
		return Microsoft.Identity.Client.Instance.Authority.CreateAuthority(Authority, validateAuthority: true)?.TenantId;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This api is now obsolete and has been replaced with CreateFromAuthenticationResponseAsync(...)")]
	public static Task<WwwAuthenticateParameters> CreateFromResourceResponseAsync(string resourceUri)
	{
		return CreateFromResourceResponseAsync(resourceUri, default(CancellationToken));
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This api is now obsolete and has been replaced with CreateFromAuthenticationResponseAsync(...)")]
	public static Task<WwwAuthenticateParameters> CreateFromResourceResponseAsync(string resourceUri, CancellationToken cancellationToken = default(CancellationToken))
	{
		return CreateFromResourceResponseAsync(AuthenticationHeaderParser.GetHttpClient(), resourceUri, cancellationToken);
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This api is now obsolete and has been replaced with replaced with CreateFromAuthenticationResponseAsync(HttpResponseHeaders, string)")]
	public static async Task<WwwAuthenticateParameters> CreateFromResourceResponseAsync(HttpClient httpClient, string resourceUri, CancellationToken cancellationToken = default(CancellationToken))
	{
		if (httpClient == null)
		{
			throw new ArgumentNullException("httpClient");
		}
		if (string.IsNullOrWhiteSpace(resourceUri))
		{
			throw new ArgumentNullException("resourceUri");
		}
		return CreateFromResponseHeaders((await httpClient.GetAsync(resourceUri, cancellationToken).ConfigureAwait(continueOnCapturedContext: false)).Headers);
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This api is now obsolete and has been replaced with CreateFromAuthenticationHeaders(...)")]
	public static WwwAuthenticateParameters CreateFromResponseHeaders(HttpResponseHeaders httpResponseHeaders, string scheme = "Bearer")
	{
		if (httpResponseHeaders.WwwAuthenticate.Any())
		{
			AuthenticationHeaderValue authenticationHeaderValue = httpResponseHeaders.WwwAuthenticate.FirstOrDefault((AuthenticationHeaderValue v) => string.Equals(v.Scheme, scheme, StringComparison.OrdinalIgnoreCase));
			if (authenticationHeaderValue != null)
			{
				WwwAuthenticateParameters wwwAuthenticateParameters = CreateFromWwwAuthenticateHeaderValue(authenticationHeaderValue.Parameter);
				wwwAuthenticateParameters.AuthenticationScheme = scheme;
				return wwwAuthenticateParameters;
			}
		}
		return CreateWwwAuthenticateParameters(new Dictionary<string, string>(), string.Empty);
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This api is now obsolete and should not be used.")]
	public static WwwAuthenticateParameters CreateFromWwwAuthenticateHeaderValue(string wwwAuthenticateValue)
	{
		return CreateFromWwwAuthenticationHeaderValue(wwwAuthenticateValue, string.Empty);
	}

	public static Task<WwwAuthenticateParameters> CreateFromAuthenticationResponseAsync(string resourceUri, string scheme, CancellationToken cancellationToken = default(CancellationToken))
	{
		return CreateFromAuthenticationResponseAsync(resourceUri, scheme, AuthenticationHeaderParser.GetHttpClient(), cancellationToken);
	}

	public static async Task<WwwAuthenticateParameters> CreateFromAuthenticationResponseAsync(string resourceUri, string scheme, HttpClient httpClient, CancellationToken cancellationToken = default(CancellationToken))
	{
		if (httpClient == null)
		{
			throw new ArgumentNullException("httpClient");
		}
		if (string.IsNullOrWhiteSpace(resourceUri))
		{
			throw new ArgumentNullException("resourceUri");
		}
		return CreateFromAuthenticationHeaders((await httpClient.GetAsync(resourceUri, cancellationToken).ConfigureAwait(continueOnCapturedContext: false)).Headers, scheme);
	}

	public static WwwAuthenticateParameters CreateFromAuthenticationHeaders(HttpResponseHeaders httpResponseHeaders, string scheme)
	{
		AuthenticationHeaderValue authenticationHeaderValue = httpResponseHeaders.WwwAuthenticate.FirstOrDefault((AuthenticationHeaderValue v) => string.Equals(v.Scheme, scheme, StringComparison.OrdinalIgnoreCase));
		if (authenticationHeaderValue != null)
		{
			string parameter = authenticationHeaderValue.Parameter;
			try
			{
				return CreateFromWwwAuthenticationHeaderValue(parameter, scheme);
			}
			catch (Exception ex)
			{
				if (ex is MsalException)
				{
					throw;
				}
				throw new MsalClientException("unable_to_parse_authentication_header", "MSAL is unable to parse the authentication header returned from the resource endpoint. This can be a result of a malformed header returned in either the WWW-Authenticate or the Authentication-Info collections acquired from the provided endpoint." + $"Response Headers: {httpResponseHeaders} See inner exception for details.", ex);
			}
		}
		return CreateWwwAuthenticateParameters(new Dictionary<string, string>(), string.Empty);
	}

	public static Task<IReadOnlyList<WwwAuthenticateParameters>> CreateFromAuthenticationResponseAsync(string resourceUri, CancellationToken cancellationToken = default(CancellationToken))
	{
		return CreateFromAuthenticationResponseAsync(resourceUri, AuthenticationHeaderParser.GetHttpClient(), cancellationToken);
	}

	public static async Task<IReadOnlyList<WwwAuthenticateParameters>> CreateFromAuthenticationResponseAsync(string resourceUri, HttpClient httpClient, CancellationToken cancellationToken = default(CancellationToken))
	{
		if (httpClient == null)
		{
			throw new ArgumentNullException("httpClient");
		}
		if (string.IsNullOrWhiteSpace(resourceUri))
		{
			throw new ArgumentNullException("resourceUri");
		}
		return CreateFromAuthenticationHeaders((await httpClient.GetAsync(resourceUri, cancellationToken).ConfigureAwait(continueOnCapturedContext: false)).Headers);
	}

	public static IReadOnlyList<WwwAuthenticateParameters> CreateFromAuthenticationHeaders(HttpResponseHeaders httpResponseHeaders)
	{
		List<WwwAuthenticateParameters> list = new List<WwwAuthenticateParameters>();
		foreach (AuthenticationHeaderValue item2 in httpResponseHeaders.WwwAuthenticate)
		{
			try
			{
				WwwAuthenticateParameters item = CreateFromWwwAuthenticationHeaderValue(item2.Parameter, item2.Scheme);
				list.Add(item);
			}
			catch (Exception ex) when (!(ex is MsalException))
			{
				throw new MsalClientException("unable_to_parse_authentication_header", "MSAL is unable to parse the authentication header returned from the resource endpoint. This can be a result of a malformed header returned in either the WWW-Authenticate or the Authentication-Info collections acquired from the provided endpoint. See inner exception for details.", ex);
			}
		}
		return list;
	}

	public static string GetClaimChallengeFromResponseHeaders(HttpResponseHeaders httpResponseHeaders, string scheme = "Bearer")
	{
		WwwAuthenticateParameters wwwAuthenticateParameters = CreateFromAuthenticationHeaders(httpResponseHeaders, scheme);
		if (wwwAuthenticateParameters.Claims != null && string.Equals(wwwAuthenticateParameters.Error, "insufficient_claims", StringComparison.OrdinalIgnoreCase))
		{
			return wwwAuthenticateParameters.Claims;
		}
		return null;
	}

	private static WwwAuthenticateParameters CreateFromWwwAuthenticationHeaderValue(string wwwAuthenticateValue, string scheme)
	{
		IDictionary<string, string> values = null;
		if (!string.IsNullOrWhiteSpace(wwwAuthenticateValue))
		{
			values = (from v in GetParsedAuthValueElements(wwwAuthenticateValue)
				select AuthenticationHeaderParser.CreateKeyValuePair(v.Trim(), scheme)).ToDictionary<KeyValuePair<string, string>, string, string>((KeyValuePair<string, string> pair) => pair.Key, (KeyValuePair<string, string> pair) => pair.Value, StringComparer.OrdinalIgnoreCase);
		}
		return CreateWwwAuthenticateParameters(values, scheme);
	}

	private static IEnumerable<string> GetParsedAuthValueElements(string wwwAuthenticateValue)
	{
		char[] charsToTrim = new char[2] { ',', ' ' };
		IReadOnlyList<string> readOnlyList = CoreHelpers.SplitWithQuotes(wwwAuthenticateValue, ' ');
		if (s_knownAuthenticationSchemes.Contains(readOnlyList[0]))
		{
			readOnlyList = readOnlyList.Skip(1).ToList();
		}
		return readOnlyList.Select((string authValue) => authValue.TrimEnd(charsToTrim));
	}

	internal static WwwAuthenticateParameters CreateWwwAuthenticateParameters(IDictionary<string, string> values, string scheme)
	{
		WwwAuthenticateParameters wwwAuthenticateParameters = new WwwAuthenticateParameters();
		wwwAuthenticateParameters.AuthenticationScheme = scheme;
		if (values == null)
		{
			wwwAuthenticateParameters.RawParameters = new Dictionary<string, string>();
			return wwwAuthenticateParameters;
		}
		wwwAuthenticateParameters.RawParameters = values;
		if (values.TryGetValue("authorization_uri", out var value))
		{
			wwwAuthenticateParameters.Authority = value.Replace("/v2.0", string.Empty).Replace("/oauth2/authorize", string.Empty);
		}
		if (string.IsNullOrEmpty(wwwAuthenticateParameters.Authority) && values.TryGetValue("authorization", out value))
		{
			wwwAuthenticateParameters.Authority = value.Replace("/v2.0", string.Empty).Replace("/oauth2/authorize", string.Empty);
		}
		if (string.IsNullOrEmpty(wwwAuthenticateParameters.Authority) && values.TryGetValue("authority", out value))
		{
			wwwAuthenticateParameters.Authority = value.TrimEnd('/');
		}
		if (values.TryGetValue("claims", out value))
		{
			wwwAuthenticateParameters.Claims = GetJsonFragment(value);
		}
		if (values.TryGetValue("error", out value))
		{
			wwwAuthenticateParameters.Error = value;
		}
		if (values.TryGetValue("nonce", out value))
		{
			wwwAuthenticateParameters.Nonce = value;
		}
		return wwwAuthenticateParameters;
	}

	private static string GetJsonFragment(string inputString)
	{
		if (string.IsNullOrEmpty(inputString) || inputString.Length % 4 != 0 || inputString.Any(char.IsWhiteSpace))
		{
			return inputString;
		}
		try
		{
			byte[] bytes = Convert.FromBase64String(inputString);
			return Encoding.UTF8.GetString(bytes);
		}
		catch
		{
			return inputString;
		}
	}
}
