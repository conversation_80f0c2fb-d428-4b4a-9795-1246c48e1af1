namespace System;

internal static class HResults
{
	internal const int S_OK = 0;

	internal const int S_FALSE = 1;

	internal const int COR_E_ABANDONEDMUTEX = -2146233043;

	internal const int COR_E_AMBIGUOUSIMPLEMENTATION = -2146234262;

	internal const int COR_E_AMBIGUOUSMATCH = -2147475171;

	internal const int COR_E_APPDOMAINUNLOADED = -2146234348;

	internal const int COR_E_APPLICATION = -2146232832;

	internal const int COR_E_ARGUMENT = -2147024809;

	internal const int COR_E_ARGUMENTOUTOFRANGE = -2146233086;

	internal const int COR_E_ARITHMETIC = -2147024362;

	internal const int COR_E_ARRAYTYPEMISMATCH = -2146233085;

	internal const int COR_E_BADEXEFORMAT = -2147024703;

	internal const int COR_E_BADIMAGEFORMAT = -2147024885;

	internal const int COR_E_CANNOTUNLOADAPPDOMAIN = -2146234347;

	internal const int COR_E_CODECONTRACTFAILED = -2146233022;

	internal const int COR_E_CONTEXTMARSHAL = -2146233084;

	internal const int COR_E_CUSTOMATTRIBUTEFORMAT = -2146232827;

	internal const int COR_E_DATAMISALIGNED = -2146233023;

	internal const int COR_E_DIRECTORYNOTFOUND = -2147024893;

	internal const int COR_E_DIVIDEBYZERO = -2147352558;

	internal const int COR_E_DLLNOTFOUND = -2146233052;

	internal const int COR_E_DUPLICATEWAITOBJECT = -2146233047;

	internal const int COR_E_ENDOFSTREAM = -2147024858;

	internal const int COR_E_ENTRYPOINTNOTFOUND = -2146233053;

	internal const int COR_E_EXCEPTION = -2146233088;

	internal const int COR_E_EXECUTIONENGINE = -2146233082;

	internal const int COR_E_FAILFAST = -2146232797;

	internal const int COR_E_FIELDACCESS = -2146233081;

	internal const int COR_E_FILELOAD = -2146232799;

	internal const int COR_E_FILENOTFOUND = -2147024894;

	internal const int COR_E_FORMAT = -2146233033;

	internal const int COR_E_INDEXOUTOFRANGE = -2146233080;

	internal const int COR_E_INSUFFICIENTEXECUTIONSTACK = -2146232968;

	internal const int COR_E_INSUFFICIENTMEMORY = -2146233027;

	internal const int COR_E_INVALIDCAST = -2147467262;

	internal const int COR_E_INVALIDCOMOBJECT = -2146233049;

	internal const int COR_E_INVALIDFILTERCRITERIA = -2146232831;

	internal const int COR_E_INVALIDOLEVARIANTTYPE = -2146233039;

	internal const int COR_E_INVALIDOPERATION = -2146233079;

	internal const int COR_E_INVALIDPROGRAM = -2146233030;

	internal const int COR_E_IO = -2146232800;

	internal const int COR_E_KEYNOTFOUND = -2146232969;

	internal const int COR_E_MARSHALDIRECTIVE = -2146233035;

	internal const int COR_E_MEMBERACCESS = -2146233062;

	internal const int COR_E_METHODACCESS = -2146233072;

	internal const int COR_E_MISSINGFIELD = -2146233071;

	internal const int COR_E_MISSINGMANIFESTRESOURCE = -2146233038;

	internal const int COR_E_MISSINGMEMBER = -2146233070;

	internal const int COR_E_MISSINGMETHOD = -2146233069;

	internal const int COR_E_MISSINGSATELLITEASSEMBLY = -2146233034;

	internal const int COR_E_MULTICASTNOTSUPPORTED = -2146233068;

	internal const int COR_E_NOTFINITENUMBER = -2146233048;

	internal const int COR_E_NOTSUPPORTED = -2146233067;

	internal const int COR_E_OBJECTDISPOSED = -2146232798;

	internal const int COR_E_OPERATIONCANCELED = -2146233029;

	internal const int COR_E_OUTOFMEMORY = -2147024882;

	internal const int COR_E_OVERFLOW = -2146233066;

	internal const int COR_E_PATHTOOLONG = -2147024690;

	internal const int COR_E_PLATFORMNOTSUPPORTED = -2146233031;

	internal const int COR_E_RANK = -2146233065;

	internal const int COR_E_REFLECTIONTYPELOAD = -2146232830;

	internal const int COR_E_RUNTIMEWRAPPED = -2146233026;

	internal const int COR_E_SAFEARRAYRANKMISMATCH = -2146233032;

	internal const int COR_E_SAFEARRAYTYPEMISMATCH = -2146233037;

	internal const int COR_E_SECURITY = -2146233078;

	internal const int COR_E_SERIALIZATION = -2146233076;

	internal const int COR_E_STACKOVERFLOW = -2147023895;

	internal const int COR_E_SYNCHRONIZATIONLOCK = -2146233064;

	internal const int COR_E_SYSTEM = -2146233087;

	internal const int COR_E_TARGET = -2146232829;

	internal const int COR_E_TARGETINVOCATION = -2146232828;

	internal const int COR_E_TARGETPARAMCOUNT = -2147352562;

	internal const int COR_E_THREADABORTED = -2146233040;

	internal const int COR_E_THREADINTERRUPTED = -2146233063;

	internal const int COR_E_THREADSTART = -2146233051;

	internal const int COR_E_THREADSTATE = -2146233056;

	internal const int COR_E_TIMEOUT = -2146233083;

	internal const int COR_E_TYPEACCESS = -2146233021;

	internal const int COR_E_TYPEINITIALIZATION = -2146233036;

	internal const int COR_E_TYPELOAD = -2146233054;

	internal const int COR_E_TYPEUNLOADED = -2146234349;

	internal const int COR_E_UNAUTHORIZEDACCESS = -2147024891;

	internal const int COR_E_VERIFICATION = -2146233075;

	internal const int COR_E_WAITHANDLECANNOTBEOPENED = -2146233044;

	internal const int CO_E_NOTINITIALIZED = -2147221008;

	internal const int DISP_E_OVERFLOW = -2147352566;

	internal const int E_BOUNDS = -2147483637;

	internal const int E_CHANGED_STATE = -2147483636;

	internal const int E_FILENOTFOUND = -2147024894;

	internal const int E_FAIL = -2147467259;

	internal const int E_HANDLE = -2147024890;

	internal const int E_INVALIDARG = -2147024809;

	internal const int E_NOTIMPL = -2147467263;

	internal const int E_POINTER = -2147467261;

	internal const int ERROR_MRM_MAP_NOT_FOUND = -2147009761;

	internal const int ERROR_TIMEOUT = -2147023436;

	internal const int RO_E_CLOSED = -2147483629;

	internal const int RPC_E_CHANGED_MODE = -2147417850;

	internal const int TYPE_E_TYPEMISMATCH = -2147316576;

	internal const int STG_E_PATHNOTFOUND = -2147287037;

	internal const int CTL_E_PATHNOTFOUND = -2146828212;

	internal const int CTL_E_FILENOTFOUND = -2146828235;

	internal const int FUSION_E_INVALID_NAME = -2146234297;

	internal const int FUSION_E_PRIVATE_ASM_DISALLOWED = -2146234300;

	internal const int FUSION_E_REF_DEF_MISMATCH = -2146234304;

	internal const int ERROR_TOO_MANY_OPEN_FILES = -2147024892;

	internal const int ERROR_SHARING_VIOLATION = -2147024864;

	internal const int ERROR_LOCK_VIOLATION = -2147024863;

	internal const int ERROR_OPEN_FAILED = -2147024786;

	internal const int ERROR_DISK_CORRUPT = -2147023503;

	internal const int ERROR_UNRECOGNIZED_VOLUME = -2147023891;

	internal const int ERROR_DLL_INIT_FAILED = -2147023782;

	internal const int MSEE_E_ASSEMBLYLOADINPROGRESS = -2146234346;

	internal const int ERROR_FILE_INVALID = -2147023890;
}
