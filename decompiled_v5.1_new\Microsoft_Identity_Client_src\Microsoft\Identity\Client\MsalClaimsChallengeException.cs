using System;

namespace Microsoft.Identity.Client;

public class MsalClaimsChallengeException : MsalUiRequiredException
{
	public MsalClaimsChallengeException(string errorCode, string errorMessage)
		: base(errorCode, errorMessage)
	{
	}

	public MsalClaimsChallengeException(string errorCode, string errorMessage, Exception innerException)
		: this(errorCode, errorMessage, innerException, UiRequiredExceptionClassification.None)
	{
	}

	public MsalClaimsChallengeException(string errorCode, string errorMessage, Exception innerException, UiRequiredExceptionClassification classification)
		: base(errorCode, errorMessage, innerException, classification)
	{
	}
}
