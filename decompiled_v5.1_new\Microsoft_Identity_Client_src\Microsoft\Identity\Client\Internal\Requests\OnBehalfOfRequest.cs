using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Internal.Requests;

internal class OnBehalfOfRequest : RequestBase
{
	private readonly AcquireTokenOnBehalfOfParameters _onBehalfOfParameters;

	private string _ccsRoutingHint;

	public OnBehalfOfRequest(IServiceBundle serviceBundle, AuthenticationRequestParameters authenticationRequestParameters, AcquireTokenOnBehalfOfParameters onBehalfOfParameters)
		: base(serviceBundle, authenticationRequestParameters, onBehalfOfParameters)
	{
		_onBehalfOfParameters = onBehalfOfParameters;
	}

	protected override async Task<AuthenticationResult> ExecuteAsync(CancellationToken cancellationToken)
	{
		if (base.AuthenticationRequestParameters.Scope == null || base.AuthenticationRequestParameters.Scope.Count == 0)
		{
			throw new MsalClientException("scopes_required_client_credentials", "At least one scope needs to be requested for this authentication flow. ");
		}
		await ResolveAuthorityAsync().ConfigureAwait(continueOnCapturedContext: false);
		MsalAccessTokenCacheItem cachedAccessToken = null;
		ILoggerAdapter logger = base.AuthenticationRequestParameters.RequestContext.Logger;
		AuthenticationResult authResult = null;
		if (base.AuthenticationRequestParameters.Authority is AadAuthority aadAuthority && aadAuthority.IsCommonOrOrganizationsTenant())
		{
			logger.Error("The current authority is targeting the /common or /organizations endpoint. Instead, it should target the same tenant as the client, which can be found in the 'tid' claim of the incoming client token. See https://aka.ms/msal-net-on-behalf-of for more details.");
		}
		CacheRefreshReason cacheInfoTelemetry = CacheRefreshReason.NotApplicable;
		if (base.AuthenticationRequestParameters.ApiId == ApiEvent.ApiIds.InitiateLongRunningObo && !_onBehalfOfParameters.SearchInCacheForLongRunningObo)
		{
			logger.Info("[OBO Request] Initiating long running process. Fetching OBO token from ESTS.");
			return await FetchNewAccessTokenAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		}
		if (!_onBehalfOfParameters.ForceRefresh && string.IsNullOrEmpty(base.AuthenticationRequestParameters.Claims))
		{
			using (logger.LogBlockDuration("[OBO Request] Looking in the cache for an access token"))
			{
				cachedAccessToken = await base.CacheManager.FindAccessTokenAsync().ConfigureAwait(continueOnCapturedContext: false);
			}
			if (cachedAccessToken != null)
			{
				MsalIdTokenCacheItem cachedIdToken = await base.CacheManager.GetIdTokenCacheItemAsync(cachedAccessToken).ConfigureAwait(continueOnCapturedContext: false);
				Account account = await base.CacheManager.GetAccountAssociatedWithAccessTokenAsync(cachedAccessToken).ConfigureAwait(continueOnCapturedContext: false);
				logger.Info(() => "[OBO Request] Found a valid access token in the cache. ID token also found? " + (cachedIdToken != null));
				base.AuthenticationRequestParameters.RequestContext.ApiEvent.IsAccessTokenCacheHit = true;
				Metrics.IncrementTotalAccessTokensFromCache();
				authResult = new AuthenticationResult(cachedAccessToken, cachedIdToken, base.AuthenticationRequestParameters.AuthenticationScheme, base.AuthenticationRequestParameters.RequestContext.CorrelationId, TokenSource.Cache, base.AuthenticationRequestParameters.RequestContext.ApiEvent, account, null, null);
			}
			else if (base.AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo != CacheRefreshReason.Expired)
			{
				cacheInfoTelemetry = CacheRefreshReason.NoCachedAccessToken;
			}
		}
		else
		{
			logger.Info("[OBO Request] Skipped looking for an Access Token in the cache because ForceRefresh or Claims were set. ");
			cacheInfoTelemetry = CacheRefreshReason.ForceRefreshOrClaims;
		}
		if (base.AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo == CacheRefreshReason.NotApplicable)
		{
			base.AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo = cacheInfoTelemetry;
		}
		try
		{
			if (cachedAccessToken == null)
			{
				authResult = await RefreshRtOrFetchNewAccessTokenAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			}
			else if (SilentRequestHelper.NeedsRefresh(cachedAccessToken))
			{
				base.AuthenticationRequestParameters.RequestContext.ApiEvent.CacheInfo = CacheRefreshReason.ProactivelyRefreshed;
				SilentRequestHelper.ProcessFetchInBackground(cachedAccessToken, delegate
				{
					using CancellationTokenSource cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
					return RefreshRtOrFetchNewAccessTokenAsync(cancellationTokenSource.Token);
				}, logger, base.ServiceBundle, base.AuthenticationRequestParameters.RequestContext.ApiEvent.ApiId);
			}
			return authResult;
		}
		catch (MsalServiceException e)
		{
			return await HandleTokenRefreshErrorAsync(e, cachedAccessToken).ConfigureAwait(continueOnCapturedContext: false);
		}
	}

	private async Task<AuthenticationResult> RefreshRtOrFetchNewAccessTokenAsync(CancellationToken cancellationToken)
	{
		ILoggerAdapter logger = base.AuthenticationRequestParameters.RequestContext.Logger;
		if (ApiEvent.IsLongRunningObo(base.AuthenticationRequestParameters.ApiId))
		{
			base.AuthenticationRequestParameters.RequestContext.Logger.Info("[OBO request] Long-running OBO flow, trying to refresh using a refresh token flow.");
			MsalRefreshTokenCacheItem msalRefreshTokenCacheItem = await base.CacheManager.FindRefreshTokenAsync().ConfigureAwait(continueOnCapturedContext: false);
			if (msalRefreshTokenCacheItem != null)
			{
				logger.Info("[OBO request] Found a refresh token");
				if (!string.IsNullOrEmpty(msalRefreshTokenCacheItem.RawClientInfo))
				{
					ClientInfo clientInfo = ClientInfo.CreateFromJson(msalRefreshTokenCacheItem.RawClientInfo);
					_ccsRoutingHint = CoreHelpers.GetCcsClientInfoHint(clientInfo.UniqueObjectIdentifier, clientInfo.UniqueTenantIdentifier);
				}
				else
				{
					logger.Info("[OBO request] No client info associated with RT. This is OBO for a Service Principal.");
				}
				return await CacheTokenResponseAndCreateAuthenticationResultAsync(await SilentRequestHelper.RefreshAccessTokenAsync(msalRefreshTokenCacheItem, this, base.AuthenticationRequestParameters, cancellationToken).ConfigureAwait(continueOnCapturedContext: false)).ConfigureAwait(continueOnCapturedContext: false);
			}
			if (base.AuthenticationRequestParameters.ApiId == ApiEvent.ApiIds.AcquireTokenInLongRunningObo)
			{
				base.AuthenticationRequestParameters.RequestContext.Logger.Error("[OBO request] AcquireTokenInLongRunningProcess was called and no access or refresh tokens were found in the cache.");
				throw new MsalClientException("obo_cache_key_not_in_cache_error", "The token cache does not contain a token with an OBO cache key that matches the longRunningProcessSessionKey passed into ILongRunningWebApi.AcquireTokenInLongRunningProcess method. Call ILongRunningWebApi.InitiateLongRunningProcessInWebApi method with this longRunningProcessSessionKey first or call ILongRunningWebApi.AcquireTokenInLongRunningProcess method with an already used longRunningProcessSessionKey. See https://aka.ms/msal-net-long-running-obo .");
			}
			base.AuthenticationRequestParameters.RequestContext.Logger.Info("[OBO request] No refresh token was found in the cache. Fetching OBO tokens from ESTS.");
		}
		else
		{
			logger.Info("[OBO request] Fetching tokens via normal OBO flow.");
		}
		return await FetchNewAccessTokenAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}

	private async Task<AuthenticationResult> FetchNewAccessTokenAsync(CancellationToken cancellationToken)
	{
		MsalTokenResponse msalTokenResponse = await SendTokenRequestAsync(GetBodyParameters(), cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		if (!ApiEvent.IsLongRunningObo(base.AuthenticationRequestParameters.ApiId))
		{
			msalTokenResponse.RefreshToken = null;
		}
		if (msalTokenResponse.ClientInfo == null && base.AuthenticationRequestParameters.AuthorityInfo.IsClientInfoSupported)
		{
			base.AuthenticationRequestParameters.RequestContext.Logger.Info("[OBO request] This is an on behalf of request for a service principal as no client info returned in the token response.");
		}
		return await CacheTokenResponseAndCreateAuthenticationResultAsync(msalTokenResponse).ConfigureAwait(continueOnCapturedContext: false);
	}

	private Dictionary<string, string> GetBodyParameters()
	{
		return new Dictionary<string, string>
		{
			["client_info"] = "1",
			["grant_type"] = _onBehalfOfParameters.UserAssertion.AssertionType,
			["assertion"] = _onBehalfOfParameters.UserAssertion.Assertion,
			["requested_token_use"] = "on_behalf_of"
		};
	}

	protected override KeyValuePair<string, string>? GetCcsHeader(IDictionary<string, string> additionalBodyParameters)
	{
		if (string.IsNullOrEmpty(_ccsRoutingHint))
		{
			return null;
		}
		return new KeyValuePair<string, string>("x-anchormailbox", _ccsRoutingHint);
	}
}
