using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace EnumsNET;

public static class FlagEnums
{
	internal const string DefaultDelimiter = ", ";

	internal const string DefaultParsingDelimiter = ",";

	public static bool IsFlagEnum<TEnum>() where TEnum : struct, Enum
	{
		return Enums.Cache<TEnum>.Instance.IsFlagEnum;
	}

	public static TEnum GetAllFlags<TEnum>() where TEnum : struct, Enum
	{
		TEnum source = default(TEnum);
		Enums.Cache<TEnum>.Instance.GetAllFlags(ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static bool IsValidFlagCombination<TEnum>(TEnum value) where TEnum : struct, Enum
	{
		return Enums.Cache<TEnum>.Instance.IsValidFlagCombination(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static string FormatFlags<TEnum>(TEnum value) where TEnum : struct, Enum
	{
		return Enums.Cache<TEnum>.Instance.FormatFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), null, Enums.DefaultFormats);
	}

	public static string FormatFlags<TEnum>(TEnum value, string? delimiter) where TEnum : struct, Enum
	{
		return Enums.Cache<TEnum>.Instance.FormatFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), delimiter, Enums.DefaultFormats);
	}

	public static string? FormatFlags<TEnum>(TEnum value, string? delimiter, EnumFormat format) where TEnum : struct, Enum
	{
		return Enums.Cache<TEnum>.Instance.FormatFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), delimiter, ValueCollection.Create(format));
	}

	public static string? FormatFlags<TEnum>(TEnum value, string? delimiter, EnumFormat format0, EnumFormat format1) where TEnum : struct, Enum
	{
		return Enums.Cache<TEnum>.Instance.FormatFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), delimiter, ValueCollection.Create(format0, format1));
	}

	public static string? FormatFlags<TEnum>(TEnum value, string? delimiter, EnumFormat format0, EnumFormat format1, EnumFormat format2) where TEnum : struct, Enum
	{
		return Enums.Cache<TEnum>.Instance.FormatFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), delimiter, ValueCollection.Create(format0, format1, format2));
	}

	public static string? FormatFlags<TEnum>(TEnum value, string? delimiter, params EnumFormat[]? formats) where TEnum : struct, Enum
	{
		return Enums.Cache<TEnum>.Instance.FormatFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), delimiter, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	public static bool TryFormatFlags<TEnum>(TEnum value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> delimiter = default(ReadOnlySpan<char>), params EnumFormat[]? formats) where TEnum : struct, Enum
	{
		return Enums.Cache<TEnum>.Instance.TryFormatFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), destination, out charsWritten, delimiter, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	public static IReadOnlyList<TEnum> GetFlags<TEnum>(this TEnum value) where TEnum : struct, Enum
	{
		return UnsafeUtility.As<IReadOnlyList<TEnum>>(Enums.Cache<TEnum>.Instance.GetFlags(ref UnsafeUtility.As<TEnum, byte>(ref value)));
	}

	public static IReadOnlyList<EnumMember<TEnum>> GetFlagMembers<TEnum>(TEnum value) where TEnum : struct, Enum
	{
		return UnsafeUtility.As<IReadOnlyList<EnumMember<TEnum>>>(Enums.Cache<TEnum>.Instance.GetFlagMembers(ref UnsafeUtility.As<TEnum, byte>(ref value)));
	}

	public static int GetFlagCount<TEnum>() where TEnum : struct, Enum
	{
		return Enums.Cache<TEnum>.Instance.GetFlagCount();
	}

	public static int GetFlagCount<TEnum>(this TEnum value) where TEnum : struct, Enum
	{
		return Enums.Cache<TEnum>.Instance.GetFlagCount(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static int GetFlagCount<TEnum>(this TEnum value, TEnum otherFlags) where TEnum : struct, Enum
	{
		return Enums.Cache<TEnum>.Instance.GetFlagCount(ref UnsafeUtility.As<TEnum, byte>(ref value), ref UnsafeUtility.As<TEnum, byte>(ref otherFlags));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public static bool HasAnyFlags<TEnum>(this TEnum value) where TEnum : struct, Enum
	{
		return UnsafeUtility.SizeOf<TEnum>() switch
		{
			1 => UnsafeUtility.As<TEnum, byte>(ref value) != 0, 
			2 => UnsafeUtility.As<TEnum, ushort>(ref value) != 0, 
			4 => UnsafeUtility.As<TEnum, uint>(ref value) != 0, 
			_ => UnsafeUtility.As<TEnum, ulong>(ref value) != 0, 
		};
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public static bool HasAnyFlags<TEnum>(this TEnum value, TEnum otherFlags) where TEnum : struct, Enum
	{
		return UnsafeUtility.SizeOf<TEnum>() switch
		{
			1 => (UnsafeUtility.As<TEnum, byte>(ref value) & UnsafeUtility.As<TEnum, byte>(ref otherFlags)) != 0, 
			2 => (UnsafeUtility.As<TEnum, ushort>(ref value) & UnsafeUtility.As<TEnum, ushort>(ref otherFlags)) != 0, 
			4 => (UnsafeUtility.As<TEnum, uint>(ref value) & UnsafeUtility.As<TEnum, uint>(ref otherFlags)) != 0, 
			_ => (UnsafeUtility.As<TEnum, ulong>(ref value) & UnsafeUtility.As<TEnum, ulong>(ref otherFlags)) != 0, 
		};
	}

	public static bool HasAllFlags<TEnum>(this TEnum value) where TEnum : struct, Enum
	{
		return Enums.Cache<TEnum>.Instance.HasAllFlags(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public static bool HasAllFlags<TEnum>(this TEnum value, TEnum otherFlags) where TEnum : struct, Enum
	{
		return UnsafeUtility.SizeOf<TEnum>() switch
		{
			1 => (UnsafeUtility.As<TEnum, byte>(ref value) & UnsafeUtility.As<TEnum, byte>(ref otherFlags)) == UnsafeUtility.As<TEnum, byte>(ref otherFlags), 
			2 => (UnsafeUtility.As<TEnum, ushort>(ref value) & UnsafeUtility.As<TEnum, ushort>(ref otherFlags)) == UnsafeUtility.As<TEnum, ushort>(ref otherFlags), 
			4 => (UnsafeUtility.As<TEnum, uint>(ref value) & UnsafeUtility.As<TEnum, uint>(ref otherFlags)) == UnsafeUtility.As<TEnum, uint>(ref otherFlags), 
			_ => (UnsafeUtility.As<TEnum, ulong>(ref value) & UnsafeUtility.As<TEnum, ulong>(ref otherFlags)) == UnsafeUtility.As<TEnum, ulong>(ref otherFlags), 
		};
	}

	public static TEnum ToggleFlags<TEnum>(TEnum value) where TEnum : struct, Enum
	{
		TEnum source = default(TEnum);
		Enums.Cache<TEnum>.Instance.ToggleFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public static TEnum ToggleFlags<TEnum>(TEnum value, TEnum otherFlags) where TEnum : struct, Enum
	{
		switch (UnsafeUtility.SizeOf<TEnum>())
		{
		case 1:
		{
			byte source4 = (byte)(UnsafeUtility.As<TEnum, byte>(ref value) ^ UnsafeUtility.As<TEnum, byte>(ref otherFlags));
			return UnsafeUtility.As<byte, TEnum>(ref source4);
		}
		case 2:
		{
			ushort source3 = (ushort)(UnsafeUtility.As<TEnum, ushort>(ref value) ^ UnsafeUtility.As<TEnum, ushort>(ref otherFlags));
			return UnsafeUtility.As<ushort, TEnum>(ref source3);
		}
		case 4:
		{
			uint source2 = UnsafeUtility.As<TEnum, uint>(ref value) ^ UnsafeUtility.As<TEnum, uint>(ref otherFlags);
			return UnsafeUtility.As<uint, TEnum>(ref source2);
		}
		default:
		{
			ulong source = UnsafeUtility.As<TEnum, ulong>(ref value) ^ UnsafeUtility.As<TEnum, ulong>(ref otherFlags);
			return UnsafeUtility.As<ulong, TEnum>(ref source);
		}
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public static TEnum CommonFlags<TEnum>(this TEnum value, TEnum otherFlags) where TEnum : struct, Enum
	{
		switch (UnsafeUtility.SizeOf<TEnum>())
		{
		case 1:
		{
			byte source4 = (byte)(UnsafeUtility.As<TEnum, byte>(ref value) & UnsafeUtility.As<TEnum, byte>(ref otherFlags));
			return UnsafeUtility.As<byte, TEnum>(ref source4);
		}
		case 2:
		{
			ushort source3 = (ushort)(UnsafeUtility.As<TEnum, ushort>(ref value) & UnsafeUtility.As<TEnum, ushort>(ref otherFlags));
			return UnsafeUtility.As<ushort, TEnum>(ref source3);
		}
		case 4:
		{
			uint source2 = UnsafeUtility.As<TEnum, uint>(ref value) & UnsafeUtility.As<TEnum, uint>(ref otherFlags);
			return UnsafeUtility.As<uint, TEnum>(ref source2);
		}
		default:
		{
			ulong source = UnsafeUtility.As<TEnum, ulong>(ref value) & UnsafeUtility.As<TEnum, ulong>(ref otherFlags);
			return UnsafeUtility.As<ulong, TEnum>(ref source);
		}
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public static TEnum CombineFlags<TEnum>(this TEnum value, TEnum otherFlags) where TEnum : struct, Enum
	{
		switch (UnsafeUtility.SizeOf<TEnum>())
		{
		case 1:
		{
			byte source4 = (byte)(UnsafeUtility.As<TEnum, byte>(ref value) | UnsafeUtility.As<TEnum, byte>(ref otherFlags));
			return UnsafeUtility.As<byte, TEnum>(ref source4);
		}
		case 2:
		{
			ushort source3 = (ushort)(UnsafeUtility.As<TEnum, ushort>(ref value) | UnsafeUtility.As<TEnum, ushort>(ref otherFlags));
			return UnsafeUtility.As<ushort, TEnum>(ref source3);
		}
		case 4:
		{
			uint source2 = UnsafeUtility.As<TEnum, uint>(ref value) | UnsafeUtility.As<TEnum, uint>(ref otherFlags);
			return UnsafeUtility.As<uint, TEnum>(ref source2);
		}
		default:
		{
			ulong source = UnsafeUtility.As<TEnum, ulong>(ref value) | UnsafeUtility.As<TEnum, ulong>(ref otherFlags);
			return UnsafeUtility.As<ulong, TEnum>(ref source);
		}
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public static TEnum CombineFlags<TEnum>(TEnum flag0, TEnum flag1, TEnum flag2) where TEnum : struct, Enum
	{
		switch (UnsafeUtility.SizeOf<TEnum>())
		{
		case 1:
		{
			byte source4 = (byte)(UnsafeUtility.As<TEnum, byte>(ref flag0) | UnsafeUtility.As<TEnum, byte>(ref flag1) | UnsafeUtility.As<TEnum, byte>(ref flag2));
			return UnsafeUtility.As<byte, TEnum>(ref source4);
		}
		case 2:
		{
			ushort source3 = (ushort)(UnsafeUtility.As<TEnum, ushort>(ref flag0) | UnsafeUtility.As<TEnum, ushort>(ref flag1) | UnsafeUtility.As<TEnum, ushort>(ref flag2));
			return UnsafeUtility.As<ushort, TEnum>(ref source3);
		}
		case 4:
		{
			uint source2 = UnsafeUtility.As<TEnum, uint>(ref flag0) | UnsafeUtility.As<TEnum, uint>(ref flag1) | UnsafeUtility.As<TEnum, uint>(ref flag2);
			return UnsafeUtility.As<uint, TEnum>(ref source2);
		}
		default:
		{
			ulong source = UnsafeUtility.As<TEnum, ulong>(ref flag0) | UnsafeUtility.As<TEnum, ulong>(ref flag1) | UnsafeUtility.As<TEnum, ulong>(ref flag2);
			return UnsafeUtility.As<ulong, TEnum>(ref source);
		}
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public static TEnum CombineFlags<TEnum>(TEnum flag0, TEnum flag1, TEnum flag2, TEnum flag3) where TEnum : struct, Enum
	{
		switch (UnsafeUtility.SizeOf<TEnum>())
		{
		case 1:
		{
			byte source4 = (byte)(UnsafeUtility.As<TEnum, byte>(ref flag0) | UnsafeUtility.As<TEnum, byte>(ref flag1) | UnsafeUtility.As<TEnum, byte>(ref flag2) | UnsafeUtility.As<TEnum, byte>(ref flag3));
			return UnsafeUtility.As<byte, TEnum>(ref source4);
		}
		case 2:
		{
			ushort source3 = (ushort)(UnsafeUtility.As<TEnum, ushort>(ref flag0) | UnsafeUtility.As<TEnum, ushort>(ref flag1) | UnsafeUtility.As<TEnum, ushort>(ref flag2) | UnsafeUtility.As<TEnum, ushort>(ref flag3));
			return UnsafeUtility.As<ushort, TEnum>(ref source3);
		}
		case 4:
		{
			uint source2 = UnsafeUtility.As<TEnum, uint>(ref flag0) | UnsafeUtility.As<TEnum, uint>(ref flag1) | UnsafeUtility.As<TEnum, uint>(ref flag2) | UnsafeUtility.As<TEnum, uint>(ref flag3);
			return UnsafeUtility.As<uint, TEnum>(ref source2);
		}
		default:
		{
			ulong source = UnsafeUtility.As<TEnum, ulong>(ref flag0) | UnsafeUtility.As<TEnum, ulong>(ref flag1) | UnsafeUtility.As<TEnum, ulong>(ref flag2) | UnsafeUtility.As<TEnum, ulong>(ref flag3);
			return UnsafeUtility.As<ulong, TEnum>(ref source);
		}
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public static TEnum CombineFlags<TEnum>(TEnum flag0, TEnum flag1, TEnum flag2, TEnum flag3, TEnum flag4) where TEnum : struct, Enum
	{
		switch (UnsafeUtility.SizeOf<TEnum>())
		{
		case 1:
		{
			byte source4 = (byte)(UnsafeUtility.As<TEnum, byte>(ref flag0) | UnsafeUtility.As<TEnum, byte>(ref flag1) | UnsafeUtility.As<TEnum, byte>(ref flag2) | UnsafeUtility.As<TEnum, byte>(ref flag3) | UnsafeUtility.As<TEnum, byte>(ref flag4));
			return UnsafeUtility.As<byte, TEnum>(ref source4);
		}
		case 2:
		{
			ushort source3 = (ushort)(UnsafeUtility.As<TEnum, ushort>(ref flag0) | UnsafeUtility.As<TEnum, ushort>(ref flag1) | UnsafeUtility.As<TEnum, ushort>(ref flag2) | UnsafeUtility.As<TEnum, ushort>(ref flag3) | UnsafeUtility.As<TEnum, ushort>(ref flag4));
			return UnsafeUtility.As<ushort, TEnum>(ref source3);
		}
		case 4:
		{
			uint source2 = UnsafeUtility.As<TEnum, uint>(ref flag0) | UnsafeUtility.As<TEnum, uint>(ref flag1) | UnsafeUtility.As<TEnum, uint>(ref flag2) | UnsafeUtility.As<TEnum, uint>(ref flag3) | UnsafeUtility.As<TEnum, uint>(ref flag4);
			return UnsafeUtility.As<uint, TEnum>(ref source2);
		}
		default:
		{
			ulong source = UnsafeUtility.As<TEnum, ulong>(ref flag0) | UnsafeUtility.As<TEnum, ulong>(ref flag1) | UnsafeUtility.As<TEnum, ulong>(ref flag2) | UnsafeUtility.As<TEnum, ulong>(ref flag3) | UnsafeUtility.As<TEnum, ulong>(ref flag4);
			return UnsafeUtility.As<ulong, TEnum>(ref source);
		}
		}
	}

	public static TEnum CombineFlags<TEnum>(params TEnum[]? flags) where TEnum : struct, Enum
	{
		return CombineFlags((IEnumerable<TEnum>?)flags);
	}

	public static TEnum CombineFlags<TEnum>(IEnumerable<TEnum>? flags) where TEnum : struct, Enum
	{
		if (flags == null)
		{
			return default(TEnum);
		}
		switch (UnsafeUtility.SizeOf<TEnum>())
		{
		case 1:
		{
			byte source7 = 0;
			foreach (TEnum flag in flags)
			{
				TEnum source8 = flag;
				source7 |= UnsafeUtility.As<TEnum, byte>(ref source8);
			}
			return UnsafeUtility.As<byte, TEnum>(ref source7);
		}
		case 2:
		{
			ushort source3 = 0;
			foreach (TEnum flag2 in flags)
			{
				TEnum source4 = flag2;
				source3 |= UnsafeUtility.As<TEnum, ushort>(ref source4);
			}
			return UnsafeUtility.As<ushort, TEnum>(ref source3);
		}
		case 4:
		{
			uint source5 = 0u;
			foreach (TEnum flag3 in flags)
			{
				TEnum source6 = flag3;
				source5 |= UnsafeUtility.As<TEnum, uint>(ref source6);
			}
			return UnsafeUtility.As<uint, TEnum>(ref source5);
		}
		default:
		{
			ulong source = 0uL;
			foreach (TEnum flag4 in flags)
			{
				TEnum source2 = flag4;
				source |= UnsafeUtility.As<TEnum, ulong>(ref source2);
			}
			return UnsafeUtility.As<ulong, TEnum>(ref source);
		}
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public static TEnum RemoveFlags<TEnum>(this TEnum value, TEnum otherFlags) where TEnum : struct, Enum
	{
		switch (UnsafeUtility.SizeOf<TEnum>())
		{
		case 1:
		{
			byte source4 = (byte)(UnsafeUtility.As<TEnum, byte>(ref value) & ~UnsafeUtility.As<TEnum, byte>(ref otherFlags));
			return UnsafeUtility.As<byte, TEnum>(ref source4);
		}
		case 2:
		{
			ushort source3 = (ushort)(UnsafeUtility.As<TEnum, ushort>(ref value) & ~UnsafeUtility.As<TEnum, ushort>(ref otherFlags));
			return UnsafeUtility.As<ushort, TEnum>(ref source3);
		}
		case 4:
		{
			uint source2 = UnsafeUtility.As<TEnum, uint>(ref value) & ~UnsafeUtility.As<TEnum, uint>(ref otherFlags);
			return UnsafeUtility.As<uint, TEnum>(ref source2);
		}
		default:
		{
			ulong source = UnsafeUtility.As<TEnum, ulong>(ref value) & ~UnsafeUtility.As<TEnum, ulong>(ref otherFlags);
			return UnsafeUtility.As<ulong, TEnum>(ref source);
		}
		}
	}

	public static TEnum ParseFlags<TEnum>(string value) where TEnum : struct, Enum
	{
		return ParseFlags<TEnum>(value, ignoreCase: false, null, Enums.DefaultFormats);
	}

	public static TEnum ParseFlags<TEnum>(string value, bool ignoreCase) where TEnum : struct, Enum
	{
		return ParseFlags<TEnum>(value, ignoreCase, null, Enums.DefaultFormats);
	}

	public static TEnum ParseFlags<TEnum>(string value, bool ignoreCase, string? delimiter) where TEnum : struct, Enum
	{
		return ParseFlags<TEnum>(value, ignoreCase, delimiter, Enums.DefaultFormats);
	}

	public static TEnum ParseFlags<TEnum>(string value, bool ignoreCase, string? delimiter, EnumFormat format) where TEnum : struct, Enum
	{
		return ParseFlags<TEnum>(value, ignoreCase, delimiter, ValueCollection.Create(format));
	}

	public static TEnum ParseFlags<TEnum>(string value, bool ignoreCase, string? delimiter, EnumFormat format0, EnumFormat format1) where TEnum : struct, Enum
	{
		return ParseFlags<TEnum>(value, ignoreCase, delimiter, ValueCollection.Create(format0, format1));
	}

	public static TEnum ParseFlags<TEnum>(string value, bool ignoreCase, string? delimiter, EnumFormat format0, EnumFormat format1, EnumFormat format2) where TEnum : struct, Enum
	{
		return ParseFlags<TEnum>(value, ignoreCase, delimiter, ValueCollection.Create(format0, format1, format2));
	}

	public static TEnum ParseFlags<TEnum>(string value, bool ignoreCase, string? delimiter, params EnumFormat[]? formats) where TEnum : struct, Enum
	{
		return ParseFlags<TEnum>(value, ignoreCase, delimiter, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static TEnum ParseFlags<TEnum>(string value, bool ignoreCase, string? delimiter, ValueCollection<EnumFormat> formats) where TEnum : struct, Enum
	{
		Preconditions.NotNull(value, "value");
		TEnum source = default(TEnum);
		Enums.Cache<TEnum>.Instance.ParseFlags(value, ignoreCase, delimiter, formats, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ParseFlags<TEnum>(ReadOnlySpan<char> value, bool ignoreCase = false, string? delimiter = null) where TEnum : struct, Enum
	{
		return ParseFlags<TEnum>(value, ignoreCase, delimiter, Enums.DefaultFormats);
	}

	public static TEnum ParseFlags<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, EnumFormat format) where TEnum : struct, Enum
	{
		return ParseFlags<TEnum>(value, ignoreCase, delimiter, ValueCollection.Create(format));
	}

	public static TEnum ParseFlags<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, EnumFormat format0, EnumFormat format1) where TEnum : struct, Enum
	{
		return ParseFlags<TEnum>(value, ignoreCase, delimiter, ValueCollection.Create(format0, format1));
	}

	public static TEnum ParseFlags<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, EnumFormat format0, EnumFormat format1, EnumFormat format2) where TEnum : struct, Enum
	{
		return ParseFlags<TEnum>(value, ignoreCase, delimiter, ValueCollection.Create(format0, format1, format2));
	}

	public static TEnum ParseFlags<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, params EnumFormat[]? formats) where TEnum : struct, Enum
	{
		return ParseFlags<TEnum>(value, ignoreCase, delimiter, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static TEnum ParseFlags<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, ValueCollection<EnumFormat> formats) where TEnum : struct, Enum
	{
		TEnum source = default(TEnum);
		Enums.Cache<TEnum>.Instance.ParseFlags(value, ignoreCase, delimiter, formats, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static bool TryParseFlags<TEnum>(string? value, out TEnum result) where TEnum : struct, Enum
	{
		return TryParseFlags<TEnum>(value, ignoreCase: false, null, out result);
	}

	public static bool TryParseFlags<TEnum>(string? value, bool ignoreCase, out TEnum result) where TEnum : struct, Enum
	{
		return TryParseFlags<TEnum>(value, ignoreCase, null, out result);
	}

	public static bool TryParseFlags<TEnum>(string? value, bool ignoreCase, string? delimiter, out TEnum result) where TEnum : struct, Enum
	{
		return TryParseFlags<TEnum>(value, ignoreCase, delimiter, out result, Enums.DefaultFormats);
	}

	public static bool TryParseFlags<TEnum>(string? value, bool ignoreCase, string? delimiter, out TEnum result, EnumFormat format) where TEnum : struct, Enum
	{
		return TryParseFlags<TEnum>(value, ignoreCase, delimiter, out result, ValueCollection.Create(format));
	}

	public static bool TryParseFlags<TEnum>(string? value, bool ignoreCase, string? delimiter, out TEnum result, EnumFormat format0, EnumFormat format1) where TEnum : struct, Enum
	{
		return TryParseFlags<TEnum>(value, ignoreCase, delimiter, out result, ValueCollection.Create(format0, format1));
	}

	public static bool TryParseFlags<TEnum>(string? value, bool ignoreCase, string? delimiter, out TEnum result, EnumFormat format0, EnumFormat format1, EnumFormat format2) where TEnum : struct, Enum
	{
		return TryParseFlags<TEnum>(value, ignoreCase, delimiter, out result, ValueCollection.Create(format0, format1, format2));
	}

	public static bool TryParseFlags<TEnum>(string? value, bool ignoreCase, string? delimiter, out TEnum result, params EnumFormat[]? formats) where TEnum : struct, Enum
	{
		return TryParseFlags<TEnum>(value, ignoreCase, delimiter, out result, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	public static bool TryParseFlags<TEnum>(ReadOnlySpan<char> value, out TEnum result) where TEnum : struct, Enum
	{
		return TryParseFlags<TEnum>(value, ignoreCase: false, null, out result);
	}

	public static bool TryParseFlags<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, out TEnum result) where TEnum : struct, Enum
	{
		return TryParseFlags<TEnum>(value, ignoreCase, null, out result);
	}

	public static bool TryParseFlags<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out TEnum result) where TEnum : struct, Enum
	{
		return TryParseFlags<TEnum>(value, ignoreCase, delimiter, out result, Enums.DefaultFormats);
	}

	public static bool TryParseFlags<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out TEnum result, EnumFormat format) where TEnum : struct, Enum
	{
		return TryParseFlags<TEnum>(value, ignoreCase, delimiter, out result, ValueCollection.Create(format));
	}

	public static bool TryParseFlags<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out TEnum result, EnumFormat format0, EnumFormat format1) where TEnum : struct, Enum
	{
		return TryParseFlags<TEnum>(value, ignoreCase, delimiter, out result, ValueCollection.Create(format0, format1));
	}

	public static bool TryParseFlags<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out TEnum result, EnumFormat format0, EnumFormat format1, EnumFormat format2) where TEnum : struct, Enum
	{
		return TryParseFlags<TEnum>(value, ignoreCase, delimiter, out result, ValueCollection.Create(format0, format1, format2));
	}

	public static bool TryParseFlags<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out TEnum result, params EnumFormat[]? formats) where TEnum : struct, Enum
	{
		return TryParseFlags<TEnum>(value, ignoreCase, delimiter, out result, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool TryParseFlags<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out TEnum result, ValueCollection<EnumFormat> formats) where TEnum : struct, Enum
	{
		result = default(TEnum);
		return Enums.Cache<TEnum>.Instance.TryParseFlags(value, ignoreCase, delimiter, ref UnsafeUtility.As<TEnum, byte>(ref result), formats);
	}

	public static bool IsFlagEnumUnsafe<TEnum>()
	{
		return Enums.GetCacheUnsafe<TEnum>().IsFlagEnum;
	}

	public static TEnum GetAllFlagsUnsafe<TEnum>()
	{
		TEnum source = default(TEnum);
		Enums.GetCacheUnsafe<TEnum>().GetAllFlags(ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static bool IsValidFlagCombinationUnsafe<TEnum>(TEnum value)
	{
		return Enums.GetCacheUnsafe<TEnum>().IsValidFlagCombination(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static string FormatFlagsUnsafe<TEnum>(TEnum value)
	{
		return Enums.GetCacheUnsafe<TEnum>().FormatFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), null, Enums.DefaultFormats);
	}

	public static string FormatFlagsUnsafe<TEnum>(TEnum value, string? delimiter)
	{
		return Enums.GetCacheUnsafe<TEnum>().FormatFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), delimiter, Enums.DefaultFormats);
	}

	public static string? FormatFlagsUnsafe<TEnum>(TEnum value, string? delimiter, EnumFormat format)
	{
		return Enums.GetCacheUnsafe<TEnum>().FormatFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), delimiter, ValueCollection.Create(format));
	}

	public static string? FormatFlagsUnsafe<TEnum>(TEnum value, string? delimiter, EnumFormat format0, EnumFormat format1)
	{
		return Enums.GetCacheUnsafe<TEnum>().FormatFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), delimiter, ValueCollection.Create(format0, format1));
	}

	public static string? FormatFlagsUnsafe<TEnum>(TEnum value, string? delimiter, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return Enums.GetCacheUnsafe<TEnum>().FormatFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), delimiter, ValueCollection.Create(format0, format1, format2));
	}

	public static string? FormatFlagsUnsafe<TEnum>(TEnum value, string? delimiter, params EnumFormat[]? formats)
	{
		return Enums.GetCacheUnsafe<TEnum>().FormatFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), delimiter, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	public static bool TryFormatFlagsUnsafe<TEnum>(TEnum value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> delimiter = default(ReadOnlySpan<char>), params EnumFormat[]? formats)
	{
		return Enums.GetCacheUnsafe<TEnum>().TryFormatFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), destination, out charsWritten, delimiter, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	public static IReadOnlyList<TEnum> GetFlagsUnsafe<TEnum>(TEnum value)
	{
		return UnsafeUtility.As<IReadOnlyList<TEnum>>(Enums.GetCacheUnsafe<TEnum>().GetFlags(ref UnsafeUtility.As<TEnum, byte>(ref value)));
	}

	public static IReadOnlyList<EnumMember<TEnum>> GetFlagMembersUnsafe<TEnum>(TEnum value)
	{
		return UnsafeUtility.As<IReadOnlyList<EnumMember<TEnum>>>(Enums.GetCacheUnsafe<TEnum>().GetFlagMembers(ref UnsafeUtility.As<TEnum, byte>(ref value)));
	}

	public static int GetFlagCountUnsafe<TEnum>()
	{
		return Enums.GetCacheUnsafe<TEnum>().GetFlagCount();
	}

	public static int GetFlagCountUnsafe<TEnum>(TEnum value)
	{
		return Enums.GetCacheUnsafe<TEnum>().GetFlagCount(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static int GetFlagCountUnsafe<TEnum>(TEnum value, TEnum otherFlags)
	{
		return Enums.GetCacheUnsafe<TEnum>().GetFlagCount(ref UnsafeUtility.As<TEnum, byte>(ref value), ref UnsafeUtility.As<TEnum, byte>(ref otherFlags));
	}

	public static bool HasAnyFlagsUnsafe<TEnum>(TEnum value)
	{
		return Enums.GetCacheUnsafe<TEnum>().HasAnyFlags(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static bool HasAnyFlagsUnsafe<TEnum>(TEnum value, TEnum otherFlags)
	{
		return Enums.GetCacheUnsafe<TEnum>().HasAnyFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), ref UnsafeUtility.As<TEnum, byte>(ref otherFlags));
	}

	public static bool HasAllFlagsUnsafe<TEnum>(TEnum value)
	{
		return Enums.GetCacheUnsafe<TEnum>().HasAllFlags(ref UnsafeUtility.As<TEnum, byte>(ref value));
	}

	public static bool HasAllFlagsUnsafe<TEnum>(TEnum value, TEnum otherFlags)
	{
		return Enums.GetCacheUnsafe<TEnum>().HasAllFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), ref UnsafeUtility.As<TEnum, byte>(ref otherFlags));
	}

	public static TEnum ToggleFlagsUnsafe<TEnum>(TEnum value)
	{
		TEnum source = default(TEnum);
		Enums.GetCacheUnsafe<TEnum>().ToggleFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ToggleFlagsUnsafe<TEnum>(TEnum value, TEnum otherFlags)
	{
		TEnum source = default(TEnum);
		Enums.GetCacheUnsafe<TEnum>().ToggleFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), ref UnsafeUtility.As<TEnum, byte>(ref otherFlags), ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum CommonFlagsUnsafe<TEnum>(TEnum value, TEnum otherFlags)
	{
		TEnum source = default(TEnum);
		Enums.GetCacheUnsafe<TEnum>().CommonFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), ref UnsafeUtility.As<TEnum, byte>(ref otherFlags), ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum CombineFlagsUnsafe<TEnum>(TEnum value, TEnum otherFlags)
	{
		TEnum source = default(TEnum);
		Enums.GetCacheUnsafe<TEnum>().CombineFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), ref UnsafeUtility.As<TEnum, byte>(ref otherFlags), ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum CombineFlagsUnsafe<TEnum>(TEnum flag0, TEnum flag1, TEnum flag2)
	{
		TEnum source = default(TEnum);
		Enums.GetCacheUnsafe<TEnum>().CombineFlags(ref UnsafeUtility.As<TEnum, byte>(ref flag0), ref UnsafeUtility.As<TEnum, byte>(ref flag1), ref UnsafeUtility.As<TEnum, byte>(ref flag2), ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum CombineFlagsUnsafe<TEnum>(TEnum flag0, TEnum flag1, TEnum flag2, TEnum flag3)
	{
		TEnum source = default(TEnum);
		Enums.GetCacheUnsafe<TEnum>().CombineFlags(ref UnsafeUtility.As<TEnum, byte>(ref flag0), ref UnsafeUtility.As<TEnum, byte>(ref flag1), ref UnsafeUtility.As<TEnum, byte>(ref flag2), ref UnsafeUtility.As<TEnum, byte>(ref flag3), ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum CombineFlagsUnsafe<TEnum>(TEnum flag0, TEnum flag1, TEnum flag2, TEnum flag3, TEnum flag4)
	{
		TEnum source = default(TEnum);
		Enums.GetCacheUnsafe<TEnum>().CombineFlags(ref UnsafeUtility.As<TEnum, byte>(ref flag0), ref UnsafeUtility.As<TEnum, byte>(ref flag1), ref UnsafeUtility.As<TEnum, byte>(ref flag2), ref UnsafeUtility.As<TEnum, byte>(ref flag3), ref UnsafeUtility.As<TEnum, byte>(ref flag4), ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum CombineFlagsUnsafe<TEnum>(params TEnum[]? flags)
	{
		return CombineFlagsUnsafe((IEnumerable<TEnum>?)flags);
	}

	public static TEnum CombineFlagsUnsafe<TEnum>(IEnumerable<TEnum>? flags)
	{
		EnumCache cacheUnsafe = Enums.GetCacheUnsafe<TEnum>();
		TEnum source = default(TEnum);
		if (flags != null)
		{
			ref byte reference = ref UnsafeUtility.As<TEnum, byte>(ref source);
			foreach (TEnum flag in flags)
			{
				TEnum source2 = flag;
				cacheUnsafe.CombineFlags(ref UnsafeUtility.As<TEnum, byte>(ref source2), ref reference, ref reference);
			}
		}
		return source;
	}

	public static TEnum RemoveFlagsUnsafe<TEnum>(TEnum value, TEnum otherFlags)
	{
		TEnum source = default(TEnum);
		Enums.GetCacheUnsafe<TEnum>().RemoveFlags(ref UnsafeUtility.As<TEnum, byte>(ref value), ref UnsafeUtility.As<TEnum, byte>(ref otherFlags), ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ParseFlagsUnsafe<TEnum>(string value)
	{
		return ParseFlagsUnsafe<TEnum>(value, ignoreCase: false, null, Enums.DefaultFormats);
	}

	public static TEnum ParseFlagsUnsafe<TEnum>(string value, bool ignoreCase)
	{
		return ParseFlagsUnsafe<TEnum>(value, ignoreCase, null, Enums.DefaultFormats);
	}

	public static TEnum ParseFlagsUnsafe<TEnum>(string value, bool ignoreCase, string? delimiter)
	{
		return ParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, Enums.DefaultFormats);
	}

	public static TEnum ParseFlagsUnsafe<TEnum>(string value, bool ignoreCase, string? delimiter, EnumFormat format)
	{
		return ParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, ValueCollection.Create(format));
	}

	public static TEnum ParseFlagsUnsafe<TEnum>(string value, bool ignoreCase, string? delimiter, EnumFormat format0, EnumFormat format1)
	{
		return ParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, ValueCollection.Create(format0, format1));
	}

	public static TEnum ParseFlagsUnsafe<TEnum>(string value, bool ignoreCase, string? delimiter, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return ParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, ValueCollection.Create(format0, format1, format2));
	}

	public static TEnum ParseFlagsUnsafe<TEnum>(string value, bool ignoreCase, string? delimiter, params EnumFormat[]? formats)
	{
		return ParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static TEnum ParseFlagsUnsafe<TEnum>(string value, bool ignoreCase, string? delimiter, ValueCollection<EnumFormat> formats)
	{
		Preconditions.NotNull(value, "value");
		TEnum source = default(TEnum);
		Enums.GetCacheUnsafe<TEnum>().ParseFlags(value, ignoreCase, delimiter, formats, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static TEnum ParseFlagsUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase = false, string? delimiter = null)
	{
		return ParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, Enums.DefaultFormats);
	}

	public static TEnum ParseFlagsUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, EnumFormat format)
	{
		return ParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, ValueCollection.Create(format));
	}

	public static TEnum ParseFlagsUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, EnumFormat format0, EnumFormat format1)
	{
		return ParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, ValueCollection.Create(format0, format1));
	}

	public static TEnum ParseFlagsUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return ParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, ValueCollection.Create(format0, format1, format2));
	}

	public static TEnum ParseFlagsUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, params EnumFormat[]? formats)
	{
		return ParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static TEnum ParseFlagsUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, ValueCollection<EnumFormat> formats)
	{
		TEnum source = default(TEnum);
		Enums.GetCacheUnsafe<TEnum>().ParseFlags(value, ignoreCase, delimiter, formats, ref UnsafeUtility.As<TEnum, byte>(ref source));
		return source;
	}

	public static bool TryParseFlagsUnsafe<TEnum>(string? value, out TEnum result)
	{
		return TryParseFlagsUnsafe<TEnum>(value, ignoreCase: false, null, out result, Enums.DefaultFormats);
	}

	public static bool TryParseFlagsUnsafe<TEnum>(string? value, bool ignoreCase, out TEnum result)
	{
		return TryParseFlagsUnsafe<TEnum>(value, ignoreCase, null, out result, Enums.DefaultFormats);
	}

	public static bool TryParseFlagsUnsafe<TEnum>(string? value, bool ignoreCase, string? delimiter, out TEnum result)
	{
		return TryParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, out result);
	}

	public static bool TryParseFlagsUnsafe<TEnum>(string? value, bool ignoreCase, string? delimiter, out TEnum result, EnumFormat format)
	{
		return TryParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, out result, ValueCollection.Create(format));
	}

	public static bool TryParseFlagsUnsafe<TEnum>(string? value, bool ignoreCase, string? delimiter, out TEnum result, EnumFormat format0, EnumFormat format1)
	{
		return TryParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, out result, ValueCollection.Create(format0, format1));
	}

	public static bool TryParseFlagsUnsafe<TEnum>(string? value, bool ignoreCase, string? delimiter, out TEnum result, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return TryParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, out result, ValueCollection.Create(format0, format1, format2));
	}

	public static bool TryParseFlagsUnsafe<TEnum>(string? value, bool ignoreCase, string? delimiter, out TEnum result, params EnumFormat[]? formats)
	{
		return TryParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, out result, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	public static bool TryParseFlagsUnsafe<TEnum>(ReadOnlySpan<char> value, out TEnum result)
	{
		return TryParseFlagsUnsafe<TEnum>(value, ignoreCase: false, null, out result, Enums.DefaultFormats);
	}

	public static bool TryParseFlagsUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, out TEnum result)
	{
		return TryParseFlagsUnsafe<TEnum>(value, ignoreCase, null, out result, Enums.DefaultFormats);
	}

	public static bool TryParseFlagsUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out TEnum result)
	{
		return TryParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, out result);
	}

	public static bool TryParseFlagsUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out TEnum result, EnumFormat format)
	{
		return TryParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, out result, ValueCollection.Create(format));
	}

	public static bool TryParseFlagsUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out TEnum result, EnumFormat format0, EnumFormat format1)
	{
		return TryParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, out result, ValueCollection.Create(format0, format1));
	}

	public static bool TryParseFlagsUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out TEnum result, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return TryParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, out result, ValueCollection.Create(format0, format1, format2));
	}

	public static bool TryParseFlagsUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out TEnum result, params EnumFormat[]? formats)
	{
		return TryParseFlagsUnsafe<TEnum>(value, ignoreCase, delimiter, out result, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool TryParseFlagsUnsafe<TEnum>(ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out TEnum result, ValueCollection<EnumFormat> formats)
	{
		result = default(TEnum);
		return Enums.GetCacheUnsafe<TEnum>().TryParseFlags(value, ignoreCase, delimiter, ref UnsafeUtility.As<TEnum, byte>(ref result), formats);
	}

	public static bool IsFlagEnum(Type enumType)
	{
		return Enums.GetCache(enumType).IsFlagEnum;
	}

	public static object GetAllFlags(Type enumType)
	{
		return Enums.GetCache(enumType).GetAllFlags();
	}

	public static bool IsValidFlagCombination(Type enumType, object value)
	{
		return Enums.GetCache(enumType).IsValidFlagCombination(value);
	}

	public static string FormatFlags(Type enumType, object value)
	{
		return Enums.GetCache(enumType).FormatFlags(value, null, Enums.DefaultFormats);
	}

	public static string FormatFlags(Type enumType, object value, string? delimiter)
	{
		return Enums.GetCache(enumType).FormatFlags(value, delimiter, Enums.DefaultFormats);
	}

	public static string? FormatFlags(Type enumType, object value, string? delimiter, EnumFormat format)
	{
		return Enums.GetCache(enumType).FormatFlags(value, delimiter, ValueCollection.Create(format));
	}

	public static string? FormatFlags(Type enumType, object value, string? delimiter, EnumFormat format0, EnumFormat format1)
	{
		return Enums.GetCache(enumType).FormatFlags(value, delimiter, ValueCollection.Create(format0, format1));
	}

	public static string? FormatFlags(Type enumType, object value, string? delimiter, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return Enums.GetCache(enumType).FormatFlags(value, delimiter, ValueCollection.Create(format0, format1, format2));
	}

	public static string? FormatFlags(Type enumType, object value, string? delimiter, params EnumFormat[]? formats)
	{
		return Enums.GetCache(enumType).FormatFlags(value, delimiter, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	public static bool TryFormatFlags(Type enumType, object value, Span<char> destination, out int charsWritten, ReadOnlySpan<char> delimiter = default(ReadOnlySpan<char>), params EnumFormat[]? formats)
	{
		return Enums.GetCache(enumType).TryFormatFlags(value, destination, out charsWritten, delimiter, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	public static IReadOnlyList<object> GetFlags(Type enumType, object value)
	{
		return Enums.GetCache(enumType).GetFlags(value);
	}

	public static IReadOnlyList<EnumMember> GetFlagMembers(Type enumType, object value)
	{
		return Enums.GetCache(enumType).GetFlagMembers(value);
	}

	public static int GetFlagCount(Type enumType)
	{
		return Enums.GetCache(enumType).GetFlagCount();
	}

	public static int GetFlagCount(Type enumType, object value)
	{
		return Enums.GetCache(enumType).GetFlagCount(value);
	}

	public static int GetFlagCount(Type enumType, object value, object otherFlags)
	{
		return Enums.GetCache(enumType).GetFlagCount(value, otherFlags);
	}

	public static bool HasAnyFlags(Type enumType, object value)
	{
		return Enums.GetCache(enumType).HasAnyFlags(value);
	}

	public static bool HasAnyFlags(Type enumType, object value, object otherFlags)
	{
		return Enums.GetCache(enumType).HasAnyFlags(value, otherFlags);
	}

	public static bool HasAllFlags(Type enumType, object value)
	{
		return Enums.GetCache(enumType).HasAllFlags(value);
	}

	public static bool HasAllFlags(Type enumType, object value, object otherFlags)
	{
		return Enums.GetCache(enumType).HasAllFlags(value, otherFlags);
	}

	public static object ToggleFlags(Type enumType, object value)
	{
		return Enums.GetCache(enumType).ToggleFlags(value);
	}

	public static object ToggleFlags(Type enumType, object value, object otherFlags)
	{
		return Enums.GetCache(enumType).ToggleFlags(value, otherFlags);
	}

	public static object CommonFlags(Type enumType, object value, object otherFlags)
	{
		return Enums.GetCache(enumType).CommonFlags(value, otherFlags);
	}

	public static object CombineFlags(Type enumType, object value, object otherFlags)
	{
		return Enums.GetCache(enumType).CombineFlags(value, otherFlags);
	}

	public static object CombineFlags(Type enumType, params object[]? flags)
	{
		return CombineFlags(enumType, (IEnumerable<object>?)flags);
	}

	public static object CombineFlags(Type enumType, IEnumerable<object>? flags)
	{
		return Enums.GetCache(enumType).CombineFlags(flags, isNullable: false);
	}

	public static object RemoveFlags(Type enumType, object value, object otherFlags)
	{
		return Enums.GetCache(enumType).RemoveFlags(value, otherFlags);
	}

	public static object ParseFlags(Type enumType, string value)
	{
		return ParseFlags(enumType, value, ignoreCase: false, null, Enums.DefaultFormats);
	}

	public static object ParseFlags(Type enumType, string value, bool ignoreCase)
	{
		return ParseFlags(enumType, value, ignoreCase, null, Enums.DefaultFormats);
	}

	public static object ParseFlags(Type enumType, string value, bool ignoreCase, string? delimiter)
	{
		return ParseFlags(enumType, value, ignoreCase, delimiter, Enums.DefaultFormats);
	}

	public static object ParseFlags(Type enumType, string value, bool ignoreCase, string? delimiter, EnumFormat format)
	{
		return ParseFlags(enumType, value, ignoreCase, delimiter, ValueCollection.Create(format));
	}

	public static object ParseFlags(Type enumType, string value, bool ignoreCase, string? delimiter, EnumFormat format0, EnumFormat format1)
	{
		return ParseFlags(enumType, value, ignoreCase, delimiter, ValueCollection.Create(format0, format1));
	}

	public static object ParseFlags(Type enumType, string value, bool ignoreCase, string? delimiter, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return ParseFlags(enumType, value, ignoreCase, delimiter, ValueCollection.Create(format0, format1, format2));
	}

	public static object ParseFlags(Type enumType, string value, bool ignoreCase, string? delimiter, params EnumFormat[]? formats)
	{
		return ParseFlags(enumType, value, ignoreCase, delimiter, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static object ParseFlags(Type enumType, string value, bool ignoreCase, string? delimiter, ValueCollection<EnumFormat> formats)
	{
		Preconditions.NotNull(value, "value");
		return Enums.GetCache(enumType).ParseFlags(value, ignoreCase, delimiter, formats);
	}

	public static object? ParseFlags(Type enumType, ReadOnlySpan<char> value, bool ignoreCase = false, string? delimiter = null)
	{
		return Enums.GetCache(enumType).ParseFlags(value, ignoreCase, delimiter, Enums.DefaultFormats);
	}

	public static object? ParseFlags(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, EnumFormat format)
	{
		return Enums.GetCache(enumType).ParseFlags(value, ignoreCase, delimiter, ValueCollection.Create(format));
	}

	public static object? ParseFlags(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, EnumFormat format0, EnumFormat format1)
	{
		return Enums.GetCache(enumType).ParseFlags(value, ignoreCase, delimiter, ValueCollection.Create(format0, format1));
	}

	public static object? ParseFlags(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return Enums.GetCache(enumType).ParseFlags(value, ignoreCase, delimiter, ValueCollection.Create(format0, format1, format2));
	}

	public static object? ParseFlags(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, params EnumFormat[]? formats)
	{
		return Enums.GetCache(enumType).ParseFlags(value, ignoreCase, delimiter, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	public static bool TryParseFlags(Type enumType, string value, out object? result)
	{
		return Enums.GetCache(enumType).TryParseFlags(value, ignoreCase: false, null, out result, Enums.DefaultFormats);
	}

	public static bool TryParseFlags(Type enumType, string value, bool ignoreCase, out object? result)
	{
		return Enums.GetCache(enumType).TryParseFlags(value, ignoreCase, null, out result, Enums.DefaultFormats);
	}

	public static bool TryParseFlags(Type enumType, string value, bool ignoreCase, string? delimiter, out object? result)
	{
		return Enums.GetCache(enumType).TryParseFlags(value, ignoreCase, delimiter, out result, Enums.DefaultFormats);
	}

	public static bool TryParseFlags(Type enumType, string value, bool ignoreCase, string? delimiter, out object? result, EnumFormat format)
	{
		return Enums.GetCache(enumType).TryParseFlags(value, ignoreCase, delimiter, out result, ValueCollection.Create(format));
	}

	public static bool TryParseFlags(Type enumType, string value, bool ignoreCase, string? delimiter, out object? result, EnumFormat format0, EnumFormat format1)
	{
		return Enums.GetCache(enumType).TryParseFlags(value, ignoreCase, delimiter, out result, ValueCollection.Create(format0, format1));
	}

	public static bool TryParseFlags(Type enumType, string value, bool ignoreCase, string? delimiter, out object? result, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return Enums.GetCache(enumType).TryParseFlags(value, ignoreCase, delimiter, out result, ValueCollection.Create(format0, format1, format2));
	}

	public static bool TryParseFlags(Type enumType, string value, bool ignoreCase, string? delimiter, out object? result, params EnumFormat[]? formats)
	{
		return Enums.GetCache(enumType).TryParseFlags(value, ignoreCase, delimiter, out result, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	public static bool TryParseFlags(Type enumType, ReadOnlySpan<char> value, out object? result)
	{
		return Enums.GetCache(enumType).TryParseFlags(value, ignoreCase: false, null, out result, Enums.DefaultFormats);
	}

	public static bool TryParseFlags(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, out object? result)
	{
		return Enums.GetCache(enumType).TryParseFlags(value, ignoreCase, null, out result, Enums.DefaultFormats);
	}

	public static bool TryParseFlags(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out object? result)
	{
		return Enums.GetCache(enumType).TryParseFlags(value, ignoreCase, delimiter, out result, Enums.DefaultFormats);
	}

	public static bool TryParseFlags(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out object? result, EnumFormat format)
	{
		return Enums.GetCache(enumType).TryParseFlags(value, ignoreCase, delimiter, out result, ValueCollection.Create(format));
	}

	public static bool TryParseFlags(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out object? result, EnumFormat format0, EnumFormat format1)
	{
		return Enums.GetCache(enumType).TryParseFlags(value, ignoreCase, delimiter, out result, ValueCollection.Create(format0, format1));
	}

	public static bool TryParseFlags(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out object? result, EnumFormat format0, EnumFormat format1, EnumFormat format2)
	{
		return Enums.GetCache(enumType).TryParseFlags(value, ignoreCase, delimiter, out result, ValueCollection.Create(format0, format1, format2));
	}

	public static bool TryParseFlags(Type enumType, ReadOnlySpan<char> value, bool ignoreCase, string? delimiter, out object? result, params EnumFormat[]? formats)
	{
		return Enums.GetCache(enumType).TryParseFlags(value, ignoreCase, delimiter, out result, (formats != null && formats.Length != 0) ? ValueCollection.Create(formats) : Enums.DefaultFormats);
	}

	public static bool IsValidFlagCombination(this EnumMember member)
	{
		Preconditions.NotNull(member, "member");
		return member.IsValidFlagCombination();
	}

	public static IReadOnlyList<TEnum> GetFlags<TEnum>(this EnumMember<TEnum> member)
	{
		Preconditions.NotNull(member, "member");
		return UnsafeUtility.As<IReadOnlyList<TEnum>>(member.GetFlags());
	}

	public static IReadOnlyList<EnumMember<TEnum>> GetFlagMembers<TEnum>(this EnumMember<TEnum> member)
	{
		Preconditions.NotNull(member, "member");
		return UnsafeUtility.As<IReadOnlyList<EnumMember<TEnum>>>(member.GetFlagMembers());
	}

	public static bool HasAnyFlags(this EnumMember member)
	{
		Preconditions.NotNull(member, "member");
		return member.HasAnyFlags();
	}

	public static bool HasAllFlags(this EnumMember member)
	{
		Preconditions.NotNull(member, "member");
		return member.HasAllFlags();
	}

	public static int GetFlagCount(this EnumMember member)
	{
		Preconditions.NotNull(member, "member");
		return member.GetFlagCount();
	}

	public static IReadOnlyList<object> GetFlags(this EnumMember member)
	{
		Preconditions.NotNull(member, "member");
		return member.GetFlags().GetNonGenericContainer();
	}

	public static IReadOnlyList<EnumMember> GetFlagMembers(this EnumMember member)
	{
		Preconditions.NotNull(member, "member");
		return member.GetFlagMembers();
	}
}
