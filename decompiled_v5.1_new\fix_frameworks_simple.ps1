# 简单的目标框架修复脚本
Write-Host "开始修复项目文件..." -ForegroundColor Green

$files = Get-ChildItem -Recurse -Filter "*.csproj" | Where-Object { $_.Name -notlike "*_Fixed*" }
$count = 0

foreach ($file in $files) {
    Write-Host "处理: $($file.Name)" -ForegroundColor Cyan
    
    $content = Get-Content $file.FullName -Raw
    $original = $content
    
    # 替换目标框架
    $content = $content -replace '<TargetFramework>netcoreapp8\.0</TargetFramework>', '<TargetFramework>net8.0</TargetFramework>'
    $content = $content -replace '<TargetFramework>netcoreapp6\.0</TargetFramework>', '<TargetFramework>net8.0</TargetFramework>'
    $content = $content -replace '<TargetFramework>net6\.0</TargetFramework>', '<TargetFramework>net8.0</TargetFramework>'
    
    if ($content -ne $original) {
        Set-Content $file.FullName -Value $content -Encoding UTF8
        Write-Host "  已修复" -ForegroundColor Green
        $count++
    } else {
        Write-Host "  无需修改" -ForegroundColor Gray
    }
}

Write-Host "完成! 修改了 $count 个文件" -ForegroundColor Green