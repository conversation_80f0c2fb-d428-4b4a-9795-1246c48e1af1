using System;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.OAuth2;

namespace Microsoft.Identity.Client.Instance.Oidc;

internal static class OidcRetrieverWithCache
{
	private static readonly ConcurrentDictionary<string, OidcMetadata> s_cache = new ConcurrentDictionary<string, OidcMetadata>();

	private static readonly SemaphoreSlim s_lockOidcRetrieval = new SemaphoreSlim(1);

	public static async Task<OidcMetadata> GetOidcAsync(string authority, RequestContext requestContext)
	{
		if (s_cache.TryGetValue(authority, out var value))
		{
			requestContext.Logger.Verbose(() => "[OIDC Discovery] OIDC discovery found a cached entry for " + authority);
			return value;
		}
		await s_lockOidcRetrieval.WaitAsync().ConfigureAwait(continueOnCapturedContext: false);
		Uri oidcMetadataEndpoint = null;
		try
		{
			if (s_cache.TryGetValue(authority, out value))
			{
				requestContext.Logger.Verbose(() => "[OIDC Discovery] OIDC discovery found a cached entry for " + authority);
				return value;
			}
			UriBuilder uriBuilder = new UriBuilder(authority);
			string path = uriBuilder.Path;
			uriBuilder.Path = path.TrimEnd('/') + "/.well-known/openid-configuration";
			oidcMetadataEndpoint = uriBuilder.Uri;
			value = await new OAuth2Client(requestContext.Logger, requestContext.ServiceBundle.HttpManager).DiscoverOidcMetadataAsync(oidcMetadataEndpoint, requestContext).ConfigureAwait(continueOnCapturedContext: false);
			s_cache[authority] = value;
			requestContext.Logger.Verbose(() => "[OIDC Discovery] OIDC discovery retrieved metadata from the network for " + authority);
			return value;
		}
		catch (Exception ex)
		{
			requestContext.Logger.Error($"[OIDC Discovery] Failed to retrieve OpenID configuration from the OpenID endpoint {authority + ".well-known/openid-configuration"} due to {ex}");
			if (ex is MsalServiceException)
			{
				throw;
			}
			throw new MsalServiceException("oidc_failure", $"Failed to retrieve OIDC configuration from {oidcMetadataEndpoint}. See inner exception. ", ex);
		}
		finally
		{
			s_lockOidcRetrieval.Release();
		}
	}

	public static void ResetCacheForTest()
	{
		s_cache.Clear();
	}
}
