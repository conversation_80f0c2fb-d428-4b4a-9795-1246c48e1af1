using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Security.Claims;
using Microsoft.Identity.Client.AuthScheme;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;

namespace Microsoft.Identity.Client;

public class AuthenticationResult
{
	private readonly IAuthenticationScheme _authenticationScheme;

	public string AccessToken { get; }

	[Obsolete("This feature has been deprecated", false)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public bool IsExtendedLifeTimeToken { get; }

	public string UniqueId { get; }

	public DateTimeOffset ExpiresOn { get; }

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This feature has been deprecated", false)]
	public DateTimeOffset ExtendedExpiresOn { get; }

	public string TenantId { get; }

	public IAccount Account { get; }

	public string IdToken { get; }

	public IEnumerable<string> Scopes { get; }

	public Guid CorrelationId { get; }

	public string TokenType { get; }

	public string SpaAuthCode { get; }

	public IReadOnlyDictionary<string, string> AdditionalResponseParameters { get; }

	public ClaimsPrincipal ClaimsPrincipal { get; }

	internal ApiEvent ApiEvent { get; }

	public AuthenticationResultMetadata AuthenticationResultMetadata { get; }

	[EditorBrowsable(EditorBrowsableState.Never)]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[Obsolete("Use Account instead (See https://aka.ms/msal-net-2-released)", true)]
	public IUser User
	{
		get
		{
			throw new NotImplementedException();
		}
	}

	public AuthenticationResult(string accessToken, bool isExtendedLifeTimeToken, string uniqueId, DateTimeOffset expiresOn, DateTimeOffset extendedExpiresOn, string tenantId, IAccount account, string idToken, IEnumerable<string> scopes, Guid correlationId, string tokenType = "Bearer", AuthenticationResultMetadata authenticationResultMetadata = null, ClaimsPrincipal claimsPrincipal = null, string spaAuthCode = null, IReadOnlyDictionary<string, string> additionalResponseParameters = null)
	{
		AccessToken = accessToken;
		IsExtendedLifeTimeToken = isExtendedLifeTimeToken;
		ExtendedExpiresOn = extendedExpiresOn;
		UniqueId = uniqueId;
		ExpiresOn = expiresOn;
		TenantId = tenantId;
		Account = account;
		IdToken = idToken;
		Scopes = scopes;
		CorrelationId = correlationId;
		TokenType = tokenType;
		AuthenticationResultMetadata = authenticationResultMetadata;
		ClaimsPrincipal = claimsPrincipal;
		SpaAuthCode = spaAuthCode;
		AdditionalResponseParameters = additionalResponseParameters;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	public AuthenticationResult(string accessToken, bool isExtendedLifeTimeToken, string uniqueId, DateTimeOffset expiresOn, DateTimeOffset extendedExpiresOn, string tenantId, IAccount account, string idToken, IEnumerable<string> scopes, Guid correlationId, AuthenticationResultMetadata authenticationResultMetadata, string tokenType = "Bearer")
		: this(accessToken, isExtendedLifeTimeToken, uniqueId, expiresOn, extendedExpiresOn, tenantId, account, idToken, scopes, correlationId, tokenType, authenticationResultMetadata)
	{
	}

	internal AuthenticationResult(MsalAccessTokenCacheItem msalAccessTokenCacheItem, MsalIdTokenCacheItem msalIdTokenCacheItem, IAuthenticationScheme authenticationScheme, Guid correlationID, TokenSource tokenSource, ApiEvent apiEvent, Account account, string spaAuthCode, IReadOnlyDictionary<string, string> additionalResponseParameters)
	{
		_authenticationScheme = authenticationScheme ?? throw new ArgumentNullException("authenticationScheme");
		string text = msalAccessTokenCacheItem?.HomeAccountId ?? msalIdTokenCacheItem?.HomeAccountId;
		string environment = msalAccessTokenCacheItem?.Environment ?? msalIdTokenCacheItem?.Environment;
		ClaimsPrincipal = msalIdTokenCacheItem?.IdToken.ClaimsPrincipal;
		if (account != null)
		{
			Account = account;
		}
		else if (text != null)
		{
			Account = new Account(text, msalIdTokenCacheItem?.GetUsername(), environment);
		}
		UniqueId = msalIdTokenCacheItem?.IdToken?.GetUniqueId();
		TenantId = msalIdTokenCacheItem?.IdToken?.TenantId;
		IdToken = msalIdTokenCacheItem?.Secret;
		SpaAuthCode = spaAuthCode;
		CorrelationId = correlationID;
		ApiEvent = apiEvent;
		AuthenticationResultMetadata = new AuthenticationResultMetadata(tokenSource);
		AdditionalResponseParameters = additionalResponseParameters;
		if (msalAccessTokenCacheItem != null)
		{
			AccessToken = authenticationScheme.FormatAccessToken(msalAccessTokenCacheItem);
			ExpiresOn = msalAccessTokenCacheItem.ExpiresOn;
			Scopes = msalAccessTokenCacheItem.ScopeSet;
			ExtendedExpiresOn = msalAccessTokenCacheItem.ExtendedExpiresOn;
			IsExtendedLifeTimeToken = msalAccessTokenCacheItem.IsExtendedLifeTimeToken;
			TokenType = msalAccessTokenCacheItem.TokenType;
			if (msalAccessTokenCacheItem.RefreshOn.HasValue)
			{
				AuthenticationResultMetadata.RefreshOn = msalAccessTokenCacheItem.RefreshOn;
			}
		}
	}

	internal AuthenticationResult()
	{
	}

	public string CreateAuthorizationHeader()
	{
		return (_authenticationScheme?.AuthorizationHeaderPrefix ?? TokenType) + " " + AccessToken;
	}
}
