using System.Collections.Generic;
using System.Threading;

namespace Microsoft.Identity.Client.Extensibility;

public class AppTokenProviderParameters
{
	public IEnumerable<string> Scopes { get; internal set; }

	public string CorrelationId { get; internal set; }

	public string Claims { get; internal set; }

	public string TenantId { get; internal set; }

	public CancellationToken CancellationToken { get; internal set; }
}
