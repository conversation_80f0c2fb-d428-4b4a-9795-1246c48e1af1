using System.Collections.Concurrent;
using System.Text;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;

namespace Microsoft.Identity.Client.TelemetryCore.Http;

internal class HttpTelemetryManager : IHttpTelemetryManager
{
	private int _successfullSilentCallCount;

	private ConcurrentQueue<ApiEvent> _failedEvents = new ConcurrentQueue<ApiEvent>();

	public void ResetPreviousUnsentData()
	{
		_successfullSilentCallCount = 0;
		ApiEvent result;
		while (_failedEvents.TryDequeue(out result))
		{
		}
	}

	public void RecordStoppedEvent(ApiEvent stoppedEvent)
	{
		if (!string.IsNullOrEmpty(stoppedEvent.ApiErrorCode))
		{
			_failedEvents.Enqueue(stoppedEvent);
		}
		if (stoppedEvent.IsAccessTokenCacheHit)
		{
			_successfullSilentCallCount++;
		}
	}

	public string GetLastRequestHeader()
	{
		StringBuilder stringBuilder = new StringBuilder();
		StringBuilder stringBuilder2 = new StringBuilder();
		bool flag = true;
		foreach (ApiEvent failedEvent in _failedEvents)
		{
			if (!flag)
			{
				stringBuilder2.Append(',');
			}
			stringBuilder2.Append(HttpHeaderSanitizer.SanitizeHeader(failedEvent.ApiErrorCode));
			if (!flag)
			{
				stringBuilder.Append(',');
			}
			stringBuilder.Append(failedEvent.ApiIdString);
			stringBuilder.Append(',');
			stringBuilder.Append(failedEvent.CorrelationId.ToString());
			flag = false;
		}
		string text = $"{53}|{_successfullSilentCallCount}|{stringBuilder}|{stringBuilder2}|";
		if (text.Length > 3800)
		{
			ResetPreviousUnsentData();
			return string.Empty;
		}
		return text;
	}

	public string GetCurrentRequestHeader(ApiEvent eventInProgress)
	{
		if (eventInProgress == null)
		{
			return string.Empty;
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append('5');
		stringBuilder.Append('|');
		stringBuilder.Append(eventInProgress.ApiIdString);
		stringBuilder.Append(',');
		stringBuilder.Append(eventInProgress.CacheInfoString);
		stringBuilder.Append(',');
		stringBuilder.Append(eventInProgress.RegionUsed);
		stringBuilder.Append(',');
		stringBuilder.Append(eventInProgress.RegionAutodetectionSourceString);
		stringBuilder.Append(',');
		stringBuilder.Append(eventInProgress.RegionOutcomeString);
		stringBuilder.Append('|');
		stringBuilder.Append(eventInProgress.IsTokenCacheSerializedString);
		stringBuilder.Append(',');
		stringBuilder.Append(eventInProgress.IsLegacyCacheEnabledString);
		stringBuilder.Append(',');
		stringBuilder.Append(eventInProgress.TokenTypeString);
		return stringBuilder.ToString();
	}
}
