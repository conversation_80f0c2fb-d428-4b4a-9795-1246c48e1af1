using System;
using System.Runtime.InteropServices;

namespace Microsoft.Identity.Client.Platforms.Features.DesktopOs;

internal static class SecurityFramework
{
	private const string SecurityFrameworkLib = "/System/Library/Frameworks/Security.framework/Security";

	public static readonly IntPtr Handle;

	public static readonly IntPtr kSecClass;

	public static readonly IntPtr kSecMatchLimit;

	public static readonly IntPtr kSecReturnAttributes;

	public static readonly IntPtr kSecReturnRef;

	public static readonly IntPtr kSecReturnPersistentRef;

	public static readonly IntPtr kSecClassGenericPassword;

	public static readonly IntPtr kSecMatchLimitOne;

	public static readonly IntPtr kSecMatchItemList;

	public static readonly IntPtr kSecAttrLabel;

	public static readonly IntPtr kSecAttrAccount;

	public static readonly IntPtr kSecAttrService;

	public static readonly IntPtr kSecValueRef;

	public static readonly IntPtr kSecValueData;

	public static readonly IntPtr kSecReturnData;

	public const int CallerSecuritySession = -1;

	public const int OK = 0;

	public const int ErrorSecNoSuchKeychain = -25294;

	public const int ErrorSecInvalidKeychain = -25295;

	public const int ErrorSecAuthFailed = -25293;

	public const int ErrorSecDuplicateItem = -25299;

	public const int ErrorSecItemNotFound = -25300;

	public const int ErrorSecInteractionNotAllowed = -25308;

	public const int ErrorSecInteractionRequired = -25315;

	public const int ErrorSecNoSuchAttr = -25303;

	static SecurityFramework()
	{
		Handle = LibSystem.dlopen("/System/Library/Frameworks/Security.framework/Security", 0);
		kSecClass = LibSystem.GetGlobal(Handle, "kSecClass");
		kSecMatchLimit = LibSystem.GetGlobal(Handle, "kSecMatchLimit");
		kSecReturnAttributes = LibSystem.GetGlobal(Handle, "kSecReturnAttributes");
		kSecReturnRef = LibSystem.GetGlobal(Handle, "kSecReturnRef");
		kSecReturnPersistentRef = LibSystem.GetGlobal(Handle, "kSecReturnPersistentRef");
		kSecClassGenericPassword = LibSystem.GetGlobal(Handle, "kSecClassGenericPassword");
		kSecMatchLimitOne = LibSystem.GetGlobal(Handle, "kSecMatchLimitOne");
		kSecMatchItemList = LibSystem.GetGlobal(Handle, "kSecMatchItemList");
		kSecAttrLabel = LibSystem.GetGlobal(Handle, "kSecAttrLabel");
		kSecAttrAccount = LibSystem.GetGlobal(Handle, "kSecAttrAccount");
		kSecAttrService = LibSystem.GetGlobal(Handle, "kSecAttrService");
		kSecValueRef = LibSystem.GetGlobal(Handle, "kSecValueRef");
		kSecValueData = LibSystem.GetGlobal(Handle, "kSecValueData");
		kSecReturnData = LibSystem.GetGlobal(Handle, "kSecReturnData");
	}

	[DllImport("/System/Library/Frameworks/Security.framework/Security", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public static extern int SessionGetInfo(int session, out int sessionId, out SessionAttributeBits attributes);

	[DllImport("/System/Library/Frameworks/Security.framework/Security", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public static extern int SecAccessCreate(IntPtr descriptor, IntPtr trustedList, out IntPtr accessRef);

	[DllImport("/System/Library/Frameworks/Security.framework/Security", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public static extern int SecKeychainItemCreateFromContent(IntPtr itemClass, IntPtr attrList, uint length, IntPtr data, IntPtr keychainRef, IntPtr initialAccess, out IntPtr itemRef);

	[DllImport("/System/Library/Frameworks/Security.framework/Security", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public static extern int SecKeychainAddGenericPassword(IntPtr keychain, uint serviceNameLength, string serviceName, uint accountNameLength, string accountName, uint passwordLength, byte[] passwordData, out IntPtr itemRef);

	[DllImport("/System/Library/Frameworks/Security.framework/Security", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public static extern int SecKeychainFindGenericPassword(IntPtr keychainOrArray, uint serviceNameLength, string serviceName, uint accountNameLength, string accountName, out uint passwordLength, out IntPtr passwordData, out IntPtr itemRef);

	[DllImport("/System/Library/Frameworks/Security.framework/Security", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public unsafe static extern int SecKeychainItemCopyAttributesAndData(IntPtr itemRef, IntPtr info, IntPtr itemClass, SecKeychainAttributeList** attrList, uint* dataLength, void** data);

	[DllImport("/System/Library/Frameworks/Security.framework/Security", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public static extern int SecKeychainItemModifyAttributesAndData(IntPtr itemRef, IntPtr attrList, uint length, byte[] data);

	[DllImport("/System/Library/Frameworks/Security.framework/Security", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public static extern int SecKeychainItemDelete(IntPtr itemRef);

	[DllImport("/System/Library/Frameworks/Security.framework/Security", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public static extern int SecKeychainItemFreeContent(IntPtr attrList, IntPtr data);

	[DllImport("/System/Library/Frameworks/Security.framework/Security", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public static extern int SecKeychainItemFreeAttributesAndData(IntPtr attrList, IntPtr data);

	[DllImport("/System/Library/Frameworks/Security.framework/Security", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public static extern int SecItemCopyMatching(IntPtr query, out IntPtr result);

	[DllImport("/System/Library/Frameworks/Security.framework/Security", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public static extern int SecKeychainItemCopyFromPersistentReference(IntPtr persistentItemRef, out IntPtr itemRef);

	[DllImport("/System/Library/Frameworks/Security.framework/Security", CallingConvention = CallingConvention.Cdecl, CharSet = CharSet.Ansi)]
	public static extern int SecKeychainItemCopyContent(IntPtr itemRef, IntPtr itemClass, IntPtr attrList, out uint length, out IntPtr outData);
}
