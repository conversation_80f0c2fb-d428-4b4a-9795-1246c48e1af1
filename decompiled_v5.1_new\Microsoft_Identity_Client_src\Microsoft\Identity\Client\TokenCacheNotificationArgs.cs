using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Threading;
using Microsoft.Identity.Client.TelemetryCore.TelemetryClient;
using Microsoft.IdentityModel.Abstractions;

namespace Microsoft.Identity.Client;

public sealed class TokenCacheNotificationArgs
{
	[EditorBrowsable(EditorBrowsableState.Never)]
	[DebuggerBrowsable(DebuggerBrowsableState.Never)]
	[Obsolete("Use Account instead (See https://aka.ms/msal-net-2-released)", true)]
	public IUser User
	{
		get
		{
			throw new NotImplementedException();
		}
	}

	public ITokenCacheSerializer TokenCache { get; }

	public string ClientId { get; }

	public IAccount Account { get; }

	public bool HasStateChanged { get; internal set; }

	public bool IsApplicationCache { get; }

	public string SuggestedCacheKey { get; }

	public bool HasTokens { get; }

	public CancellationToken CancellationToken { get; }

	public Guid CorrelationId { get; }

	public IEnumerable<string> RequestScopes { get; }

	public string RequestTenantId { get; }

	public DateTimeOffset? SuggestedCacheExpiry { get; }

	public IIdentityLogger IdentityLogger { get; }

	public bool PiiLoggingEnabled { get; }

	public TelemetryData TelemetryData { get; }

	public TokenCacheNotificationArgs(ITokenCacheSerializer tokenCache, string clientId, IAccount account, bool hasStateChanged, bool isApplicationCache, string suggestedCacheKey, bool hasTokens, DateTimeOffset? suggestedCacheExpiry, CancellationToken cancellationToken)
		: this(tokenCache, clientId, account, hasStateChanged, isApplicationCache, suggestedCacheKey, hasTokens, suggestedCacheExpiry, cancellationToken, default(Guid), null, null, null, piiLoggingEnabled: false)
	{
	}

	public TokenCacheNotificationArgs(ITokenCacheSerializer tokenCache, string clientId, IAccount account, bool hasStateChanged, bool isApplicationCache, string suggestedCacheKey, bool hasTokens, DateTimeOffset? suggestedCacheExpiry, CancellationToken cancellationToken, Guid correlationId)
		: this(tokenCache, clientId, account, hasStateChanged, isApplicationCache, suggestedCacheKey, hasTokens, suggestedCacheExpiry, cancellationToken, correlationId, null, null, null, piiLoggingEnabled: false)
	{
	}

	public TokenCacheNotificationArgs(ITokenCacheSerializer tokenCache, string clientId, IAccount account, bool hasStateChanged, bool isApplicationCache, string suggestedCacheKey, bool hasTokens, DateTimeOffset? suggestedCacheExpiry, CancellationToken cancellationToken, Guid correlationId, IEnumerable<string> requestScopes, string requestTenantId)
	{
		TokenCache = tokenCache;
		ClientId = clientId;
		Account = account;
		HasStateChanged = hasStateChanged;
		IsApplicationCache = isApplicationCache;
		SuggestedCacheKey = suggestedCacheKey;
		HasTokens = hasTokens;
		CancellationToken = cancellationToken;
		CorrelationId = correlationId;
		RequestScopes = requestScopes;
		RequestTenantId = requestTenantId;
		SuggestedCacheExpiry = suggestedCacheExpiry;
	}

	public TokenCacheNotificationArgs(ITokenCacheSerializer tokenCache, string clientId, IAccount account, bool hasStateChanged, bool isApplicationCache, string suggestedCacheKey, bool hasTokens, DateTimeOffset? suggestedCacheExpiry, CancellationToken cancellationToken, Guid correlationId, IEnumerable<string> requestScopes, string requestTenantId, IIdentityLogger identityLogger, bool piiLoggingEnabled, TelemetryData telemetryData = null)
	{
		TokenCache = tokenCache;
		ClientId = clientId;
		Account = account;
		HasStateChanged = hasStateChanged;
		IsApplicationCache = isApplicationCache;
		SuggestedCacheKey = suggestedCacheKey;
		HasTokens = hasTokens;
		CancellationToken = cancellationToken;
		CorrelationId = correlationId;
		RequestScopes = requestScopes;
		RequestTenantId = requestTenantId;
		SuggestedCacheExpiry = suggestedCacheExpiry;
		IdentityLogger = identityLogger;
		PiiLoggingEnabled = piiLoggingEnabled;
		TelemetryData = telemetryData ?? new TelemetryData();
	}
}
