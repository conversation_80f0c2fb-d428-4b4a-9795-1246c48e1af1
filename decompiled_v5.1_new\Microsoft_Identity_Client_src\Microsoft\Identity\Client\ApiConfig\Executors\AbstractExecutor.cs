using System;
using System.Globalization;
using System.Threading;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.ApiConfig.Executors;

internal abstract class AbstractExecutor
{
	public IServiceBundle ServiceBundle { get; }

	protected AbstractExecutor(IServiceBundle serviceBundle)
	{
		ServiceBundle = serviceBundle;
	}

	protected RequestContext CreateRequestContextAndLogVersionInfo(Guid correlationId, CancellationToken userCancellationToken = default(CancellationToken))
	{
		RequestContext requestContext = new RequestContext(ServiceBundle, correlationId, userCancellationToken);
		requestContext.Logger.Info(() => string.Format(CultureInfo.InvariantCulture, "MSAL {0} with assembly version '{1}'. CorrelationId({2})", ServiceBundle.PlatformProxy.GetProductName(), MsalIdHelper.GetMsalVersion(), requestContext.CorrelationId));
		return requestContext;
	}
}
