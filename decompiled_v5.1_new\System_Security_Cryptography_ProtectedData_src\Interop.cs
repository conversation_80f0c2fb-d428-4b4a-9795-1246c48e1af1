using System;
using System.CodeDom.Compiler;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.InteropServices.Marshalling;

internal static class Interop
{
	internal static class Crypt32
	{
		[Flags]
		internal enum CryptProtectDataFlags
		{
			CRYPTPROTECT_UI_FORBIDDEN = 1,
			CRYPTPROTECT_LOCAL_MACHINE = 4,
			CRYPTPROTECT_CRED_SYNC = 8,
			CRYPTPROTECT_AUDIT = 0x10,
			CRYPTPROTECT_NO_RECOVERY = 0x20,
			CRYPTPROTECT_VERIFY_PROTECTION = 0x40
		}

		internal struct DATA_BLOB
		{
			internal uint cbData;

			internal nint pbData;

			internal DATA_BLOB(nint handle, uint size)
			{
				cbData = size;
				pbData = handle;
			}

			internal byte[] ToByteArray()
			{
				if (cbData == 0)
				{
					return Array.Empty<byte>();
				}
				byte[] array = new byte[cbData];
				Marshal.Copy(pbData, array, 0, (int)cbData);
				return array;
			}

			internal unsafe ReadOnlySpan<byte> DangerousAsSpan()
			{
				return new ReadOnlySpan<byte>((void*)pbData, (int)cbData);
			}
		}

		[LibraryImport("crypt32.dll", SetLastError = true, StringMarshalling = StringMarshalling.Utf16)]
		[GeneratedCode("Microsoft.Interop.LibraryImportGenerator", "8.0.9.3103")]
		[SkipLocalsInit]
		[return: MarshalAs(UnmanagedType.Bool)]
		internal unsafe static bool CryptProtectData(in DATA_BLOB pDataIn, string szDataDescr, ref DATA_BLOB pOptionalEntropy, nint pvReserved, nint pPromptStruct, CryptProtectDataFlags dwFlags, out DATA_BLOB pDataOut)
		{
			Unsafe.SkipInit<DATA_BLOB>(out pDataOut);
			int num;
			int lastSystemError;
			fixed (DATA_BLOB* _pDataOut_native = &pDataOut)
			{
				fixed (DATA_BLOB* _pOptionalEntropy_native = &pOptionalEntropy)
				{
					fixed (char* ptr = &Utf16StringMarshaller.GetPinnableReference(szDataDescr))
					{
						void* _szDataDescr_native = ptr;
						fixed (DATA_BLOB* _pDataIn_native = &pDataIn)
						{
							Marshal.SetLastSystemError(0);
							num = __PInvoke(_pDataIn_native, (ushort*)_szDataDescr_native, _pOptionalEntropy_native, pvReserved, pPromptStruct, dwFlags, _pDataOut_native);
							lastSystemError = Marshal.GetLastSystemError();
						}
					}
				}
			}
			bool result = num != 0;
			Marshal.SetLastPInvokeError(lastSystemError);
			return result;
			[DllImport("crypt32.dll", EntryPoint = "CryptProtectData", ExactSpelling = true)]
			static extern unsafe int __PInvoke(DATA_BLOB* __pDataIn_native, ushort* __szDataDescr_native, DATA_BLOB* __pOptionalEntropy_native, nint __pvReserved_native, nint __pPromptStruct_native, CryptProtectDataFlags __dwFlags_native, DATA_BLOB* __pDataOut_native);
		}

		[LibraryImport("crypt32.dll", SetLastError = true)]
		[GeneratedCode("Microsoft.Interop.LibraryImportGenerator", "8.0.9.3103")]
		[SkipLocalsInit]
		[return: MarshalAs(UnmanagedType.Bool)]
		internal unsafe static bool CryptUnprotectData(in DATA_BLOB pDataIn, nint ppszDataDescr, ref DATA_BLOB pOptionalEntropy, nint pvReserved, nint pPromptStruct, CryptProtectDataFlags dwFlags, out DATA_BLOB pDataOut)
		{
			Unsafe.SkipInit<DATA_BLOB>(out pDataOut);
			int num;
			int lastSystemError;
			fixed (DATA_BLOB* _pDataOut_native = &pDataOut)
			{
				fixed (DATA_BLOB* _pOptionalEntropy_native = &pOptionalEntropy)
				{
					fixed (DATA_BLOB* _pDataIn_native = &pDataIn)
					{
						Marshal.SetLastSystemError(0);
						num = __PInvoke(_pDataIn_native, ppszDataDescr, _pOptionalEntropy_native, pvReserved, pPromptStruct, dwFlags, _pDataOut_native);
						lastSystemError = Marshal.GetLastSystemError();
					}
				}
			}
			bool result = num != 0;
			Marshal.SetLastPInvokeError(lastSystemError);
			return result;
			[DllImport("crypt32.dll", EntryPoint = "CryptUnprotectData", ExactSpelling = true)]
			static extern unsafe int __PInvoke(DATA_BLOB* __pDataIn_native, nint __ppszDataDescr_native, DATA_BLOB* __pOptionalEntropy_native, nint __pvReserved_native, nint __pPromptStruct_native, CryptProtectDataFlags __dwFlags_native, DATA_BLOB* __pDataOut_native);
		}
	}

	internal static class Errors
	{
		internal const int ERROR_SUCCESS = 0;

		internal const int ERROR_INVALID_FUNCTION = 1;

		internal const int ERROR_FILE_NOT_FOUND = 2;

		internal const int ERROR_PATH_NOT_FOUND = 3;

		internal const int ERROR_ACCESS_DENIED = 5;

		internal const int ERROR_INVALID_HANDLE = 6;

		internal const int ERROR_NOT_ENOUGH_MEMORY = 8;

		internal const int ERROR_INVALID_DATA = 13;

		internal const int ERROR_INVALID_DRIVE = 15;

		internal const int ERROR_NO_MORE_FILES = 18;

		internal const int ERROR_NOT_READY = 21;

		internal const int ERROR_BAD_COMMAND = 22;

		internal const int ERROR_BAD_LENGTH = 24;

		internal const int ERROR_SHARING_VIOLATION = 32;

		internal const int ERROR_LOCK_VIOLATION = 33;

		internal const int ERROR_HANDLE_EOF = 38;

		internal const int ERROR_NOT_SUPPORTED = 50;

		internal const int ERROR_BAD_NETPATH = 53;

		internal const int ERROR_NETWORK_ACCESS_DENIED = 65;

		internal const int ERROR_BAD_NET_NAME = 67;

		internal const int ERROR_FILE_EXISTS = 80;

		internal const int ERROR_INVALID_PARAMETER = 87;

		internal const int ERROR_BROKEN_PIPE = 109;

		internal const int ERROR_DISK_FULL = 112;

		internal const int ERROR_SEM_TIMEOUT = 121;

		internal const int ERROR_CALL_NOT_IMPLEMENTED = 120;

		internal const int ERROR_INSUFFICIENT_BUFFER = 122;

		internal const int ERROR_INVALID_NAME = 123;

		internal const int ERROR_MOD_NOT_FOUND = 126;

		internal const int ERROR_NEGATIVE_SEEK = 131;

		internal const int ERROR_DIR_NOT_EMPTY = 145;

		internal const int ERROR_BAD_PATHNAME = 161;

		internal const int ERROR_LOCK_FAILED = 167;

		internal const int ERROR_BUSY = 170;

		internal const int ERROR_ALREADY_EXISTS = 183;

		internal const int ERROR_BAD_EXE_FORMAT = 193;

		internal const int ERROR_ENVVAR_NOT_FOUND = 203;

		internal const int ERROR_FILENAME_EXCED_RANGE = 206;

		internal const int ERROR_EXE_MACHINE_TYPE_MISMATCH = 216;

		internal const int ERROR_FILE_TOO_LARGE = 223;

		internal const int ERROR_PIPE_BUSY = 231;

		internal const int ERROR_NO_DATA = 232;

		internal const int ERROR_PIPE_NOT_CONNECTED = 233;

		internal const int ERROR_MORE_DATA = 234;

		internal const int ERROR_NO_MORE_ITEMS = 259;

		internal const int ERROR_DIRECTORY = 267;

		internal const int ERROR_NOT_OWNER = 288;

		internal const int ERROR_TOO_MANY_POSTS = 298;

		internal const int ERROR_PARTIAL_COPY = 299;

		internal const int ERROR_ARITHMETIC_OVERFLOW = 534;

		internal const int ERROR_PIPE_CONNECTED = 535;

		internal const int ERROR_PIPE_LISTENING = 536;

		internal const int ERROR_MUTANT_LIMIT_EXCEEDED = 587;

		internal const int ERROR_OPERATION_ABORTED = 995;

		internal const int ERROR_IO_INCOMPLETE = 996;

		internal const int ERROR_IO_PENDING = 997;

		internal const int ERROR_NO_TOKEN = 1008;

		internal const int ERROR_SERVICE_DOES_NOT_EXIST = 1060;

		internal const int ERROR_EXCEPTION_IN_SERVICE = 1064;

		internal const int ERROR_PROCESS_ABORTED = 1067;

		internal const int ERROR_NO_UNICODE_TRANSLATION = 1113;

		internal const int ERROR_DLL_INIT_FAILED = 1114;

		internal const int ERROR_COUNTER_TIMEOUT = 1121;

		internal const int ERROR_NO_ASSOCIATION = 1155;

		internal const int ERROR_DDE_FAIL = 1156;

		internal const int ERROR_DLL_NOT_FOUND = 1157;

		internal const int ERROR_NOT_FOUND = 1168;

		internal const int ERROR_CANCELLED = 1223;

		internal const int ERROR_NETWORK_UNREACHABLE = 1231;

		internal const int ERROR_NON_ACCOUNT_SID = 1257;

		internal const int ERROR_NOT_ALL_ASSIGNED = 1300;

		internal const int ERROR_UNKNOWN_REVISION = 1305;

		internal const int ERROR_INVALID_OWNER = 1307;

		internal const int ERROR_INVALID_PRIMARY_GROUP = 1308;

		internal const int ERROR_NO_SUCH_PRIVILEGE = 1313;

		internal const int ERROR_PRIVILEGE_NOT_HELD = 1314;

		internal const int ERROR_INVALID_ACL = 1336;

		internal const int ERROR_INVALID_SECURITY_DESCR = 1338;

		internal const int ERROR_INVALID_SID = 1337;

		internal const int ERROR_BAD_IMPERSONATION_LEVEL = 1346;

		internal const int ERROR_CANT_OPEN_ANONYMOUS = 1347;

		internal const int ERROR_NO_SECURITY_ON_OBJECT = 1350;

		internal const int ERROR_CANNOT_IMPERSONATE = 1368;

		internal const int ERROR_CLASS_ALREADY_EXISTS = 1410;

		internal const int ERROR_NO_SYSTEM_RESOURCES = 1450;

		internal const int ERROR_TIMEOUT = 1460;

		internal const int ERROR_EVENTLOG_FILE_CHANGED = 1503;

		internal const int ERROR_TRUSTED_RELATIONSHIP_FAILURE = 1789;

		internal const int ERROR_RESOURCE_TYPE_NOT_FOUND = 1813;

		internal const int ERROR_RESOURCE_LANG_NOT_FOUND = 1815;

		internal const int RPC_S_CALL_CANCELED = 1818;

		internal const int ERROR_NOT_A_REPARSE_POINT = 4390;

		internal const int ERROR_EVT_QUERY_RESULT_STALE = 15011;

		internal const int ERROR_EVT_QUERY_RESULT_INVALID_POSITION = 15012;

		internal const int ERROR_EVT_INVALID_EVENT_DATA = 15005;

		internal const int ERROR_EVT_PUBLISHER_METADATA_NOT_FOUND = 15002;

		internal const int ERROR_EVT_CHANNEL_NOT_FOUND = 15007;

		internal const int ERROR_EVT_MESSAGE_NOT_FOUND = 15027;

		internal const int ERROR_EVT_MESSAGE_ID_NOT_FOUND = 15028;

		internal const int ERROR_EVT_PUBLISHER_DISABLED = 15037;
	}

	internal static class Kernel32
	{
		private const int FORMAT_MESSAGE_IGNORE_INSERTS = 512;

		private const int FORMAT_MESSAGE_FROM_HMODULE = 2048;

		private const int FORMAT_MESSAGE_FROM_SYSTEM = 4096;

		private const int FORMAT_MESSAGE_ARGUMENT_ARRAY = 8192;

		private const int FORMAT_MESSAGE_ALLOCATE_BUFFER = 256;

		private const int ERROR_INSUFFICIENT_BUFFER = 122;

		[LibraryImport("kernel32.dll", EntryPoint = "FormatMessageW", SetLastError = true)]
		[GeneratedCode("Microsoft.Interop.LibraryImportGenerator", "8.0.9.3103")]
		[SkipLocalsInit]
		private unsafe static int FormatMessage(int dwFlags, nint lpSource, uint dwMessageId, int dwLanguageId, void* lpBuffer, int nSize, nint arguments)
		{
			Marshal.SetLastSystemError(0);
			int result = __PInvoke(dwFlags, lpSource, dwMessageId, dwLanguageId, lpBuffer, nSize, arguments);
			int lastSystemError = Marshal.GetLastSystemError();
			Marshal.SetLastPInvokeError(lastSystemError);
			return result;
			[DllImport("kernel32.dll", EntryPoint = "FormatMessageW", ExactSpelling = true)]
			static extern unsafe int __PInvoke(int __dwFlags_native, nint __lpSource_native, uint __dwMessageId_native, int __dwLanguageId_native, void* __lpBuffer_native, int __nSize_native, nint __arguments_native);
		}

		internal static string GetMessage(int errorCode)
		{
			return GetMessage(errorCode, IntPtr.Zero);
		}

		internal unsafe static string GetMessage(int errorCode, nint moduleHandle)
		{
			int num = 12800;
			if (moduleHandle != IntPtr.Zero)
			{
				num |= 0x800;
			}
			Span<char> span = stackalloc char[256];
			fixed (char* lpBuffer = span)
			{
				int num2 = FormatMessage(num, moduleHandle, (uint)errorCode, 0, lpBuffer, span.Length, IntPtr.Zero);
				if (num2 > 0)
				{
					return GetAndTrimString(span.Slice(0, num2));
				}
			}
			if (Marshal.GetLastWin32Error() == 122)
			{
				nint num3 = 0;
				try
				{
					int num4 = FormatMessage(num | 0x100, moduleHandle, (uint)errorCode, 0, &num3, 0, IntPtr.Zero);
					if (num4 > 0)
					{
						return GetAndTrimString(new Span<char>((void*)num3, num4));
					}
				}
				finally
				{
					Marshal.FreeHGlobal(num3);
				}
			}
			return $"Unknown error (0x{errorCode:x})";
		}

		private static string GetAndTrimString(Span<char> buffer)
		{
			int num = buffer.Length;
			while (num > 0 && buffer[num - 1] <= ' ')
			{
				num--;
			}
			return buffer.Slice(0, num).ToString();
		}
	}

	internal static class Libraries
	{
		internal const string Activeds = "activeds.dll";

		internal const string Advapi32 = "advapi32.dll";

		internal const string Authz = "authz.dll";

		internal const string BCrypt = "BCrypt.dll";

		internal const string Credui = "credui.dll";

		internal const string Crypt32 = "crypt32.dll";

		internal const string CryptUI = "cryptui.dll";

		internal const string Dnsapi = "dnsapi.dll";

		internal const string Dsrole = "dsrole.dll";

		internal const string Gdi32 = "gdi32.dll";

		internal const string HttpApi = "httpapi.dll";

		internal const string IpHlpApi = "iphlpapi.dll";

		internal const string Kernel32 = "kernel32.dll";

		internal const string Logoncli = "logoncli.dll";

		internal const string Mswsock = "mswsock.dll";

		internal const string NCrypt = "ncrypt.dll";

		internal const string Netapi32 = "netapi32.dll";

		internal const string Netutils = "netutils.dll";

		internal const string NtDll = "ntdll.dll";

		internal const string Odbc32 = "odbc32.dll";

		internal const string Ole32 = "ole32.dll";

		internal const string OleAut32 = "oleaut32.dll";

		internal const string Pdh = "pdh.dll";

		internal const string Secur32 = "secur32.dll";

		internal const string Shell32 = "shell32.dll";

		internal const string SspiCli = "sspicli.dll";

		internal const string User32 = "user32.dll";

		internal const string Version = "version.dll";

		internal const string WebSocket = "websocket.dll";

		internal const string Wevtapi = "wevtapi.dll";

		internal const string WinHttp = "winhttp.dll";

		internal const string WinMM = "winmm.dll";

		internal const string Wkscli = "wkscli.dll";

		internal const string Wldap32 = "wldap32.dll";

		internal const string Ws2_32 = "ws2_32.dll";

		internal const string Wtsapi32 = "wtsapi32.dll";

		internal const string CompressionNative = "System.IO.Compression.Native";

		internal const string GlobalizationNative = "System.Globalization.Native";

		internal const string MsQuic = "msquic.dll";

		internal const string HostPolicy = "hostpolicy";

		internal const string Ucrtbase = "ucrtbase.dll";

		internal const string Xolehlp = "xolehlp.dll";

		internal const string Comdlg32 = "comdlg32.dll";

		internal const string Gdiplus = "gdiplus.dll";

		internal const string Oleaut32 = "oleaut32.dll";

		internal const string Winspool = "winspool.drv";
	}
}
