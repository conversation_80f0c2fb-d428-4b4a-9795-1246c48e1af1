using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Extensibility;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.ManagedIdentity;
using Microsoft.Identity.Client.Platforms.net6;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.OAuth2;

[JsonObject]
[Preserve(AllMembers = true)]
internal class MsalTokenResponse : OAuth2ResponseBase
{
	private const string iOSBrokerErrorMetadata = "error_metadata";

	private const string iOSBrokerHomeAccountId = "home_account_id";

	[JsonExtensionData]
	public Dictionary<string, JsonElement> ExtensionData { get; set; }

	[JsonPropertyName("token_type")]
	public string TokenType { get; set; }

	[JsonPropertyName("access_token")]
	public string AccessToken { get; set; }

	[JsonPropertyName("refresh_token")]
	public string RefreshToken { get; set; }

	[JsonPropertyName("scope")]
	public string Scope { get; set; }

	[JsonPropertyName("client_info")]
	public string ClientInfo { get; set; }

	[JsonPropertyName("id_token")]
	public string IdToken { get; set; }

	[JsonPropertyName("expires_in")]
	[JsonNumberHandling(JsonNumberHandling.AllowReadingFromString)]
	public long ExpiresIn { get; set; }

	[JsonNumberHandling(JsonNumberHandling.AllowReadingFromString)]
	[JsonPropertyName("ext_expires_in")]
	public long ExtendedExpiresIn { get; set; }

	[JsonNumberHandling(JsonNumberHandling.AllowReadingFromString)]
	[JsonPropertyName("refresh_in")]
	public long? RefreshIn { get; set; }

	[JsonPropertyName("foci")]
	public string FamilyId { get; set; }

	[JsonPropertyName("spa_code")]
	public string SpaAuthCode { get; set; }

	[JsonPropertyName("authority")]
	public string AuthorityUrl { get; set; }

	public string TenantId { get; set; }

	public string Upn { get; set; }

	public string AccountUserId { get; set; }

	public string WamAccountId { get; set; }

	public TokenSource TokenSource { get; set; }

	public HttpResponse HttpResponse { get; set; }

	public IReadOnlyDictionary<string, string> CreateExtensionDataStringMap()
	{
		if (ExtensionData == null || ExtensionData.Count == 0)
		{
			return CollectionHelpers.GetEmptyDictionary<string, string>();
		}
		Dictionary<string, string> dictionary = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
		foreach (KeyValuePair<string, JsonElement> extensionDatum in ExtensionData)
		{
			if (extensionDatum.Value.ValueKind == JsonValueKind.String || extensionDatum.Value.ValueKind == JsonValueKind.Number || extensionDatum.Value.ValueKind == JsonValueKind.True || extensionDatum.Value.ValueKind == JsonValueKind.False || extensionDatum.Value.ValueKind == JsonValueKind.Null)
			{
				dictionary.Add(extensionDatum.Key, extensionDatum.Value.ToString());
			}
		}
		return dictionary;
	}

	internal static MsalTokenResponse CreateFromiOSBrokerResponse(Dictionary<string, string> responseDictionary)
	{
		if (responseDictionary.TryGetValue("broker_error_code", out var value))
		{
			string value2;
			string text = (responseDictionary.TryGetValue("error_metadata", out value2) ? value2 : null);
			Dictionary<string, string> dictionary = null;
			if (text != null)
			{
				string json = Uri.UnescapeDataString(text);
				dictionary = new Dictionary<string, string>();
				foreach (JsonProperty item in JsonDocument.Parse(json).RootElement.EnumerateObject())
				{
					dictionary.Add(item.Name, item.Value.GetString());
				}
			}
			string value3 = null;
			dictionary?.TryGetValue("home_account_id", out value3);
			string value4;
			string value5;
			string value6;
			return new MsalTokenResponse
			{
				Error = value,
				ErrorDescription = (responseDictionary.TryGetValue("error_description", out value4) ? CoreHelpers.UrlDecode(value4) : string.Empty),
				SubError = (responseDictionary.TryGetValue("suberror", out value5) ? value5 : string.Empty),
				AccountUserId = ((value3 != null) ? AccountId.ParseFromString(value3).ObjectId : null),
				TenantId = ((value3 != null) ? AccountId.ParseFromString(value3).TenantId : null),
				Upn = ((dictionary != null && dictionary.ContainsKey("username")) ? dictionary["username"] : null),
				CorrelationId = (responseDictionary.TryGetValue("correlation_id", out value6) ? value6 : null)
			};
		}
		string value7;
		string value8;
		string value9;
		MsalTokenResponse msalTokenResponse = new MsalTokenResponse
		{
			AccessToken = responseDictionary["access_token"],
			RefreshToken = (responseDictionary.TryGetValue("refresh_token", out value7) ? value7 : null),
			IdToken = responseDictionary["id_token"],
			TokenType = "Bearer",
			CorrelationId = responseDictionary["correlation_id"],
			Scope = responseDictionary["scope"],
			ExpiresIn = (responseDictionary.TryGetValue("expires_on", out value8) ? DateTimeHelpers.GetDurationFromNowInSeconds(value8) : 0),
			ClientInfo = (responseDictionary.TryGetValue("client_info", out value9) ? value9 : null),
			TokenSource = TokenSource.Broker
		};
		if (responseDictionary.TryGetValue("refresh_in", out var value10))
		{
			msalTokenResponse.RefreshIn = long.Parse(value10, CultureInfo.InvariantCulture);
		}
		return msalTokenResponse;
	}

	internal static MsalTokenResponse CreateFromManagedIdentityResponse(ManagedIdentityResponse managedIdentityResponse)
	{
		ValidateManagedIdentityResult(managedIdentityResponse);
		long durationFromNowInSeconds = DateTimeHelpers.GetDurationFromNowInSeconds(managedIdentityResponse.ExpiresOn);
		return new MsalTokenResponse
		{
			AccessToken = managedIdentityResponse.AccessToken,
			ExpiresIn = durationFromNowInSeconds,
			TokenType = managedIdentityResponse.TokenType,
			TokenSource = TokenSource.IdentityProvider,
			RefreshIn = InferManagedIdentityRefreshInValue(durationFromNowInSeconds)
		};
	}

	private static long? InferManagedIdentityRefreshInValue(long expiresIn)
	{
		if (expiresIn > 7200)
		{
			return expiresIn / 2;
		}
		return null;
	}

	private static void ValidateManagedIdentityResult(ManagedIdentityResponse response)
	{
		if (string.IsNullOrEmpty(response.AccessToken))
		{
			HandleInvalidExternalValueError("AccessToken");
		}
		if (DateTimeHelpers.GetDurationFromNowInSeconds(response.ExpiresOn) <= 0)
		{
			HandleInvalidExternalValueError("ExpiresOn");
		}
	}

	internal static MsalTokenResponse CreateFromAppProviderResponse(AppTokenProviderResult tokenProviderResponse)
	{
		ValidateTokenProviderResult(tokenProviderResponse);
		return new MsalTokenResponse
		{
			AccessToken = tokenProviderResponse.AccessToken,
			RefreshToken = null,
			IdToken = null,
			TokenType = "Bearer",
			ExpiresIn = tokenProviderResponse.ExpiresInSeconds,
			ClientInfo = null,
			TokenSource = TokenSource.IdentityProvider,
			TenantId = null,
			RefreshIn = (tokenProviderResponse.RefreshInSeconds ?? EstimateRefreshIn(tokenProviderResponse.ExpiresInSeconds))
		};
	}

	private static long? EstimateRefreshIn(long expiresInSeconds)
	{
		if (expiresInSeconds >= 7200)
		{
			return expiresInSeconds / 2;
		}
		return null;
	}

	private static void ValidateTokenProviderResult(AppTokenProviderResult TokenProviderResult)
	{
		if (string.IsNullOrEmpty(TokenProviderResult.AccessToken))
		{
			HandleInvalidExternalValueError("AccessToken");
		}
		if (TokenProviderResult.ExpiresInSeconds == 0L || TokenProviderResult.ExpiresInSeconds < 0)
		{
			HandleInvalidExternalValueError("ExpiresInSeconds");
		}
	}

	private static void HandleInvalidExternalValueError(string nameOfValue)
	{
		throw new MsalClientException("invalid_token_provider_response_value", MsalErrorMessage.InvalidTokenProviderResponseValue(nameOfValue));
	}

	internal static MsalTokenResponse CreateFromAndroidBrokerResponse(string jsonResponse, string correlationId)
	{
		JsonObject jsonObject = JsonHelper.ParseIntoJsonObject(jsonResponse);
		string text = jsonObject["broker_error_code"]?.ToString();
		if (!string.IsNullOrEmpty(text))
		{
			return new MsalTokenResponse
			{
				Error = text,
				ErrorDescription = jsonObject["broker_error_message"]?.ToString(),
				AuthorityUrl = jsonObject["authority"]?.ToString(),
				TenantId = jsonObject["tenant_id"]?.ToString(),
				Upn = jsonObject["username"]?.ToString(),
				AccountUserId = jsonObject["local_account_id"]?.ToString()
			};
		}
		return new MsalTokenResponse
		{
			AccessToken = jsonObject["access_token"].ToString(),
			IdToken = jsonObject["id_token"].ToString(),
			CorrelationId = correlationId,
			Scope = jsonObject["scopes"].ToString(),
			ExpiresIn = DateTimeHelpers.GetDurationFromNowInSeconds(jsonObject["expires_on"].ToString()),
			ExtendedExpiresIn = DateTimeHelpers.GetDurationFromNowInSeconds(jsonObject["ext_expires_on"].ToString()),
			ClientInfo = jsonObject["client_info"].ToString(),
			TokenType = (jsonObject["token_type"]?.ToString() ?? "Bearer"),
			TokenSource = TokenSource.Broker,
			AuthorityUrl = jsonObject["authority"]?.ToString(),
			TenantId = jsonObject["tenant_id"]?.ToString(),
			Upn = jsonObject["username"]?.ToString(),
			AccountUserId = jsonObject["local_account_id"]?.ToString()
		};
	}

	public void Log(ILoggerAdapter logger, LogLevel logLevel)
	{
		if (logger.IsLoggingEnabled(logLevel))
		{
			string messageWithPii = $"\r\n[MsalTokenResponse]\r\nError: {base.Error}\r\nErrorDescription: {base.ErrorDescription}\r\nScopes: {Scope}\r\nExpiresIn: {ExpiresIn}\r\nRefreshIn: {RefreshIn}\r\nAccessToken returned: {!string.IsNullOrEmpty(AccessToken)}\r\nAccessToken Type: {TokenType}\r\nRefreshToken returned: {!string.IsNullOrEmpty(RefreshToken)}\r\nIdToken returned: {!string.IsNullOrEmpty(IdToken)}\r\nClientInfo: {ClientInfo}\r\nFamilyId: {FamilyId}\r\nWamAccountId exists: {!string.IsNullOrEmpty(WamAccountId)}";
			string messageScrubbed = $"\r\n[MsalTokenResponse]\r\nError: {base.Error}\r\nErrorDescription: {base.ErrorDescription}\r\nScopes: {Scope}\r\nExpiresIn: {ExpiresIn}\r\nRefreshIn: {RefreshIn}\r\nAccessToken returned: {!string.IsNullOrEmpty(AccessToken)}\r\nAccessToken Type: {TokenType}\r\nRefreshToken returned: {!string.IsNullOrEmpty(RefreshToken)}\r\nIdToken returned: {!string.IsNullOrEmpty(IdToken)}\r\nClientInfo returned: {!string.IsNullOrEmpty(ClientInfo)}\r\nFamilyId: {FamilyId}\r\nWamAccountId exists: {!string.IsNullOrEmpty(WamAccountId)}";
			logger.Log(logLevel, messageWithPii, messageScrubbed);
		}
	}
}
