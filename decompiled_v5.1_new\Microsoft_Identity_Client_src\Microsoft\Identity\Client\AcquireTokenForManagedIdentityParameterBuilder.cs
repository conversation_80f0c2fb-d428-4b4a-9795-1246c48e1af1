using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Executors;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.AppConfig;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

public sealed class AcquireTokenForManagedIdentityParameterBuilder : AbstractManagedIdentityAcquireTokenParameterBuilder<AcquireTokenForManagedIdentityParameterBuilder>
{
	private AcquireTokenForManagedIdentityParameters Parameters { get; } = new AcquireTokenForManagedIdentityParameters();

	internal AcquireTokenForManagedIdentityParameterBuilder(IManagedIdentityApplicationExecutor managedIdentityApplicationExecutor)
		: base(managedIdentityApplicationExecutor)
	{
	}

	internal static AcquireTokenForManagedIdentityParameterBuilder Create(IManagedIdentityApplicationExecutor managedIdentityApplicationExecutor, string resource)
	{
		return new AcquireTokenForManagedIdentityParameterBuilder(managedIdentityApplicationExecutor).WithResource(resource);
	}

	private AcquireTokenForManagedIdentityParameterBuilder WithResource(string resource)
	{
		Parameters.Resource = ScopeHelper.RemoveDefaultSuffixIfPresent(resource);
		base.CommonParameters.Scopes = new string[1] { Parameters.Resource };
		return this;
	}

	public AcquireTokenForManagedIdentityParameterBuilder WithForceRefresh(bool forceRefresh)
	{
		Parameters.ForceRefresh = forceRefresh;
		return this;
	}

	internal override Task<AuthenticationResult> ExecuteInternalAsync(CancellationToken cancellationToken)
	{
		return base.ManagedIdentityApplicationExecutor.ExecuteAsync(base.CommonParameters, Parameters, cancellationToken);
	}

	internal override ApiEvent.ApiIds CalculateApiEventId()
	{
		if (base.ServiceBundle.Config.ManagedIdentityId.IdType == ManagedIdentityIdType.SystemAssigned)
		{
			return ApiEvent.ApiIds.AcquireTokenForSystemAssignedManagedIdentity;
		}
		return ApiEvent.ApiIds.AcquireTokenForUserAssignedManagedIdentity;
	}
}
