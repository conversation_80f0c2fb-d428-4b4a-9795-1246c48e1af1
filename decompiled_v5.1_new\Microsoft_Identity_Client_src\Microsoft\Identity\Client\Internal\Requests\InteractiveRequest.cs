using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.Internal.Broker;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.UI;

namespace Microsoft.Identity.Client.Internal.Requests;

internal class InteractiveRequest : RequestBase
{
	private readonly AuthenticationRequestParameters _requestParams;

	private readonly AcquireTokenInteractiveParameters _interactiveParameters;

	private readonly IServiceBundle _serviceBundle;

	private readonly ILoggerAdapter _logger;

	private const string InstanceAwareParam = "instance_aware";

	private readonly IAuthCodeRequestComponent _authCodeRequestComponentOverride;

	private readonly ITokenRequestComponent _authCodeExchangeComponentOverride;

	private readonly ITokenRequestComponent _brokerInteractiveComponent;

	public InteractiveRequest(AuthenticationRequestParameters requestParams, AcquireTokenInteractiveParameters interactiveParameters, IAuthCodeRequestComponent authCodeRequestComponentOverride = null, ITokenRequestComponent authCodeExchangeComponentOverride = null, ITokenRequestComponent brokerExchangeComponentOverride = null)
		: base(requestParams?.RequestContext?.ServiceBundle, requestParams, interactiveParameters)
	{
		_requestParams = requestParams ?? throw new ArgumentNullException("requestParams");
		_interactiveParameters = interactiveParameters ?? throw new ArgumentNullException("interactiveParameters");
		_authCodeRequestComponentOverride = authCodeRequestComponentOverride;
		_authCodeExchangeComponentOverride = authCodeExchangeComponentOverride;
		_brokerInteractiveComponent = brokerExchangeComponentOverride;
		_serviceBundle = requestParams.RequestContext.ServiceBundle;
		_logger = requestParams.RequestContext.Logger;
	}

	protected override async Task<AuthenticationResult> ExecuteAsync(CancellationToken cancellationToken)
	{
		await ResolveAuthorityAsync().ConfigureAwait(continueOnCapturedContext: false);
		cancellationToken.ThrowIfCancellationRequested();
		return await CacheTokenResponseAndCreateAuthenticationResultAsync(await GetTokenResponseAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false)).ConfigureAwait(continueOnCapturedContext: false);
	}

	private async Task<MsalTokenResponse> FetchTokensFromBrokerAsync(string brokerInstallUrl, CancellationToken cancellationToken)
	{
		IBroker broker = _serviceBundle.PlatformProxy.CreateBroker(_serviceBundle.Config, _interactiveParameters.UiParent);
		return await (_brokerInteractiveComponent ?? new BrokerInteractiveRequestComponent(_requestParams, _interactiveParameters, broker, brokerInstallUrl)).FetchTokensAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}

	private async Task<MsalTokenResponse> GetTokenResponseAsync(CancellationToken cancellationToken)
	{
		cancellationToken.ThrowIfCancellationRequested();
		if (_requestParams.AppConfig.IsBrokerEnabled)
		{
			_logger.Info("Broker is configured. Starting broker flow without knowing the broker installation app link. ");
			MsalTokenResponse msalTokenResponse = await FetchTokensFromBrokerAsync(null, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			if (msalTokenResponse != null)
			{
				_logger.Info("Broker attempt completed successfully. ");
				Metrics.IncrementTotalAccessTokensFromBroker();
				return msalTokenResponse;
			}
			if (string.Equals(_requestParams.AuthenticationScheme.AccessTokenType, "pop"))
			{
				_logger.Error("A broker application is required for Proof-of-Possesion, but one could not be found or communicated with. See https://aka.ms/msal-net-pop");
				throw new MsalClientException("broker_application_required", "MSAL cannot invoke the broker and it is required for Proof-of-Possession. WAM (Broker) may not be installed on the user's device or there was an error invoking the broker. Use IPublicClientApplication.IsProofOfPossessionSupportedByClient to ensure Proof-of-Possession can be performed before using WithProofOfPossession.Check logs for more details and see https://aka.ms/msal-net-pop. ");
			}
			_logger.Info("Broker attempt did not complete, most likely because the broker is not installed. Attempting to use a browser / web UI. ");
			cancellationToken.ThrowIfCancellationRequested();
		}
		if (_requestParams.AppConfig.MultiCloudSupportEnabled)
		{
			_logger.Info("Instance Aware was configured.");
			_requestParams.AppConfig.ExtraQueryParameters["instance_aware"] = "true";
		}
		Tuple<AuthorizationResult, string> tuple = await (_authCodeRequestComponentOverride ?? new AuthCodeRequestComponent(_requestParams, _interactiveParameters)).FetchAuthCodeAndPkceVerifierAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		_logger.Info("An authorization code was retrieved from the /authorize endpoint. ");
		AuthorizationResult authResult = tuple.Item1;
		string authCode = authResult.Code;
		string pkceCodeVerifier = tuple.Item2;
		if (BrokerInteractiveRequestComponent.IsBrokerRequiredAuthCode(authCode, out var installationUri))
		{
			return await RunBrokerWithInstallUriAsync(installationUri, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		}
		if (_requestParams.AppConfig.MultiCloudSupportEnabled && !string.IsNullOrEmpty(authResult.CloudInstanceHost))
		{
			_logger.Info("Updating the authority to the cloud specific authority.");
			_requestParams.AuthorityManager = new AuthorityManager(_requestParams.RequestContext, Authority.CreateAuthorityWithEnvironment(_requestParams.Authority.AuthorityInfo, authResult.CloudInstanceHost));
			await ResolveAuthorityAsync().ConfigureAwait(continueOnCapturedContext: false);
		}
		_logger.Info("Exchanging the auth code for tokens. ");
		MsalTokenResponse result = await (_authCodeExchangeComponentOverride ?? new AuthCodeExchangeComponent(_requestParams, _interactiveParameters, authCode, pkceCodeVerifier, authResult.ClientInfo)).FetchTokensAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		Metrics.IncrementTotalAccessTokensFromIdP();
		return result;
	}

	private async Task<MsalTokenResponse> RunBrokerWithInstallUriAsync(string brokerInstallUri, CancellationToken cancellationToken)
	{
		_logger.Info(() => "Based on the auth code, the broker flow is required. Starting broker flow knowing the broker installation app link. ");
		cancellationToken.ThrowIfCancellationRequested();
		MsalTokenResponse tokenResponse = await FetchTokensFromBrokerAsync(brokerInstallUri, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		_logger.Info(() => "Broker attempt completed successfully " + (tokenResponse != null));
		Metrics.IncrementTotalAccessTokensFromBroker();
		return tokenResponse;
	}
}
