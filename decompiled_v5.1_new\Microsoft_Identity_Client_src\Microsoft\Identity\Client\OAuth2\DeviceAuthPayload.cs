using System;
using System.Text.Json.Serialization;
using Microsoft.Identity.Client.Platforms.net6;

namespace Microsoft.Identity.Client.OAuth2;

[JsonObject]
[Preserve(AllMembers = true)]
internal class DeviceAuthPayload
{
	private readonly Lazy<long> _defaultDeviceAuthJWTTimeSpan = new Lazy<long>(() => (long)(DateTime.UtcNow - new DateTime(1970, 1, 1, 0, 0, 0)).TotalSeconds);

	[JsonPropertyName("iat")]
	[JsonNumberHandling(JsonNumberHandling.AllowReadingFromString)]
	public long Iat { get; set; }

	[JsonPropertyName("aud")]
	public string Audience { get; set; }

	[JsonPropertyName("nonce")]
	public string Nonce { get; private set; }

	public DeviceAuthPayload(string audience, string nonce)
	{
		Nonce = nonce;
		Audience = audience;
		Iat = _defaultDeviceAuthJWTTimeSpan.Value;
	}
}
