using System.Configuration;
using System.Runtime.Versioning;

namespace System.Runtime.Caching.Configuration;

[UnsupportedOSPlatform("browser")]
internal sealed class MemoryCacheSection : ConfigurationSection
{
	private static readonly ConfigurationProperty s_propNamedCaches = new ConfigurationProperty("namedCaches", typeof(MemoryCacheSettingsCollection), null, ConfigurationPropertyOptions.None);

	private static readonly ConfigurationPropertyCollection s_properties = new ConfigurationPropertyCollection { s_propNamedCaches };

	protected override ConfigurationPropertyCollection Properties => s_properties;

	[ConfigurationProperty("namedCaches")]
	public MemoryCacheSettingsCollection NamedCaches => (MemoryCacheSettingsCollection)base[s_propNamedCaches];
}
