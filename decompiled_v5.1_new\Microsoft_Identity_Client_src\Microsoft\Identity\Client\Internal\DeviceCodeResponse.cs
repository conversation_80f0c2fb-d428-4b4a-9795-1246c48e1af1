using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.Platforms.net6;

namespace Microsoft.Identity.Client.Internal;

[JsonObject]
[Preserve(AllMembers = true)]
internal class DeviceCodeResponse : OAuth2ResponseBase
{
	[JsonPropertyName("user_code")]
	public string UserCode { get; set; }

	[JsonPropertyName("device_code")]
	public string DeviceCode { get; set; }

	[JsonPropertyName("verification_url")]
	public string VerificationUrl { get; set; }

	[JsonPropertyName("verification_uri")]
	public string VerificationUri { get; set; }

	[JsonPropertyName("expires_in")]
	[JsonNumberHandling(JsonNumberHandling.AllowReadingFromString)]
	public long ExpiresIn { get; set; }

	[JsonPropertyName("interval")]
	[JsonNumberHandling(JsonNumberHandling.AllowReadingFromString)]
	public long Interval { get; set; }

	[JsonPropertyName("message")]
	public string Message { get; set; }

	public DeviceCodeResult GetResult(string clientId, ISet<string> scopes)
	{
		string verificationUrl = (string.IsNullOrWhiteSpace(VerificationUri) ? VerificationUrl : VerificationUri);
		return new DeviceCodeResult(UserCode, DeviceCode, verificationUrl, DateTime.UtcNow.AddSeconds(ExpiresIn), Interval, Message, clientId, scopes);
	}
}
