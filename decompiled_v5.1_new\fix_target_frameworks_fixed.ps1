# 批量修复项目文件目标框架脚本
# 将所有项目统一为 net8.0 目标框架

Write-Host "开始批量修复项目文件目标框架..." -ForegroundColor Green

# 获取所有 .csproj 文件
$projectFiles = Get-ChildItem -Path "." -Recurse -Filter "*.csproj" | Where-Object { $_.Name -notlike "*_Fixed*" -and $_.Name -notlike "*_Updated*" }

$totalFiles = $projectFiles.Count
$processedFiles = 0
$modifiedFiles = 0

Write-Host "找到 $totalFiles 个项目文件" -ForegroundColor Yellow

foreach ($file in $projectFiles) {
    $processedFiles++
    Write-Host "[$processedFiles/$totalFiles] 处理: $($file.Name)" -ForegroundColor Cyan
    
    try {
        # 读取文件内容
        $content = Get-Content $file.FullName -Encoding UTF8
        $modified = $false
        
        # 替换目标框架
        $newContent = @()
        foreach ($line in $content) {
            # 修复 netcoreapp8.0 -> net8.0
            if ($line -match '<TargetFramework>netcoreapp8\.0</TargetFramework>') {
                $line = $line -replace 'netcoreapp8\.0', 'net8.0'
                $modified = $true
                Write-Host "  修复: netcoreapp8.0 -> net8.0" -ForegroundColor Green
            }
            
            # 修复 netcoreapp6.0 -> net8.0
            if ($line -match '<TargetFramework>netcoreapp6\.0</TargetFramework>') {
                $line = $line -replace 'netcoreapp6\.0', 'net8.0'
                $modified = $true
                Write-Host "  修复: netcoreapp6.0 -> net8.0" -ForegroundColor Green
            }
            
            # 修复 net6.0 -> net8.0
            if ($line -match '<TargetFramework>net6\.0</TargetFramework>') {
                $line = $line -replace 'net6\.0', 'net8.0'
                $modified = $true
                Write-Host "  修复: net6.0 -> net8.0" -ForegroundColor Green
            }
            
            $newContent += $line
        }
        
        # 如果有修改，保存文件
        if ($modified) {
            $newContent | Set-Content $file.FullName -Encoding UTF8
            $modifiedFiles++
            Write-Host "  ✓ 已保存修改" -ForegroundColor Green
        } else {
            Write-Host "  - 无需修改" -ForegroundColor Gray
        }
        
    } catch {
        Write-Host "  ✗ 处理失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "修复完成!" -ForegroundColor Green
Write-Host "总计处理: $totalFiles 个文件" -ForegroundColor Yellow
Write-Host "成功修改: $modifiedFiles 个文件" -ForegroundColor Green