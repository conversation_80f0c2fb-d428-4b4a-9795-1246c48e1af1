using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Instance.Discovery;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.TelemetryCore;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;
using Microsoft.Identity.Client.TelemetryCore.TelemetryClient;
using Microsoft.Identity.Client.Utils;
using Microsoft.IdentityModel.Abstractions;

namespace Microsoft.Identity.Client.Internal.Requests;

internal abstract class RequestBase
{
	internal AuthenticationRequestParameters AuthenticationRequestParameters { get; }

	internal ICacheSessionManager CacheManager => AuthenticationRequestParameters.CacheSessionManager;

	internal IServiceBundle ServiceBundle { get; }

	protected RequestBase(IServiceBundle serviceBundle, AuthenticationRequestParameters authenticationRequestParameters, IAcquireTokenParameters acquireTokenParameters)
	{
		ServiceBundle = serviceBundle ?? throw new ArgumentNullException("serviceBundle");
		AuthenticationRequestParameters = authenticationRequestParameters ?? throw new ArgumentNullException("authenticationRequestParameters");
		if (acquireTokenParameters == null)
		{
			throw new ArgumentNullException("acquireTokenParameters");
		}
		acquireTokenParameters.LogParameters(AuthenticationRequestParameters.RequestContext.Logger);
	}

	protected virtual SortedSet<string> GetOverriddenScopes(ISet<string> inputScopes)
	{
		return null;
	}

	protected abstract Task<AuthenticationResult> ExecuteAsync(CancellationToken cancellationToken);

	public async Task<AuthenticationResult> RunAsync(CancellationToken cancellationToken = default(CancellationToken))
	{
		ApiEvent apiEvent = null;
		MsalTelemetryEventDetails telemetryEventDetails = null;
		ITelemetryClient[] telemetryClients = null;
		MeasureDurationResult measureTelemetryDurationResult = StopwatchService.MeasureCodeBlock(delegate
		{
			apiEvent = InitializeApiEvent(AuthenticationRequestParameters.Account?.HomeAccountId?.Identifier);
			AuthenticationRequestParameters.RequestContext.ApiEvent = apiEvent;
			telemetryEventDetails = new MsalTelemetryEventDetails("acquire_token");
			telemetryClients = AuthenticationRequestParameters.RequestContext.ServiceBundle.Config.TelemetryClients;
		});
		using (AuthenticationRequestParameters.RequestContext.CreateTelemetryHelper(apiEvent))
		{
			try
			{
				AuthenticationResult authenticationResult = null;
				MeasureDurationResult measureDurationResult = await StopwatchService.MeasureCodeBlockAsync(async delegate
				{
					AuthenticationRequestParameters.LogParameters();
					LogRequestStarted(AuthenticationRequestParameters);
					authenticationResult = await ExecuteAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
					LogReturnedToken(authenticationResult);
				}).ConfigureAwait(continueOnCapturedContext: false);
				UpdateTelemetry(measureDurationResult.Milliseconds + measureTelemetryDurationResult.Milliseconds, apiEvent, authenticationResult);
				LogMetricsFromAuthResult(authenticationResult, AuthenticationRequestParameters.RequestContext.Logger);
				LogSuccessfulTelemetryToClient(authenticationResult, telemetryEventDetails, telemetryClients);
				LogSuccessTelemetryToOtel(authenticationResult, apiEvent.ApiId, measureDurationResult.Microseconds);
				return authenticationResult;
			}
			catch (MsalException ex)
			{
				apiEvent.ApiErrorCode = ex.ErrorCode;
				if (string.IsNullOrWhiteSpace(ex.CorrelationId))
				{
					ex.CorrelationId = AuthenticationRequestParameters.CorrelationId.ToString();
				}
				AuthenticationRequestParameters.RequestContext.Logger.ErrorPii(ex);
				LogMsalErrorTelemetryToClient(ex, telemetryEventDetails, telemetryClients);
				LogFailureTelemetryToOtel(ex.ErrorCode, apiEvent.ApiId, apiEvent.CacheInfo);
				throw;
			}
			catch (Exception ex2)
			{
				apiEvent.ApiErrorCode = ex2.GetType().Name;
				AuthenticationRequestParameters.RequestContext.Logger.ErrorPii(ex2);
				LogMsalErrorTelemetryToClient(ex2, telemetryEventDetails, telemetryClients);
				LogFailureTelemetryToOtel(ex2.GetType().Name, apiEvent.ApiId, apiEvent.CacheInfo);
				throw;
			}
			finally
			{
				telemetryClients.TrackEvent(telemetryEventDetails);
			}
		}
	}

	private void LogSuccessTelemetryToOtel(AuthenticationResult authenticationResult, ApiEvent.ApiIds apiId, long durationInUs)
	{
		ServiceBundle.PlatformProxy.OtelInstrumentation.LogSuccessMetrics(ServiceBundle.PlatformProxy.GetProductName(), apiId, GetCacheLevel(authenticationResult), durationInUs, authenticationResult.AuthenticationResultMetadata, AuthenticationRequestParameters.RequestContext.Logger);
	}

	private void LogFailureTelemetryToOtel(string errorCodeToLog, ApiEvent.ApiIds apiId, CacheRefreshReason cacheRefreshReason)
	{
		ServiceBundle.PlatformProxy.OtelInstrumentation.LogFailureMetrics(ServiceBundle.PlatformProxy.GetProductName(), errorCodeToLog, apiId, cacheRefreshReason);
	}

	private static void LogMsalErrorTelemetryToClient(Exception ex, MsalTelemetryEventDetails telemetryEventDetails, ITelemetryClient[] telemetryClients)
	{
		if (telemetryClients.HasEnabledClients("acquire_token"))
		{
			telemetryEventDetails.SetProperty("Succeeded", value: false);
			telemetryEventDetails.SetProperty("ErrorMessage", ex.Message);
			if (ex is MsalClientException ex2)
			{
				telemetryEventDetails.SetProperty("ErrorCode", ex2.ErrorCode);
			}
			else if (ex is MsalServiceException ex3)
			{
				telemetryEventDetails.SetProperty("ErrorCode", ex3.ErrorCode);
				telemetryEventDetails.SetProperty("StsErrorCode", ex3.ErrorCodes?.FirstOrDefault());
			}
			else
			{
				telemetryEventDetails.SetProperty("ErrorCode", ex.GetType().ToString());
			}
		}
	}

	private void LogSuccessfulTelemetryToClient(AuthenticationResult authenticationResult, MsalTelemetryEventDetails telemetryEventDetails, ITelemetryClient[] telemetryClients)
	{
		if (telemetryClients.HasEnabledClients("acquire_token"))
		{
			telemetryEventDetails.SetProperty("CacheInfoTelemetry", Convert.ToInt64(authenticationResult.AuthenticationResultMetadata.CacheRefreshReason));
			telemetryEventDetails.SetProperty("TokenSource", Convert.ToInt64(authenticationResult.AuthenticationResultMetadata.TokenSource));
			telemetryEventDetails.SetProperty("Duration", authenticationResult.AuthenticationResultMetadata.DurationTotalInMs);
			telemetryEventDetails.SetProperty("DurationInCache", authenticationResult.AuthenticationResultMetadata.DurationInCacheInMs);
			telemetryEventDetails.SetProperty("DurationInHttp", authenticationResult.AuthenticationResultMetadata.DurationInHttpInMs);
			telemetryEventDetails.SetProperty("Succeeded", value: true);
			telemetryEventDetails.SetProperty("TokenType", (long)AuthenticationRequestParameters.RequestContext.ApiEvent.TokenType.Value);
			telemetryEventDetails.SetProperty("RemainingLifetime", (authenticationResult.ExpiresOn - DateTime.Now).TotalMilliseconds);
			telemetryEventDetails.SetProperty("ActivityId", authenticationResult.CorrelationId);
			if (authenticationResult.AuthenticationResultMetadata.RefreshOn.HasValue)
			{
				telemetryEventDetails.SetProperty("RefreshOn", authenticationResult.AuthenticationResultMetadata.RefreshOn.Value.ToUnixTimeMilliseconds());
			}
			telemetryEventDetails.SetProperty("AssertionType", (long)AuthenticationRequestParameters.RequestContext.ApiEvent.AssertionType);
			telemetryEventDetails.SetProperty("Endpoint", AuthenticationRequestParameters.Authority.AuthorityInfo.CanonicalAuthority.ToString());
			telemetryEventDetails.SetProperty("CacheLevel", (long)authenticationResult.AuthenticationResultMetadata.CacheLevel);
			Tuple<string, string> tuple = ParseScopesForTelemetry();
			if (tuple.Item1 != null)
			{
				telemetryEventDetails.SetProperty("Resource", tuple.Item1);
			}
			if (tuple.Item2 != null)
			{
				telemetryEventDetails.SetProperty("Scopes", tuple.Item2);
			}
		}
	}

	private Tuple<string, string> ParseScopesForTelemetry()
	{
		string item = null;
		string item2 = null;
		if (AuthenticationRequestParameters.Scope.Count > 0)
		{
			string uriString = AuthenticationRequestParameters.Scope.First();
			if (Uri.IsWellFormedUriString(uriString, UriKind.Absolute))
			{
				Uri uri = new Uri(uriString);
				item = uri.Scheme + "://" + uri.Host;
				StringBuilder stringBuilder = new StringBuilder();
				foreach (string item3 in AuthenticationRequestParameters.Scope)
				{
					string[] array = item3.Split(new string[1] { uri.Host }, StringSplitOptions.None);
					string value = ((array.Length > 1) ? (array[1].TrimStart('/') + " ") : array.FirstOrDefault());
					stringBuilder.Append(value);
				}
				item2 = stringBuilder.ToString().TrimEnd(' ');
			}
			else
			{
				item2 = AuthenticationRequestParameters.Scope.AsSingleString();
			}
		}
		return new Tuple<string, string>(item, item2);
	}

	private CacheLevel GetCacheLevel(AuthenticationResult authenticationResult)
	{
		if (authenticationResult.AuthenticationResultMetadata.TokenSource == TokenSource.Cache)
		{
			if (AuthenticationRequestParameters.RequestContext.ApiEvent.CacheLevel > CacheLevel.Unknown)
			{
				return AuthenticationRequestParameters.RequestContext.ApiEvent.CacheLevel;
			}
			return CacheLevel.Unknown;
		}
		return CacheLevel.None;
	}

	private static void LogMetricsFromAuthResult(AuthenticationResult authenticationResult, ILoggerAdapter logger)
	{
		if (logger.IsLoggingEnabled(LogLevel.Always))
		{
			AuthenticationResultMetadata authenticationResultMetadata = authenticationResult.AuthenticationResultMetadata;
			logger.Always($"\r\n[LogMetricsFromAuthResult] Cache Refresh Reason: {authenticationResultMetadata.CacheRefreshReason}\r\n[LogMetricsFromAuthResult] DurationInCacheInMs: {authenticationResultMetadata.DurationInCacheInMs}\r\n[LogMetricsFromAuthResult] DurationTotalInMs: {authenticationResultMetadata.DurationTotalInMs}\r\n[LogMetricsFromAuthResult] DurationInHttpInMs: {authenticationResultMetadata.DurationInHttpInMs}");
			logger.AlwaysPii("[LogMetricsFromAuthResult] TokenEndpoint: " + authenticationResultMetadata.TokenEndpoint, "TokenEndpoint: ****");
		}
	}

	private void UpdateTelemetry(long elapsedMilliseconds, ApiEvent apiEvent, AuthenticationResult authenticationResult)
	{
		authenticationResult.AuthenticationResultMetadata.DurationTotalInMs = elapsedMilliseconds;
		authenticationResult.AuthenticationResultMetadata.DurationInHttpInMs = apiEvent.DurationInHttpInMs;
		authenticationResult.AuthenticationResultMetadata.DurationInCacheInMs = apiEvent.DurationInCacheInMs;
		authenticationResult.AuthenticationResultMetadata.TokenEndpoint = apiEvent.TokenEndpoint;
		authenticationResult.AuthenticationResultMetadata.CacheRefreshReason = apiEvent.CacheInfo;
		authenticationResult.AuthenticationResultMetadata.CacheLevel = GetCacheLevel(authenticationResult);
		authenticationResult.AuthenticationResultMetadata.Telemetry = apiEvent.MsalRuntimeTelemetry;
		authenticationResult.AuthenticationResultMetadata.RegionDetails = CreateRegionDetails(apiEvent);
		Metrics.IncrementTotalDurationInMs(authenticationResult.AuthenticationResultMetadata.DurationTotalInMs);
	}

	protected virtual void EnrichTelemetryApiEvent(ApiEvent apiEvent)
	{
	}

	private ApiEvent InitializeApiEvent(string accountId)
	{
		ApiEvent apiEvent = new ApiEvent(AuthenticationRequestParameters.RequestContext.CorrelationId)
		{
			ApiId = AuthenticationRequestParameters.ApiId
		};
		apiEvent.IsTokenCacheSerialized = AuthenticationRequestParameters.CacheSessionManager.TokenCacheInternal.IsAppSubscribedToSerializationEvents();
		apiEvent.IsLegacyCacheEnabled = AuthenticationRequestParameters.RequestContext.ServiceBundle.Config.LegacyCacheCompatibilityEnabled;
		apiEvent.CacheInfo = CacheRefreshReason.NotApplicable;
		apiEvent.TokenType = AuthenticationRequestParameters.AuthenticationScheme.TelemetryTokenType;
		apiEvent.AssertionType = GetAssertionType();
		EnrichTelemetryApiEvent(apiEvent);
		return apiEvent;
	}

	private AssertionType GetAssertionType()
	{
		if (ServiceBundle.Config.IsManagedIdentity || ServiceBundle.Config.AppTokenProvider != null)
		{
			return AssertionType.ManagedIdentity;
		}
		if (ServiceBundle.Config.ClientCredential != null)
		{
			if (ServiceBundle.Config.ClientCredential.AssertionType == AssertionType.CertificateWithoutSni)
			{
				if (ServiceBundle.Config.SendX5C)
				{
					return AssertionType.CertificateWithSni;
				}
				return AssertionType.CertificateWithoutSni;
			}
			return ServiceBundle.Config.ClientCredential.AssertionType;
		}
		return AssertionType.None;
	}

	protected async Task<AuthenticationResult> CacheTokenResponseAndCreateAuthenticationResultAsync(MsalTokenResponse msalTokenResponse)
	{
		AuthenticationRequestParameters.RequestContext.Logger.Info("Checking client info returned from the server..");
		ClientInfo fromServer = null;
		if (!AuthenticationRequestParameters.IsClientCredentialRequest && AuthenticationRequestParameters.ApiId != ApiEvent.ApiIds.AcquireTokenForSystemAssignedManagedIdentity && AuthenticationRequestParameters.ApiId != ApiEvent.ApiIds.AcquireTokenForUserAssignedManagedIdentity && AuthenticationRequestParameters.ApiId != ApiEvent.ApiIds.AcquireTokenByRefreshToken && AuthenticationRequestParameters.AuthorityInfo.AuthorityType != AuthorityType.Adfs && msalTokenResponse.ClientInfo != null)
		{
			fromServer = ClientInfo.CreateFromJson(msalTokenResponse.ClientInfo);
		}
		ValidateAccountIdentifiers(fromServer);
		AuthenticationRequestParameters.RequestContext.Logger.Info("Saving token response to cache..");
		Tuple<MsalAccessTokenCacheItem, MsalIdTokenCacheItem, Account> obj = await CacheManager.SaveTokenResponseAsync(msalTokenResponse).ConfigureAwait(continueOnCapturedContext: false);
		MsalAccessTokenCacheItem item = obj.Item1;
		MsalIdTokenCacheItem item2 = obj.Item2;
		Account item3 = obj.Item3;
		return new AuthenticationResult(item, item2, AuthenticationRequestParameters.AuthenticationScheme, AuthenticationRequestParameters.RequestContext.CorrelationId, msalTokenResponse.TokenSource, AuthenticationRequestParameters.RequestContext.ApiEvent, item3, msalTokenResponse.SpaAuthCode, msalTokenResponse.CreateExtensionDataStringMap());
	}

	protected virtual void ValidateAccountIdentifiers(ClientInfo fromServer)
	{
	}

	protected Task ResolveAuthorityAsync()
	{
		return AuthenticationRequestParameters.AuthorityManager.RunInstanceDiscoveryAndValidationAsync();
	}

	internal async Task<MsalTokenResponse> SendTokenRequestAsync(IDictionary<string, string> additionalBodyParameters, CancellationToken cancellationToken)
	{
		MsalTokenResponse result = await SendTokenRequestAsync(await AuthenticationRequestParameters.Authority.GetTokenEndpointAsync(AuthenticationRequestParameters.RequestContext).ConfigureAwait(continueOnCapturedContext: false), additionalBodyParameters, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		Metrics.IncrementTotalAccessTokensFromIdP();
		return result;
	}

	protected Task<MsalTokenResponse> SendTokenRequestAsync(string tokenEndpoint, IDictionary<string, string> additionalBodyParameters, CancellationToken cancellationToken)
	{
		string scopeOverride = GetOverriddenScopes(AuthenticationRequestParameters.Scope).AsSingleString();
		TokenClient tokenClient = new TokenClient(AuthenticationRequestParameters);
		KeyValuePair<string, string>? ccsHeader = GetCcsHeader(additionalBodyParameters);
		if (ccsHeader.HasValue && !string.IsNullOrEmpty(ccsHeader.Value.Key))
		{
			tokenClient.AddHeaderToClient(ccsHeader.Value.Key, ccsHeader.Value.Value);
		}
		InjectPcaSsoPolicyHeader(tokenClient);
		return tokenClient.SendTokenRequestAsync(additionalBodyParameters, scopeOverride, tokenEndpoint, cancellationToken);
	}

	private void InjectPcaSsoPolicyHeader(TokenClient tokenClient)
	{
		if (!ServiceBundle.Config.IsPublicClient || !ServiceBundle.Config.IsWebviewSsoPolicyEnabled)
		{
			return;
		}
		foreach (KeyValuePair<string, string> ssoPolicyHeader in ServiceBundle.Config.BrokerCreatorFunc(null, ServiceBundle.Config, AuthenticationRequestParameters.RequestContext.Logger).GetSsoPolicyHeaders())
		{
			tokenClient.AddHeaderToClient(ssoPolicyHeader.Key, ssoPolicyHeader.Value);
		}
	}

	protected virtual KeyValuePair<string, string>? GetCcsHeader(IDictionary<string, string> additionalBodyParameters)
	{
		if (AuthenticationRequestParameters?.Account?.HomeAccountId != null)
		{
			if (!string.IsNullOrEmpty(AuthenticationRequestParameters.Account.HomeAccountId.Identifier))
			{
				string objectId = AuthenticationRequestParameters.Account.HomeAccountId.ObjectId;
				string tenantId = AuthenticationRequestParameters.Account.HomeAccountId.TenantId;
				string ccsClientInfoHint = CoreHelpers.GetCcsClientInfoHint(objectId, tenantId);
				return new KeyValuePair<string, string>("x-anchormailbox", ccsClientInfoHint);
			}
			if (!string.IsNullOrEmpty(AuthenticationRequestParameters.Account.Username))
			{
				return GetCcsUpnHeader(AuthenticationRequestParameters.Account.Username);
			}
		}
		if (additionalBodyParameters.TryGetValue("username", out var value))
		{
			return GetCcsUpnHeader(value);
		}
		if (!string.IsNullOrEmpty(AuthenticationRequestParameters.LoginHint))
		{
			return GetCcsUpnHeader(AuthenticationRequestParameters.LoginHint);
		}
		return null;
	}

	protected KeyValuePair<string, string>? GetCcsUpnHeader(string upnHeader)
	{
		if (AuthenticationRequestParameters.Authority.AuthorityInfo.AuthorityType == AuthorityType.B2C)
		{
			return null;
		}
		string ccsUpnHint = CoreHelpers.GetCcsUpnHint(upnHeader);
		return new KeyValuePair<string, string>("x-anchormailbox", ccsUpnHint);
	}

	private void LogRequestStarted(AuthenticationRequestParameters authenticationRequestParameters)
	{
		if (authenticationRequestParameters.RequestContext.Logger.IsLoggingEnabled(LogLevel.Info))
		{
			string text = authenticationRequestParameters.Scope.AsSingleString();
			string name = GetType().Name;
			string messageWithPii = $"=== Token Acquisition ({name}) started:\n\tAuthority: {authenticationRequestParameters.AuthorityInfo?.CanonicalAuthority}\n\tScope: {text}\n\tClientId: {authenticationRequestParameters.AppConfig.ClientId}\n\t";
			string text2 = "=== Token Acquisition (" + name + ") started:\n\t Scopes: " + text;
			if (authenticationRequestParameters.AuthorityInfo != null && KnownMetadataProvider.IsKnownEnvironment(authenticationRequestParameters.AuthorityInfo?.Host))
			{
				text2 = text2 + "\n\tAuthority Host: " + authenticationRequestParameters.AuthorityInfo?.Host;
			}
			authenticationRequestParameters.RequestContext.Logger.InfoPii(messageWithPii, text2);
		}
		if (authenticationRequestParameters.AppConfig.IsConfidentialClient && !authenticationRequestParameters.IsClientCredentialRequest && !CacheManager.TokenCacheInternal.IsAppSubscribedToSerializationEvents())
		{
			authenticationRequestParameters.RequestContext.Logger.Warning("Only in-memory caching is used. The cache is not persisted and will be lost if the machine is restarted. It also does not scale for a web app or web API, where the number of users can grow large. In production, web apps and web APIs should use distributed caching like Redis. See https://aka.ms/msal-net-cca-token-cache-serialization");
		}
	}

	private void LogReturnedToken(AuthenticationResult result)
	{
		if (result.AccessToken == null || !AuthenticationRequestParameters.RequestContext.Logger.IsLoggingEnabled(LogLevel.Info))
		{
			return;
		}
		string scopes = string.Join(" ", result.Scopes);
		AuthenticationRequestParameters.RequestContext.Logger.Info("\n\t=== Token Acquisition finished successfully:");
		AuthenticationRequestParameters.RequestContext.Logger.InfoPii(() => $" AT expiration time: {result.ExpiresOn}, scopes: {scopes}. source: {result.AuthenticationResultMetadata.TokenSource}", () => $" AT expiration time: {result.ExpiresOn}, scopes: {scopes}. source: {result.AuthenticationResultMetadata.TokenSource}");
		if (result.AuthenticationResultMetadata.TokenSource != TokenSource.Cache)
		{
			Uri canonicalAuthority = AuthenticationRequestParameters.AuthorityInfo.CanonicalAuthority;
			AuthenticationRequestParameters.RequestContext.Logger.InfoPii(() => $"Fetched access token from host {canonicalAuthority.Host}. Endpoint: {canonicalAuthority}. ", () => "Fetched access token from host " + canonicalAuthority.Host + ". ");
		}
	}

	internal async Task<AuthenticationResult> HandleTokenRefreshErrorAsync(MsalServiceException e, MsalAccessTokenCacheItem cachedAccessTokenItem)
	{
		ILoggerAdapter logger = AuthenticationRequestParameters.RequestContext.Logger;
		logger.Warning($"Fetching a new AT failed. Is exception retry-able? {e.IsRetryable}. Is there an AT in the cache that is usable? {cachedAccessTokenItem != null}");
		if (cachedAccessTokenItem != null && e.IsRetryable)
		{
			logger.Info("Returning existing access token. It is not expired, but should be refreshed. ");
			MsalIdTokenCacheItem idToken = await CacheManager.GetIdTokenCacheItemAsync(cachedAccessTokenItem).ConfigureAwait(continueOnCapturedContext: false);
			Account account = await CacheManager.GetAccountAssociatedWithAccessTokenAsync(cachedAccessTokenItem).ConfigureAwait(continueOnCapturedContext: false);
			return new AuthenticationResult(cachedAccessTokenItem, idToken, AuthenticationRequestParameters.AuthenticationScheme, AuthenticationRequestParameters.RequestContext.CorrelationId, TokenSource.Cache, AuthenticationRequestParameters.RequestContext.ApiEvent, account, null, null);
		}
		logger.Warning("Either the exception does not indicate a problem with AAD or the token cache does not have an AT that is usable. ");
		throw e;
	}

	private static RegionDetails CreateRegionDetails(ApiEvent apiEvent)
	{
		return new RegionDetails(apiEvent.RegionOutcome, apiEvent.RegionUsed, apiEvent.RegionDiscoveryFailureReason);
	}
}
