using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.AppConfig;
using Microsoft.Identity.Client.AuthScheme;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Extensibility;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Internal.Requests;

internal class AuthenticationRequestParameters
{
	private readonly IServiceBundle _serviceBundle;

	private readonly AcquireTokenCommonParameters _commonParameters;

	private string _loginHint;

	public ApplicationConfiguration AppConfig => _serviceBundle.Config;

	public ApiEvent.ApiIds ApiId => _commonParameters.ApiId;

	public RequestContext RequestContext { get; }

	public AuthorityManager AuthorityManager { get; set; }

	public Authority Authority => AuthorityManager.Authority;

	public AuthorityInfo AuthorityInfo => AuthorityManager.Authority.AuthorityInfo;

	public AuthorityInfo AuthorityOverride => _commonParameters.AuthorityOverride;

	public ICacheSessionManager CacheSessionManager { get; }

	public HashSet<string> Scope { get; }

	public Uri RedirectUri { get; set; }

	public IDictionary<string, string> ExtraQueryParameters { get; }

	public string ClaimsAndClientCapabilities { get; private set; }

	public Guid CorrelationId => _commonParameters.CorrelationId;

	public string Claims => _commonParameters.Claims;

	public IAuthenticationScheme AuthenticationScheme => _commonParameters.AuthenticationScheme;

	public bool SendX5C { get; set; }

	public string LoginHint
	{
		get
		{
			if (string.IsNullOrEmpty(_loginHint) && Account != null)
			{
				return Account.Username;
			}
			return _loginHint;
		}
		set
		{
			_loginHint = value;
		}
	}

	public IAccount Account { get; set; }

	public string HomeAccountId { get; }

	public Func<OnBeforeTokenRequestData, Task> OnBeforeTokenRequestHandler => _commonParameters.OnBeforeTokenRequestHandler;

	public IDictionary<string, string> ExtraHttpHeaders => _commonParameters.ExtraHttpHeaders;

	public bool IsClientCredentialRequest => ApiId == ApiEvent.ApiIds.AcquireTokenForClient;

	public PoPAuthenticationConfiguration PopAuthenticationConfiguration => _commonParameters.PopAuthenticationConfiguration;

	public UserAssertion UserAssertion { get; set; }

	public string LongRunningOboCacheKey { get; set; }

	public KeyValuePair<string, string>? CcsRoutingHint { get; set; }

	public AuthenticationRequestParameters(IServiceBundle serviceBundle, ITokenCacheInternal tokenCache, AcquireTokenCommonParameters commonParameters, RequestContext requestContext, Authority initialAuthority, string homeAccountId = null)
	{
		_serviceBundle = serviceBundle;
		_commonParameters = commonParameters;
		RequestContext = requestContext;
		CacheSessionManager = new CacheSessionManager(tokenCache, this);
		Scope = ScopeHelper.CreateScopeSet(commonParameters.Scopes);
		RedirectUri = new Uri(serviceBundle.Config.RedirectUri);
		AuthorityManager = new AuthorityManager(RequestContext, initialAuthority);
		ExtraQueryParameters = serviceBundle.Config.ExtraQueryParameters ?? new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
		if (commonParameters.ExtraQueryParameters != null)
		{
			foreach (KeyValuePair<string, string> extraQueryParameter in commonParameters.ExtraQueryParameters)
			{
				ExtraQueryParameters[extraQueryParameter.Key] = extraQueryParameter.Value;
			}
		}
		ClaimsAndClientCapabilities = ClaimsHelper.GetMergedClaimsAndClientCapabilities(_commonParameters.Claims, _serviceBundle.Config.ClientCapabilities);
		HomeAccountId = homeAccountId;
	}

	public void LogParameters()
	{
		ILoggerAdapter logger = RequestContext.Logger;
		if (logger.IsLoggingEnabled(LogLevel.Info))
		{
			StringBuilder stringBuilder = new StringBuilder(Environment.NewLine + "=== Request Data ===" + Environment.NewLine + "Authority Provided? - " + (Authority != null) + Environment.NewLine);
			stringBuilder.AppendLine("Client Id - " + AppConfig.ClientId);
			stringBuilder.AppendLine("Scopes - " + Scope?.AsSingleString());
			stringBuilder.AppendLine("Redirect Uri - " + RedirectUri?.OriginalString);
			stringBuilder.AppendLine("Extra Query Params Keys (space separated) - " + ExtraQueryParameters.Keys.AsSingleString());
			stringBuilder.AppendLine("ClaimsAndClientCapabilities - " + ClaimsAndClientCapabilities);
			stringBuilder.AppendLine("Authority - " + AuthorityInfo?.CanonicalAuthority);
			stringBuilder.AppendLine("ApiId - " + ApiId);
			stringBuilder.AppendLine("IsConfidentialClient - " + AppConfig.IsConfidentialClient);
			stringBuilder.AppendLine("SendX5C - " + SendX5C);
			stringBuilder.AppendLine("LoginHint - " + LoginHint);
			stringBuilder.AppendLine("IsBrokerConfigured - " + AppConfig.IsBrokerEnabled);
			stringBuilder.AppendLine("HomeAccountId - " + HomeAccountId);
			stringBuilder.AppendLine("CorrelationId - " + CorrelationId);
			stringBuilder.AppendLine("UserAssertion set: " + (UserAssertion != null));
			stringBuilder.AppendLine("LongRunningOboCacheKey set: " + !string.IsNullOrWhiteSpace(LongRunningOboCacheKey));
			stringBuilder.AppendLine("Region configured: " + AppConfig.AzureRegion);
			string messageWithPii = stringBuilder.ToString();
			stringBuilder = new StringBuilder(Environment.NewLine + "=== Request Data ===" + Environment.NewLine + "Authority Provided? - " + (Authority != null) + Environment.NewLine);
			stringBuilder.AppendLine("Scopes - " + Scope?.AsSingleString());
			stringBuilder.AppendLine("Extra Query Params Keys (space separated) - " + ExtraQueryParameters.Keys.AsSingleString());
			stringBuilder.AppendLine("ApiId - " + ApiId);
			stringBuilder.AppendLine("IsConfidentialClient - " + AppConfig.IsConfidentialClient);
			stringBuilder.AppendLine("SendX5C - " + SendX5C);
			stringBuilder.AppendLine("LoginHint ? " + !string.IsNullOrEmpty(LoginHint));
			stringBuilder.AppendLine("IsBrokerConfigured - " + AppConfig.IsBrokerEnabled);
			stringBuilder.AppendLine("HomeAccountId - " + !string.IsNullOrEmpty(HomeAccountId));
			stringBuilder.AppendLine("CorrelationId - " + CorrelationId);
			stringBuilder.AppendLine("UserAssertion set: " + (UserAssertion != null));
			stringBuilder.AppendLine("LongRunningOboCacheKey set: " + !string.IsNullOrWhiteSpace(LongRunningOboCacheKey));
			stringBuilder.AppendLine("Region configured: " + AppConfig.AzureRegion);
			logger.InfoPii(messageWithPii, stringBuilder.ToString());
		}
	}
}
