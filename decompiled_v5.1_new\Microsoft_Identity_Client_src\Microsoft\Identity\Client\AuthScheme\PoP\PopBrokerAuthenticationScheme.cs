using System.Collections.Generic;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.AuthScheme.PoP;

internal class PopBrokerAuthenticationScheme : IAuthenticationScheme
{
	public TokenType TelemetryTokenType => TokenType.Pop;

	public string AuthorizationHeaderPrefix => "PoP";

	public string KeyId => string.Empty;

	public string AccessTokenType => "pop";

	public string FormatAccessToken(MsalAccessTokenCacheItem msalAccessTokenCacheItem)
	{
		return msalAccessTokenCacheItem.Secret;
	}

	public IReadOnlyDictionary<string, string> GetTokenRequestParams()
	{
		return CollectionHelpers.GetEmptyDictionary<string, string>();
	}
}
