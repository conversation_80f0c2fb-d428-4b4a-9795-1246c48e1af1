using System;
using System.IO;

namespace Newtonsoft.Json.Bson;

internal sealed class AsyncBinaryReaderOwningReader : AsyncBinaryReader
{
	private readonly BinaryReader _reader;

	public AsyncBinaryReaderOwningReader(BinaryReader reader)
		: base(reader.BaseStream)
	{
		_reader = reader;
	}

	public override void Close()
	{
		_reader.Close();
	}

	protected override void Dispose(bool disposing)
	{
		((IDisposable)_reader).Dispose();
	}
}
