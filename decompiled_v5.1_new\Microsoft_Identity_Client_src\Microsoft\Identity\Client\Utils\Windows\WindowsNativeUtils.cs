using System;
using System.ComponentModel;
using System.Runtime.InteropServices;

namespace Microsoft.Identity.Client.Utils.Windows;

[Obsolete("This workaround for previous WAM broker implementation is not necessary with the improved broker.", true)]
[EditorBrowsable(EditorBrowsableState.Never)]
public static class WindowsNativeUtils
{
	private enum RpcAuthnLevel
	{
		Default,
		None,
		Connect,
		Call,
		Pkt,
		PktIntegrity,
		PktPrivacy
	}

	private enum RpcImpLevel
	{
		Default,
		Anonymous,
		Identify,
		Impersonate,
		Delegate
	}

	private enum EoAuthnCap
	{
		None = 0,
		MutualAuth = 1,
		StaticCloaking = 32,
		DynamicCloaking = 64,
		AnyAuthority = 128,
		MakeFullSIC = 256,
		Default = 2048,
		SecureRefs = 2,
		AccessControl = 4,
		AppID = 8,
		Dynamic = 16,
		RequireFullSIC = 512,
		AutoImpersonate = 1024,
		NoCustomMarshal = 8192,
		DisableAAA = 4096
	}

	public static bool IsElevatedUser()
	{
		return IsUserAnAdmin();
	}

	public static void InitializeProcessSecurity()
	{
		int num = CoInitializeSecurity(IntPtr.Zero, -1, IntPtr.Zero, IntPtr.Zero, RpcAuthnLevel.None, RpcImpLevel.Impersonate, IntPtr.Zero, EoAuthnCap.None, IntPtr.Zero);
		if (num != 0)
		{
			throw new MsalClientException("initialize_process_security_error", MsalErrorMessage.InitializeProcessSecurityError($"0x{num:x}"));
		}
	}

	[DllImport("shell32.dll")]
	[return: MarshalAs(UnmanagedType.Bool)]
	private static extern bool IsUserAnAdmin();

	[DllImport("ole32.dll")]
	private static extern int CoInitializeSecurity(IntPtr pVoid, int cAuthSvc, IntPtr asAuthSvc, IntPtr pReserved1, RpcAuthnLevel level, RpcImpLevel impers, IntPtr pAuthList, EoAuthnCap dwCapabilities, IntPtr pReserved3);
}
