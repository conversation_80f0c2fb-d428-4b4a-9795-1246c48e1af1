using System.Diagnostics.CodeAnalysis;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace System.ClientModel.Primitives;

[RequiresUnreferencedCode("The constructors of the type being deserialized are dynamically accessed and may be trimmed.")]
internal class JsonModelConverter : JsonConverter<IJsonModel<object>>
{
	public ModelReaderWriterOptions Options { get; }

	public JsonModelConverter()
		: this(ModelReaderWriterOptions.Json)
	{
	}

	public JsonModelConverter(ModelReaderWriterOptions options)
	{
		Options = options;
	}

	public override bool CanConvert(Type typeToConvert)
	{
		return !Attribute.IsDefined(typeToConvert, typeof(JsonConverterAttribute));
	}

	public override IJsonModel<object> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
	{
		using JsonDocument jsonDocument = JsonDocument.ParseValue(ref reader);
		return (IJsonModel<object>)ModelReaderWriter.Read(BinaryData.FromString(jsonDocument.RootElement.GetRawText()), typeToConvert, Options);
	}

	public override void Write(Utf8JsonWriter writer, IJsonModel<object> value, JsonSerializerOptions options)
	{
		value.Write(writer, Options);
	}
}
