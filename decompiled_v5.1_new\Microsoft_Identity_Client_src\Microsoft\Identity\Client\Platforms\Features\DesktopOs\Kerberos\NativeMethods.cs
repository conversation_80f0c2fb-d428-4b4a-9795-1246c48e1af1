using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.InteropServices;

namespace Microsoft.Identity.Client.Platforms.Features.DesktopOs.Kerberos;

internal class NativeMethods
{
	public struct KERB_INTERACTIVE_LOGON
	{
		public KERB_LOGON_SUBMIT_TYPE MessageType;

		public UNICODE_STRING LogonDomainName;

		public UNICODE_STRING UserName;

		public UNICODE_STRING Password;
	}

	public struct TOKEN_SOURCE
	{
		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
		public byte[] SourceName;

		public LUID SourceIdentifier;
	}

	public struct KERB_S4U_LOGON
	{
		public KERB_LOGON_SUBMIT_TYPE MessageType;

		public S4uFlags Flags;

		public UNICODE_STRING ClientUpn;

		public UNICODE_STRING ClientRealm;
	}

	public struct UNICODE_STRING
	{
		public ushort Length;

		public ushort MaximumLength;

		public IntPtr Buffer;
	}

	[Flags]
	public enum S4uFlags
	{
		KERB_S4U_LOGON_FLAG_CHECK_LOGONHOURS = 2,
		KERB_S4U_LOGON_FLAG_IDENTIFY = 8
	}

	public enum KERB_LOGON_SUBMIT_TYPE
	{
		KerbInteractiveLogon = 2,
		KerbSmartCardLogon = 6,
		KerbWorkstationUnlockLogon = 7,
		KerbSmartCardUnlockLogon = 8,
		KerbProxyLogon = 9,
		KerbTicketLogon = 10,
		KerbTicketUnlockLogon = 11,
		KerbS4ULogon = 12,
		KerbCertificateLogon = 13,
		KerbCertificateS4ULogon = 14,
		KerbCertificateUnlockLogon = 15,
		KerbNoElevationLogon = 83,
		KerbLuidLogon = 84
	}

	public enum SECURITY_LOGON_TYPE
	{
		UndefinedLogonType = 0,
		Interactive = 2,
		Network = 3,
		Batch = 4,
		Service = 5,
		Proxy = 6,
		Unlock = 7,
		NetworkCleartext = 8,
		NewCredentials = 9,
		RemoteInteractive = 10,
		CachedInteractive = 11,
		CachedRemoteInteractive = 12,
		CachedUnlock = 13
	}

	internal struct LSA_STRING
	{
		public ushort Length;

		public ushort MaximumLength;

		public string Buffer;
	}

	internal struct LUID
	{
		public uint LowPart;

		public int HighPart;

		public static implicit operator ulong(LUID luid)
		{
			return (ulong)(((long)luid.HighPart << 32) + luid.LowPart);
		}

		public static implicit operator LUID(long luid)
		{
			return new LUID
			{
				LowPart = (uint)(luid & 0xFFFFFFFFu),
				HighPart = (int)(luid >> 32)
			};
		}
	}

	public struct KERB_SUBMIT_TKT_REQUEST
	{
		public KERB_PROTOCOL_MESSAGE_TYPE MessageType;

		public LUID LogonId;

		public int Flags;

		public KERB_CRYPTO_KEY32 Key;

		public int KerbCredSize;

		public int KerbCredOffset;
	}

	public struct KERB_PURGE_TKT_CACHE_EX_REQUEST
	{
		public KERB_PROTOCOL_MESSAGE_TYPE MessageType;

		public LUID LogonId;

		public int Flags;

		public KERB_TICKET_CACHE_INFO_EX TicketTemplate;
	}

	public struct KERB_TICKET_CACHE_INFO_EX
	{
		public UNICODE_STRING ClientName;

		public UNICODE_STRING ClientRealm;

		public UNICODE_STRING ServerName;

		public UNICODE_STRING ServerRealm;

		public long StartTime;

		public long EndTime;

		public long RenewTime;

		public int EncryptionType;

		public int TicketFlags;
	}

	public enum KERB_PROTOCOL_MESSAGE_TYPE : uint
	{
		KerbDebugRequestMessage,
		KerbQueryTicketCacheMessage,
		KerbChangeMachinePasswordMessage,
		KerbVerifyPacMessage,
		KerbRetrieveTicketMessage,
		KerbUpdateAddressesMessage,
		KerbPurgeTicketCacheMessage,
		KerbChangePasswordMessage,
		KerbRetrieveEncodedTicketMessage,
		KerbDecryptDataMessage,
		KerbAddBindingCacheEntryMessage,
		KerbSetPasswordMessage,
		KerbSetPasswordExMessage,
		KerbVerifyCredentialsMessage,
		KerbQueryTicketCacheExMessage,
		KerbPurgeTicketCacheExMessage,
		KerbRefreshSmartcardCredentialsMessage,
		KerbAddExtraCredentialsMessage,
		KerbQuerySupplementalCredentialsMessage,
		KerbTransferCredentialsMessage,
		KerbQueryTicketCacheEx2Message,
		KerbSubmitTicketMessage,
		KerbAddExtraCredentialsExMessage,
		KerbQueryKdcProxyCacheMessage,
		KerbPurgeKdcProxyCacheMessage,
		KerbQueryTicketCacheEx3Message,
		KerbCleanupMachinePkinitCredsMessage,
		KerbAddBindingCacheEntryExMessage,
		KerbQueryBindingCacheMessage,
		KerbPurgeBindingCacheMessage,
		KerbPinKdcMessage,
		KerbUnpinAllKdcsMessage,
		KerbQueryDomainExtendedPoliciesMessage,
		KerbQueryS4U2ProxyCacheMessage,
		KerbRetrieveKeyTabMessage,
		KerbRefreshPolicyMessage
	}

	public struct KERB_CRYPTO_KEY32
	{
		public int KeyType;

		public int Length;

		public int Offset;
	}

	internal enum SecBufferType
	{
		SECBUFFER_VERSION,
		SECBUFFER_DATA,
		SECBUFFER_TOKEN
	}

	internal struct SECURITY_HANDLE
	{
		public ulong dwLower;

		public ulong dwUpper;

		public bool IsSet
		{
			get
			{
				if (dwLower == 0)
				{
					return dwUpper != 0;
				}
				return true;
			}
		}
	}

	internal struct SECURITY_INTEGER
	{
		public uint LowPart;

		public int HighPart;
	}

	internal struct SecPkgContext_SecString
	{
		public unsafe void* sValue;
	}

	internal struct SecBuffer
	{
		public int cbBuffer;

		public SecBufferType BufferType;

		public IntPtr pvBuffer;

		public SecBuffer(int bufferSize)
		{
			cbBuffer = bufferSize;
			BufferType = SecBufferType.SECBUFFER_TOKEN;
			pvBuffer = Marshal.AllocHGlobal(bufferSize);
		}

		public SecBuffer(byte[] secBufferBytes)
			: this(secBufferBytes.Length)
		{
			Marshal.Copy(secBufferBytes, 0, pvBuffer, cbBuffer);
		}

		public void Dispose()
		{
			if (pvBuffer != IntPtr.Zero)
			{
				Marshal.FreeHGlobal(pvBuffer);
				pvBuffer = IntPtr.Zero;
			}
		}
	}

	internal struct SecBufferDesc : IDisposable
	{
		private readonly SecBufferType ulVersion;

		public int cBuffers;

		public IntPtr pBuffers;

		public SecBufferDesc(int bufferSize)
			: this(new SecBuffer(bufferSize))
		{
		}

		public SecBufferDesc(byte[] secBufferBytes)
			: this(new SecBuffer(secBufferBytes))
		{
		}

		private SecBufferDesc(SecBuffer secBuffer)
		{
			ulVersion = SecBufferType.SECBUFFER_VERSION;
			cBuffers = 1;
			pBuffers = Marshal.AllocHGlobal(Marshal.SizeOf(secBuffer));
			Marshal.StructureToPtr(secBuffer, pBuffers, fDeleteOld: false);
		}

		public void Dispose()
		{
			if (pBuffers != IntPtr.Zero)
			{
				ForEachBuffer(delegate(SecBuffer thisSecBuffer)
				{
					thisSecBuffer.Dispose();
				});
				Marshal.FreeHGlobal(pBuffers);
				pBuffers = IntPtr.Zero;
			}
		}

		private void ForEachBuffer(Action<SecBuffer> onBuffer)
		{
			for (int i = 0; i < cBuffers; i++)
			{
				int offset = i * Marshal.SizeOf(typeof(SecBuffer));
				SecBuffer obj = (SecBuffer)Marshal.PtrToStructure(IntPtr.Add(pBuffers, offset), typeof(SecBuffer));
				onBuffer(obj);
			}
		}

		public byte[] ReadBytes()
		{
			if (cBuffers <= 0)
			{
				return Array.Empty<byte>();
			}
			int finalLen = 0;
			List<byte[]> bufferList = new List<byte[]>();
			ForEachBuffer(delegate(SecBuffer thisSecBuffer)
			{
				if (thisSecBuffer.cbBuffer > 0)
				{
					byte[] array2 = new byte[thisSecBuffer.cbBuffer];
					Marshal.Copy(thisSecBuffer.pvBuffer, array2, 0, thisSecBuffer.cbBuffer);
					bufferList.Add(array2);
					finalLen += thisSecBuffer.cbBuffer;
				}
			});
			byte[] array = new byte[finalLen];
			int num = 0;
			for (int num2 = 0; num2 < bufferList.Count; num2++)
			{
				bufferList[num2].CopyTo(array, num);
				num += bufferList[num2].Length - 1;
			}
			return array;
		}
	}

	private const string SECUR32 = "secur32.dll";

	private const string ADVAPI32 = "advapi32.dll";

	private const string KERNEL32 = "kernel32.dll";

	[DllImport("secur32.dll", BestFitMapping = false, CharSet = CharSet.Auto, EntryPoint = "InitializeSecurityContext", SetLastError = true, ThrowOnUnmappableChar = true)]
	internal static extern SecStatus InitializeSecurityContext_0(ref SECURITY_HANDLE phCredential, IntPtr phContext, string pszTargetName, InitContextFlag fContextReq, int Reserved1, int TargetDataRep, IntPtr pInput, int Reserved2, ref SECURITY_HANDLE phNewContext, ref SecBufferDesc pOutput, out InitContextFlag pfContextAttr, IntPtr ptsExpiry);

	[DllImport("secur32.dll", BestFitMapping = false, CharSet = CharSet.Auto, ThrowOnUnmappableChar = true)]
	internal unsafe static extern SecStatus AcquireCredentialsHandle(string pszPrincipal, string pszPackage, int fCredentialUse, IntPtr PAuthenticationID, void* pAuthData, IntPtr pGetKeyFn, IntPtr pvGetKeyArgument, ref SECURITY_HANDLE phCredential, IntPtr ptsExpiry);

	[DllImport("secur32.dll")]
	internal unsafe static extern uint FreeCredentialsHandle(SECURITY_HANDLE* handle);

	[DllImport("secur32.dll")]
	public unsafe static extern SecStatus DeleteSecurityContext(SECURITY_HANDLE* context);

	[DllImport("secur32.dll")]
	public static extern int LsaDeregisterLogonProcess(IntPtr LsaHandle);

	[DllImport("secur32.dll")]
	public static extern int LsaLookupAuthenticationPackage(LsaSafeHandle LsaHandle, ref LSA_STRING PackageName, out int AuthenticationPackage);

	[DllImport("secur32.dll")]
	public static extern int LsaConnectUntrusted(out LsaSafeHandle LsaHandle);

	[DllImport("secur32.dll")]
	public unsafe static extern int LsaCallAuthenticationPackage(LsaSafeHandle LsaHandle, int AuthenticationPackage, void* ProtocolSubmitBuffer, int SubmitBufferLength, out LsaBufferSafeHandle ProtocolReturnBuffer, out int ReturnBufferLength, out int ProtocolStatus);

	[DllImport("secur32.dll")]
	public static extern int LsaFreeReturnBuffer(IntPtr Buffer);

	[DllImport("advapi32.dll")]
	public static extern int LsaNtStatusToWinError(int Status);

	[DllImport("kernel32.dll")]
	public static extern bool CloseHandle(IntPtr hObject);

	[DllImport("advapi32.dll")]
	public static extern bool ImpersonateLoggedOnUser(LsaTokenSafeHandle hToken);

	[DllImport("advapi32.dll")]
	public static extern bool RevertToSelf();

	public static void LsaThrowIfError(int result)
	{
		if (result != 0)
		{
			result = LsaNtStatusToWinError(result);
			throw new Win32Exception(result);
		}
	}
}
