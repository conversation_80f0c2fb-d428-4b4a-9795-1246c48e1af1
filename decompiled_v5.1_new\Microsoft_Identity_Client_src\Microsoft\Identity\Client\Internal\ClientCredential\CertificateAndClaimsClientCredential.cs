using System.Collections.Generic;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.TelemetryCore;

namespace Microsoft.Identity.Client.Internal.ClientCredential;

internal class CertificateAndClaimsClientCredential : IClientCredential
{
	private readonly IDictionary<string, string> _claimsToSign;

	private readonly bool _appendDefaultClaims;

	public X509Certificate2 Certificate { get; }

	public AssertionType AssertionType => AssertionType.CertificateWithoutSni;

	public CertificateAndClaimsClientCredential(X509Certificate2 certificate, IDictionary<string, string> claimsToSign, bool appendDefaultClaims)
	{
		Certificate = certificate;
		_claimsToSign = claimsToSign;
		_appendDefaultClaims = appendDefaultClaims;
	}

	public Task AddConfidentialClientParametersAsync(OAuth2Client oAuth2Client, ILoggerAdapter logger, ICryptographyManager cryptographyManager, string clientId, string tokenEndpoint, bool sendX5C, bool useSha2AndPss, CancellationToken cancellationToken)
	{
		string value = new JsonWebToken(cryptographyManager, clientId, tokenEndpoint, _claimsToSign, _appendDefaultClaims).Sign(Certificate, sendX5C, useSha2AndPss);
		oAuth2Client.AddBodyParameter("client_assertion_type", "urn:ietf:params:oauth:client-assertion-type:jwt-bearer");
		oAuth2Client.AddBodyParameter("client_assertion", value);
		return Task.CompletedTask;
	}
}
