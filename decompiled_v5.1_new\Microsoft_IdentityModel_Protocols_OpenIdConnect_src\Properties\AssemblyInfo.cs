using System;
using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;

[assembly: AssemblyInformationalVersion("6.35.0.41201040912.c94c7fc235501d478221e979062bd8837a55575b")]
[assembly: AssemblyFileVersion("6.35.0.41201")]
[assembly: AssemblyMetadata("Serviceable", "True")]
[assembly: CLSCompliant(true)]
[assembly: ComVisible(false)]
[assembly: AssemblyCompany("Microsoft Corporation.")]
[assembly: AssemblyConfiguration("Release")]
[assembly: AssemblyCopyright("© Microsoft Corporation. All rights reserved.")]
[assembly: AssemblyDescription("Includes types that provide support for OpenIdConnect protocol.")]
[assembly: AssemblyProduct("Microsoft IdentityModel")]
[assembly: AssemblyTitle("Microsoft.IdentityModel.Protocols.OpenIdConnect")]
[assembly: AssemblyMetadata("RepositoryUrl", "https://github.com/AzureAD/azure-activedirectory-identitymodel-extensions-for-dotnet")]
[assembly: AssemblyVersion("********")]
