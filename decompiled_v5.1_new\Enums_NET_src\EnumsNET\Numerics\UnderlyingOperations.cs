using System;
using System.Globalization;
using System.Runtime.InteropServices;

namespace EnumsNET.Numerics;

[StructLayout(LayoutKind.Sequential, Size = 1)]
internal struct UnderlyingOperations : IUnderlyingOperations<bool>, IUnderlyingOperations<byte>, IUnderlyingOperations<char>, IUnderlyingOperations<short>, IUnderlyingOperations<int>, IUnderlyingOperations<long>, IUnderlyingOperations<sbyte>, IUnderlyingOperations<ushort>, IUnderlyingOperations<uint>, IUnderlyingOperations<ulong>
{
	bool IUnderlyingOperations<bool>.One => true;

	byte IUnderlyingOperations<byte>.One => 1;

	char IUnderlyingOperations<char>.One => '\u0001';

	short IUnderlyingOperations<short>.One => 1;

	int IUnderlyingOperations<int>.One => 1;

	long IUnderlyingOperations<long>.One => 1L;

	sbyte IUnderlyingOperations<sbyte>.One => 1;

	ushort IUnderlyingOperations<ushort>.One => 1;

	uint IUnderlyingOperations<uint>.One => 1u;

	ulong IUnderlyingOperations<ulong>.One => 1uL;

	public bool And(bool left, bool right)
	{
		return left && right;
	}

	public byte And(byte left, byte right)
	{
		return (byte)(left & right);
	}

	public char And(char left, char right)
	{
		return (char)(left & right);
	}

	public short And(short left, short right)
	{
		return (short)(left & right);
	}

	public int And(int left, int right)
	{
		return left & right;
	}

	public long And(long left, long right)
	{
		return left & right;
	}

	public sbyte And(sbyte left, sbyte right)
	{
		return (sbyte)(left & right);
	}

	public ushort And(ushort left, ushort right)
	{
		return (ushort)(left & right);
	}

	public uint And(uint left, uint right)
	{
		return left & right;
	}

	public ulong And(ulong left, ulong right)
	{
		return left & right;
	}

	public int BitCount(bool value)
	{
		return Number.BitCount(Convert.ToByte(value));
	}

	public int BitCount(byte value)
	{
		return Number.BitCount(value);
	}

	public int BitCount(char value)
	{
		return Number.BitCount(value);
	}

	public int BitCount(short value)
	{
		return Number.BitCount(value);
	}

	public int BitCount(int value)
	{
		return Number.BitCount(value);
	}

	public int BitCount(long value)
	{
		return Number.BitCount(value);
	}

	public int BitCount(sbyte value)
	{
		return Number.BitCount(value);
	}

	public int BitCount(ushort value)
	{
		return Number.BitCount(value);
	}

	public int BitCount(uint value)
	{
		return Number.BitCount((int)value);
	}

	public int BitCount(ulong value)
	{
		return Number.BitCount((long)value);
	}

	bool IUnderlyingOperations<bool>.Create(long value)
	{
		return value != 0;
	}

	byte IUnderlyingOperations<byte>.Create(long value)
	{
		return (byte)value;
	}

	char IUnderlyingOperations<char>.Create(long value)
	{
		return (char)value;
	}

	short IUnderlyingOperations<short>.Create(long value)
	{
		return (short)value;
	}

	int IUnderlyingOperations<int>.Create(long value)
	{
		return (int)value;
	}

	long IUnderlyingOperations<long>.Create(long value)
	{
		return value;
	}

	sbyte IUnderlyingOperations<sbyte>.Create(long value)
	{
		return (sbyte)value;
	}

	ushort IUnderlyingOperations<ushort>.Create(long value)
	{
		return (ushort)value;
	}

	uint IUnderlyingOperations<uint>.Create(long value)
	{
		return (uint)value;
	}

	ulong IUnderlyingOperations<ulong>.Create(long value)
	{
		return (ulong)value;
	}

	public bool InRange(bool value, bool minValue, bool maxValue)
	{
		if (value != minValue)
		{
			return value == maxValue;
		}
		return true;
	}

	public bool InRange(byte value, byte minValue, byte maxValue)
	{
		return (uint)(value - minValue) <= (uint)(maxValue - minValue);
	}

	public bool InRange(char value, char minValue, char maxValue)
	{
		return (uint)(value - minValue) <= (uint)(maxValue - minValue);
	}

	public bool InRange(short value, short minValue, short maxValue)
	{
		return (uint)(value - minValue) <= (uint)(maxValue - minValue);
	}

	public bool InRange(int value, int minValue, int maxValue)
	{
		return (uint)(value - minValue) <= (uint)(maxValue - minValue);
	}

	public bool InRange(long value, long minValue, long maxValue)
	{
		return (ulong)(value - minValue) <= (ulong)(maxValue - minValue);
	}

	public bool InRange(sbyte value, sbyte minValue, sbyte maxValue)
	{
		return (uint)(value - minValue) <= (uint)(maxValue - minValue);
	}

	public bool InRange(ushort value, ushort minValue, ushort maxValue)
	{
		return (uint)(value - minValue) <= (uint)(maxValue - minValue);
	}

	public bool InRange(uint value, uint minValue, uint maxValue)
	{
		return value - minValue <= maxValue - minValue;
	}

	public bool InRange(ulong value, ulong minValue, ulong maxValue)
	{
		return value - minValue <= maxValue - minValue;
	}

	bool IUnderlyingOperations<bool>.IsInValueRange(ulong value)
	{
		return value <= 255;
	}

	bool IUnderlyingOperations<byte>.IsInValueRange(ulong value)
	{
		return value <= 255;
	}

	bool IUnderlyingOperations<char>.IsInValueRange(ulong value)
	{
		return value <= 65535;
	}

	bool IUnderlyingOperations<short>.IsInValueRange(ulong value)
	{
		return value <= 32767;
	}

	bool IUnderlyingOperations<int>.IsInValueRange(ulong value)
	{
		return value <= int.MaxValue;
	}

	bool IUnderlyingOperations<long>.IsInValueRange(ulong value)
	{
		return value <= long.MaxValue;
	}

	bool IUnderlyingOperations<sbyte>.IsInValueRange(ulong value)
	{
		return value <= 127;
	}

	bool IUnderlyingOperations<ushort>.IsInValueRange(ulong value)
	{
		return value <= 65535;
	}

	bool IUnderlyingOperations<uint>.IsInValueRange(ulong value)
	{
		return value <= uint.MaxValue;
	}

	bool IUnderlyingOperations<ulong>.IsInValueRange(ulong value)
	{
		return true;
	}

	bool IUnderlyingOperations<bool>.IsInValueRange(long value)
	{
		if (value >= 0)
		{
			return value <= 255;
		}
		return false;
	}

	bool IUnderlyingOperations<byte>.IsInValueRange(long value)
	{
		if (value >= 0)
		{
			return value <= 255;
		}
		return false;
	}

	bool IUnderlyingOperations<char>.IsInValueRange(long value)
	{
		if (value >= 0)
		{
			return value <= 65535;
		}
		return false;
	}

	bool IUnderlyingOperations<short>.IsInValueRange(long value)
	{
		if (value >= -32768)
		{
			return value <= 32767;
		}
		return false;
	}

	bool IUnderlyingOperations<int>.IsInValueRange(long value)
	{
		if (value >= int.MinValue)
		{
			return value <= int.MaxValue;
		}
		return false;
	}

	bool IUnderlyingOperations<long>.IsInValueRange(long value)
	{
		return true;
	}

	bool IUnderlyingOperations<sbyte>.IsInValueRange(long value)
	{
		if (value >= -128)
		{
			return value <= 127;
		}
		return false;
	}

	bool IUnderlyingOperations<ushort>.IsInValueRange(long value)
	{
		if (value >= 0)
		{
			return value <= 65535;
		}
		return false;
	}

	bool IUnderlyingOperations<uint>.IsInValueRange(long value)
	{
		if (value >= 0)
		{
			return value <= uint.MaxValue;
		}
		return false;
	}

	bool IUnderlyingOperations<ulong>.IsInValueRange(long value)
	{
		return value >= 0;
	}

	public bool LeftShift(bool value, int amount)
	{
		return !value && amount == 1;
	}

	public byte LeftShift(byte value, int amount)
	{
		return (byte)(value << amount);
	}

	public char LeftShift(char value, int amount)
	{
		return (char)((uint)value << amount);
	}

	public short LeftShift(short value, int amount)
	{
		return (short)(value << amount);
	}

	public int LeftShift(int value, int amount)
	{
		return value << amount;
	}

	public long LeftShift(long value, int amount)
	{
		return value << amount;
	}

	public sbyte LeftShift(sbyte value, int amount)
	{
		return (sbyte)(value << amount);
	}

	public ushort LeftShift(ushort value, int amount)
	{
		return (ushort)(value << amount);
	}

	public uint LeftShift(uint value, int amount)
	{
		return value << amount;
	}

	public ulong LeftShift(ulong value, int amount)
	{
		return value << amount;
	}

	public bool LessThan(bool left, bool right)
	{
		return !left && right;
	}

	public bool LessThan(byte left, byte right)
	{
		return left < right;
	}

	public bool LessThan(char left, char right)
	{
		return left < right;
	}

	public bool LessThan(short left, short right)
	{
		return left < right;
	}

	public bool LessThan(int left, int right)
	{
		return left < right;
	}

	public bool LessThan(long left, long right)
	{
		return left < right;
	}

	public bool LessThan(sbyte left, sbyte right)
	{
		return left < right;
	}

	public bool LessThan(ushort left, ushort right)
	{
		return left < right;
	}

	public bool LessThan(uint left, uint right)
	{
		return left < right;
	}

	public bool LessThan(ulong left, ulong right)
	{
		return left < right;
	}

	public bool Not(bool value)
	{
		return !value;
	}

	public byte Not(byte value)
	{
		return (byte)(~value);
	}

	public char Not(char value)
	{
		return (char)(~(uint)value);
	}

	public short Not(short value)
	{
		return (short)(~value);
	}

	public int Not(int value)
	{
		return ~value;
	}

	public long Not(long value)
	{
		return ~value;
	}

	public sbyte Not(sbyte value)
	{
		return (sbyte)(~value);
	}

	public ushort Not(ushort value)
	{
		return (ushort)(~value);
	}

	public uint Not(uint value)
	{
		return ~value;
	}

	public ulong Not(ulong value)
	{
		return ~value;
	}

	public bool Or(bool left, bool right)
	{
		return left || right;
	}

	public byte Or(byte left, byte right)
	{
		return (byte)(left | right);
	}

	public char Or(char left, char right)
	{
		return (char)(left | right);
	}

	public short Or(short left, short right)
	{
		return (short)(left | right);
	}

	public int Or(int left, int right)
	{
		return left | right;
	}

	public long Or(long left, long right)
	{
		return left | right;
	}

	public sbyte Or(sbyte left, sbyte right)
	{
		return (sbyte)(left | right);
	}

	public ushort Or(ushort left, ushort right)
	{
		return (ushort)(left | right);
	}

	public uint Or(uint left, uint right)
	{
		return left | right;
	}

	public ulong Or(ulong left, ulong right)
	{
		return left | right;
	}

	public bool Subtract(bool left, bool right)
	{
		return left ^ right;
	}

	public byte Subtract(byte left, byte right)
	{
		return (byte)(left - right);
	}

	public char Subtract(char left, char right)
	{
		return (char)(left - right);
	}

	public short Subtract(short left, short right)
	{
		return (short)(left - right);
	}

	public int Subtract(int left, int right)
	{
		return left - right;
	}

	public long Subtract(long left, long right)
	{
		return left - right;
	}

	public sbyte Subtract(sbyte left, sbyte right)
	{
		return (sbyte)(left - right);
	}

	public ushort Subtract(ushort left, ushort right)
	{
		return (ushort)(left - right);
	}

	public uint Subtract(uint left, uint right)
	{
		return left - right;
	}

	public ulong Subtract(ulong left, ulong right)
	{
		return left - right;
	}

	public string ToHexadecimalString(bool value)
	{
		return Convert.ToByte(value).ToString("X2");
	}

	public string ToHexadecimalString(byte value)
	{
		return value.ToString("X2");
	}

	public string ToHexadecimalString(char value)
	{
		ushort num = value;
		return num.ToString("X4");
	}

	public string ToHexadecimalString(short value)
	{
		return value.ToString("X4");
	}

	public string ToHexadecimalString(int value)
	{
		return value.ToString("X8");
	}

	public string ToHexadecimalString(long value)
	{
		return value.ToString("X16");
	}

	public string ToHexadecimalString(sbyte value)
	{
		return value.ToString("X2");
	}

	public string ToHexadecimalString(ushort value)
	{
		return value.ToString("X4");
	}

	public string ToHexadecimalString(uint value)
	{
		return value.ToString("X8");
	}

	public string ToHexadecimalString(ulong value)
	{
		return value.ToString("X16");
	}

	public string ToDecimalString(bool value)
	{
		return Convert.ToByte(value).ToString();
	}

	public string ToDecimalString(byte value)
	{
		return value.ToString();
	}

	public string ToDecimalString(char value)
	{
		ushort num = value;
		return num.ToString();
	}

	public string ToDecimalString(short value)
	{
		return value.ToString();
	}

	public string ToDecimalString(int value)
	{
		return value.ToString();
	}

	public string ToDecimalString(long value)
	{
		return value.ToString();
	}

	public string ToDecimalString(sbyte value)
	{
		return value.ToString();
	}

	public string ToDecimalString(ushort value)
	{
		return value.ToString();
	}

	public string ToDecimalString(uint value)
	{
		return value.ToString();
	}

	public string ToDecimalString(ulong value)
	{
		return value.ToString();
	}

	public bool TryFormat(bool value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryFormat(byte value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryFormat(char value, Span<char> destination, out int charsWritten)
	{
		if (destination.Length >= 1)
		{
			destination[0] = value;
			charsWritten = 1;
			return true;
		}
		charsWritten = 0;
		return false;
	}

	public bool TryFormat(short value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryFormat(int value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryFormat(long value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryFormat(sbyte value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryFormat(ushort value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryFormat(uint value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryFormat(ulong value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryToHexadecimalString(bool value, Span<char> destination, out int charsWritten)
	{
		return Convert.ToByte(value).TryFormat(destination, out charsWritten, "X2");
	}

	public bool TryToHexadecimalString(byte value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten, "X2");
	}

	public bool TryToHexadecimalString(char value, Span<char> destination, out int charsWritten)
	{
		ushort num = value;
		return num.TryFormat(destination, out charsWritten, "X4");
	}

	public bool TryToHexadecimalString(short value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten, "X4");
	}

	public bool TryToHexadecimalString(int value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten, "X8");
	}

	public bool TryToHexadecimalString(long value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten, "X16");
	}

	public bool TryToHexadecimalString(sbyte value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten, "X2");
	}

	public bool TryToHexadecimalString(ushort value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten, "X4");
	}

	public bool TryToHexadecimalString(uint value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten, "X8");
	}

	public bool TryToHexadecimalString(ulong value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten, "X16");
	}

	public bool TryToDecimalString(bool value, Span<char> destination, out int charsWritten)
	{
		return Convert.ToByte(value).TryFormat(destination, out charsWritten);
	}

	public bool TryToDecimalString(byte value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryToDecimalString(char value, Span<char> destination, out int charsWritten)
	{
		ushort num = value;
		return num.TryFormat(destination, out charsWritten);
	}

	public bool TryToDecimalString(short value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryToDecimalString(int value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryToDecimalString(long value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryToDecimalString(sbyte value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryToDecimalString(ushort value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryToDecimalString(uint value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryToDecimalString(ulong value, Span<char> destination, out int charsWritten)
	{
		return value.TryFormat(destination, out charsWritten);
	}

	public bool TryParseNumber(ReadOnlySpan<char> s, NumberStyles style, IFormatProvider provider, out bool result)
	{
		byte result3;
		bool result2 = byte.TryParse(s, style, provider, out result3);
		result = Convert.ToBoolean(result3);
		return result2;
	}

	public bool TryParseNumber(ReadOnlySpan<char> s, NumberStyles style, IFormatProvider provider, out byte result)
	{
		return byte.TryParse(s, style, provider, out result);
	}

	public bool TryParseNumber(ReadOnlySpan<char> s, NumberStyles style, IFormatProvider provider, out char result)
	{
		ushort result3;
		bool result2 = ushort.TryParse(s, style, provider, out result3);
		result = (char)result3;
		return result2;
	}

	public bool TryParseNumber(ReadOnlySpan<char> s, NumberStyles style, IFormatProvider provider, out short result)
	{
		return short.TryParse(s, style, provider, out result);
	}

	public bool TryParseNumber(ReadOnlySpan<char> s, NumberStyles style, IFormatProvider provider, out int result)
	{
		return int.TryParse(s, style, provider, out result);
	}

	public bool TryParseNumber(ReadOnlySpan<char> s, NumberStyles style, IFormatProvider provider, out long result)
	{
		return long.TryParse(s, style, provider, out result);
	}

	public bool TryParseNumber(ReadOnlySpan<char> s, NumberStyles style, IFormatProvider provider, out sbyte result)
	{
		return sbyte.TryParse(s, style, provider, out result);
	}

	public bool TryParseNumber(ReadOnlySpan<char> s, NumberStyles style, IFormatProvider provider, out ushort result)
	{
		return ushort.TryParse(s, style, provider, out result);
	}

	public bool TryParseNumber(ReadOnlySpan<char> s, NumberStyles style, IFormatProvider provider, out uint result)
	{
		return uint.TryParse(s, style, provider, out result);
	}

	public bool TryParseNumber(ReadOnlySpan<char> s, NumberStyles style, IFormatProvider provider, out ulong result)
	{
		return ulong.TryParse(s, style, provider, out result);
	}

	public bool TryParseNative(ReadOnlySpan<char> s, out bool result)
	{
		return bool.TryParse(s, out result);
	}

	public bool TryParseNative(ReadOnlySpan<char> s, out byte result)
	{
		return byte.TryParse(s, NumberStyles.AllowLeadingSign, CultureInfo.InvariantCulture, out result);
	}

	public bool TryParseNative(ReadOnlySpan<char> s, out char result)
	{
		if (s.Length == 1)
		{
			result = s[0];
			return true;
		}
		result = '\0';
		return false;
	}

	public bool TryParseNative(ReadOnlySpan<char> s, out short result)
	{
		return short.TryParse(s, NumberStyles.AllowLeadingSign, CultureInfo.InvariantCulture, out result);
	}

	public bool TryParseNative(ReadOnlySpan<char> s, out int result)
	{
		return int.TryParse(s, NumberStyles.AllowLeadingSign, CultureInfo.InvariantCulture, out result);
	}

	public bool TryParseNative(ReadOnlySpan<char> s, out long result)
	{
		return long.TryParse(s, NumberStyles.AllowLeadingSign, CultureInfo.InvariantCulture, out result);
	}

	public bool TryParseNative(ReadOnlySpan<char> s, out sbyte result)
	{
		return sbyte.TryParse(s, NumberStyles.AllowLeadingSign, CultureInfo.InvariantCulture, out result);
	}

	public bool TryParseNative(ReadOnlySpan<char> s, out ushort result)
	{
		return ushort.TryParse(s, NumberStyles.AllowLeadingSign, CultureInfo.InvariantCulture, out result);
	}

	public bool TryParseNative(ReadOnlySpan<char> s, out uint result)
	{
		return uint.TryParse(s, NumberStyles.AllowLeadingSign, CultureInfo.InvariantCulture, out result);
	}

	public bool TryParseNative(ReadOnlySpan<char> s, out ulong result)
	{
		return ulong.TryParse(s, NumberStyles.AllowLeadingSign, CultureInfo.InvariantCulture, out result);
	}

	public bool Xor(bool left, bool right)
	{
		return left != right;
	}

	public byte Xor(byte left, byte right)
	{
		return (byte)(left ^ right);
	}

	public char Xor(char left, char right)
	{
		return (char)(left ^ right);
	}

	public short Xor(short left, short right)
	{
		return (short)(left ^ right);
	}

	public int Xor(int left, int right)
	{
		return left ^ right;
	}

	public long Xor(long left, long right)
	{
		return left ^ right;
	}

	public sbyte Xor(sbyte left, sbyte right)
	{
		return (sbyte)(left ^ right);
	}

	public ushort Xor(ushort left, ushort right)
	{
		return (ushort)(left ^ right);
	}

	public uint Xor(uint left, uint right)
	{
		return left ^ right;
	}

	public ulong Xor(ulong left, ulong right)
	{
		return left ^ right;
	}
}
