using System;
using System.Text.Json.Serialization;
using Microsoft.Identity.Client.Platforms.net6;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Internal;

[JsonObject]
[Preserve(AllMembers = true)]
internal class ClientInfo
{
	[JsonPropertyName("uid")]
	public string UniqueObjectIdentifier { get; set; }

	[JsonPropertyName("utid")]
	public string UniqueTenantIdentifier { get; set; }

	public static ClientInfo CreateFromJson(string clientInfo)
	{
		if (string.IsNullOrEmpty(clientInfo))
		{
			throw new MsalClientException("json_parse_failed", "client info is null");
		}
		try
		{
			return JsonHelper.DeserializeFromJson<ClientInfo>(Base64UrlHelpers.DecodeBytes(clientInfo));
		}
		catch (Exception innerException)
		{
			throw new MsalClientException("json_parse_failed", "Failed to parse the returned client info.", innerException);
		}
	}

	public string ToAccountIdentifier()
	{
		return UniqueObjectIdentifier + "." + UniqueTenantIdentifier;
	}
}
