using System;
using System.Collections.Generic;
using System.Diagnostics.Metrics;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;
using Microsoft.Identity.Client.TelemetryCore.OpenTelemetry;

namespace Microsoft.Identity.Client.Platforms.Features.OpenTelemetry;

internal class OtelInstrumentation : IOtelInstrumentation
{
	public const string MeterName = "MicrosoftIdentityClient_Common_Meter";

	private const string SuccessCounterName = "MsalSuccess";

	private const string FailedCounterName = "MsalFailure";

	private const string TotalDurationHistogramName = "MsalTotalDuration.1A";

	private const string DurationInL1CacheHistogramName = "MsalDurationInL1CacheInUs.1B";

	private const string DurationInL2CacheHistogramName = "MsalDurationInL2Cache.1A";

	private const string DurationInHttpHistogramName = "MsalDurationInHttp.1A";

	internal static readonly Meter Meter = new Meter("MicrosoftIdentityClient_Common_Meter", "1.0.0");

	internal static readonly Lazy<Counter<long>> s_successCounter = new Lazy<Counter<long>>(() => Meter.CreateCounter<long>("MsalSuccess", null, "Number of successful token acquisition calls"));

	internal static readonly Lazy<Counter<long>> s_failureCounter = new Lazy<Counter<long>>(() => Meter.CreateCounter<long>("MsalFailure", null, "Number of failed token acquisition calls"));

	internal static readonly Lazy<Histogram<long>> s_durationTotal = new Lazy<Histogram<long>>(() => Meter.CreateHistogram<long>("MsalTotalDuration.1A", "ms", "Performance of token acquisition calls total latency"));

	internal static readonly Lazy<Histogram<long>> s_durationInL1CacheInUs = new Lazy<Histogram<long>>(() => Meter.CreateHistogram<long>("MsalDurationInL1CacheInUs.1B", "us", "Performance of token acquisition calls total latency in microseconds when L1 cache is used."));

	internal static readonly Lazy<Histogram<long>> s_durationInL2Cache = new Lazy<Histogram<long>>(() => Meter.CreateHistogram<long>("MsalDurationInL2Cache.1A", "ms", "Performance of token acquisition calls cache latency"));

	internal static readonly Lazy<Histogram<long>> s_durationInHttp = new Lazy<Histogram<long>>(() => Meter.CreateHistogram<long>("MsalDurationInHttp.1A", "ms", "Performance of token acquisition calls network latency"));

	public OtelInstrumentation()
	{
		_ = Meter.Version;
	}

	public void LogSuccessMetrics(string platform, ApiEvent.ApiIds apiId, CacheLevel cacheLevel, long totalDurationInUs, AuthenticationResultMetadata authResultMetadata, ILoggerAdapter logger)
	{
		IncrementSuccessCounter(platform, apiId, authResultMetadata.TokenSource, authResultMetadata.CacheRefreshReason, cacheLevel, logger);
		if (s_durationTotal.Value.Enabled)
		{
			s_durationTotal.Value.Record(authResultMetadata.DurationTotalInMs, new KeyValuePair<string, object>("MsalVersion", MsalIdHelper.GetMsalVersion()), new KeyValuePair<string, object>("Platform", platform), new KeyValuePair<string, object>("ApiId", apiId), new KeyValuePair<string, object>("TokenSource", authResultMetadata.TokenSource), new KeyValuePair<string, object>("CacheLevel", cacheLevel), new KeyValuePair<string, object>("CacheRefreshReason", authResultMetadata.CacheRefreshReason));
		}
		if (s_durationInL2Cache.Value.Enabled && cacheLevel == CacheLevel.L2Cache)
		{
			s_durationInL2Cache.Value.Record(authResultMetadata.DurationInCacheInMs, new KeyValuePair<string, object>("MsalVersion", MsalIdHelper.GetMsalVersion()), new KeyValuePair<string, object>("Platform", platform), new KeyValuePair<string, object>("ApiId", apiId), new KeyValuePair<string, object>("CacheRefreshReason", authResultMetadata.CacheRefreshReason));
		}
		if (s_durationInHttp.Value.Enabled && authResultMetadata.TokenSource == TokenSource.IdentityProvider)
		{
			s_durationInHttp.Value.Record(authResultMetadata.DurationInHttpInMs, new KeyValuePair<string, object>("MsalVersion", MsalIdHelper.GetMsalVersion()), new KeyValuePair<string, object>("Platform", platform), new KeyValuePair<string, object>("ApiId", apiId));
		}
		if (s_durationInL1CacheInUs.Value.Enabled && authResultMetadata.TokenSource == TokenSource.Cache && authResultMetadata.CacheLevel.Equals(CacheLevel.L1Cache))
		{
			s_durationInL1CacheInUs.Value.Record(totalDurationInUs, new KeyValuePair<string, object>("MsalVersion", MsalIdHelper.GetMsalVersion()), new KeyValuePair<string, object>("Platform", platform), new KeyValuePair<string, object>("ApiId", apiId), new KeyValuePair<string, object>("TokenSource", authResultMetadata.TokenSource), new KeyValuePair<string, object>("CacheLevel", authResultMetadata.CacheLevel), new KeyValuePair<string, object>("CacheRefreshReason", authResultMetadata.CacheRefreshReason));
		}
	}

	public void IncrementSuccessCounter(string platform, ApiEvent.ApiIds apiId, TokenSource tokenSource, CacheRefreshReason cacheRefreshReason, CacheLevel cacheLevel, ILoggerAdapter logger)
	{
		if (s_successCounter.Value.Enabled)
		{
			s_successCounter.Value.Add(1L, new KeyValuePair<string, object>("MsalVersion", MsalIdHelper.GetMsalVersion()), new KeyValuePair<string, object>("Platform", platform), new KeyValuePair<string, object>("ApiId", apiId), new KeyValuePair<string, object>("TokenSource", tokenSource), new KeyValuePair<string, object>("CacheRefreshReason", cacheRefreshReason), new KeyValuePair<string, object>("CacheLevel", cacheLevel));
			logger.Verbose(() => "[OpenTelemetry] Completed incrementing to success counter.");
		}
	}

	public void LogFailureMetrics(string platform, string errorCode, ApiEvent.ApiIds apiId, CacheRefreshReason cacheRefreshReason)
	{
		if (s_failureCounter.Value.Enabled)
		{
			s_failureCounter.Value.Add(1L, new KeyValuePair<string, object>("MsalVersion", MsalIdHelper.GetMsalVersion()), new KeyValuePair<string, object>("Platform", platform), new KeyValuePair<string, object>("ErrorCode", errorCode), new KeyValuePair<string, object>("ApiId", apiId), new KeyValuePair<string, object>("CacheRefreshReason", cacheRefreshReason));
		}
	}
}
