using System;
using System.Diagnostics;
using System.Reflection;
using System.Resources;
using System.Runtime.CompilerServices;
using System.Runtime.Versioning;

[assembly: InternalsVisibleTo("Newtonsoft.Json.Bson.Tests, PublicKey=0024000004800000940000000602000000240000525341310004000001000100f561df277c6c0b497d629032b410cdcf286e537c054724f7ffa0164345f62b3e642029d7a80cc351918955328c4adc8a048823ef90b0cf38ea7db0d729caf2b633c3babe08b0310198c1081995c19029bc675193744eab9d7345b8a67258ec17d112cebdbbb2a281487dceeafb9d83aa930f32103fbe1d2911425bc5744002c7")]
[assembly: AssemblyTrademark("")]
[assembly: CLSCompliant(true)]
[assembly: AssemblyCompany("Newtonsoft")]
[assembly: AssemblyConfiguration("Release")]
[assembly: AssemblyCopyright("Copyright © James Newton-King 2017")]
[assembly: AssemblyDescription("Json.NET BSON adds support for reading and writing BSON")]
[assembly: AssemblyFileVersion("1.0.2.22727")]
[assembly: AssemblyInformationalVersion("1.0.2+a1db92678e7e72eb733f37079f3a93bfb6215338")]
[assembly: AssemblyProduct("Json.NET BSON")]
[assembly: AssemblyTitle("Json.NET BSON .NET Standard 2.0")]
[assembly: NeutralResourcesLanguage("en-US")]
[assembly: AssemblyVersion("*******")]
