using System.Runtime.CompilerServices;
using Microsoft.Identity.Client.Internal.Logger;
using Microsoft.IdentityModel.Abstractions;

namespace Microsoft.Identity.Client.Core;

internal interface ILoggerAdapter
{
	bool PiiLoggingEnabled { get; }

	bool IsDefaultPlatformLoggingEnabled { get; }

	string ClientName { get; }

	string ClientVersion { get; }

	IIdentityLogger IdentityLogger { get; }

	bool IsLoggingEnabled(LogLevel logLevel);

	void Log(LogLevel logLevel, string messageWithPii, string messageScrubbed);

	DurationLogHelper LogBlockDuration(string measuredBlockName, LogLevel logLevel = LogLevel.Verbose);

	DurationLogHelper LogMethodDuration(LogLevel logLevel = LogLevel.Verbose, [CallerMemberName] string methodName = null, [CallerFilePath] string filePath = null);
}
