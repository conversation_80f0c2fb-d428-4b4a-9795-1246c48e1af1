using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Internal.Broker;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Internal.Requests;

internal class AuthCodeExchangeComponent : ITokenRequestComponent
{
	private readonly AuthenticationRequestParameters _requestParams;

	private readonly AcquireTokenInteractiveParameters _interactiveParameters;

	private readonly string _authorizationCode;

	private readonly string _pkceCodeVerifier;

	private readonly TokenClient _tokenClient;

	private readonly string _clientInfo;

	public AuthCodeExchangeComponent(AuthenticationRequestParameters requestParams, AcquireTokenInteractiveParameters interactiveParameters, string authorizationCode, string pkceCodeVerifier, string clientInfo)
	{
		_requestParams = requestParams ?? throw new ArgumentNullException("requestParams");
		_interactiveParameters = interactiveParameters ?? throw new ArgumentNullException("interactiveParameters");
		_authorizationCode = authorizationCode ?? throw new ArgumentNullException("authorizationCode");
		_pkceCodeVerifier = pkceCodeVerifier ?? throw new ArgumentNullException("pkceCodeVerifier");
		_clientInfo = clientInfo;
		_tokenClient = new TokenClient(requestParams);
		_interactiveParameters.LogParameters(requestParams.RequestContext.Logger);
	}

	public Task<MsalTokenResponse> FetchTokensAsync(CancellationToken cancellationToken)
	{
		AddCcsHeadersToTokenClient();
		return _tokenClient.SendTokenRequestAsync(GetBodyParameters(), null, null, cancellationToken);
	}

	private Dictionary<string, string> GetBodyParameters()
	{
		return new Dictionary<string, string>
		{
			["client_info"] = "1",
			["grant_type"] = "authorization_code",
			["code"] = _authorizationCode,
			["redirect_uri"] = _requestParams.RedirectUri.OriginalString,
			["code_verifier"] = _pkceCodeVerifier
		};
	}

	private void AddCcsHeadersToTokenClient()
	{
		if (!string.IsNullOrEmpty(_clientInfo))
		{
			ClientInfo clientInfo = ClientInfo.CreateFromJson(_clientInfo);
			_tokenClient.AddHeaderToClient("x-anchormailbox", CoreHelpers.GetCcsClientInfoHint(clientInfo.UniqueObjectIdentifier, clientInfo.UniqueTenantIdentifier));
		}
		else if (!string.IsNullOrEmpty(_interactiveParameters.LoginHint))
		{
			_tokenClient.AddHeaderToClient("x-anchormailbox", CoreHelpers.GetCcsUpnHint(_interactiveParameters.LoginHint));
		}
	}
}
