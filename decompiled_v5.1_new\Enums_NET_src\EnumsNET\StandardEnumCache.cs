using System;
using System.Runtime.CompilerServices;
using EnumsNET.Numerics;

namespace EnumsNET;

internal abstract class StandardEnumCache<TUnderlying, TUnderlyingOperations> : EnumCache<TUnderlying, TUnderlyingOperations> where TUnderlying : struct, IComparable<TUnderlying>, IEquatable<TUnderlying>, IConvertible where TUnderlyingOperations : struct, IUnderlyingOperations<TUnderlying>
{
	protected StandardEnumCache(Type enumType, IEnumBridge<TUnderlying, TUnderlyingOperations> enumBridge, EnumMemberInternal<TUnderlying, TUnderlyingOperations>[] members, EnumMemberInternal<TUnderlying, TUnderlyingOperations>?[] buckets, TUnderlying allFlags, int distinctCount, bool isContiguous, object? customValidator)
		: base(enumType, enumBridge, isFlagEnum: false, members, buckets, allFlags, distinctCount, isContiguous, customValidator)
	{
	}

	public sealed override void Parse(ReadOnlySpan<char> value, bool ignoreCase, ValueCollection<EnumFormat> formats, ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = ParseInternal(value, ignoreCase, formats);
	}

	public sealed override object Parse(ReadOnlySpan<char> value, bool ignoreCase, ValueCollection<EnumFormat> formats)
	{
		return EnumBridge.ToObjectUnchecked(ParseInternal(value, ignoreCase, formats));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public TUnderlying ParseInternal(ReadOnlySpan<char> value, bool ignoreCase, ValueCollection<EnumFormat> formats)
	{
		value = value.Trim();
		if (TryParseInternal(value, ignoreCase, out TUnderlying result, out EnumMemberInternal<TUnderlying, TUnderlyingOperations> _, formats))
		{
			return result;
		}
		if (EnumCache.IsNumeric(value))
		{
			throw new OverflowException("value is outside the underlying type's value range");
		}
		throw new ArgumentException($"string was not recognized as being a member of {EnumType}", "value");
	}

	public sealed override void Parse(ReadOnlySpan<char> value, bool ignoreCase, ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = ParseInternal(value, ignoreCase);
	}

	public sealed override object Parse(ReadOnlySpan<char> value, bool ignoreCase)
	{
		return EnumBridge.ToObjectUnchecked(ParseInternal(value, ignoreCase));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public new TUnderlying ParseInternal(ReadOnlySpan<char> value, bool ignoreCase)
	{
		value = value.Trim();
		if (TryParseInternal(value, ignoreCase, out var result))
		{
			return result;
		}
		if (EnumCache.IsNumeric(value))
		{
			throw new OverflowException("value is outside the underlying type's value range");
		}
		throw new ArgumentException($"string was not recognized as being a member of {EnumType}", "value");
	}

	public sealed override bool TryParse(ReadOnlySpan<char> value, bool ignoreCase, ref byte result, ValueCollection<EnumFormat> formats)
	{
		if (value != null && TryParseInternal(value.Trim(), ignoreCase, out TUnderlying result2, out EnumMemberInternal<TUnderlying, TUnderlyingOperations> _, formats))
		{
			UnsafeUtility.As<byte, TUnderlying>(ref result) = result2;
			return true;
		}
		return false;
	}

	public sealed override bool TryParse(ReadOnlySpan<char> value, bool ignoreCase, out object? result, ValueCollection<EnumFormat> formats)
	{
		if (value != null && TryParseInternal(value.Trim(), ignoreCase, out TUnderlying result2, out EnumMemberInternal<TUnderlying, TUnderlyingOperations> _, formats))
		{
			result = EnumBridge.ToObjectUnchecked(result2);
			return true;
		}
		result = null;
		return false;
	}

	public sealed override bool TryParse(ReadOnlySpan<char> value, bool ignoreCase, ref byte result)
	{
		if (value != null && TryParseInternal(value.Trim(), ignoreCase, out var result2))
		{
			UnsafeUtility.As<byte, TUnderlying>(ref result) = result2;
			return true;
		}
		return false;
	}

	public sealed override bool TryParse(ReadOnlySpan<char> value, bool ignoreCase, out object? result)
	{
		if (value != null && TryParseInternal(value.Trim(), ignoreCase, out var result2))
		{
			result = EnumBridge.ToObjectUnchecked(result2);
			return true;
		}
		result = null;
		return false;
	}
}
