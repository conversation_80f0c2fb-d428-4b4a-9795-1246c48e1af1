using System;
using System.Collections.Generic;
using System.Numerics;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

namespace SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper;

internal sealed class PolygonClipper
{
	[StructLayout(LayoutKind.Sequential, Size = 1)]
	private struct LocMinSorter : IComparer<LocalMinima>
	{
		public readonly int Compare(LocalMinima locMin1, LocalMinima locMin2)
		{
			return locMin2.Vertex.Point.Y.CompareTo(locMin1.Vertex.Point.Y);
		}
	}

	private readonly struct LocalMinima
	{
		public readonly Vertex Vertex;

		public readonly ClippingType Polytype;

		public readonly bool IsOpen;

		public LocalMinima(Vertex vertex, ClippingType polytype, bool isOpen = false)
		{
			Vertex = vertex;
			Polytype = polytype;
			IsOpen = isOpen;
		}

		public static bool operator ==(LocalMinima lm1, LocalMinima lm2)
		{
			return lm1.Vertex == lm2.Vertex;
		}

		public static bool operator !=(LocalMinima lm1, LocalMinima lm2)
		{
			return !(lm1 == lm2);
		}

		public override bool Equals(object obj)
		{
			if (obj is LocalMinima localMinima)
			{
				return this == localMinima;
			}
			return false;
		}

		public override int GetHashCode()
		{
			return Vertex.GetHashCode();
		}
	}

	private readonly struct IntersectNode
	{
		public readonly Vector2 Point;

		public readonly Active Edge1;

		public readonly Active Edge2;

		public IntersectNode(Vector2 pt, Active edge1, Active edge2)
		{
			Point = pt;
			Edge1 = edge1;
			Edge2 = edge2;
		}
	}

	[StructLayout(LayoutKind.Sequential, Size = 1)]
	private struct HorzSegSorter : IComparer<HorzSegment>
	{
		public readonly int Compare(HorzSegment hs1, HorzSegment hs2)
		{
			if (hs1 == null || hs2 == null)
			{
				return 0;
			}
			if (hs1.RightOp == null)
			{
				return (hs2.RightOp != null) ? 1 : 0;
			}
			if (hs2.RightOp == null)
			{
				return -1;
			}
			return hs1.LeftOp.Point.X.CompareTo(hs2.LeftOp.Point.X);
		}
	}

	[StructLayout(LayoutKind.Sequential, Size = 1)]
	private struct IntersectListSort : IComparer<IntersectNode>
	{
		public readonly int Compare(IntersectNode a, IntersectNode b)
		{
			if (a.Point.Y == b.Point.Y)
			{
				if (a.Point.X == b.Point.X)
				{
					return 0;
				}
				if (!(a.Point.X < b.Point.X))
				{
					return 1;
				}
				return -1;
			}
			if (!(a.Point.Y > b.Point.Y))
			{
				return 1;
			}
			return -1;
		}
	}

	private class HorzSegment
	{
		public OutPt LeftOp { get; set; }

		public OutPt RightOp { get; set; }

		public bool LeftToRight { get; set; }

		public HorzSegment(OutPt op)
		{
			LeftOp = op;
			RightOp = null;
			LeftToRight = true;
		}
	}

	private class HorzJoin
	{
		public OutPt Op1 { get; }

		public OutPt Op2 { get; }

		public HorzJoin(OutPt ltor, OutPt rtol)
		{
			Op1 = ltor;
			Op2 = rtol;
		}
	}

	private class OutPt
	{
		public Vector2 Point { get; }

		public OutPt Next { get; set; }

		public OutPt Prev { get; set; }

		public OutRec OutRec { get; set; }

		public HorzSegment HorizSegment { get; set; }

		public OutPt(Vector2 pt, OutRec outrec)
		{
			Point = pt;
			OutRec = outrec;
			Next = this;
			Prev = this;
			HorizSegment = null;
		}
	}

	private class OutRec
	{
		public int Idx { get; set; }

		public OutRec Owner { get; set; }

		public Active FrontEdge { get; set; }

		public Active BackEdge { get; set; }

		public OutPt Pts { get; set; }

		public PolyPathF PolyPath { get; set; }

		public BoundsF Bounds { get; set; }

		public PathF Path { get; set; } = new PathF();

		public bool IsOpen { get; set; }

		public List<int> Splits { get; set; }
	}

	private class Vertex
	{
		public Vector2 Point { get; }

		public Vertex Next { get; set; }

		public Vertex Prev { get; set; }

		public VertexFlags Flags { get; set; }

		public Vertex(Vector2 pt, VertexFlags flags, Vertex prev)
		{
			Point = pt;
			Flags = flags;
			Next = null;
			Prev = prev;
		}
	}

	private class Active
	{
		public Vector2 Bot { get; set; }

		public Vector2 Top { get; set; }

		public float CurX { get; set; }

		public float Dx { get; set; }

		public int WindDx { get; set; }

		public int WindCount { get; set; }

		public int WindCount2 { get; set; }

		public OutRec Outrec { get; set; }

		public Active PrevInAEL { get; set; }

		public Active NextInAEL { get; set; }

		public Active PrevInSEL { get; set; }

		public Active NextInSEL { get; set; }

		public Active Jump { get; set; }

		public Vertex VertexTop { get; set; }

		public LocalMinima LocalMin { get; set; }

		public bool IsLeftBound { get; set; }

		public JoinWith JoinWith { get; set; }
	}

	private ClippingOperation clipType;

	private FillRule fillRule;

	private Active actives;

	private Active flaggedHorizontal;

	private readonly List<LocalMinima> minimaList;

	private readonly List<IntersectNode> intersectList;

	private readonly List<Vertex> vertexList;

	private readonly List<OutRec> outrecList;

	private readonly List<float> scanlineList;

	private readonly List<HorzSegment> horzSegList;

	private readonly List<HorzJoin> horzJoinList;

	private int currentLocMin;

	private float currentBotY;

	private bool isSortedMinimaList;

	private bool hasOpenPaths;

	public bool PreserveCollinear { get; set; }

	public bool ReverseSolution { get; set; }

	public PolygonClipper()
	{
		minimaList = new List<LocalMinima>();
		intersectList = new List<IntersectNode>();
		vertexList = new List<Vertex>();
		outrecList = new List<OutRec>();
		scanlineList = new List<float>();
		horzSegList = new List<HorzSegment>();
		horzJoinList = new List<HorzJoin>();
		PreserveCollinear = true;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public void AddSubject(PathsF paths)
	{
		AddPaths(paths, ClippingType.Subject);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public void AddPath(PathF path, ClippingType polytype, bool isOpen = false)
	{
		PathsF paths = new PathsF(1) { path };
		AddPaths(paths, polytype, isOpen);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public void AddPaths(PathsF paths, ClippingType polytype, bool isOpen = false)
	{
		if (isOpen)
		{
			hasOpenPaths = true;
		}
		isSortedMinimaList = false;
		AddPathsToVertexList(paths, polytype, isOpen);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public void Execute(ClippingOperation clipType, FillRule fillRule, PathsF solutionClosed)
	{
		Execute(clipType, fillRule, solutionClosed, new PathsF());
	}

	public void Execute(ClippingOperation clipType, FillRule fillRule, PathsF solutionClosed, PathsF solutionOpen)
	{
		solutionClosed.Clear();
		solutionOpen.Clear();
		try
		{
			ExecuteInternal(clipType, fillRule);
			BuildPaths(solutionClosed, solutionOpen);
		}
		catch (Exception innerException)
		{
			throw new ClipperException("An error occurred while attempting to clip the polygon. See the inner exception for details.", innerException);
		}
		finally
		{
			ClearSolutionOnly();
		}
	}

	private void ExecuteInternal(ClippingOperation ct, FillRule fillRule)
	{
		if (ct == ClippingOperation.None)
		{
			return;
		}
		this.fillRule = fillRule;
		clipType = ct;
		Reset();
		if (!PopScanline(out var y))
		{
			return;
		}
		while (true)
		{
			InsertLocalMinimaIntoAEL(y);
			Active ae;
			while (PopHorz(out ae))
			{
				DoHorizontal(ae);
			}
			if (horzSegList.Count > 0)
			{
				ConvertHorzSegsToJoins();
				horzSegList.Clear();
			}
			currentBotY = y;
			if (!PopScanline(out y))
			{
				break;
			}
			DoIntersections(y);
			DoTopOfScanbeam(y);
			while (PopHorz(out ae))
			{
				DoHorizontal(ae);
			}
		}
		ProcessHorzJoins();
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void DoIntersections(float topY)
	{
		if (BuildIntersectList(topY))
		{
			ProcessIntersectList();
			DisposeIntersectNodes();
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void DisposeIntersectNodes()
	{
		intersectList.Clear();
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void AddNewIntersectNode(Active ae1, Active ae2, float topY)
	{
		if (!ClipperUtils.GetIntersectPt(ae1.Bot, ae1.Top, ae2.Bot, ae2.Top, out var ip))
		{
			ip = new Vector2(ae1.CurX, topY);
		}
		if (ip.Y > currentBotY || ip.Y < topY)
		{
			float num = MathF.Abs(ae1.Dx);
			float num2 = MathF.Abs(ae2.Dx);
			if (num > 100f && num2 > 100f)
			{
				ip = ((!(num > num2)) ? ClipperUtils.GetClosestPtOnSegment(ip, ae2.Bot, ae2.Top) : ClipperUtils.GetClosestPtOnSegment(ip, ae1.Bot, ae1.Top));
			}
			else if (num > 100f)
			{
				ip = ClipperUtils.GetClosestPtOnSegment(ip, ae1.Bot, ae1.Top);
			}
			else if (num2 > 100f)
			{
				ip = ClipperUtils.GetClosestPtOnSegment(ip, ae2.Bot, ae2.Top);
			}
			else
			{
				if (ip.Y < topY)
				{
					ip.Y = topY;
				}
				else
				{
					ip.Y = currentBotY;
				}
				if (num < num2)
				{
					ip.X = TopX(ae1, ip.Y);
				}
				else
				{
					ip.X = TopX(ae2, ip.Y);
				}
			}
		}
		IntersectNode item = new IntersectNode(ip, ae1, ae2);
		intersectList.Add(item);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool SetHorzSegHeadingForward(HorzSegment hs, OutPt opP, OutPt opN)
	{
		if (opP.Point.X == opN.Point.X)
		{
			return false;
		}
		if (opP.Point.X < opN.Point.X)
		{
			hs.LeftOp = opP;
			hs.RightOp = opN;
			hs.LeftToRight = true;
		}
		else
		{
			hs.LeftOp = opN;
			hs.RightOp = opP;
			hs.LeftToRight = false;
		}
		return true;
	}

	private static bool UpdateHorzSegment(HorzSegment hs)
	{
		OutPt leftOp = hs.LeftOp;
		OutRec realOutRec = GetRealOutRec(leftOp.OutRec);
		bool flag = realOutRec.FrontEdge != null;
		float y = leftOp.Point.Y;
		OutPt outPt = leftOp;
		OutPt outPt2 = leftOp;
		if (flag)
		{
			OutPt pts = realOutRec.Pts;
			OutPt next = pts.Next;
			while (outPt != next && outPt.Prev.Point.Y == y)
			{
				outPt = outPt.Prev;
			}
			while (outPt2 != pts && outPt2.Next.Point.Y == y)
			{
				outPt2 = outPt2.Next;
			}
		}
		else
		{
			while (outPt.Prev != outPt2 && outPt.Prev.Point.Y == y)
			{
				outPt = outPt.Prev;
			}
			while (outPt2.Next != outPt && outPt2.Next.Point.Y == y)
			{
				outPt2 = outPt2.Next;
			}
		}
		int num;
		if (SetHorzSegHeadingForward(hs, outPt, outPt2))
		{
			num = ((hs.LeftOp.HorizSegment == null) ? 1 : 0);
			if (num != 0)
			{
				hs.LeftOp.HorizSegment = hs;
				return (byte)num != 0;
			}
		}
		else
		{
			num = 0;
		}
		hs.RightOp = null;
		return (byte)num != 0;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static OutPt DuplicateOp(OutPt op, bool insert_after)
	{
		OutPt outPt = new OutPt(op.Point, op.OutRec);
		if (insert_after)
		{
			outPt.Next = op.Next;
			outPt.Next.Prev = outPt;
			outPt.Prev = op;
			op.Next = outPt;
		}
		else
		{
			outPt.Prev = op.Prev;
			outPt.Prev.Next = outPt;
			outPt.Next = op;
			op.Prev = outPt;
		}
		return outPt;
	}

	private void ConvertHorzSegsToJoins()
	{
		int num = 0;
		foreach (HorzSegment horzSeg in horzSegList)
		{
			if (UpdateHorzSegment(horzSeg))
			{
				num++;
			}
		}
		if (num < 2)
		{
			return;
		}
		horzSegList.Sort(default(HorzSegSorter));
		for (int i = 0; i < num - 1; i++)
		{
			HorzSegment horzSegment = horzSegList[i];
			for (int j = i + 1; j < num; j++)
			{
				HorzSegment horzSegment2 = horzSegList[j];
				if (horzSegment2.LeftOp.Point.X >= horzSegment.RightOp.Point.X || horzSegment2.LeftToRight == horzSegment.LeftToRight || horzSegment2.RightOp.Point.X <= horzSegment.LeftOp.Point.X)
				{
					continue;
				}
				float y = horzSegment.LeftOp.Point.Y;
				if (horzSegment.LeftToRight)
				{
					while (horzSegment.LeftOp.Next.Point.Y == y && horzSegment.LeftOp.Next.Point.X <= horzSegment2.LeftOp.Point.X)
					{
						horzSegment.LeftOp = horzSegment.LeftOp.Next;
					}
					while (horzSegment2.LeftOp.Prev.Point.Y == y && horzSegment2.LeftOp.Prev.Point.X <= horzSegment.LeftOp.Point.X)
					{
						horzSegment2.LeftOp = horzSegment2.LeftOp.Prev;
					}
					HorzJoin item = new HorzJoin(DuplicateOp(horzSegment.LeftOp, insert_after: true), DuplicateOp(horzSegment2.LeftOp, insert_after: false));
					horzJoinList.Add(item);
				}
				else
				{
					while (horzSegment.LeftOp.Prev.Point.Y == y && horzSegment.LeftOp.Prev.Point.X <= horzSegment2.LeftOp.Point.X)
					{
						horzSegment.LeftOp = horzSegment.LeftOp.Prev;
					}
					while (horzSegment2.LeftOp.Next.Point.Y == y && horzSegment2.LeftOp.Next.Point.X <= horzSegment.LeftOp.Point.X)
					{
						horzSegment2.LeftOp = horzSegment2.LeftOp.Next;
					}
					HorzJoin item2 = new HorzJoin(DuplicateOp(horzSegment2.LeftOp, insert_after: true), DuplicateOp(horzSegment.LeftOp, insert_after: false));
					horzJoinList.Add(item2);
				}
			}
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void ClearSolutionOnly()
	{
		while (actives != null)
		{
			DeleteFromAEL(actives);
		}
		scanlineList.Clear();
		DisposeIntersectNodes();
		outrecList.Clear();
		horzSegList.Clear();
		horzJoinList.Clear();
	}

	private bool BuildPaths(PathsF solutionClosed, PathsF solutionOpen)
	{
		solutionClosed.Clear();
		solutionOpen.Clear();
		solutionClosed.Capacity = outrecList.Count;
		solutionOpen.Capacity = outrecList.Count;
		int num = 0;
		while (num < outrecList.Count)
		{
			OutRec outRec = outrecList[num++];
			if (outRec.Pts == null)
			{
				continue;
			}
			PathF pathF = new PathF();
			if (outRec.IsOpen)
			{
				if (BuildPath(outRec.Pts, ReverseSolution, isOpen: true, pathF))
				{
					solutionOpen.Add(pathF);
				}
				continue;
			}
			CleanCollinear(outRec);
			if (BuildPath(outRec.Pts, ReverseSolution, isOpen: false, pathF))
			{
				solutionClosed.Add(pathF);
			}
		}
		return true;
	}

	private static bool BuildPath(OutPt op, bool reverse, bool isOpen, PathF path)
	{
		if (op == null || op.Next == op || (!isOpen && op.Next == op.Prev))
		{
			return false;
		}
		path.Clear();
		Vector2 point;
		OutPt outPt;
		if (reverse)
		{
			point = op.Point;
			outPt = op.Prev;
		}
		else
		{
			op = op.Next;
			point = op.Point;
			outPt = op.Next;
		}
		path.Add(point);
		while (outPt != op)
		{
			if (outPt.Point != point)
			{
				point = outPt.Point;
				path.Add(point);
			}
			outPt = ((!reverse) ? outPt.Next : outPt.Prev);
		}
		if (path.Count == 3)
		{
			return !IsVerySmallTriangle(outPt);
		}
		return true;
	}

	private void DoHorizontal(Active horz)
	{
		bool flag = IsOpen(horz);
		float y = horz.Bot.Y;
		Vertex vertex = (flag ? GetCurrYMaximaVertex_Open(horz) : GetCurrYMaximaVertex(horz));
		if (vertex != null && !flag && vertex != horz.VertexTop)
		{
			TrimHorz(horz, PreserveCollinear);
		}
		float leftX;
		float rightX;
		bool flag2 = ResetHorzDirection(horz, vertex, out leftX, out rightX);
		if (IsHotEdge(horz))
		{
			OutPt op = AddOutPt(horz, new Vector2(horz.CurX, y));
			AddToHorzSegList(op);
		}
		OutRec outrec = horz.Outrec;
		while (true)
		{
			Active active = (flag2 ? horz.NextInAEL : horz.PrevInAEL);
			while (active != null)
			{
				if (active.VertexTop == vertex)
				{
					if (IsHotEdge(horz) && IsJoined(active))
					{
						Split(active, active.Top);
					}
					if (IsHotEdge(horz))
					{
						while (horz.VertexTop != vertex)
						{
							AddOutPt(horz, horz.Top);
							UpdateEdgeIntoAEL(horz);
						}
						if (flag2)
						{
							AddLocalMaxPoly(horz, active, horz.Top);
						}
						else
						{
							AddLocalMaxPoly(active, horz, horz.Top);
						}
					}
					DeleteFromAEL(active);
					DeleteFromAEL(horz);
					return;
				}
				Vector2 point;
				if (vertex != horz.VertexTop || IsOpenEnd(horz))
				{
					if ((flag2 && active.CurX > rightX) || (!flag2 && active.CurX < leftX))
					{
						break;
					}
					if (active.CurX == horz.Top.X && !IsHorizontal(active))
					{
						point = NextVertex(horz).Point;
						if (IsOpen(active) && !IsSamePolyType(active, horz) && !IsHotEdge(active))
						{
							if ((flag2 && TopX(active, point.Y) > point.X) || (!flag2 && TopX(active, point.Y) < point.X))
							{
								break;
							}
						}
						else if ((flag2 && TopX(active, point.Y) >= point.X) || (!flag2 && TopX(active, point.Y) <= point.X))
						{
							break;
						}
					}
				}
				point = new Vector2(active.CurX, y);
				if (flag2)
				{
					IntersectEdges(horz, active, point);
					SwapPositionsInAEL(horz, active);
					horz.CurX = active.CurX;
					active = horz.NextInAEL;
				}
				else
				{
					IntersectEdges(active, horz, point);
					SwapPositionsInAEL(active, horz);
					horz.CurX = active.CurX;
					active = horz.PrevInAEL;
				}
				if (IsHotEdge(horz) && horz.Outrec != outrec)
				{
					outrec = horz.Outrec;
					AddToHorzSegList(GetLastOp(horz));
				}
			}
			if (flag && IsOpenEnd(horz))
			{
				if (IsHotEdge(horz))
				{
					AddOutPt(horz, horz.Top);
					if (IsFront(horz))
					{
						horz.Outrec.FrontEdge = null;
					}
					else
					{
						horz.Outrec.BackEdge = null;
					}
					horz.Outrec = null;
				}
				DeleteFromAEL(horz);
				return;
			}
			if (NextVertex(horz).Point.Y != horz.Top.Y)
			{
				break;
			}
			if (IsHotEdge(horz))
			{
				AddOutPt(horz, horz.Top);
			}
			UpdateEdgeIntoAEL(horz);
			if (PreserveCollinear && !flag && HorzIsSpike(horz))
			{
				TrimHorz(horz, preserveCollinear: true);
			}
			flag2 = ResetHorzDirection(horz, vertex, out leftX, out rightX);
		}
		if (IsHotEdge(horz))
		{
			AddToHorzSegList(AddOutPt(horz, horz.Top));
		}
		UpdateEdgeIntoAEL(horz);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void DoTopOfScanbeam(float y)
	{
		flaggedHorizontal = null;
		Active active = actives;
		while (active != null)
		{
			if (active.Top.Y == y)
			{
				active.CurX = active.Top.X;
				if (IsMaxima(active))
				{
					active = DoMaxima(active);
					continue;
				}
				if (IsHotEdge(active))
				{
					AddOutPt(active, active.Top);
				}
				UpdateEdgeIntoAEL(active);
				if (IsHorizontal(active))
				{
					PushHorz(active);
				}
			}
			else
			{
				active.CurX = TopX(active, y);
			}
			active = active.NextInAEL;
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private Active DoMaxima(Active ae)
	{
		Active prevInAEL = ae.PrevInAEL;
		Active nextInAEL = ae.NextInAEL;
		if (IsOpenEnd(ae))
		{
			if (IsHotEdge(ae))
			{
				AddOutPt(ae, ae.Top);
			}
			if (!IsHorizontal(ae))
			{
				if (IsHotEdge(ae))
				{
					if (IsFront(ae))
					{
						ae.Outrec.FrontEdge = null;
					}
					else
					{
						ae.Outrec.BackEdge = null;
					}
					ae.Outrec = null;
				}
				DeleteFromAEL(ae);
			}
			return nextInAEL;
		}
		Active maximaPair = GetMaximaPair(ae);
		if (maximaPair == null)
		{
			return nextInAEL;
		}
		if (IsJoined(ae))
		{
			Split(ae, ae.Top);
		}
		if (IsJoined(maximaPair))
		{
			Split(maximaPair, maximaPair.Top);
		}
		while (nextInAEL != maximaPair)
		{
			IntersectEdges(ae, nextInAEL, ae.Top);
			SwapPositionsInAEL(ae, nextInAEL);
			nextInAEL = ae.NextInAEL;
		}
		if (IsOpen(ae))
		{
			if (IsHotEdge(ae))
			{
				AddLocalMaxPoly(ae, maximaPair, ae.Top);
			}
			DeleteFromAEL(maximaPair);
			DeleteFromAEL(ae);
			if (prevInAEL == null)
			{
				return actives;
			}
			return prevInAEL.NextInAEL;
		}
		if (IsHotEdge(ae))
		{
			AddLocalMaxPoly(ae, maximaPair, ae.Top);
		}
		DeleteFromAEL(ae);
		DeleteFromAEL(maximaPair);
		if (prevInAEL == null)
		{
			return actives;
		}
		return prevInAEL.NextInAEL;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static void TrimHorz(Active horzEdge, bool preserveCollinear)
	{
		bool flag = false;
		Vector2 point = NextVertex(horzEdge).Point;
		while (point.Y == horzEdge.Top.Y && (!preserveCollinear || point.X < horzEdge.Top.X == horzEdge.Bot.X < horzEdge.Top.X))
		{
			horzEdge.VertexTop = NextVertex(horzEdge);
			horzEdge.Top = point;
			flag = true;
			if (IsMaxima(horzEdge))
			{
				break;
			}
			point = NextVertex(horzEdge).Point;
		}
		if (flag)
		{
			SetDx(horzEdge);
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void AddToHorzSegList(OutPt op)
	{
		if (!op.OutRec.IsOpen)
		{
			horzSegList.Add(new HorzSegment(op));
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static OutPt GetLastOp(Active hotEdge)
	{
		OutRec outrec = hotEdge.Outrec;
		if (hotEdge != outrec.FrontEdge)
		{
			return outrec.Pts.Next;
		}
		return outrec.Pts;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static Vertex GetCurrYMaximaVertex_Open(Active ae)
	{
		Vertex vertex = ae.VertexTop;
		if (ae.WindDx > 0)
		{
			while (vertex.Next.Point.Y == vertex.Point.Y && (vertex.Flags & (VertexFlags.OpenEnd | VertexFlags.LocalMax)) == 0)
			{
				vertex = vertex.Next;
			}
		}
		else
		{
			while (vertex.Prev.Point.Y == vertex.Point.Y && (vertex.Flags & (VertexFlags.OpenEnd | VertexFlags.LocalMax)) == 0)
			{
				vertex = vertex.Prev;
			}
		}
		if (!IsMaxima(vertex))
		{
			vertex = null;
		}
		return vertex;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static Vertex GetCurrYMaximaVertex(Active ae)
	{
		Vertex vertex = ae.VertexTop;
		if (ae.WindDx > 0)
		{
			while (vertex.Next.Point.Y == vertex.Point.Y)
			{
				vertex = vertex.Next;
			}
		}
		else
		{
			while (vertex.Prev.Point.Y == vertex.Point.Y)
			{
				vertex = vertex.Prev;
			}
		}
		if (!IsMaxima(vertex))
		{
			vertex = null;
		}
		return vertex;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsVerySmallTriangle(OutPt op)
	{
		if (op.Next.Next == op.Prev)
		{
			if (!PtsReallyClose(op.Prev.Point, op.Next.Point) && !PtsReallyClose(op.Point, op.Next.Point))
			{
				return PtsReallyClose(op.Point, op.Prev.Point);
			}
			return true;
		}
		return false;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsValidClosedPath(OutPt op)
	{
		if (op != null && op.Next != op)
		{
			if (op.Next == op.Prev)
			{
				return !IsVerySmallTriangle(op);
			}
			return true;
		}
		return false;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static OutPt DisposeOutPt(OutPt op)
	{
		OutPt result = ((op.Next == op) ? null : op.Next);
		op.Prev.Next = op.Next;
		op.Next.Prev = op.Prev;
		return result;
	}

	private void ProcessHorzJoins()
	{
		foreach (HorzJoin horzJoin in horzJoinList)
		{
			OutRec realOutRec = GetRealOutRec(horzJoin.Op1.OutRec);
			OutRec realOutRec2 = GetRealOutRec(horzJoin.Op2.OutRec);
			OutPt next = horzJoin.Op1.Next;
			OutPt prev = horzJoin.Op2.Prev;
			horzJoin.Op1.Next = horzJoin.Op2;
			horzJoin.Op2.Prev = horzJoin.Op1;
			next.Prev = prev;
			prev.Next = next;
			if (realOutRec == realOutRec2)
			{
				realOutRec2 = new OutRec
				{
					Pts = next
				};
				FixOutRecPts(realOutRec2);
				if (realOutRec.Pts.OutRec == realOutRec2)
				{
					realOutRec.Pts = horzJoin.Op1;
					realOutRec.Pts.OutRec = realOutRec;
				}
				realOutRec2.Owner = realOutRec;
				outrecList.Add(realOutRec2);
			}
			else
			{
				realOutRec2.Pts = null;
				realOutRec2.Owner = realOutRec;
			}
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool PtsReallyClose(Vector2 pt1, Vector2 pt2)
	{
		if (Math.Abs(pt1.X - pt2.X) < 2f)
		{
			return Math.Abs(pt1.Y - pt2.Y) < 2f;
		}
		return false;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void CleanCollinear(OutRec outrec)
	{
		outrec = GetRealOutRec(outrec);
		if (outrec == null || outrec.IsOpen)
		{
			return;
		}
		if (!IsValidClosedPath(outrec.Pts))
		{
			outrec.Pts = null;
			return;
		}
		OutPt outPt = outrec.Pts;
		OutPt outPt2 = outPt;
		do
		{
			if (ClipperUtils.CrossProduct(outPt2.Prev.Point, outPt2.Point, outPt2.Next.Point) == 0f && (outPt2.Point == outPt2.Prev.Point || outPt2.Point == outPt2.Next.Point || !PreserveCollinear || ClipperUtils.DotProduct(outPt2.Prev.Point, outPt2.Point, outPt2.Next.Point) < 0f))
			{
				if (outPt2 == outrec.Pts)
				{
					outrec.Pts = outPt2.Prev;
				}
				outPt2 = DisposeOutPt(outPt2);
				if (!IsValidClosedPath(outPt2))
				{
					outrec.Pts = null;
					return;
				}
				outPt = outPt2;
			}
			else
			{
				outPt2 = outPt2.Next;
			}
		}
		while (outPt2 != outPt);
		FixSelfIntersects(outrec);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void DoSplitOp(OutRec outrec, OutPt splitOp)
	{
		OutPt prev = splitOp.Prev;
		OutPt next = splitOp.Next.Next;
		outrec.Pts = prev;
		ClipperUtils.GetIntersectPoint(prev.Point, splitOp.Point, splitOp.Next.Point, next.Point, out var ip);
		float num = Area(prev);
		float num2 = Math.Abs(num);
		if (num2 < 2f)
		{
			outrec.Pts = null;
			return;
		}
		float num3 = AreaTriangle(ip, splitOp.Point, splitOp.Next.Point);
		float num4 = Math.Abs(num3);
		if (ip == prev.Point || ip == next.Point)
		{
			next.Prev = prev;
			prev.Next = next;
		}
		else
		{
			OutPt next2 = (next.Prev = new OutPt(ip, outrec)
			{
				Prev = prev,
				Next = next
			});
			prev.Next = next2;
		}
		if (num4 > 1f && (num4 > num2 || num3 > 0f == num > 0f))
		{
			OutRec outRec = NewOutRec();
			outRec.Owner = outrec.Owner;
			splitOp.OutRec = outRec;
			splitOp.Next.OutRec = outRec;
			OutPt next3 = (splitOp.Prev = (outRec.Pts = new OutPt(ip, outRec)
			{
				Prev = splitOp.Next,
				Next = splitOp
			}));
			splitOp.Next.Next = next3;
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void FixSelfIntersects(OutRec outrec)
	{
		OutPt outPt = outrec.Pts;
		while (outPt.Prev != outPt.Next.Next)
		{
			if (ClipperUtils.SegsIntersect(outPt.Prev.Point, outPt.Point, outPt.Next.Point, outPt.Next.Next.Point))
			{
				DoSplitOp(outrec, outPt);
				if (outrec.Pts == null)
				{
					break;
				}
				outPt = outrec.Pts;
			}
			else
			{
				outPt = outPt.Next;
				if (outPt == outrec.Pts)
				{
					break;
				}
			}
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void Reset()
	{
		if (!isSortedMinimaList)
		{
			minimaList.Sort(default(LocMinSorter));
			isSortedMinimaList = true;
		}
		scanlineList.Capacity = minimaList.Count;
		for (int num = minimaList.Count - 1; num >= 0; num--)
		{
			scanlineList.Add(minimaList[num].Vertex.Point.Y);
		}
		currentBotY = 0f;
		currentLocMin = 0;
		actives = null;
		flaggedHorizontal = null;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void InsertScanline(float y)
	{
		int num = scanlineList.BinarySearch(y);
		if (num < 0)
		{
			num = ~num;
			scanlineList.Insert(num, y);
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private bool PopScanline(out float y)
	{
		int num = scanlineList.Count - 1;
		if (num < 0)
		{
			y = 0f;
			return false;
		}
		y = scanlineList[num];
		scanlineList.RemoveAt(num--);
		while (num >= 0 && y == scanlineList[num])
		{
			scanlineList.RemoveAt(num--);
		}
		return true;
	}

	private void InsertLocalMinimaIntoAEL(float botY)
	{
		while (HasLocMinAtY(botY))
		{
			LocalMinima localMin = PopLocalMinima();
			Active ae;
			if ((localMin.Vertex.Flags & VertexFlags.OpenStart) != VertexFlags.None)
			{
				ae = null;
			}
			else
			{
				ae = new Active
				{
					Bot = localMin.Vertex.Point,
					CurX = localMin.Vertex.Point.X,
					WindDx = -1,
					VertexTop = localMin.Vertex.Prev,
					Top = localMin.Vertex.Prev.Point,
					Outrec = null,
					LocalMin = localMin
				};
				SetDx(ae);
			}
			Active ae2;
			if ((localMin.Vertex.Flags & VertexFlags.OpenEnd) != VertexFlags.None)
			{
				ae2 = null;
			}
			else
			{
				ae2 = new Active
				{
					Bot = localMin.Vertex.Point,
					CurX = localMin.Vertex.Point.X,
					WindDx = 1,
					VertexTop = localMin.Vertex.Next,
					Top = localMin.Vertex.Next.Point,
					Outrec = null,
					LocalMin = localMin
				};
				SetDx(ae2);
			}
			if (ae != null && ae2 != null)
			{
				if (IsHorizontal(ae))
				{
					if (IsHeadingRightHorz(ae))
					{
						SwapActives(ref ae, ref ae2);
					}
				}
				else if (IsHorizontal(ae2))
				{
					if (IsHeadingLeftHorz(ae2))
					{
						SwapActives(ref ae, ref ae2);
					}
				}
				else if (ae.Dx < ae2.Dx)
				{
					SwapActives(ref ae, ref ae2);
				}
			}
			else if (ae == null)
			{
				ae = ae2;
				ae2 = null;
			}
			ae.IsLeftBound = true;
			InsertLeftEdge(ae);
			bool flag;
			if (IsOpen(ae))
			{
				SetWindCountForOpenPathEdge(ae);
				flag = IsContributingOpen(ae);
			}
			else
			{
				SetWindCountForClosedPathEdge(ae);
				flag = IsContributingClosed(ae);
			}
			if (ae2 != null)
			{
				ae2.WindCount = ae.WindCount;
				ae2.WindCount2 = ae.WindCount2;
				InsertRightEdge(ae, ae2);
				if (flag)
				{
					AddLocalMinPoly(ae, ae2, ae.Bot, isNew: true);
					if (!IsHorizontal(ae))
					{
						CheckJoinLeft(ae, ae.Bot);
					}
				}
				while (ae2.NextInAEL != null && IsValidAelOrder(ae2.NextInAEL, ae2))
				{
					IntersectEdges(ae2, ae2.NextInAEL, ae2.Bot);
					SwapPositionsInAEL(ae2, ae2.NextInAEL);
				}
				if (IsHorizontal(ae2))
				{
					PushHorz(ae2);
				}
				else
				{
					CheckJoinRight(ae2, ae2.Bot);
					InsertScanline(ae2.Top.Y);
				}
			}
			else if (flag)
			{
				StartOpenPath(ae, ae.Bot);
			}
			if (IsHorizontal(ae))
			{
				PushHorz(ae);
			}
			else
			{
				InsertScanline(ae.Top.Y);
			}
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static Active ExtractFromSEL(Active ae)
	{
		Active nextInSEL = ae.NextInSEL;
		if (nextInSEL != null)
		{
			nextInSEL.PrevInSEL = ae.PrevInSEL;
		}
		ae.PrevInSEL.NextInSEL = nextInSEL;
		return nextInSEL;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static void Insert1Before2InSEL(Active ae1, Active ae2)
	{
		ae1.PrevInSEL = ae2.PrevInSEL;
		if (ae1.PrevInSEL != null)
		{
			ae1.PrevInSEL.NextInSEL = ae1;
		}
		ae1.NextInSEL = ae2;
		ae2.PrevInSEL = ae1;
	}

	private bool BuildIntersectList(float topY)
	{
		if (actives == null || actives.NextInAEL == null)
		{
			return false;
		}
		AdjustCurrXAndCopyToSEL(topY);
		Active active = flaggedHorizontal;
		while (active.Jump != null)
		{
			Active active2 = null;
			while (active?.Jump != null)
			{
				Active active3 = active;
				Active active4 = active.Jump;
				Active active5 = active4;
				Active active6 = (active.Jump = active4.Jump);
				while (active != active5 && active4 != active6)
				{
					if (active4.CurX < active.CurX)
					{
						Active prevInSEL = active4.PrevInSEL;
						while (true)
						{
							AddNewIntersectNode(prevInSEL, active4, topY);
							if (prevInSEL == active)
							{
								break;
							}
							prevInSEL = prevInSEL.PrevInSEL;
						}
						prevInSEL = active4;
						active4 = ExtractFromSEL(prevInSEL);
						active5 = active4;
						Insert1Before2InSEL(prevInSEL, active);
						if (active == active3)
						{
							active3 = prevInSEL;
							active3.Jump = active6;
							if (active2 == null)
							{
								flaggedHorizontal = active3;
							}
							else
							{
								active2.Jump = active3;
							}
						}
					}
					else
					{
						active = active.NextInSEL;
					}
				}
				active2 = active3;
				active = active6;
			}
			active = flaggedHorizontal;
		}
		return intersectList.Count > 0;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void ProcessIntersectList()
	{
		intersectList.Sort(default(IntersectListSort));
		for (int i = 0; i < intersectList.Count; i++)
		{
			if (!EdgesAdjacentInAEL(intersectList[i]))
			{
				int j;
				for (j = i + 1; !EdgesAdjacentInAEL(intersectList[j]); j++)
				{
				}
				List<IntersectNode> list = intersectList;
				int index = j;
				List<IntersectNode> list2 = intersectList;
				int index2 = i;
				IntersectNode value = intersectList[i];
				IntersectNode value2 = intersectList[j];
				list[index] = value;
				list2[index2] = value2;
			}
			IntersectNode intersectNode = intersectList[i];
			IntersectEdges(intersectNode.Edge1, intersectNode.Edge2, intersectNode.Point);
			SwapPositionsInAEL(intersectNode.Edge1, intersectNode.Edge2);
			intersectNode.Edge1.CurX = intersectNode.Point.X;
			intersectNode.Edge2.CurX = intersectNode.Point.X;
			CheckJoinLeft(intersectNode.Edge2, intersectNode.Point, checkCurrX: true);
			CheckJoinRight(intersectNode.Edge1, intersectNode.Point, checkCurrX: true);
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void SwapPositionsInAEL(Active ae1, Active ae2)
	{
		Active nextInAEL = ae2.NextInAEL;
		if (nextInAEL != null)
		{
			nextInAEL.PrevInAEL = ae1;
		}
		Active prevInAEL = ae1.PrevInAEL;
		if (prevInAEL != null)
		{
			prevInAEL.NextInAEL = ae2;
		}
		ae2.PrevInAEL = prevInAEL;
		ae2.NextInAEL = ae1;
		ae1.PrevInAEL = ae2;
		ae1.NextInAEL = nextInAEL;
		if (ae2.PrevInAEL == null)
		{
			actives = ae2;
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool ResetHorzDirection(Active horz, Vertex vertexMax, out float leftX, out float rightX)
	{
		if (horz.Bot.X == horz.Top.X)
		{
			leftX = horz.CurX;
			rightX = horz.CurX;
			Active nextInAEL = horz.NextInAEL;
			while (nextInAEL != null && nextInAEL.VertexTop != vertexMax)
			{
				nextInAEL = nextInAEL.NextInAEL;
			}
			return nextInAEL != null;
		}
		if (horz.CurX < horz.Top.X)
		{
			leftX = horz.CurX;
			rightX = horz.Top.X;
			return true;
		}
		leftX = horz.Top.X;
		rightX = horz.CurX;
		return false;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool HorzIsSpike(Active horz)
	{
		Vector2 point = NextVertex(horz).Point;
		return horz.Bot.X < horz.Top.X != horz.Top.X < point.X;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static Active FindEdgeWithMatchingLocMin(Active e)
	{
		Active active;
		for (active = e.NextInAEL; active != null; active = ((IsHorizontal(active) || !(e.Bot != active.Bot)) ? active.NextInAEL : null))
		{
			if (active.LocalMin == e.LocalMin)
			{
				return active;
			}
		}
		for (active = e.PrevInAEL; active != null; active = active.PrevInAEL)
		{
			if (active.LocalMin == e.LocalMin)
			{
				return active;
			}
			if (!IsHorizontal(active) && e.Bot != active.Bot)
			{
				return null;
			}
		}
		return active;
	}

	private OutPt IntersectEdges(Active ae1, Active ae2, Vector2 pt)
	{
		OutPt result = null;
		if (hasOpenPaths && (IsOpen(ae1) || IsOpen(ae2)))
		{
			if (IsOpen(ae1) && IsOpen(ae2))
			{
				return null;
			}
			if (IsOpen(ae2))
			{
				SwapActives(ref ae1, ref ae2);
			}
			if (IsJoined(ae2))
			{
				Split(ae2, pt);
			}
			if (clipType == ClippingOperation.Union)
			{
				if (!IsHotEdge(ae2))
				{
					return null;
				}
			}
			else if (ae2.LocalMin.Polytype == ClippingType.Subject)
			{
				return null;
			}
			switch (fillRule)
			{
			case FillRule.Positive:
				if (ae2.WindCount != 1)
				{
					return null;
				}
				break;
			case FillRule.Negative:
				if (ae2.WindCount != -1)
				{
					return null;
				}
				break;
			default:
				if (Math.Abs(ae2.WindCount) != 1)
				{
					return null;
				}
				break;
			}
			if (IsHotEdge(ae1))
			{
				result = AddOutPt(ae1, pt);
				if (IsFront(ae1))
				{
					ae1.Outrec.FrontEdge = null;
				}
				else
				{
					ae1.Outrec.BackEdge = null;
				}
				ae1.Outrec = null;
			}
			else if (pt == ae1.LocalMin.Vertex.Point && !IsOpenEnd(ae1.LocalMin.Vertex))
			{
				Active active = FindEdgeWithMatchingLocMin(ae1);
				if (active != null && IsHotEdge(active))
				{
					ae1.Outrec = active.Outrec;
					if (ae1.WindDx > 0)
					{
						SetSides(active.Outrec, ae1, active);
					}
					else
					{
						SetSides(active.Outrec, active, ae1);
					}
					return active.Outrec.Pts;
				}
				result = StartOpenPath(ae1, pt);
			}
			else
			{
				result = StartOpenPath(ae1, pt);
			}
			return result;
		}
		if (IsJoined(ae1))
		{
			Split(ae1, pt);
		}
		if (IsJoined(ae2))
		{
			Split(ae2, pt);
		}
		int windCount;
		if (ae1.LocalMin.Polytype == ae2.LocalMin.Polytype)
		{
			if (fillRule == FillRule.EvenOdd)
			{
				windCount = ae1.WindCount;
				ae1.WindCount = ae2.WindCount;
				ae2.WindCount = windCount;
			}
			else
			{
				if (ae1.WindCount + ae2.WindDx == 0)
				{
					ae1.WindCount = -ae1.WindCount;
				}
				else
				{
					ae1.WindCount += ae2.WindDx;
				}
				if (ae2.WindCount - ae1.WindDx == 0)
				{
					ae2.WindCount = -ae2.WindCount;
				}
				else
				{
					ae2.WindCount -= ae1.WindDx;
				}
			}
		}
		else
		{
			if (fillRule != FillRule.EvenOdd)
			{
				ae1.WindCount2 += ae2.WindDx;
			}
			else
			{
				ae1.WindCount2 = ((ae1.WindCount2 == 0) ? 1 : 0);
			}
			if (fillRule != FillRule.EvenOdd)
			{
				ae2.WindCount2 -= ae1.WindDx;
			}
			else
			{
				ae2.WindCount2 = ((ae2.WindCount2 == 0) ? 1 : 0);
			}
		}
		int num;
		switch (fillRule)
		{
		case FillRule.Positive:
			windCount = ae1.WindCount;
			num = ae2.WindCount;
			break;
		case FillRule.Negative:
			windCount = -ae1.WindCount;
			num = -ae2.WindCount;
			break;
		default:
			windCount = Math.Abs(ae1.WindCount);
			num = Math.Abs(ae2.WindCount);
			break;
		}
		bool flag = (uint)windCount <= 1u;
		bool flag2 = flag;
		flag = (uint)num <= 1u;
		bool flag3 = flag;
		if ((!IsHotEdge(ae1) && !flag2) || (!IsHotEdge(ae2) && !flag3))
		{
			return null;
		}
		if (IsHotEdge(ae1) && IsHotEdge(ae2))
		{
			if ((windCount != 0 && windCount != 1) || (num != 0 && num != 1) || (ae1.LocalMin.Polytype != ae2.LocalMin.Polytype && clipType != ClippingOperation.Xor))
			{
				result = AddLocalMaxPoly(ae1, ae2, pt);
			}
			else if (IsFront(ae1) || ae1.Outrec == ae2.Outrec)
			{
				result = AddLocalMaxPoly(ae1, ae2, pt);
				AddLocalMinPoly(ae1, ae2, pt);
			}
			else
			{
				result = AddOutPt(ae1, pt);
				AddOutPt(ae2, pt);
				SwapOutrecs(ae1, ae2);
			}
		}
		else if (IsHotEdge(ae1))
		{
			result = AddOutPt(ae1, pt);
			SwapOutrecs(ae1, ae2);
		}
		else if (IsHotEdge(ae2))
		{
			result = AddOutPt(ae2, pt);
			SwapOutrecs(ae1, ae2);
		}
		else
		{
			float num2;
			float num3;
			switch (fillRule)
			{
			case FillRule.Positive:
				num2 = ae1.WindCount2;
				num3 = ae2.WindCount2;
				break;
			case FillRule.Negative:
				num2 = -ae1.WindCount2;
				num3 = -ae2.WindCount2;
				break;
			default:
				num2 = Math.Abs(ae1.WindCount2);
				num3 = Math.Abs(ae2.WindCount2);
				break;
			}
			if (!IsSamePolyType(ae1, ae2))
			{
				result = AddLocalMinPoly(ae1, ae2, pt);
			}
			else if (windCount == 1 && num == 1)
			{
				result = null;
				switch (clipType)
				{
				case ClippingOperation.Union:
					if (num2 > 0f && num3 > 0f)
					{
						return null;
					}
					result = AddLocalMinPoly(ae1, ae2, pt);
					break;
				case ClippingOperation.Difference:
					if ((GetPolyType(ae1) == ClippingType.Clip && num2 > 0f && num3 > 0f) || (GetPolyType(ae1) == ClippingType.Subject && num2 <= 0f && num3 <= 0f))
					{
						result = AddLocalMinPoly(ae1, ae2, pt);
					}
					break;
				case ClippingOperation.Xor:
					result = AddLocalMinPoly(ae1, ae2, pt);
					break;
				default:
					if (num2 <= 0f || num3 <= 0f)
					{
						return null;
					}
					result = AddLocalMinPoly(ae1, ae2, pt);
					break;
				}
			}
		}
		return result;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void DeleteFromAEL(Active ae)
	{
		Active prevInAEL = ae.PrevInAEL;
		Active nextInAEL = ae.NextInAEL;
		if (prevInAEL != null || nextInAEL != null || ae == actives)
		{
			if (prevInAEL != null)
			{
				prevInAEL.NextInAEL = nextInAEL;
			}
			else
			{
				actives = nextInAEL;
			}
			if (nextInAEL != null)
			{
				nextInAEL.PrevInAEL = prevInAEL;
			}
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void AdjustCurrXAndCopyToSEL(float topY)
	{
		for (Active active = (flaggedHorizontal = actives); active != null; active = active.NextInAEL)
		{
			active.PrevInSEL = active.PrevInAEL;
			active.NextInSEL = active.NextInAEL;
			active.Jump = active.NextInSEL;
			if (active.JoinWith == JoinWith.Left)
			{
				active.CurX = active.PrevInAEL.CurX;
			}
			else
			{
				active.CurX = TopX(active, topY);
			}
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private bool HasLocMinAtY(float y)
	{
		if (currentLocMin < minimaList.Count)
		{
			return minimaList[currentLocMin].Vertex.Point.Y == y;
		}
		return false;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private LocalMinima PopLocalMinima()
	{
		return minimaList[currentLocMin++];
	}

	private void AddPathsToVertexList(PathsF paths, ClippingType polytype, bool isOpen)
	{
		int num = 0;
		for (int i = 0; i < paths.Count; i++)
		{
			PathF pathF = paths[i];
			num += pathF.Count;
		}
		vertexList.Capacity = vertexList.Count + num;
		foreach (PathF path in paths)
		{
			Vertex vertex = null;
			Vertex vertex2 = null;
			foreach (Vector2 item in path)
			{
				if (vertex == null)
				{
					vertex = new Vertex(item, VertexFlags.None, null);
					vertexList.Add(vertex);
					vertex2 = vertex;
				}
				else if (vertex2.Point != item)
				{
					Vertex vertex3 = new Vertex(item, VertexFlags.None, vertex2);
					vertexList.Add(vertex3);
					vertex2.Next = vertex3;
					vertex2 = vertex3;
				}
			}
			if (vertex2 == null || vertex2.Prev == null)
			{
				continue;
			}
			if (!isOpen && vertex2.Point == vertex.Point)
			{
				vertex2 = vertex2.Prev;
			}
			vertex2.Next = vertex;
			vertex.Prev = vertex2;
			if (!isOpen && vertex2.Next == vertex2)
			{
				continue;
			}
			bool flag;
			if (isOpen)
			{
				Vertex vertex3 = vertex.Next;
				while (vertex3 != vertex && vertex3.Point.Y == vertex.Point.Y)
				{
					vertex3 = vertex3.Next;
				}
				flag = vertex3.Point.Y <= vertex.Point.Y;
				if (flag)
				{
					vertex.Flags = VertexFlags.OpenStart;
					AddLocMin(vertex, polytype, isOpen: true);
				}
				else
				{
					vertex.Flags = VertexFlags.OpenStart | VertexFlags.LocalMax;
				}
			}
			else
			{
				vertex2 = vertex.Prev;
				while (vertex2 != vertex && vertex2.Point.Y == vertex.Point.Y)
				{
					vertex2 = vertex2.Prev;
				}
				if (vertex2 == vertex)
				{
					continue;
				}
				flag = vertex2.Point.Y > vertex.Point.Y;
			}
			bool flag2 = flag;
			vertex2 = vertex;
			for (Vertex vertex3 = vertex.Next; vertex3 != vertex; vertex3 = vertex3.Next)
			{
				if (vertex3.Point.Y > vertex2.Point.Y && flag)
				{
					vertex2.Flags |= VertexFlags.LocalMax;
					flag = false;
				}
				else if (vertex3.Point.Y < vertex2.Point.Y && !flag)
				{
					flag = true;
					AddLocMin(vertex2, polytype, isOpen);
				}
				vertex2 = vertex3;
			}
			if (isOpen)
			{
				vertex2.Flags |= VertexFlags.OpenEnd;
				if (flag)
				{
					vertex2.Flags |= VertexFlags.LocalMax;
				}
				else
				{
					AddLocMin(vertex2, polytype, isOpen);
				}
			}
			else if (flag != flag2)
			{
				if (flag2)
				{
					AddLocMin(vertex2, polytype, isOpen: false);
				}
				else
				{
					vertex2.Flags |= VertexFlags.LocalMax;
				}
			}
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void AddLocMin(Vertex vert, ClippingType polytype, bool isOpen)
	{
		if ((vert.Flags & VertexFlags.LocalMin) == 0)
		{
			vert.Flags |= VertexFlags.LocalMin;
			LocalMinima item = new LocalMinima(vert, polytype, isOpen);
			minimaList.Add(item);
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void PushHorz(Active ae)
	{
		ae.NextInSEL = flaggedHorizontal;
		flaggedHorizontal = ae;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private bool PopHorz(out Active ae)
	{
		ae = flaggedHorizontal;
		if (flaggedHorizontal == null)
		{
			return false;
		}
		flaggedHorizontal = flaggedHorizontal.NextInSEL;
		return true;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private OutPt AddLocalMinPoly(Active ae1, Active ae2, Vector2 pt, bool isNew = false)
	{
		OutRec outRec = (ae2.Outrec = (ae1.Outrec = NewOutRec()));
		if (IsOpen(ae1))
		{
			outRec.Owner = null;
			outRec.IsOpen = true;
			if (ae1.WindDx > 0)
			{
				SetSides(outRec, ae1, ae2);
			}
			else
			{
				SetSides(outRec, ae2, ae1);
			}
		}
		else
		{
			outRec.IsOpen = false;
			Active prevHotEdge = GetPrevHotEdge(ae1);
			if (prevHotEdge != null)
			{
				outRec.Owner = prevHotEdge.Outrec;
				if (OutrecIsAscending(prevHotEdge) == isNew)
				{
					SetSides(outRec, ae2, ae1);
				}
				else
				{
					SetSides(outRec, ae1, ae2);
				}
			}
			else
			{
				outRec.Owner = null;
				if (isNew)
				{
					SetSides(outRec, ae1, ae2);
				}
				else
				{
					SetSides(outRec, ae2, ae1);
				}
			}
		}
		return outRec.Pts = new OutPt(pt, outRec);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static void SetDx(Active ae)
	{
		ae.Dx = GetDx(ae.Bot, ae.Top);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static float GetDx(Vector2 pt1, Vector2 pt2)
	{
		float num = pt2.Y - pt1.Y;
		if (num != 0f)
		{
			return (pt2.X - pt1.X) / num;
		}
		if (pt2.X > pt1.X)
		{
			return float.NegativeInfinity;
		}
		return float.PositiveInfinity;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static float TopX(Active ae, float currentY)
	{
		Vector2 top = ae.Top;
		Vector2 bot = ae.Bot;
		if (currentY == top.Y || top.X == bot.X)
		{
			return top.X;
		}
		if (currentY == bot.Y)
		{
			return bot.X;
		}
		return bot.X + ae.Dx * (currentY - bot.Y);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsHorizontal(Active ae)
	{
		return ae.Top.Y == ae.Bot.Y;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsHeadingRightHorz(Active ae)
	{
		return float.IsNegativeInfinity(ae.Dx);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsHeadingLeftHorz(Active ae)
	{
		return float.IsPositiveInfinity(ae.Dx);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static void SwapActives(ref Active ae1, ref Active ae2)
	{
		Active active = ae1;
		Active active2 = ae2;
		ae2 = active;
		ae1 = active2;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static ClippingType GetPolyType(Active ae)
	{
		return ae.LocalMin.Polytype;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsSamePolyType(Active ae1, Active ae2)
	{
		return ae1.LocalMin.Polytype == ae2.LocalMin.Polytype;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private bool IsContributingClosed(Active ae)
	{
		switch (fillRule)
		{
		case FillRule.Positive:
			if (ae.WindCount != 1)
			{
				return false;
			}
			break;
		case FillRule.Negative:
			if (ae.WindCount != -1)
			{
				return false;
			}
			break;
		case FillRule.NonZero:
			if (Math.Abs(ae.WindCount) != 1)
			{
				return false;
			}
			break;
		}
		switch (clipType)
		{
		case ClippingOperation.Intersection:
			return fillRule switch
			{
				FillRule.Positive => ae.WindCount2 > 0, 
				FillRule.Negative => ae.WindCount2 < 0, 
				_ => ae.WindCount2 != 0, 
			};
		case ClippingOperation.Union:
			return fillRule switch
			{
				FillRule.Positive => ae.WindCount2 <= 0, 
				FillRule.Negative => ae.WindCount2 >= 0, 
				_ => ae.WindCount2 == 0, 
			};
		case ClippingOperation.Difference:
		{
			bool flag = fillRule switch
			{
				FillRule.Positive => ae.WindCount2 <= 0, 
				FillRule.Negative => ae.WindCount2 >= 0, 
				_ => ae.WindCount2 == 0, 
			};
			if (GetPolyType(ae) != ClippingType.Subject)
			{
				return !flag;
			}
			return flag;
		}
		case ClippingOperation.Xor:
			return true;
		default:
			return false;
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private bool IsContributingOpen(Active ae)
	{
		bool flag;
		bool flag2;
		switch (fillRule)
		{
		case FillRule.Positive:
			flag = ae.WindCount > 0;
			flag2 = ae.WindCount2 > 0;
			break;
		case FillRule.Negative:
			flag = ae.WindCount < 0;
			flag2 = ae.WindCount2 < 0;
			break;
		default:
			flag = ae.WindCount != 0;
			flag2 = ae.WindCount2 != 0;
			break;
		}
		return clipType switch
		{
			ClippingOperation.Intersection => flag2, 
			ClippingOperation.Union => !flag && !flag2, 
			_ => !flag2, 
		};
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void SetWindCountForClosedPathEdge(Active ae)
	{
		Active prevInAEL = ae.PrevInAEL;
		ClippingType polyType = GetPolyType(ae);
		while (prevInAEL != null && (GetPolyType(prevInAEL) != polyType || IsOpen(prevInAEL)))
		{
			prevInAEL = prevInAEL.PrevInAEL;
		}
		if (prevInAEL == null)
		{
			ae.WindCount = ae.WindDx;
			prevInAEL = actives;
		}
		else if (fillRule == FillRule.EvenOdd)
		{
			ae.WindCount = ae.WindDx;
			ae.WindCount2 = prevInAEL.WindCount2;
			prevInAEL = prevInAEL.NextInAEL;
		}
		else
		{
			if (prevInAEL.WindCount * prevInAEL.WindDx < 0)
			{
				if (Math.Abs(prevInAEL.WindCount) > 1)
				{
					if (prevInAEL.WindDx * ae.WindDx < 0)
					{
						ae.WindCount = prevInAEL.WindCount;
					}
					else
					{
						ae.WindCount = prevInAEL.WindCount + ae.WindDx;
					}
				}
				else
				{
					ae.WindCount = (IsOpen(ae) ? 1 : ae.WindDx);
				}
			}
			else if (prevInAEL.WindDx * ae.WindDx < 0)
			{
				ae.WindCount = prevInAEL.WindCount;
			}
			else
			{
				ae.WindCount = prevInAEL.WindCount + ae.WindDx;
			}
			ae.WindCount2 = prevInAEL.WindCount2;
			prevInAEL = prevInAEL.NextInAEL;
		}
		if (fillRule == FillRule.EvenOdd)
		{
			while (prevInAEL != ae)
			{
				if (GetPolyType(prevInAEL) != polyType && !IsOpen(prevInAEL))
				{
					ae.WindCount2 = ((ae.WindCount2 == 0) ? 1 : 0);
				}
				prevInAEL = prevInAEL.NextInAEL;
			}
			return;
		}
		while (prevInAEL != ae)
		{
			if (GetPolyType(prevInAEL) != polyType && !IsOpen(prevInAEL))
			{
				ae.WindCount2 += prevInAEL.WindDx;
			}
			prevInAEL = prevInAEL.NextInAEL;
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void SetWindCountForOpenPathEdge(Active ae)
	{
		Active nextInAEL = actives;
		if (fillRule == FillRule.EvenOdd)
		{
			int num = 0;
			int num2 = 0;
			while (nextInAEL != ae)
			{
				if (GetPolyType(nextInAEL) == ClippingType.Clip)
				{
					num2++;
				}
				else if (!IsOpen(nextInAEL))
				{
					num++;
				}
				nextInAEL = nextInAEL.NextInAEL;
			}
			ae.WindCount = (IsOdd(num) ? 1 : 0);
			ae.WindCount2 = (IsOdd(num2) ? 1 : 0);
			return;
		}
		while (nextInAEL != ae)
		{
			if (GetPolyType(nextInAEL) == ClippingType.Clip)
			{
				ae.WindCount2 += nextInAEL.WindDx;
			}
			else if (!IsOpen(nextInAEL))
			{
				ae.WindCount += nextInAEL.WindDx;
			}
			nextInAEL = nextInAEL.NextInAEL;
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsValidAelOrder(Active resident, Active newcomer)
	{
		if (newcomer.CurX != resident.CurX)
		{
			return newcomer.CurX > resident.CurX;
		}
		float num = ClipperUtils.CrossProduct(resident.Top, newcomer.Bot, newcomer.Top);
		if (num != 0f)
		{
			return num < 0f;
		}
		if (!IsMaxima(resident) && resident.Top.Y > newcomer.Top.Y)
		{
			return ClipperUtils.CrossProduct(newcomer.Bot, resident.Top, NextVertex(resident).Point) <= 0f;
		}
		if (!IsMaxima(newcomer) && newcomer.Top.Y > resident.Top.Y)
		{
			return ClipperUtils.CrossProduct(newcomer.Bot, newcomer.Top, NextVertex(newcomer).Point) >= 0f;
		}
		float y = newcomer.Bot.Y;
		bool isLeftBound = newcomer.IsLeftBound;
		if (resident.Bot.Y != y || resident.LocalMin.Vertex.Point.Y != y)
		{
			return newcomer.IsLeftBound;
		}
		if (resident.IsLeftBound != isLeftBound)
		{
			return isLeftBound;
		}
		if (ClipperUtils.CrossProduct(PrevPrevVertex(resident).Point, resident.Bot, resident.Top) == 0f)
		{
			return true;
		}
		return ClipperUtils.CrossProduct(PrevPrevVertex(resident).Point, newcomer.Bot, PrevPrevVertex(newcomer).Point) > 0f == isLeftBound;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void InsertLeftEdge(Active ae)
	{
		if (actives == null)
		{
			ae.PrevInAEL = null;
			ae.NextInAEL = null;
			actives = ae;
			return;
		}
		if (!IsValidAelOrder(actives, ae))
		{
			ae.PrevInAEL = null;
			ae.NextInAEL = actives;
			actives.PrevInAEL = ae;
			actives = ae;
			return;
		}
		Active nextInAEL = actives;
		while (nextInAEL.NextInAEL != null && IsValidAelOrder(nextInAEL.NextInAEL, ae))
		{
			nextInAEL = nextInAEL.NextInAEL;
		}
		if (nextInAEL.JoinWith == JoinWith.Right)
		{
			nextInAEL = nextInAEL.NextInAEL;
		}
		ae.NextInAEL = nextInAEL.NextInAEL;
		if (nextInAEL.NextInAEL != null)
		{
			nextInAEL.NextInAEL.PrevInAEL = ae;
		}
		ae.PrevInAEL = nextInAEL;
		nextInAEL.NextInAEL = ae;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static void InsertRightEdge(Active ae, Active ae2)
	{
		ae2.NextInAEL = ae.NextInAEL;
		if (ae.NextInAEL != null)
		{
			ae.NextInAEL.PrevInAEL = ae2;
		}
		ae2.PrevInAEL = ae;
		ae.NextInAEL = ae2;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static Vertex NextVertex(Active ae)
	{
		if (ae.WindDx > 0)
		{
			return ae.VertexTop.Next;
		}
		return ae.VertexTop.Prev;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static Vertex PrevPrevVertex(Active ae)
	{
		if (ae.WindDx > 0)
		{
			return ae.VertexTop.Prev.Prev;
		}
		return ae.VertexTop.Next.Next;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsMaxima(Vertex vertex)
	{
		return (vertex.Flags & VertexFlags.LocalMax) != 0;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsMaxima(Active ae)
	{
		return IsMaxima(ae.VertexTop);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static Active GetMaximaPair(Active ae)
	{
		for (Active nextInAEL = ae.NextInAEL; nextInAEL != null; nextInAEL = nextInAEL.NextInAEL)
		{
			if (nextInAEL.VertexTop == ae.VertexTop)
			{
				return nextInAEL;
			}
		}
		return null;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsOdd(int val)
	{
		return (val & 1) != 0;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsHotEdge(Active ae)
	{
		return ae.Outrec != null;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsOpen(Active ae)
	{
		return ae.LocalMin.IsOpen;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsOpenEnd(Active ae)
	{
		if (ae.LocalMin.IsOpen)
		{
			return IsOpenEnd(ae.VertexTop);
		}
		return false;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsOpenEnd(Vertex v)
	{
		return (v.Flags & (VertexFlags.OpenStart | VertexFlags.OpenEnd)) != 0;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static Active GetPrevHotEdge(Active ae)
	{
		Active prevInAEL = ae.PrevInAEL;
		while (prevInAEL != null && (IsOpen(prevInAEL) || !IsHotEdge(prevInAEL)))
		{
			prevInAEL = prevInAEL.PrevInAEL;
		}
		return prevInAEL;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static void JoinOutrecPaths(Active ae1, Active ae2)
	{
		OutPt pts = ae1.Outrec.Pts;
		OutPt pts2 = ae2.Outrec.Pts;
		OutPt next = pts.Next;
		OutPt next2 = pts2.Next;
		if (IsFront(ae1))
		{
			next2.Prev = pts;
			pts.Next = next2;
			pts2.Next = next;
			next.Prev = pts2;
			ae1.Outrec.Pts = pts2;
			ae1.Outrec.FrontEdge = ae2.Outrec.FrontEdge;
			if (ae1.Outrec.FrontEdge != null)
			{
				ae1.Outrec.FrontEdge.Outrec = ae1.Outrec;
			}
		}
		else
		{
			next.Prev = pts2;
			pts2.Next = next;
			pts.Next = next2;
			next2.Prev = pts;
			ae1.Outrec.BackEdge = ae2.Outrec.BackEdge;
			if (ae1.Outrec.BackEdge != null)
			{
				ae1.Outrec.BackEdge.Outrec = ae1.Outrec;
			}
		}
		ae2.Outrec.FrontEdge = null;
		ae2.Outrec.BackEdge = null;
		ae2.Outrec.Pts = null;
		SetOwner(ae2.Outrec, ae1.Outrec);
		if (IsOpenEnd(ae1))
		{
			ae2.Outrec.Pts = ae1.Outrec.Pts;
			ae1.Outrec.Pts = null;
		}
		ae1.Outrec = null;
		ae2.Outrec = null;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static OutPt AddOutPt(Active ae, Vector2 pt)
	{
		OutRec outrec = ae.Outrec;
		bool flag = IsFront(ae);
		OutPt pts = outrec.Pts;
		OutPt next = pts.Next;
		if (flag && pt == pts.Point)
		{
			return pts;
		}
		if (!flag && pt == next.Point)
		{
			return next;
		}
		OutPt outPt = (next.Prev = new OutPt(pt, outrec));
		outPt.Prev = pts;
		outPt.Next = next;
		pts.Next = outPt;
		if (flag)
		{
			outrec.Pts = outPt;
		}
		return outPt;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private OutRec NewOutRec()
	{
		OutRec outRec = new OutRec
		{
			Idx = outrecList.Count
		};
		outrecList.Add(outRec);
		return outRec;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private OutPt StartOpenPath(Active ae, Vector2 pt)
	{
		OutRec outRec = NewOutRec();
		outRec.IsOpen = true;
		if (ae.WindDx > 0)
		{
			outRec.FrontEdge = ae;
			outRec.BackEdge = null;
		}
		else
		{
			outRec.FrontEdge = null;
			outRec.BackEdge = ae;
		}
		ae.Outrec = outRec;
		return outRec.Pts = new OutPt(pt, outRec);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void UpdateEdgeIntoAEL(Active ae)
	{
		ae.Bot = ae.Top;
		ae.VertexTop = NextVertex(ae);
		ae.Top = ae.VertexTop.Point;
		ae.CurX = ae.Bot.X;
		SetDx(ae);
		if (IsJoined(ae))
		{
			Split(ae, ae.Bot);
		}
		if (!IsHorizontal(ae))
		{
			InsertScanline(ae.Top.Y);
			CheckJoinLeft(ae, ae.Bot);
			CheckJoinRight(ae, ae.Bot, checkCurrX: true);
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static void SetSides(OutRec outrec, Active startEdge, Active endEdge)
	{
		outrec.FrontEdge = startEdge;
		outrec.BackEdge = endEdge;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static void SwapOutrecs(Active ae1, Active ae2)
	{
		OutRec outrec = ae1.Outrec;
		OutRec outrec2 = ae2.Outrec;
		if (outrec == outrec2)
		{
			OutRec outRec = outrec;
			Active frontEdge = outrec.FrontEdge;
			Active backEdge = outrec.BackEdge;
			outrec.BackEdge = frontEdge;
			outRec.FrontEdge = backEdge;
			return;
		}
		if (outrec != null)
		{
			if (ae1 == outrec.FrontEdge)
			{
				outrec.FrontEdge = ae2;
			}
			else
			{
				outrec.BackEdge = ae2;
			}
		}
		if (outrec2 != null)
		{
			if (ae2 == outrec2.FrontEdge)
			{
				outrec2.FrontEdge = ae1;
			}
			else
			{
				outrec2.BackEdge = ae1;
			}
		}
		ae1.Outrec = outrec2;
		ae2.Outrec = outrec;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static void SetOwner(OutRec outrec, OutRec newOwner)
	{
		while (newOwner.Owner != null && newOwner.Owner.Pts == null)
		{
			newOwner.Owner = newOwner.Owner.Owner;
		}
		OutRec outRec = newOwner;
		while (outRec != null && outRec != outrec)
		{
			outRec = outRec.Owner;
		}
		if (outRec != null)
		{
			newOwner.Owner = outrec.Owner;
		}
		outrec.Owner = newOwner;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static float Area(OutPt op)
	{
		float num = 0f;
		OutPt outPt = op;
		do
		{
			num += (outPt.Prev.Point.Y + outPt.Point.Y) * (outPt.Prev.Point.X - outPt.Point.X);
			outPt = outPt.Next;
		}
		while (outPt != op);
		return num * 0.5f;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static float AreaTriangle(Vector2 pt1, Vector2 pt2, Vector2 pt3)
	{
		return (pt3.Y + pt1.Y) * (pt3.X - pt1.X) + (pt1.Y + pt2.Y) * (pt1.X - pt2.X) + (pt2.Y + pt3.Y) * (pt2.X - pt3.X);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static OutRec GetRealOutRec(OutRec outRec)
	{
		while (outRec != null && outRec.Pts == null)
		{
			outRec = outRec.Owner;
		}
		return outRec;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static void UncoupleOutRec(Active ae)
	{
		OutRec outrec = ae.Outrec;
		if (outrec != null)
		{
			outrec.FrontEdge.Outrec = null;
			outrec.BackEdge.Outrec = null;
			outrec.FrontEdge = null;
			outrec.BackEdge = null;
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool OutrecIsAscending(Active hotEdge)
	{
		return hotEdge == hotEdge.Outrec.FrontEdge;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static void SwapFrontBackSides(OutRec outrec)
	{
		Active frontEdge = outrec.FrontEdge;
		Active backEdge = outrec.BackEdge;
		outrec.BackEdge = frontEdge;
		outrec.FrontEdge = backEdge;
		outrec.Pts = outrec.Pts.Next;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool EdgesAdjacentInAEL(IntersectNode inode)
	{
		if (inode.Edge1.NextInAEL != inode.Edge2)
		{
			return inode.Edge1.PrevInAEL == inode.Edge2;
		}
		return true;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void CheckJoinLeft(Active e, Vector2 pt, bool checkCurrX = false)
	{
		Active prevInAEL = e.PrevInAEL;
		if (prevInAEL == null || IsOpen(e) || IsOpen(prevInAEL) || !IsHotEdge(e) || !IsHotEdge(prevInAEL) || ((pt.Y < e.Top.Y + 2f || pt.Y < prevInAEL.Top.Y + 2f) && (e.Bot.Y > pt.Y || prevInAEL.Bot.Y > pt.Y)))
		{
			return;
		}
		if (checkCurrX)
		{
			if ((double)ClipperUtils.PerpendicDistFromLineSqrd(pt, prevInAEL.Bot, prevInAEL.Top) > 0.25)
			{
				return;
			}
		}
		else if (e.CurX != prevInAEL.CurX)
		{
			return;
		}
		if (ClipperUtils.CrossProduct(e.Top, pt, prevInAEL.Top) == 0f)
		{
			if (e.Outrec.Idx == prevInAEL.Outrec.Idx)
			{
				AddLocalMaxPoly(prevInAEL, e, pt);
			}
			else if (e.Outrec.Idx < prevInAEL.Outrec.Idx)
			{
				JoinOutrecPaths(e, prevInAEL);
			}
			else
			{
				JoinOutrecPaths(prevInAEL, e);
			}
			prevInAEL.JoinWith = JoinWith.Right;
			e.JoinWith = JoinWith.Left;
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private void CheckJoinRight(Active e, Vector2 pt, bool checkCurrX = false)
	{
		Active nextInAEL = e.NextInAEL;
		if (IsOpen(e) || !IsHotEdge(e) || IsJoined(e) || nextInAEL == null || IsOpen(nextInAEL) || !IsHotEdge(nextInAEL) || ((pt.Y < e.Top.Y + 2f || pt.Y < nextInAEL.Top.Y + 2f) && (e.Bot.Y > pt.Y || nextInAEL.Bot.Y > pt.Y)))
		{
			return;
		}
		if (checkCurrX)
		{
			if ((double)ClipperUtils.PerpendicDistFromLineSqrd(pt, nextInAEL.Bot, nextInAEL.Top) > 0.25)
			{
				return;
			}
		}
		else if (e.CurX != nextInAEL.CurX)
		{
			return;
		}
		if (ClipperUtils.CrossProduct(e.Top, pt, nextInAEL.Top) == 0f)
		{
			if (e.Outrec.Idx == nextInAEL.Outrec.Idx)
			{
				AddLocalMaxPoly(e, nextInAEL, pt);
			}
			else if (e.Outrec.Idx < nextInAEL.Outrec.Idx)
			{
				JoinOutrecPaths(e, nextInAEL);
			}
			else
			{
				JoinOutrecPaths(nextInAEL, e);
			}
			e.JoinWith = JoinWith.Right;
			nextInAEL.JoinWith = JoinWith.Left;
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static void FixOutRecPts(OutRec outrec)
	{
		OutPt outPt = outrec.Pts;
		do
		{
			outPt.OutRec = outrec;
			outPt = outPt.Next;
		}
		while (outPt != outrec.Pts);
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private OutPt AddLocalMaxPoly(Active ae1, Active ae2, Vector2 pt)
	{
		if (IsJoined(ae1))
		{
			Split(ae1, pt);
		}
		if (IsJoined(ae2))
		{
			Split(ae2, pt);
		}
		if (IsFront(ae1) == IsFront(ae2))
		{
			if (IsOpenEnd(ae1))
			{
				SwapFrontBackSides(ae1.Outrec);
			}
			else
			{
				if (!IsOpenEnd(ae2))
				{
					return null;
				}
				SwapFrontBackSides(ae2.Outrec);
			}
		}
		OutPt outPt = AddOutPt(ae1, pt);
		if (ae1.Outrec == ae2.Outrec)
		{
			ae1.Outrec.Pts = outPt;
			UncoupleOutRec(ae1);
		}
		else if (IsOpen(ae1))
		{
			if (ae1.WindDx < 0)
			{
				JoinOutrecPaths(ae1, ae2);
			}
			else
			{
				JoinOutrecPaths(ae2, ae1);
			}
		}
		else if (ae1.Outrec.Idx < ae2.Outrec.Idx)
		{
			JoinOutrecPaths(ae1, ae2);
		}
		else
		{
			JoinOutrecPaths(ae2, ae1);
		}
		return outPt;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsJoined(Active e)
	{
		return e.JoinWith != JoinWith.None;
	}

	private void Split(Active e, Vector2 currPt)
	{
		if (e.JoinWith == JoinWith.Right)
		{
			e.JoinWith = JoinWith.None;
			e.NextInAEL.JoinWith = JoinWith.None;
			AddLocalMinPoly(e, e.NextInAEL, currPt, isNew: true);
		}
		else
		{
			e.JoinWith = JoinWith.None;
			e.PrevInAEL.JoinWith = JoinWith.None;
			AddLocalMinPoly(e.PrevInAEL, e, currPt, isNew: true);
		}
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private static bool IsFront(Active ae)
	{
		return ae == ae.Outrec.FrontEdge;
	}
}
