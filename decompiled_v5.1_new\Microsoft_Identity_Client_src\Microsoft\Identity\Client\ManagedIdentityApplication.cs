using System;
using Microsoft.Identity.Client.ApiConfig.Executors;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.ManagedIdentity;

namespace Microsoft.Identity.Client;

public sealed class ManagedIdentityApplication : ApplicationBase, IManagedIdentityApplication, IApplicationBase
{
	internal ITokenCacheInternal AppTokenCacheInternal { get; }

	internal ManagedIdentityApplication(ApplicationConfiguration configuration)
		: base(configuration)
	{
		ApplicationBase.GuardMobileFrameworks();
		AppTokenCacheInternal = configuration.AppTokenCacheInternalForTest ?? new TokenCache(base.ServiceBundle, isApplicationTokenCache: true);
		base.ServiceBundle.ApplicationLogger.Verbose(() => $"ManagedIdentityApplication {configuration.GetHashCode()} created");
	}

	public AcquireTokenForManagedIdentityParameterBuilder AcquireTokenForManagedIdentity(string resource)
	{
		if (string.IsNullOrEmpty(resource))
		{
			throw new ArgumentNullException("resource");
		}
		return AcquireTokenForManagedIdentityParameterBuilder.Create(ClientExecutorFactory.CreateManagedIdentityExecutor(this), resource);
	}

	public static ManagedIdentitySource GetManagedIdentitySource()
	{
		return ManagedIdentityClient.s_managedIdentitySourceDetected.Value;
	}
}
