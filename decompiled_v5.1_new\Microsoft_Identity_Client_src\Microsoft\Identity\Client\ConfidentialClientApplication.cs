using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Executors;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Internal.Requests;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;

namespace Microsoft.Identity.Client;

public sealed class ConfidentialClientApplication : ClientApplicationBase, IConfidentialClientApplication, IClientApplicationBase, IApplicationBase, IConfidentialClientApplicationWithCertificate, IByRefreshToken, ILongRunningWebApi
{
	public const string AttemptRegionDiscovery = "TryAutoDetect";

	public ITokenCache AppTokenCache => AppTokenCacheInternal;

	public X509Certificate2 Certificate { get; }

	internal ITokenCacheInternal AppTokenCacheInternal { get; }

	internal ConfidentialClientApplication(ApplicationConfiguration configuration)
		: base(configuration)
	{
		ApplicationBase.GuardMobileFrameworks();
		AppTokenCacheInternal = configuration.AppTokenCacheInternalForTest ?? new TokenCache(base.ServiceBundle, isApplicationTokenCache: true);
		Certificate = configuration.ClientCredentialCertificate;
		base.ServiceBundle.ApplicationLogger.Verbose(() => $"ConfidentialClientApplication {configuration.GetHashCode()} created");
	}

	public AcquireTokenByAuthorizationCodeParameterBuilder AcquireTokenByAuthorizationCode(IEnumerable<string> scopes, string authorizationCode)
	{
		return AcquireTokenByAuthorizationCodeParameterBuilder.Create(ClientExecutorFactory.CreateConfidentialClientExecutor(this), scopes, authorizationCode);
	}

	public AcquireTokenForClientParameterBuilder AcquireTokenForClient(IEnumerable<string> scopes)
	{
		return AcquireTokenForClientParameterBuilder.Create(ClientExecutorFactory.CreateConfidentialClientExecutor(this), scopes);
	}

	public AcquireTokenOnBehalfOfParameterBuilder AcquireTokenOnBehalfOf(IEnumerable<string> scopes, UserAssertion userAssertion)
	{
		if (userAssertion == null)
		{
			base.ServiceBundle.ApplicationLogger.Error("User assertion for OBO request should not be null");
			throw new MsalClientException("user_assertion_null");
		}
		return AcquireTokenOnBehalfOfParameterBuilder.Create(ClientExecutorFactory.CreateConfidentialClientExecutor(this), scopes, userAssertion);
	}

	public AcquireTokenOnBehalfOfParameterBuilder InitiateLongRunningProcessInWebApi(IEnumerable<string> scopes, string userToken, ref string longRunningProcessSessionKey)
	{
		if (string.IsNullOrEmpty(userToken))
		{
			throw new ArgumentNullException("userToken");
		}
		UserAssertion userAssertion = new UserAssertion(userToken);
		if (string.IsNullOrEmpty(longRunningProcessSessionKey))
		{
			longRunningProcessSessionKey = userAssertion.AssertionHash;
		}
		return AcquireTokenOnBehalfOfParameterBuilder.Create(ClientExecutorFactory.CreateConfidentialClientExecutor(this), scopes, userAssertion, longRunningProcessSessionKey);
	}

	public AcquireTokenOnBehalfOfParameterBuilder AcquireTokenInLongRunningProcess(IEnumerable<string> scopes, string longRunningProcessSessionKey)
	{
		if (string.IsNullOrEmpty(longRunningProcessSessionKey))
		{
			throw new ArgumentNullException("longRunningProcessSessionKey");
		}
		return AcquireTokenOnBehalfOfParameterBuilder.Create(ClientExecutorFactory.CreateConfidentialClientExecutor(this), scopes, longRunningProcessSessionKey);
	}

	public async Task<bool> StopLongRunningProcessInWebApiAsync(string longRunningProcessSessionKey, CancellationToken cancellationToken = default(CancellationToken))
	{
		if (string.IsNullOrEmpty(longRunningProcessSessionKey))
		{
			throw new ArgumentNullException("longRunningProcessSessionKey");
		}
		Guid correlationId = Guid.NewGuid();
		RequestContext requestContext = CreateRequestContext(correlationId, cancellationToken);
		requestContext.ApiEvent = new ApiEvent(correlationId);
		requestContext.ApiEvent.ApiId = ApiEvent.ApiIds.RemoveOboTokens;
		Authority initialAuthority = await Microsoft.Identity.Client.Instance.Authority.CreateAuthorityForRequestAsync(requestContext, null).ConfigureAwait(continueOnCapturedContext: false);
		AuthenticationRequestParameters requestParameters = new AuthenticationRequestParameters(base.ServiceBundle, base.UserTokenCacheInternal, new AcquireTokenCommonParameters
		{
			ApiId = requestContext.ApiEvent.ApiId
		}, requestContext, initialAuthority);
		if (base.UserTokenCacheInternal != null)
		{
			return await base.UserTokenCacheInternal.StopLongRunningOboProcessAsync(longRunningProcessSessionKey, requestParameters).ConfigureAwait(continueOnCapturedContext: false);
		}
		return false;
	}

	public GetAuthorizationRequestUrlParameterBuilder GetAuthorizationRequestUrl(IEnumerable<string> scopes)
	{
		return GetAuthorizationRequestUrlParameterBuilder.Create(ClientExecutorFactory.CreateConfidentialClientExecutor(this), scopes);
	}

	AcquireTokenByRefreshTokenParameterBuilder IByRefreshToken.AcquireTokenByRefreshToken(IEnumerable<string> scopes, string refreshToken)
	{
		return AcquireTokenByRefreshTokenParameterBuilder.Create(ClientExecutorFactory.CreateClientApplicationBaseExecutor(this), scopes, refreshToken);
	}

	internal override async Task<AuthenticationRequestParameters> CreateRequestParametersAsync(AcquireTokenCommonParameters commonParameters, RequestContext requestContext, ITokenCacheInternal cache)
	{
		return await base.CreateRequestParametersAsync(commonParameters, requestContext, cache).ConfigureAwait(continueOnCapturedContext: false);
	}

	[Obsolete("Use ConfidentialClientApplicationBuilder instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public ConfidentialClientApplication(string clientId, string redirectUri, ClientCredential clientCredential, TokenCache userTokenCache, TokenCache appTokenCache)
		: this(ConfidentialClientApplicationBuilder.Create(clientId).BuildConfiguration())
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use ConfidentialClientApplicationBuilder instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public ConfidentialClientApplication(string clientId, string authority, string redirectUri, ClientCredential clientCredential, TokenCache userTokenCache, TokenCache appTokenCache)
		: this(ConfidentialClientApplicationBuilder.Create(clientId).BuildConfiguration())
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenOnBehalfOf instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenOnBehalfOfAsync(IEnumerable<string> scopes, UserAssertion userAssertion)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenOnBehalfOf instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenOnBehalfOfAsync(IEnumerable<string> scopes, UserAssertion userAssertion, string authority)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenOnBehalfOf instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	Task<AuthenticationResult> IConfidentialClientApplicationWithCertificate.AcquireTokenOnBehalfOfWithCertificateAsync(IEnumerable<string> scopes, UserAssertion userAssertion)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenOnBehalfOf instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	Task<AuthenticationResult> IConfidentialClientApplicationWithCertificate.AcquireTokenOnBehalfOfWithCertificateAsync(IEnumerable<string> scopes, UserAssertion userAssertion, string authority)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenByAuthorizationCode instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenByAuthorizationCodeAsync(string authorizationCode, IEnumerable<string> scopes)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenForClient instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenForClientAsync(IEnumerable<string> scopes)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenForClient instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<AuthenticationResult> AcquireTokenForClientAsync(IEnumerable<string> scopes, bool forceRefresh)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenForClient instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	Task<AuthenticationResult> IConfidentialClientApplicationWithCertificate.AcquireTokenForClientWithCertificateAsync(IEnumerable<string> scopes)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenForClient instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	Task<AuthenticationResult> IConfidentialClientApplicationWithCertificate.AcquireTokenForClientWithCertificateAsync(IEnumerable<string> scopes, bool forceRefresh)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use AcquireTokenByRefreshToken instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	Task<AuthenticationResult> IByRefreshToken.AcquireTokenByRefreshTokenAsync(IEnumerable<string> scopes, string refreshToken)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use GetAuthorizationRequestUrl instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<Uri> GetAuthorizationRequestUrlAsync(IEnumerable<string> scopes, string loginHint, string extraQueryParameters)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use GetAuthorizationRequestUrl instead. See https://aka.ms/msal-net-3-breaking-changes. ", true)]
	public Task<Uri> GetAuthorizationRequestUrlAsync(IEnumerable<string> scopes, string redirectUri, string loginHint, string extraQueryParameters, IEnumerable<string> extraScopesToConsent, string authority)
	{
		throw MigrationHelper.CreateMsalNet3BreakingChangesException();
	}
}
