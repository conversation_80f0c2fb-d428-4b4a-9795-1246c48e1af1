using System;
using System.Collections.Generic;
using System.Collections.Immutable;

namespace Microsoft.Identity.Client.Utils;

internal static class CollectionHelpers
{
	public static IReadOnlyList<T> GetEmptyReadOnlyList<T>()
	{
		return Array.Empty<T>();
	}

	public static List<T> GetEmptyList<T>()
	{
		return new List<T>();
	}

	public static IReadOnlyDictionary<TKey, TValue> GetEmptyDictionary<TKey, TValue>()
	{
		return ImmutableDictionary<TKey, TValue>.Empty;
	}
}
