using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Globalization;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.IdentityModel.Json.Linq;
using Microsoft.IdentityModel.Logging;
using Microsoft.IdentityModel.Tokens;

namespace Microsoft.IdentityModel.JsonWebTokens;

public class JwtTokenUtilities
{
	private const string _unrecognizedEncodedToken = "UnrecognizedEncodedToken";

	public static Regex RegexJws = new Regex("^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*$", RegexOptions.Compiled | RegexOptions.CultureInvariant, TimeSpan.FromMilliseconds(100.0));

	public static Regex RegexJwe = new Regex("^[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]*\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+\\.[A-Za-z0-9-_]+$", RegexOptions.Compiled | RegexOptions.CultureInvariant, TimeSpan.FromMilliseconds(100.0));

	internal static IList<string> DefaultHeaderParameters = new List<string> { "alg", "kid", "x5t", "enc", "zip" };

	public static string CreateEncodedSignature(string input, SigningCredentials signingCredentials)
	{
		if (input == null)
		{
			throw LogHelper.LogArgumentNullException("input");
		}
		if (signingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("signingCredentials");
		}
		CryptoProviderFactory cryptoProviderFactory = signingCredentials.CryptoProviderFactory ?? signingCredentials.Key.CryptoProviderFactory;
		SignatureProvider signatureProvider = cryptoProviderFactory.CreateForSigning(signingCredentials.Key, signingCredentials.Algorithm);
		if (signatureProvider == null)
		{
			throw LogHelper.LogExceptionMessage(new InvalidOperationException(LogHelper.FormatInvariant("IDX10637: CryptoProviderFactory.CreateForSigning returned null for key: '{0}', signatureAlgorithm: '{1}'.", (signingCredentials.Key == null) ? "Null" : signingCredentials.Key.ToString(), LogHelper.MarkAsNonPII(signingCredentials.Algorithm))));
		}
		try
		{
			LogHelper.LogVerbose("IDX14200: Creating raw signature using the signature credentials.");
			return Base64UrlEncoder.Encode(signatureProvider.Sign(Encoding.UTF8.GetBytes(input)));
		}
		finally
		{
			cryptoProviderFactory.ReleaseSignatureProvider(signatureProvider);
		}
	}

	public static string CreateEncodedSignature(string input, SigningCredentials signingCredentials, bool cacheProvider)
	{
		if (input == null)
		{
			throw LogHelper.LogArgumentNullException("input");
		}
		if (signingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("signingCredentials");
		}
		CryptoProviderFactory cryptoProviderFactory = signingCredentials.CryptoProviderFactory ?? signingCredentials.Key.CryptoProviderFactory;
		SignatureProvider signatureProvider = cryptoProviderFactory.CreateForSigning(signingCredentials.Key, signingCredentials.Algorithm, cacheProvider);
		if (signatureProvider == null)
		{
			throw LogHelper.LogExceptionMessage(new InvalidOperationException(LogHelper.FormatInvariant("IDX10637: CryptoProviderFactory.CreateForSigning returned null for key: '{0}', signatureAlgorithm: '{1}'.", (signingCredentials.Key == null) ? "Null" : signingCredentials.Key.ToString(), LogHelper.MarkAsNonPII(signingCredentials.Algorithm))));
		}
		try
		{
			LogHelper.LogVerbose(LogHelper.FormatInvariant("IDX14201: Creating raw signature using the signature credentials. Caching SignatureProvider: '{0}'.", LogHelper.MarkAsNonPII(cacheProvider)));
			return Base64UrlEncoder.Encode(signatureProvider.Sign(Encoding.UTF8.GetBytes(input)));
		}
		finally
		{
			cryptoProviderFactory.ReleaseSignatureProvider(signatureProvider);
		}
	}

	internal static string DecompressToken(byte[] tokenBytes, string algorithm, int maximumDeflateSize)
	{
		if (tokenBytes == null)
		{
			throw LogHelper.LogArgumentNullException("tokenBytes");
		}
		if (string.IsNullOrEmpty(algorithm))
		{
			throw LogHelper.LogArgumentNullException("algorithm");
		}
		if (!CompressionProviderFactory.Default.IsSupportedAlgorithm(algorithm))
		{
			throw LogHelper.LogExceptionMessage(new NotSupportedException(LogHelper.FormatInvariant("IDX10682: Compression algorithm '{0}' is not supported.", LogHelper.MarkAsNonPII(algorithm))));
		}
		byte[] array = CompressionProviderFactory.Default.CreateCompressionProvider(algorithm, maximumDeflateSize).Decompress(tokenBytes);
		if (array == null)
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenDecompressionFailedException(LogHelper.FormatInvariant("IDX10679: Failed to decompress using algorithm '{0}'.", LogHelper.MarkAsNonPII(algorithm))));
		}
		return Encoding.UTF8.GetString(array);
	}

	internal static string DecryptJwtToken(SecurityToken securityToken, TokenValidationParameters validationParameters, JwtTokenDecryptionParameters decryptionParameters)
	{
		if (validationParameters == null)
		{
			throw LogHelper.LogArgumentNullException("validationParameters");
		}
		if (decryptionParameters == null)
		{
			throw LogHelper.LogArgumentNullException("decryptionParameters");
		}
		bool decryptionSucceeded = false;
		bool algorithmNotSupportedByCryptoProvider = false;
		byte[] array = null;
		StringBuilder stringBuilder = new StringBuilder();
		StringBuilder stringBuilder2 = new StringBuilder();
		string text = null;
		foreach (SecurityKey key in decryptionParameters.Keys)
		{
			CryptoProviderFactory cryptoProviderFactory = validationParameters.CryptoProviderFactory ?? key.CryptoProviderFactory;
			if (cryptoProviderFactory == null)
			{
				LogHelper.LogWarning("IDX10607: Decryption skipping key: '{0}', both validationParameters.CryptoProviderFactory and key.CryptoProviderFactory are null.", key);
				continue;
			}
			try
			{
				if (securityToken is JsonWebToken jsonWebToken)
				{
					if (!cryptoProviderFactory.IsSupportedAlgorithm(jsonWebToken.Enc, key))
					{
						algorithmNotSupportedByCryptoProvider = true;
						LogHelper.LogWarning("IDX10611: Decryption failed. Encryption is not supported for: Algorithm: '{0}', SecurityKey: '{1}'.", LogHelper.MarkAsNonPII(decryptionParameters.Enc), key);
						continue;
					}
					Validators.ValidateAlgorithm(jsonWebToken.Enc, key, securityToken, validationParameters);
					array = DecryptToken(cryptoProviderFactory, key, jsonWebToken.Enc, jsonWebToken.CipherTextBytes, jsonWebToken.HeaderAsciiBytes, jsonWebToken.InitializationVectorBytes, jsonWebToken.AuthenticationTagBytes);
					text = jsonWebToken.Zip;
					decryptionSucceeded = true;
				}
				else
				{
					if (!cryptoProviderFactory.IsSupportedAlgorithm(decryptionParameters.Enc, key))
					{
						algorithmNotSupportedByCryptoProvider = true;
						LogHelper.LogWarning("IDX10611: Decryption failed. Encryption is not supported for: Algorithm: '{0}', SecurityKey: '{1}'.", LogHelper.MarkAsNonPII(decryptionParameters.Enc), key);
						continue;
					}
					Validators.ValidateAlgorithm(decryptionParameters.Enc, key, securityToken, validationParameters);
					array = DecryptToken(cryptoProviderFactory, key, decryptionParameters.Enc, decryptionParameters.CipherTextBytes, decryptionParameters.HeaderAsciiBytes, decryptionParameters.InitializationVectorBytes, decryptionParameters.AuthenticationTagBytes);
					text = decryptionParameters.Zip;
					decryptionSucceeded = true;
				}
			}
			catch (Exception ex)
			{
				stringBuilder.AppendLine(ex.ToString());
				goto IL_01a4;
			}
			break;
			IL_01a4:
			if (key != null)
			{
				stringBuilder2.AppendLine(key.ToString());
			}
		}
		ValidateDecryption(decryptionParameters, decryptionSucceeded, algorithmNotSupportedByCryptoProvider, stringBuilder, stringBuilder2);
		try
		{
			if (string.IsNullOrEmpty(text))
			{
				return Encoding.UTF8.GetString(array);
			}
			return decryptionParameters.DecompressionFunction(array, text, decryptionParameters.MaximumDeflateSize);
		}
		catch (Exception inner)
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenDecompressionFailedException(LogHelper.FormatInvariant("IDX10679: Failed to decompress using algorithm '{0}'.", text), inner));
		}
	}

	private static void ValidateDecryption(JwtTokenDecryptionParameters decryptionParameters, bool decryptionSucceeded, bool algorithmNotSupportedByCryptoProvider, StringBuilder exceptionStrings, StringBuilder keysAttempted)
	{
		if (!decryptionSucceeded && keysAttempted.Length > 0)
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenDecryptionFailedException(LogHelper.FormatInvariant("IDX10603: Decryption failed. Keys tried: '{0}'.\nExceptions caught:\n '{1}'.\ntoken: '{2}'", keysAttempted, exceptionStrings, LogHelper.MarkAsSecurityArtifact(decryptionParameters.EncodedToken, SafeLogJwtToken))));
		}
		if (!decryptionSucceeded && algorithmNotSupportedByCryptoProvider)
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenDecryptionFailedException(LogHelper.FormatInvariant("IDX10619: Decryption failed. Algorithm: '{0}'. Either the Encryption Algorithm: '{1}' or none of the Security Keys are supported by the CryptoProviderFactory.", LogHelper.MarkAsNonPII(decryptionParameters.Alg), LogHelper.MarkAsNonPII(decryptionParameters.Enc))));
		}
		if (!decryptionSucceeded)
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenDecryptionFailedException(LogHelper.FormatInvariant("IDX10609: Decryption failed. No Keys tried: token: '{0}'.", LogHelper.MarkAsSecurityArtifact(decryptionParameters.EncodedToken, SafeLogJwtToken))));
		}
	}

	private static byte[] DecryptToken(CryptoProviderFactory cryptoProviderFactory, SecurityKey key, string encAlg, byte[] ciphertext, byte[] headerAscii, byte[] initializationVector, byte[] authenticationTag)
	{
		using AuthenticatedEncryptionProvider authenticatedEncryptionProvider = cryptoProviderFactory.CreateAuthenticatedEncryptionProvider(key, encAlg);
		if (authenticatedEncryptionProvider == null)
		{
			throw LogHelper.LogExceptionMessage(new InvalidOperationException(LogHelper.FormatInvariant("IDX10610: Decryption failed. Could not create decryption provider. Key: '{0}', Algorithm: '{1}'.", key, LogHelper.MarkAsNonPII(encAlg))));
		}
		return authenticatedEncryptionProvider.Decrypt(ciphertext, headerAscii, initializationVector, authenticationTag);
	}

	public static byte[] GenerateKeyBytes(int sizeInBits)
	{
		byte[] array = null;
		if (sizeInBits != 256 && sizeInBits != 384 && sizeInBits != 512)
		{
			throw LogHelper.LogExceptionMessage(new ArgumentException("IDX10401: Invalid requested key size. Valid key sizes are: 256, 384, and 512.", "sizeInBits"));
		}
		using Aes aes = Aes.Create();
		int num = sizeInBits >> 4;
		array = new byte[num << 1];
		aes.KeySize = sizeInBits >> 1;
		aes.GenerateKey();
		Array.Copy(aes.Key, array, num);
		aes.GenerateKey();
		Array.Copy(aes.Key, 0, array, num, num);
		return array;
	}

	internal static SecurityKey GetSecurityKey(EncryptingCredentials encryptingCredentials, CryptoProviderFactory cryptoProviderFactory, IDictionary<string, object> additionalHeaderClaims, out byte[] wrappedKey)
	{
		SecurityKey securityKey = null;
		KeyWrapProvider keyWrapProvider = null;
		wrappedKey = null;
		if ("dir".Equals(encryptingCredentials.Alg))
		{
			if (!cryptoProviderFactory.IsSupportedAlgorithm(encryptingCredentials.Enc, encryptingCredentials.Key))
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenEncryptionFailedException(LogHelper.FormatInvariant("IDX10615: Encryption failed. No support for: Algorithm: '{0}', SecurityKey: '{1}'.", LogHelper.MarkAsNonPII(encryptingCredentials.Enc), encryptingCredentials.Key)));
			}
			securityKey = encryptingCredentials.Key;
		}
		else if (SupportedAlgorithms.EcdsaWrapAlgorithms.Contains(encryptingCredentials.Alg))
		{
			string apu = null;
			string apv = null;
			if (additionalHeaderClaims != null && additionalHeaderClaims.Count > 0)
			{
				if (additionalHeaderClaims.TryGetValue("apu", out var value))
				{
					apu = value?.ToString();
				}
				if (additionalHeaderClaims.TryGetValue("apv", out var value2))
				{
					apv = value2?.ToString();
				}
			}
			EcdhKeyExchangeProvider ecdhKeyExchangeProvider = new EcdhKeyExchangeProvider(encryptingCredentials.Key as ECDsaSecurityKey, encryptingCredentials.KeyExchangePublicKey, encryptingCredentials.Alg, encryptingCredentials.Enc);
			SecurityKey key = ecdhKeyExchangeProvider.GenerateKdf(apu, apv);
			keyWrapProvider = cryptoProviderFactory.CreateKeyWrapProvider(key, ecdhKeyExchangeProvider.GetEncryptionAlgorithm());
			if ("A128KW".Equals(keyWrapProvider.Algorithm, StringComparison.Ordinal))
			{
				securityKey = new SymmetricSecurityKey(GenerateKeyBytes(256));
			}
			else if ("A192KW".Equals(keyWrapProvider.Algorithm, StringComparison.Ordinal))
			{
				securityKey = new SymmetricSecurityKey(GenerateKeyBytes(384));
			}
			else
			{
				if (!"A256KW".Equals(keyWrapProvider.Algorithm, StringComparison.Ordinal))
				{
					throw LogHelper.LogExceptionMessage(new SecurityTokenEncryptionFailedException(LogHelper.FormatInvariant("IDX10617: Encryption failed. Keywrap is only supported for: '{0}', '{1}' and '{2}'. The content encryption specified is: '{3}'.", LogHelper.MarkAsNonPII("A128KW"), LogHelper.MarkAsNonPII("A192KW"), LogHelper.MarkAsNonPII("A256KW"), LogHelper.MarkAsNonPII(keyWrapProvider.Algorithm))));
				}
				securityKey = new SymmetricSecurityKey(GenerateKeyBytes(512));
			}
			wrappedKey = keyWrapProvider.WrapKey(((SymmetricSecurityKey)securityKey).Key);
		}
		else
		{
			if (!cryptoProviderFactory.IsSupportedAlgorithm(encryptingCredentials.Alg, encryptingCredentials.Key))
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenEncryptionFailedException(LogHelper.FormatInvariant("IDX10615: Encryption failed. No support for: Algorithm: '{0}', SecurityKey: '{1}'.", LogHelper.MarkAsNonPII(encryptingCredentials.Alg), encryptingCredentials.Key)));
			}
			if ("A128CBC-HS256".Equals(encryptingCredentials.Enc))
			{
				securityKey = new SymmetricSecurityKey(GenerateKeyBytes(256));
			}
			else if ("A192CBC-HS384".Equals(encryptingCredentials.Enc))
			{
				securityKey = new SymmetricSecurityKey(GenerateKeyBytes(384));
			}
			else
			{
				if (!"A256CBC-HS512".Equals(encryptingCredentials.Enc))
				{
					throw LogHelper.LogExceptionMessage(new SecurityTokenEncryptionFailedException(LogHelper.FormatInvariant("IDX10617: Encryption failed. Keywrap is only supported for: '{0}', '{1}' and '{2}'. The content encryption specified is: '{3}'.", LogHelper.MarkAsNonPII("A128CBC-HS256"), LogHelper.MarkAsNonPII("A192CBC-HS384"), LogHelper.MarkAsNonPII("A256CBC-HS512"), LogHelper.MarkAsNonPII(encryptingCredentials.Enc))));
				}
				securityKey = new SymmetricSecurityKey(GenerateKeyBytes(512));
			}
			keyWrapProvider = cryptoProviderFactory.CreateKeyWrapProvider(encryptingCredentials.Key, encryptingCredentials.Alg);
			wrappedKey = keyWrapProvider.WrapKey(((SymmetricSecurityKey)securityKey).Key);
		}
		return securityKey;
	}

	public static IEnumerable<SecurityKey> GetAllDecryptionKeys(TokenValidationParameters validationParameters)
	{
		if (validationParameters == null)
		{
			throw new ArgumentNullException("validationParameters");
		}
		Collection<SecurityKey> collection = new Collection<SecurityKey>();
		if (validationParameters.TokenDecryptionKey != null)
		{
			collection.Add(validationParameters.TokenDecryptionKey);
		}
		if (validationParameters.TokenDecryptionKeys != null)
		{
			foreach (SecurityKey tokenDecryptionKey in validationParameters.TokenDecryptionKeys)
			{
				collection.Add(tokenDecryptionKey);
			}
		}
		return collection;
	}

	internal static DateTime GetDateTime(string key, JObject payload)
	{
		if (!payload.TryGetValue(key, out JToken value))
		{
			return DateTime.MinValue;
		}
		return EpochTime.DateTime(Convert.ToInt64(Math.Truncate(Convert.ToDouble(ParseTimeValue(value, key), CultureInfo.InvariantCulture))));
	}

	private static long ParseTimeValue(JToken jToken, string claimName)
	{
		if (jToken.Type == JTokenType.Integer || jToken.Type == JTokenType.Float)
		{
			return (long)jToken;
		}
		if (jToken.Type == JTokenType.String)
		{
			if (long.TryParse((string?)jToken, out var result))
			{
				return result;
			}
			if (float.TryParse((string?)jToken, out var result2))
			{
				return (long)result2;
			}
			if (double.TryParse((string?)jToken, out var result3))
			{
				return (long)result3;
			}
		}
		throw LogHelper.LogExceptionMessage(new FormatException(LogHelper.FormatInvariant("IDX14300: Could not parse '{0}' : '{1}' as a '{2}'.", LogHelper.MarkAsNonPII(claimName), jToken.ToString(), LogHelper.MarkAsNonPII(typeof(long)))));
	}

	internal static string SafeLogJwtToken(object obj)
	{
		if (obj == null)
		{
			return string.Empty;
		}
		if (!(obj is string text))
		{
			return obj.GetType().ToString();
		}
		int num = text.LastIndexOf(".");
		if (num == -1)
		{
			return "UnrecognizedEncodedToken";
		}
		return text.Substring(0, num);
	}

	internal static SecurityKey ResolveTokenSigningKey(string kid, string x5t, TokenValidationParameters validationParameters, BaseConfiguration configuration)
	{
		return ResolveTokenSigningKey(kid, x5t, configuration?.SigningKeys) ?? ResolveTokenSigningKey(kid, x5t, ConcatSigningKeys(validationParameters));
	}

	internal static SecurityKey ResolveTokenSigningKey(string kid, string x5t, IEnumerable<SecurityKey> signingKeys)
	{
		if (signingKeys == null)
		{
			return null;
		}
		foreach (SecurityKey signingKey in signingKeys)
		{
			if (signingKey == null)
			{
				continue;
			}
			if (signingKey is X509SecurityKey x509SecurityKey)
			{
				if ((!string.IsNullOrEmpty(kid) && string.Equals(signingKey.KeyId, kid, StringComparison.OrdinalIgnoreCase)) || (!string.IsNullOrEmpty(x5t) && string.Equals(x509SecurityKey.X5t, x5t, StringComparison.OrdinalIgnoreCase)))
				{
					return signingKey;
				}
			}
			else if (!string.IsNullOrEmpty(signingKey.KeyId) && (string.Equals(signingKey.KeyId, kid) || string.Equals(signingKey.KeyId, x5t)))
			{
				return signingKey;
			}
		}
		return null;
	}

	internal static IEnumerable<SecurityKey> ConcatSigningKeys(TokenValidationParameters tvp)
	{
		if (tvp == null)
		{
			yield break;
		}
		yield return tvp.IssuerSigningKey;
		if (tvp.IssuerSigningKeys == null)
		{
			yield break;
		}
		foreach (SecurityKey issuerSigningKey in tvp.IssuerSigningKeys)
		{
			yield return issuerSigningKey;
		}
	}

	internal static JsonDocument ParseDocument(byte[] bytes, int length)
	{
		using MemoryStream utf8Json = new MemoryStream(bytes, 0, length);
		return JsonDocument.Parse((Stream)utf8Json, default(JsonDocumentOptions));
	}

	internal static JsonDocument GetJsonDocumentFromBase64UrlEncodedString(string rawString, int startIndex, int length)
	{
		return Base64UrlEncoding.Decode(rawString, startIndex, length, ParseDocument);
	}
}
