using System.Collections;
using System.Collections.Generic;
using EnumsNET.Utilities;

namespace EnumsNET;

internal sealed class NamesContainer : IReadOnlyList<string>, IEnumerable<string>, IEnumerable, IReadOnlyCollection<string>
{
	private readonly IEnumerable<EnumMemberInternal> _members;

	private string[]? _namesArray;

	public int Count { get; }

	public string this[int index] => (_namesArray ?? (_namesArray = ArrayHelper.ToArray(this, Count)))[index];

	public NamesContainer(IEnumerable<EnumMemberInternal> members, int count, bool cached)
	{
		_members = members;
		Count = count;
		if (cached)
		{
			_namesArray = ArrayHelper.ToArray(this, count);
		}
	}

	public IEnumerator<string> GetEnumerator()
	{
		if (_namesArray == null)
		{
			return Enumerate();
		}
		return ((IEnumerable<string>)_namesArray).GetEnumerator();
	}

	private IEnumerator<string> Enumerate()
	{
		foreach (EnumMemberInternal member in _members)
		{
			yield return member.Name;
		}
	}

	IEnumerator IEnumerable.GetEnumerator()
	{
		return GetEnumerator();
	}
}
