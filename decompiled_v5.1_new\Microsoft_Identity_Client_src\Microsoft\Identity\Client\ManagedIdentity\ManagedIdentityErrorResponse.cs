using System.Text.Json.Serialization;
using Microsoft.Identity.Client.Platforms.net6;

namespace Microsoft.Identity.Client.ManagedIdentity;

[JsonObject]
[Preserve(AllMembers = true)]
internal class ManagedIdentityErrorResponse
{
	[JsonPropertyName("message")]
	public string Message { get; set; }

	[JsonPropertyName("correlationId")]
	public string CorrelationId { get; set; }

	[JsonPropertyName("error")]
	public string Error { get; set; }

	[JsonPropertyName("error_description")]
	public string ErrorDescription { get; set; }
}
