using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Executors;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;

namespace Microsoft.Identity.Client;

public sealed class AcquireTokenForClientParameterBuilder : AbstractConfidentialClientAcquireTokenParameterBuilder<AcquireTokenForClientParameterBuilder>
{
	private AcquireTokenForClientParameters Parameters { get; } = new AcquireTokenForClientParameters();

	internal AcquireTokenForClientParameterBuilder(IConfidentialClientApplicationExecutor confidentialClientApplicationExecutor)
		: base(confidentialClientApplicationExecutor)
	{
	}

	internal static AcquireTokenForClientParameterBuilder Create(IConfidentialClientApplicationExecutor confidentialClientApplicationExecutor, IEnumerable<string> scopes)
	{
		return new AcquireTokenForClientParameterBuilder(confidentialClientApplicationExecutor).WithScopes(scopes);
	}

	public AcquireTokenForClientParameterBuilder WithForceRefresh(bool forceRefresh)
	{
		Parameters.ForceRefresh = forceRefresh;
		return this;
	}

	public AcquireTokenForClientParameterBuilder WithSendX5C(bool withSendX5C)
	{
		Parameters.SendX5C = withSendX5C;
		return this;
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use WithAzureRegion on the ConfidentialClientApplicationBuilder object", true)]
	public AcquireTokenForClientParameterBuilder WithAzureRegion(bool useAzureRegion)
	{
		throw new NotImplementedException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use WithAzureRegion on the ConfidentialClientApplicationBuilder object", true)]
	public AcquireTokenForClientParameterBuilder WithPreferredAzureRegion(bool useAzureRegion = true, string regionUsedIfAutoDetectFails = "", bool fallbackToGlobal = true)
	{
		throw new NotImplementedException();
	}

	internal override Task<AuthenticationResult> ExecuteInternalAsync(CancellationToken cancellationToken)
	{
		return base.ConfidentialClientApplicationExecutor.ExecuteAsync(base.CommonParameters, Parameters, cancellationToken);
	}

	protected override void Validate()
	{
		base.Validate();
		if (!Parameters.SendX5C.HasValue)
		{
			Parameters.SendX5C = base.ServiceBundle.Config.SendX5C;
		}
	}

	internal override ApiEvent.ApiIds CalculateApiEventId()
	{
		return ApiEvent.ApiIds.AcquireTokenForClient;
	}
}
