using System;
using Microsoft.Identity.Client.AuthScheme.PoP;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.Instance.Discovery;
using Microsoft.Identity.Client.Internal.Logger;
using Microsoft.Identity.Client.OAuth2.Throttling;
using Microsoft.Identity.Client.PlatformsCommon.Factories;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.TelemetryCore;
using Microsoft.Identity.Client.TelemetryCore.Http;
using Microsoft.Identity.Client.WsTrust;

namespace Microsoft.Identity.Client.Internal;

internal class ServiceBundle : IServiceBundle
{
	public ILoggerAdapter ApplicationLogger { get; }

	public IHttpManager HttpManager { get; }

	public IInstanceDiscoveryManager InstanceDiscoveryManager { get; }

	public IWsTrustWebRequestManager WsTrustWebRequestManager { get; }

	public IPlatformProxy PlatformProxy { get; private set; }

	public ApplicationConfiguration Config { get; }

	public IDeviceAuthManager DeviceAuthManager { get; }

	public IHttpTelemetryManager HttpTelemetryManager { get; }

	public IThrottlingProvider ThrottlingManager { get; }

	internal ServiceBundle(ApplicationConfiguration config, bool shouldClearCaches = false)
	{
		Config = config;
		ApplicationLogger = LoggerHelper.CreateLogger(Guid.Empty, config);
		PlatformProxy = config.PlatformProxy ?? PlatformProxyFactory.CreatePlatformProxy(ApplicationLogger);
		HttpManager = config.HttpManager ?? HttpManagerFactory.GetHttpManager(config.HttpClientFactory ?? PlatformProxy.CreateDefaultHttpClientFactory(), config.RetryOnServerErrors, config.IsManagedIdentity);
		HttpTelemetryManager = new HttpTelemetryManager();
		InstanceDiscoveryManager = new InstanceDiscoveryManager(HttpManager, shouldClearCaches, config.CustomInstanceDiscoveryMetadata, config.CustomInstanceDiscoveryMetadataUri);
		WsTrustWebRequestManager = new WsTrustWebRequestManager(HttpManager);
		ThrottlingManager = SingletonThrottlingManager.GetInstance();
		DeviceAuthManager = config.DeviceAuthManagerForTest ?? PlatformProxy.CreateDeviceAuthManager();
		if (shouldClearCaches)
		{
			AuthorityManager.ClearValidationCache();
			PoPProviderFactory.Reset();
		}
	}

	public static ServiceBundle Create(ApplicationConfiguration config)
	{
		return new ServiceBundle(config);
	}

	public void SetPlatformProxyForTest(IPlatformProxy platformProxy)
	{
		PlatformProxy = platformProxy;
	}
}
