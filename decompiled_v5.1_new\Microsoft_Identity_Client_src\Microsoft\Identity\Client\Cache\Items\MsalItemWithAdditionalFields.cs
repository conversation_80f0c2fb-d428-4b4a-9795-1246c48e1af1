using System;
using System.Text.Json.Nodes;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Cache.Items;

internal abstract class MsalItemWithAdditionalFields
{
	internal string AdditionalFieldsJson { get; set; } = "{}";

	internal virtual void PopulateFieldsFromJObject(JsonObject j)
	{
		AdditionalFieldsJson = j.<PERSON>();
	}

	internal virtual JsonObject ToJObject()
	{
		if (!string.IsNullOrWhiteSpace(AdditionalFieldsJson))
		{
			return JsonHelper.ParseIntoJsonObject(AdditionalFieldsJson);
		}
		return new JsonObject();
	}

	internal static void SetItemIfValueNotNull(JsonObject json, string key, JsonNode value)
	{
		SetValueIfFilterMatches(json, key, value, (string strVal) => !string.IsNullOrEmpty(strVal));
	}

	internal static void SetItemIfValueNotNullOrDefault(JsonObject json, string key, JsonNode value, string defaultValue)
	{
		SetValueIfFilterMatches(json, key, value, (string strVal) => !string.IsNullOrEmpty(strVal) && !strVal.Equals(defaultValue, StringComparison.OrdinalIgnoreCase));
	}

	private static void SetValueIfFilterMatches(JsonObject json, string key, JsonNode value, Func<string, bool> filter)
	{
		bool flag = true;
		if (!(value is JsonValue jsonValue))
		{
			flag = false;
		}
		else
		{
			string value2 = jsonValue.GetValue<string>();
			if (value2 != null)
			{
				flag = filter(value2);
			}
		}
		if (flag)
		{
			json[key] = value;
		}
	}
}
