using System.Text.Json.Serialization;
using Microsoft.Identity.Client.Platforms.net6;

namespace Microsoft.Identity.Client.Instance.Discovery;

[JsonObject]
[Preserve(AllMembers = true)]
internal sealed class InstanceDiscoveryMetadataEntry
{
	[JsonPropertyName("preferred_network")]
	public string PreferredNetwork { get; set; }

	[JsonPropertyName("preferred_cache")]
	public string PreferredCache { get; set; }

	[JsonPropertyName("aliases")]
	public string[] Aliases { get; set; }
}
