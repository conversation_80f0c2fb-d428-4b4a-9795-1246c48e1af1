using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.UI;

internal interface IWebUIFactory
{
	bool IsSystemWebViewAvailable { get; }

	bool IsUserInteractive { get; }

	bool IsEmbeddedWebViewAvailable { get; }

	IWebUI CreateAuthenticationDialog(CoreUIParent coreUIParent, WebViewPreference webViewPreference, RequestContext requestContext);
}
