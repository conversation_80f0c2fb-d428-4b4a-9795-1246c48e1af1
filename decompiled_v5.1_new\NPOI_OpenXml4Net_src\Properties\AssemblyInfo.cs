using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.Versioning;
using System.Security;

[assembly: AllowPartiallyTrustedCallers]
[assembly: SecurityRules(SecurityRuleSet.Level1)]
[assembly: AssemblyCompany("<PERSON>,NPOI Contributors")]
[assembly: AssemblyConfiguration("Release")]
[assembly: AssemblyCopyright("Nissl LLC")]
[assembly: AssemblyDescription(".NET port of Apache POI | Contact us on telegram: https://t.me/npoidevs")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyInformationalVersion("2.7.1+ccb8719b3")]
[assembly: AssemblyProduct("NPOI.OpenXml4Net")]
[assembly: AssemblyTitle("NPOI.OpenXml4Net")]
[assembly: AssemblyMetadata("RepositoryUrl", "https://github.com/nissl-lab/npoi")]
[assembly: AssemblyVersion("*******")]
[module: RefSafetyRules(11)]
