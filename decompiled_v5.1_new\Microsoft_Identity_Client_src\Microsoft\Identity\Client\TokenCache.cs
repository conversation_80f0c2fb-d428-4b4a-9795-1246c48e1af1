using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Cache.Items;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.Instance.Discovery;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Internal.Requests;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.PlatformsCommon.Factories;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.TelemetryCore.Internal.Events;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

public sealed class TokenCache : ITokenCacheInternal, ITokenCache, ITokenCacheSerializer
{
	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Use Microsoft.Identity.Client.TokenCacheCallback instead. See https://aka.ms/msal-net-3x-cache-breaking-change", true)]
	public delegate void TokenCacheNotification(TokenCacheNotificationArgs args);

	internal const int ExpirationTooLongInDays = 3650;

	private readonly IFeatureFlags _featureFlags;

	private volatile bool _hasStateChanged;

	private readonly OptionalSemaphoreSlim _semaphoreSlim;

	private IDictionary<string, JsonNode> _unknownNodes;

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("Please use the equivalent flag TokenCacheNotificationArgs.HasStateChanged, which indicates if the operation triggering the notification is modifying the cache or not. Setting the flag is not required.")]
	public bool HasStateChanged
	{
		get
		{
			return _hasStateChanged;
		}
		set
		{
			_hasStateChanged = value;
		}
	}

	internal ITokenCacheAccessor Accessor { get; set; }

	internal IServiceBundle ServiceBundle { get; }

	internal ILegacyCachePersistence LegacyCachePersistence { get; }

	internal string ClientId => ServiceBundle.Config.ClientId;

	ITokenCacheAccessor ITokenCacheInternal.Accessor => Accessor;

	ILegacyCachePersistence ITokenCacheInternal.LegacyPersistence => LegacyCachePersistence;

	internal bool IsAppTokenCache { get; }

	bool ITokenCacheInternal.IsApplicationCache => IsAppTokenCache;

	OptionalSemaphoreSlim ITokenCacheInternal.Semaphore => _semaphoreSlim;

	internal TokenCacheCallback BeforeAccess { get; set; }

	internal TokenCacheCallback BeforeWrite { get; set; }

	internal TokenCacheCallback AfterAccess { get; set; }

	internal Func<TokenCacheNotificationArgs, Task> AsyncBeforeAccess { get; set; }

	internal Func<TokenCacheNotificationArgs, Task> AsyncAfterAccess { get; set; }

	internal Func<TokenCacheNotificationArgs, Task> AsyncBeforeWrite { get; set; }

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This is expected to be removed in MSAL.NET v5. We recommend using SerializeMsalV3/DeserializeMsalV3. Read more: https://aka.ms/msal-net-3x-cache-breaking-change", false)]
	public CacheData SerializeUnifiedAndAdalCache()
	{
		Validate();
		ServiceBundle.ApplicationLogger.Info(() => "[ADAL Caching] Legacy SerializeUnifiedAndAdalCache being called. " + _semaphoreSlim.GetCurrentCountLogMessage() + ".");
		_semaphoreSlim.Wait();
		ServiceBundle.ApplicationLogger.Info("[ADAL Caching] Acquired semaphore");
		try
		{
			byte[] unifiedState = Serialize();
			byte[] adalV3State = LegacyCachePersistence.LoadCache();
			return new CacheData
			{
				AdalV3State = adalV3State,
				UnifiedState = unifiedState
			};
		}
		finally
		{
			_semaphoreSlim.Release();
		}
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This is expected to be removed in MSAL.NET v5. We recommend using SerializeMsalV3/DeserializeMsalV3. Read more: https://aka.ms/msal-net-3x-cache-breaking-change", false)]
	public void DeserializeUnifiedAndAdalCache(CacheData cacheData)
	{
		Validate();
		ServiceBundle.ApplicationLogger.Info("[ADAL Caching] Legacy SerializeUnifiedAndAdalCache being called. Acquiring semaphore " + _semaphoreSlim.GetCurrentCountLogMessage());
		_semaphoreSlim.Wait();
		ServiceBundle.ApplicationLogger.Info("[ADAL Caching] Acquired semaphore");
		try
		{
			Deserialize(cacheData.UnifiedState);
			LegacyCachePersistence.WriteCache(cacheData.AdalV3State);
		}
		finally
		{
			_semaphoreSlim.Release();
		}
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This is expected to be removed in MSAL.NET v5. We recommend using SerializeMsalV3/DeserializeMsalV3. Read more: https://aka.ms/msal-net-3x-cache-breaking-change", false)]
	public byte[] Serialize()
	{
		throw new NotImplementedException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This is expected to be removed in MSAL.NET v5. We recommend using SerializeMsalV3/DeserializeMsalV3. Read more: https://aka.ms/msal-net-3x-cache-breaking-change", false)]
	public void Deserialize(byte[] msalV2State)
	{
		throw new NotImplementedException();
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This is removed in MSAL.NET v4. Read more: https://aka.ms/msal-net-4x-cache-breaking-change", false)]
	public byte[] SerializeAdalV3()
	{
		throw new NotImplementedException("This is removed in MSAL.NET v4. Read more: https://aka.ms/msal-net-4x-cache-breaking-change");
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This is removed in MSAL.NET v4. Read more: https://aka.ms/msal-net-4x-cache-breaking-change", false)]
	public void DeserializeAdalV3(byte[] adalV3State)
	{
		throw new NotImplementedException("This is removed in MSAL.NET v4. Read more: https://aka.ms/msal-net-4x-cache-breaking-change");
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This is removed in MSAL.NET v4. Read more: https://aka.ms/msal-net-4x-cache-breaking-change", false)]
	public byte[] SerializeMsalV2()
	{
		throw new NotImplementedException("This is removed in MSAL.NET v4. Read more: https://aka.ms/msal-net-4x-cache-breaking-change");
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This is removed in MSAL.NET v4. Read more: https://aka.ms/msal-net-4x-cache-breaking-change", false)]
	public void DeserializeMsalV2(byte[] msalV2State)
	{
		throw new NotImplementedException("This is removed in MSAL.NET v4. Read more: https://aka.ms/msal-net-4x-cache-breaking-change");
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This is removed in MSAL.NET v4. Read more: https://aka.ms/msal-net-4x-cache-breaking-change", false)]
	public byte[] SerializeMsalV3()
	{
		throw new NotImplementedException("This is removed in MSAL.NET v4. Read more: https://aka.ms/msal-net-4x-cache-breaking-change");
	}

	[EditorBrowsable(EditorBrowsableState.Never)]
	[Obsolete("This is removed in MSAL.NET v4. Read more: https://aka.ms/msal-net-4x-cache-breaking-change", false)]
	public void DeserializeMsalV3(byte[] msalV3State, bool shouldClearExistingCache)
	{
		throw new NotImplementedException("This is removed in MSAL.NET v4. Read more: https://aka.ms/msal-net-4x-cache-breaking-change");
	}

	[Obsolete("The recommended way to get a cache is by using IClientApplicationBase.UserTokenCache or IClientApplicationBase.AppTokenCache")]
	[EditorBrowsable(EditorBrowsableState.Never)]
	public TokenCache()
		: this(null, isApplicationTokenCache: false)
	{
	}

	internal TokenCache(IServiceBundle serviceBundle, bool isApplicationTokenCache, ILegacyCachePersistence legacyCachePersistenceForTest = null)
	{
		if (serviceBundle == null)
		{
			throw new ArgumentNullException("serviceBundle");
		}
		_semaphoreSlim = new OptionalSemaphoreSlim(serviceBundle.Config.CacheSynchronizationEnabled);
		IPlatformProxy platformProxy = serviceBundle?.PlatformProxy ?? PlatformProxyFactory.CreatePlatformProxy(null);
		Accessor = platformProxy.CreateTokenCacheAccessor(serviceBundle.Config.AccessorOptions, isApplicationTokenCache);
		_featureFlags = platformProxy.GetFeatureFlags();
		LegacyCachePersistence = legacyCachePersistenceForTest ?? platformProxy.CreateLegacyCachePersistence();
		IsAppTokenCache = isApplicationTokenCache;
		ServiceBundle = serviceBundle;
	}

	public void SetIosKeychainSecurityGroup(string securityGroup)
	{
	}

	private void UpdateAppMetadata(string clientId, string environment, string familyId)
	{
		if (_featureFlags.IsFociEnabled)
		{
			MsalAppMetadataCacheItem item = new MsalAppMetadataCacheItem(clientId, environment, familyId);
			Accessor.SaveAppMetadata(item);
		}
	}

	private void DeleteAccessTokensWithIntersectingScopes(AuthenticationRequestParameters requestParams, IEnumerable<string> environmentAliases, string tenantId, HashSet<string> scopeSet, string homeAccountId, string tokenType)
	{
		if (requestParams.RequestContext.Logger.IsLoggingEnabled(LogLevel.Info))
		{
			requestParams.RequestContext.Logger.Info(() => "Looking for scopes for the authority in the cache which intersect with " + requestParams.Scope.AsSingleString());
		}
		List<MsalAccessTokenCacheItem> accessTokensToDelete = new List<MsalAccessTokenCacheItem>();
		string internalPartitionKeyFromResponse = CacheKeyFactory.GetInternalPartitionKeyFromResponse(requestParams, homeAccountId);
		foreach (MsalAccessTokenCacheItem allAccessToken in Accessor.GetAllAccessTokens(internalPartitionKeyFromResponse))
		{
			if (allAccessToken.ClientId.Equals(ClientId, StringComparison.OrdinalIgnoreCase) && environmentAliases.Contains(allAccessToken.Environment) && string.Equals(allAccessToken.TokenType ?? "", tokenType ?? "", StringComparison.OrdinalIgnoreCase) && string.Equals(allAccessToken.TenantId, tenantId, StringComparison.OrdinalIgnoreCase) && allAccessToken.ScopeSet.Overlaps(scopeSet))
			{
				requestParams.RequestContext.Logger.Verbose(() => $"Intersecting scopes found: {scopeSet}");
				accessTokensToDelete.Add(allAccessToken);
			}
		}
		requestParams.RequestContext.Logger.Info(() => "Intersecting scope entries count - " + accessTokensToDelete.Count);
		if (!requestParams.IsClientCredentialRequest && requestParams.ApiId != ApiEvent.ApiIds.AcquireTokenForSystemAssignedManagedIdentity && requestParams.ApiId != ApiEvent.ApiIds.AcquireTokenForUserAssignedManagedIdentity)
		{
			accessTokensToDelete.RemoveAll((MsalAccessTokenCacheItem item) => !item.HomeAccountId.Equals(homeAccountId, StringComparison.OrdinalIgnoreCase));
			requestParams.RequestContext.Logger.Info(() => "Matching entries after filtering by user - " + accessTokensToDelete.Count);
		}
		foreach (MsalAccessTokenCacheItem item in accessTokensToDelete)
		{
			Accessor.DeleteAccessToken(item);
		}
	}

	private static string GetAccessTokenExpireLogMessageContent(MsalAccessTokenCacheItem msalAccessTokenCacheItem)
	{
		return string.Format(CultureInfo.InvariantCulture, "[Current time ({0}) - Expiration Time ({1}) - Extended Expiration Time ({2})]", DateTime.UtcNow, msalAccessTokenCacheItem.ExpiresOn, msalAccessTokenCacheItem.ExtendedExpiresOn);
	}

	private bool RtMatchesAccount(MsalRefreshTokenCacheItem rtItem, MsalAccountCacheItem account)
	{
		bool num = rtItem.HomeAccountId.Equals(account.HomeAccountId, StringComparison.OrdinalIgnoreCase);
		bool flag = rtItem.IsFRT || rtItem.ClientId.Equals(ClientId, StringComparison.OrdinalIgnoreCase);
		return num && flag;
	}

	private static bool FrtExists(IEnumerable<MsalRefreshTokenCacheItem> refreshTokens)
	{
		return refreshTokens.Any((MsalRefreshTokenCacheItem rt) => rt.IsFRT);
	}

	async Task<Tuple<MsalAccessTokenCacheItem, MsalIdTokenCacheItem, Account>> ITokenCacheInternal.SaveTokenResponseAsync(AuthenticationRequestParameters requestParams, MsalTokenResponse response)
	{
		ILoggerAdapter logger = requestParams.RequestContext.Logger;
		response.Log(logger, LogLevel.Verbose);
		MsalAccessTokenCacheItem msalAccessTokenCacheItem = null;
		MsalRefreshTokenCacheItem msalRefreshTokenCacheItem = null;
		MsalIdTokenCacheItem msalIdTokenCacheItem = null;
		MsalAccountCacheItem msalAccountCacheItem = null;
		IdToken idToken = IdToken.Parse(response.IdToken);
		if (idToken == null)
		{
			logger.Info("[SaveTokenResponseAsync] ID Token not present in response. ");
		}
		string tenantId = TokenResponseHelper.GetTenantId(idToken, requestParams);
		string username = TokenResponseHelper.GetUsernameFromIdToken(idToken);
		string homeAccountId = TokenResponseHelper.GetHomeAccountId(requestParams, response, idToken);
		string suggestedWebCacheKey = CacheKeyFactory.GetExternalCacheKeyFromResponse(requestParams, homeAccountId);
		if (requestParams.AppConfig.MultiCloudSupportEnabled && !string.IsNullOrEmpty(response.AuthorityUrl))
		{
			Uri uri = new Uri(response.AuthorityUrl);
			requestParams.AuthorityManager = new AuthorityManager(requestParams.RequestContext, Authority.CreateAuthorityWithEnvironment(requestParams.Authority.AuthorityInfo, uri.Host));
		}
		InstanceDiscoveryMetadataEntry instanceDiscoveryMetadata = await requestParams.AuthorityManager.GetInstanceDiscoveryEntryAsync().ConfigureAwait(continueOnCapturedContext: false);
		if (!string.IsNullOrEmpty(response.AccessToken))
		{
			msalAccessTokenCacheItem = new MsalAccessTokenCacheItem(instanceDiscoveryMetadata.PreferredCache, requestParams.AppConfig.ClientId, response, tenantId, homeAccountId, requestParams.AuthenticationScheme.KeyId, CacheKeyFactory.GetOboKey(requestParams.LongRunningOboCacheKey, requestParams.UserAssertion));
		}
		if (!string.IsNullOrEmpty(response.RefreshToken))
		{
			msalRefreshTokenCacheItem = new MsalRefreshTokenCacheItem(instanceDiscoveryMetadata.PreferredCache, requestParams.AppConfig.ClientId, response, homeAccountId)
			{
				OboCacheKey = CacheKeyFactory.GetOboKey(requestParams.LongRunningOboCacheKey, requestParams.UserAssertion)
			};
			if (!_featureFlags.IsFociEnabled)
			{
				msalRefreshTokenCacheItem.FamilyId = null;
			}
		}
		Account account = null;
		if (idToken != null)
		{
			msalIdTokenCacheItem = new MsalIdTokenCacheItem(instanceDiscoveryMetadata.PreferredCache, requestParams.AppConfig.ClientId, response, tenantId, homeAccountId);
			Dictionary<string, string> wamAccountIds = TokenResponseHelper.GetWamAccountIds(requestParams, response);
			msalAccountCacheItem = new MsalAccountCacheItem(instanceDiscoveryMetadata.PreferredCache, response.ClientInfo, homeAccountId, idToken, username, tenantId, wamAccountIds);
			IDictionary<string, TenantProfile> dictionary = null;
			if (msalIdTokenCacheItem.TenantId != null)
			{
				dictionary = await GetTenantProfilesAsync(requestParams, homeAccountId).ConfigureAwait(continueOnCapturedContext: false);
				if (dictionary != null)
				{
					TenantProfile value = new TenantProfile(msalIdTokenCacheItem);
					dictionary[msalIdTokenCacheItem.TenantId] = value;
				}
			}
			account = new Account(homeAccountId, username, instanceDiscoveryMetadata.PreferredNetwork, wamAccountIds, dictionary?.Values);
		}
		logger.Verbose(() => "[SaveTokenResponseAsync] Entering token cache semaphore. Count " + _semaphoreSlim.GetCurrentCountLogMessage() + ".");
		await _semaphoreSlim.WaitAsync(requestParams.RequestContext.UserCancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		logger.Verbose(() => "[SaveTokenResponseAsync] Entered token cache semaphore. ");
		ITokenCacheInternal tokenCacheInternal = this;
		try
		{
			HasStateChanged = true;
			try
			{
				if (tokenCacheInternal.IsAppSubscribedToSerializationEvents())
				{
					TokenCacheNotificationArgs args = new TokenCacheNotificationArgs(this, ClientId, account, hasStateChanged: true, tokenCacheInternal.IsApplicationCache, suggestedWebCacheKey, tokenCacheInternal.HasTokensNoLocks(), null, requestParams.RequestContext.UserCancellationToken, requestParams.RequestContext.CorrelationId, requestParams.Scope, requestParams.AuthorityManager.OriginalAuthority.TenantId, requestParams.RequestContext.Logger.IdentityLogger, requestParams.RequestContext.Logger.PiiLoggingEnabled);
					MeasureDurationResult measureDurationResult = await StopwatchService.MeasureCodeBlockAsync(async delegate
					{
						await tokenCacheInternal.OnBeforeAccessAsync(args).ConfigureAwait(continueOnCapturedContext: false);
						await tokenCacheInternal.OnBeforeWriteAsync(args).ConfigureAwait(continueOnCapturedContext: false);
					}).ConfigureAwait(continueOnCapturedContext: false);
					requestParams.RequestContext.ApiEvent.DurationInCacheInMs += measureDurationResult.Milliseconds;
				}
				if (ShouldCacheAccessToken(msalAccessTokenCacheItem, response.TokenSource))
				{
					logger.Info("[SaveTokenResponseAsync] Saving AT in cache and removing overlapping ATs...");
					DeleteAccessTokensWithIntersectingScopes(requestParams, instanceDiscoveryMetadata.Aliases, tenantId, msalAccessTokenCacheItem.ScopeSet, msalAccessTokenCacheItem.HomeAccountId, msalAccessTokenCacheItem.TokenType);
					Accessor.SaveAccessToken(msalAccessTokenCacheItem);
				}
				if (idToken != null)
				{
					logger.Info("[SaveTokenResponseAsync] Saving Id Token and Account in cache ...");
					Accessor.SaveIdToken(msalIdTokenCacheItem);
					MergeWamAccountIds(msalAccountCacheItem);
					Accessor.SaveAccount(msalAccountCacheItem);
				}
				if (msalRefreshTokenCacheItem != null)
				{
					logger.Info("[SaveTokenResponseAsync] Saving RT in cache...");
					Accessor.SaveRefreshToken(msalRefreshTokenCacheItem);
				}
				UpdateAppMetadata(requestParams.AppConfig.ClientId, instanceDiscoveryMetadata.PreferredCache, response.FamilyId);
				SaveToLegacyAdalCache(requestParams, response, msalRefreshTokenCacheItem, msalIdTokenCacheItem, tenantId, instanceDiscoveryMetadata);
			}
			finally
			{
				if (tokenCacheInternal.IsAppSubscribedToSerializationEvents())
				{
					DateTimeOffset? suggestedCacheExpiry = CalculateSuggestedCacheExpiry(Accessor, logger);
					TokenCacheNotificationArgs args2 = new TokenCacheNotificationArgs(this, ClientId, account, hasStateChanged: true, tokenCacheInternal.IsApplicationCache, suggestedWebCacheKey, tokenCacheInternal.HasTokensNoLocks(), suggestedCacheExpiry, requestParams.RequestContext.UserCancellationToken, requestParams.RequestContext.CorrelationId, requestParams.Scope, requestParams.AuthorityManager.OriginalAuthority.TenantId, requestParams.RequestContext.Logger.IdentityLogger, requestParams.RequestContext.Logger.PiiLoggingEnabled);
					MeasureDurationResult measureDurationResult2 = await tokenCacheInternal.OnAfterAccessAsync(args2).MeasureAsync().ConfigureAwait(continueOnCapturedContext: false);
					requestParams.RequestContext.ApiEvent.DurationInCacheInMs += measureDurationResult2.Milliseconds;
					LogCacheContents(requestParams);
				}
				HasStateChanged = false;
			}
			return Tuple.Create(msalAccessTokenCacheItem, msalIdTokenCacheItem, account);
		}
		finally
		{
			_semaphoreSlim.Release();
			logger.Verbose(() => "[SaveTokenResponseAsync] Released token cache semaphore. ");
		}
	}

	private static bool ShouldCacheAccessToken(MsalAccessTokenCacheItem msalAccessTokenCacheItem, TokenSource tokenSource)
	{
		if (msalAccessTokenCacheItem != null)
		{
			return tokenSource != TokenSource.Broker;
		}
		return false;
	}

	private void LogCacheContents(AuthenticationRequestParameters requestParameters)
	{
		if (!requestParameters.RequestContext.Logger.IsLoggingEnabled(LogLevel.Verbose))
		{
			return;
		}
		List<MsalAccessTokenCacheItem> allAccessTokens = Accessor.GetAllAccessTokens();
		List<MsalRefreshTokenCacheItem> allRefreshTokens = Accessor.GetAllRefreshTokens();
		List<MsalAccessTokenCacheItem> list = allAccessTokens.Take(10).ToList();
		List<MsalRefreshTokenCacheItem> list2 = allRefreshTokens.Take(10).ToList();
		StringBuilder tokenCacheKeyLog = new StringBuilder();
		StringBuilder stringBuilder = tokenCacheKeyLog;
		StringBuilder stringBuilder2 = stringBuilder;
		StringBuilder.AppendInterpolatedStringHandler handler = new StringBuilder.AppendInterpolatedStringHandler(44, 1, stringBuilder);
		handler.AppendLiteral("Total number of access tokens in the cache: ");
		handler.AppendFormatted(allAccessTokens.Count);
		stringBuilder2.AppendLine(ref handler);
		stringBuilder = tokenCacheKeyLog;
		StringBuilder stringBuilder3 = stringBuilder;
		handler = new StringBuilder.AppendInterpolatedStringHandler(45, 1, stringBuilder);
		handler.AppendLiteral("Total number of refresh tokens in the cache: ");
		handler.AppendFormatted(allRefreshTokens.Count);
		stringBuilder3.AppendLine(ref handler);
		stringBuilder = tokenCacheKeyLog;
		StringBuilder stringBuilder4 = stringBuilder;
		handler = new StringBuilder.AppendInterpolatedStringHandler(31, 1, stringBuilder);
		handler.AppendLiteral("First ");
		handler.AppendFormatted(list.Count);
		handler.AppendLiteral(" access token cache keys:");
		stringBuilder4.AppendLine(ref handler);
		foreach (MsalAccessTokenCacheItem item in list)
		{
			stringBuilder = tokenCacheKeyLog;
			StringBuilder stringBuilder5 = stringBuilder;
			handler = new StringBuilder.AppendInterpolatedStringHandler(14, 1, stringBuilder);
			handler.AppendLiteral("AT Cache Key: ");
			handler.AppendFormatted(item.ToLogString(requestParameters.RequestContext.Logger.PiiLoggingEnabled));
			stringBuilder5.AppendLine(ref handler);
		}
		stringBuilder = tokenCacheKeyLog;
		StringBuilder stringBuilder6 = stringBuilder;
		handler = new StringBuilder.AppendInterpolatedStringHandler(32, 1, stringBuilder);
		handler.AppendLiteral("First ");
		handler.AppendFormatted(list2.Count);
		handler.AppendLiteral(" refresh token cache keys:");
		stringBuilder6.AppendLine(ref handler);
		foreach (MsalRefreshTokenCacheItem item2 in list2)
		{
			stringBuilder = tokenCacheKeyLog;
			StringBuilder stringBuilder7 = stringBuilder;
			handler = new StringBuilder.AppendInterpolatedStringHandler(14, 1, stringBuilder);
			handler.AppendLiteral("RT Cache Key: ");
			handler.AppendFormatted(item2.ToLogString(requestParameters.RequestContext.Logger.PiiLoggingEnabled));
			stringBuilder7.AppendLine(ref handler);
		}
		requestParameters.RequestContext.Logger.Verbose(() => tokenCacheKeyLog.ToString());
	}

	private bool IsLegacyAdalCacheEnabled(AuthenticationRequestParameters requestParams)
	{
		if (requestParams.IsClientCredentialRequest)
		{
			return false;
		}
		if (ServiceBundle.PlatformProxy.LegacyCacheRequiresSerialization && !((ITokenCacheInternal)this).IsAppSubscribedToSerializationEvents())
		{
			return false;
		}
		if (!ServiceBundle.Config.LegacyCacheCompatibilityEnabled)
		{
			return false;
		}
		if (requestParams.AuthorityInfo.AuthorityType != AuthorityType.Aad)
		{
			return false;
		}
		requestParams.RequestContext.Logger.Info("IsLegacyAdalCacheEnabled: yes");
		return true;
	}

	private void SaveToLegacyAdalCache(AuthenticationRequestParameters requestParams, MsalTokenResponse response, MsalRefreshTokenCacheItem msalRefreshTokenCacheItem, MsalIdTokenCacheItem msalIdTokenCacheItem, string tenantId, InstanceDiscoveryMetadataEntry instanceDiscoveryMetadata)
	{
		if (msalRefreshTokenCacheItem != null && msalRefreshTokenCacheItem.RawClientInfo != null && msalIdTokenCacheItem != null && msalIdTokenCacheItem.IdToken?.GetUniqueId() != null && IsLegacyAdalCacheEnabled(requestParams))
		{
			Authority authority = Authority.CreateAuthorityWithEnvironment(Authority.CreateAuthorityWithTenant(requestParams.AuthorityInfo, tenantId).AuthorityInfo, instanceDiscoveryMetadata.PreferredCache);
			CacheFallbackOperations.WriteAdalRefreshToken(requestParams.RequestContext.Logger, LegacyCachePersistence, msalRefreshTokenCacheItem, msalIdTokenCacheItem, authority.AuthorityInfo.CanonicalAuthority.ToString(), msalIdTokenCacheItem.IdToken.GetUniqueId(), response.Scope);
		}
		else
		{
			requestParams.RequestContext.Logger.Verbose(() => "Not saving to ADAL legacy cache. ");
		}
	}

	internal static DateTimeOffset? CalculateSuggestedCacheExpiry(ITokenCacheAccessor accessor, ILoggerAdapter logger)
	{
		if (accessor.GetAllRefreshTokens().Count == 0)
		{
			List<MsalAccessTokenCacheItem> allAccessTokens = accessor.GetAllAccessTokens();
			if (allAccessTokens.Count == 0)
			{
				logger.Warning("[CalculateSuggestedCacheExpiry] No access tokens or refresh tokens found in the accessor. Not returning any expiration.");
				return null;
			}
			DateTimeOffset dateTimeOffset = allAccessTokens.Max((MsalAccessTokenCacheItem item) => item.ExpiresOn);
			if (dateTimeOffset < DateTimeOffset.UtcNow + Constants.AccessTokenExpirationBuffer)
			{
				return null;
			}
			return dateTimeOffset;
		}
		return null;
	}

	private void MergeWamAccountIds(MsalAccountCacheItem msalAccountCacheItem)
	{
		IDictionary<string, string> other = Accessor.GetAccount(msalAccountCacheItem)?.WamAccountIds;
		msalAccountCacheItem.WamAccountIds.MergeDifferentEntries(other);
	}

	async Task<MsalAccessTokenCacheItem> ITokenCacheInternal.FindAccessTokenAsync(AuthenticationRequestParameters requestParams)
	{
		ILoggerAdapter logger = requestParams.RequestContext.Logger;
		if (requestParams.AuthorityInfo?.CanonicalAuthority == null)
		{
			logger.Warning("[FindAccessTokenAsync] No authority provided. Skipping cache lookup. ");
			return null;
		}
		string keyFromRequest = CacheKeyFactory.GetKeyFromRequest(requestParams);
		List<MsalAccessTokenCacheItem> allAccessTokens = Accessor.GetAllAccessTokens(keyFromRequest, logger);
		requestParams.RequestContext.Logger.Always($"[FindAccessTokenAsync] Discovered {allAccessTokens.Count} access tokens in cache using partition key: {keyFromRequest}");
		if (allAccessTokens.Count == 0)
		{
			logger.Verbose(() => "[FindAccessTokenAsync] No access tokens found in the cache. Skipping filtering. ");
			requestParams.RequestContext.ApiEvent.CacheInfo = CacheRefreshReason.NoCachedAccessToken;
			return null;
		}
		FilterTokensByHomeAccountTenantOrAssertion(allAccessTokens, requestParams);
		FilterTokensByTokenType(allAccessTokens, requestParams);
		FilterTokensByScopes(allAccessTokens, requestParams);
		allAccessTokens = await FilterTokensByEnvironmentAsync(allAccessTokens, requestParams).ConfigureAwait(continueOnCapturedContext: false);
		FilterTokensByClientId(allAccessTokens);
		CacheRefreshReason cacheInfo = CacheRefreshReason.NotApplicable;
		if (allAccessTokens.Count == 0)
		{
			logger.Verbose(() => "[FindAccessTokenAsync] No tokens found for matching authority, client_id, user and scopes. ");
			return null;
		}
		MsalAccessTokenCacheItem singleToken = GetSingleToken(allAccessTokens, requestParams);
		singleToken = FilterTokensByPopKeyId(singleToken, requestParams);
		singleToken = FilterTokensByExpiry(singleToken, requestParams);
		if (singleToken == null)
		{
			cacheInfo = CacheRefreshReason.Expired;
		}
		requestParams.RequestContext.ApiEvent.CacheInfo = cacheInfo;
		return singleToken;
	}

	private static void FilterTokensByScopes(List<MsalAccessTokenCacheItem> tokenCacheItems, AuthenticationRequestParameters requestParams)
	{
		ILoggerAdapter logger = requestParams.RequestContext.Logger;
		if (tokenCacheItems.Count == 0)
		{
			logger.Verbose(() => "Not filtering by scopes, because there are no candidates");
			return;
		}
		IEnumerable<string> requestScopes = requestParams.Scope.Where((string s) => !OAuth2Value.ReservedScopes.Contains(s));
		tokenCacheItems.FilterWithLogging(delegate(MsalAccessTokenCacheItem item)
		{
			bool accepted = ScopeHelper.ScopeContains(item.ScopeSet, requestScopes);
			if (logger.IsLoggingEnabled(LogLevel.Verbose))
			{
				logger.Verbose(() => $"Access token with scopes {string.Join(" ", item.ScopeSet)} passes scope filter? {accepted} ");
			}
			return accepted;
		}, logger, "Filtering by scopes");
	}

	private static void FilterTokensByTokenType(List<MsalAccessTokenCacheItem> tokenCacheItems, AuthenticationRequestParameters requestParams)
	{
		tokenCacheItems.FilterWithLogging((MsalAccessTokenCacheItem item) => string.Equals(item.TokenType ?? "bearer", requestParams.AuthenticationScheme.AccessTokenType, StringComparison.OrdinalIgnoreCase), requestParams.RequestContext.Logger, "Filtering by token type");
	}

	private static void FilterTokensByHomeAccountTenantOrAssertion(List<MsalAccessTokenCacheItem> tokenCacheItems, AuthenticationRequestParameters requestParams)
	{
		string requestTenantId = requestParams.Authority.TenantId;
		bool flag = true;
		if (ApiEvent.IsOnBehalfOfRequest(requestParams.ApiId))
		{
			tokenCacheItems.FilterWithLogging((MsalAccessTokenCacheItem item) => !string.IsNullOrEmpty(item.OboCacheKey) && item.OboCacheKey.Equals((!string.IsNullOrEmpty(requestParams.LongRunningOboCacheKey)) ? requestParams.LongRunningOboCacheKey : requestParams.UserAssertion.AssertionHash, StringComparison.OrdinalIgnoreCase), requestParams.RequestContext.Logger, (!string.IsNullOrEmpty(requestParams.LongRunningOboCacheKey)) ? ("Filtering AT by user-provided cache key: " + requestParams.LongRunningOboCacheKey) : ("Filtering AT by user assertion: " + requestParams.UserAssertion.AssertionHash));
			flag = !string.IsNullOrEmpty(requestTenantId) && !AadAuthority.IsCommonOrganizationsOrConsumersTenant(requestTenantId);
		}
		if (flag)
		{
			tokenCacheItems.FilterWithLogging((MsalAccessTokenCacheItem item) => string.Equals(item.TenantId ?? string.Empty, requestTenantId ?? string.Empty, StringComparison.OrdinalIgnoreCase), requestParams.RequestContext.Logger, "Filtering AT by tenant id");
		}
		else
		{
			requestParams.RequestContext.Logger.Warning("Have not filtered by tenant ID. This can happen in OBO scenario where authority is /common or /organizations. Please use tenanted authority.");
		}
		if (requestParams.ApiId != ApiEvent.ApiIds.AcquireTokenForClient && requestParams.ApiId != ApiEvent.ApiIds.AcquireTokenForSystemAssignedManagedIdentity && requestParams.ApiId != ApiEvent.ApiIds.AcquireTokenForUserAssignedManagedIdentity && !ApiEvent.IsOnBehalfOfRequest(requestParams.ApiId))
		{
			tokenCacheItems.FilterWithLogging((MsalAccessTokenCacheItem item) => item.HomeAccountId.Equals(requestParams.Account.HomeAccountId?.Identifier, StringComparison.OrdinalIgnoreCase), requestParams.RequestContext.Logger, "Filtering AT by home account id");
		}
	}

	private MsalAccessTokenCacheItem FilterTokensByExpiry(MsalAccessTokenCacheItem msalAccessTokenCacheItem, AuthenticationRequestParameters requestParams)
	{
		ILoggerAdapter logger = requestParams.RequestContext.Logger;
		if (msalAccessTokenCacheItem != null)
		{
			if (msalAccessTokenCacheItem.ExpiresOn > DateTime.UtcNow + Constants.AccessTokenExpirationBuffer)
			{
				if (msalAccessTokenCacheItem.ExpiresOn > DateTime.UtcNow + TimeSpan.FromDays(3650.0))
				{
					logger.Error("Access token expiration too large. This can be the result of a bug or corrupt cache. Token will be ignored as it is likely expired." + GetAccessTokenExpireLogMessageContent(msalAccessTokenCacheItem));
					return null;
				}
				logger.Info(() => "Access token is not expired. Returning the found cache entry. " + GetAccessTokenExpireLogMessageContent(msalAccessTokenCacheItem));
				return msalAccessTokenCacheItem;
			}
			if (ServiceBundle.Config.IsExtendedTokenLifetimeEnabled && msalAccessTokenCacheItem.ExtendedExpiresOn > DateTime.UtcNow + Constants.AccessTokenExpirationBuffer)
			{
				logger.Info(() => "Access token is expired.  IsExtendedLifeTimeEnabled=TRUE and ExtendedExpiresOn is not exceeded.  Returning the found cache entry. " + GetAccessTokenExpireLogMessageContent(msalAccessTokenCacheItem));
				msalAccessTokenCacheItem.IsExtendedLifeTimeToken = true;
				return msalAccessTokenCacheItem;
			}
			logger.Info(() => "Access token has expired or about to expire. " + GetAccessTokenExpireLogMessageContent(msalAccessTokenCacheItem));
		}
		return null;
	}

	private static MsalAccessTokenCacheItem GetSingleToken(List<MsalAccessTokenCacheItem> tokenCacheItems, AuthenticationRequestParameters requestParams)
	{
		if (tokenCacheItems.Count == 1)
		{
			return tokenCacheItems[0];
		}
		requestParams.RequestContext.Logger.Error("Multiple access tokens found for matching authority, client_id, user and scopes. ");
		throw new MsalClientException("multiple_matching_tokens_detected", "The cache contains multiple tokens satisfying the requirements. Try to clear token cache. ");
	}

	private async Task<List<MsalAccessTokenCacheItem>> FilterTokensByEnvironmentAsync(List<MsalAccessTokenCacheItem> tokenCacheItems, AuthenticationRequestParameters requestParams)
	{
		ILoggerAdapter logger = requestParams.RequestContext.Logger;
		if (tokenCacheItems.Count == 0)
		{
			logger.Verbose(() => "Not filtering AT by environment, because there are no candidates");
			return tokenCacheItems;
		}
		InstanceDiscoveryMetadataEntry instanceMetadata = await ServiceBundle.InstanceDiscoveryManager.GetMetadataEntryTryAvoidNetworkAsync(requestParams.AuthorityInfo, tokenCacheItems.Select((MsalAccessTokenCacheItem at) => at.Environment), requestParams.RequestContext).ConfigureAwait(continueOnCapturedContext: false);
		List<MsalAccessTokenCacheItem> itemsFilteredByAlias = tokenCacheItems.FilterWithLogging((MsalAccessTokenCacheItem item) => item.Environment.Equals(instanceMetadata.PreferredCache, StringComparison.OrdinalIgnoreCase), requestParams.RequestContext.Logger, "Filtering AT by preferred environment " + instanceMetadata.PreferredCache, updateOriginalCollection: false);
		if (itemsFilteredByAlias.Count > 0)
		{
			if (logger.IsLoggingEnabled(LogLevel.Verbose))
			{
				logger.Verbose(() => $"Filtered AT by preferred alias returning {itemsFilteredByAlias.Count} tokens.");
			}
			return itemsFilteredByAlias;
		}
		return tokenCacheItems.FilterWithLogging((MsalAccessTokenCacheItem item) => instanceMetadata.Aliases.ContainsOrdinalIgnoreCase(item.Environment), requestParams.RequestContext.Logger, "Filtering AT by environment");
	}

	private static MsalAccessTokenCacheItem FilterTokensByPopKeyId(MsalAccessTokenCacheItem item, AuthenticationRequestParameters authenticationRequest)
	{
		if (item == null)
		{
			return null;
		}
		string requestKid = authenticationRequest.AuthenticationScheme.KeyId;
		if (string.IsNullOrEmpty(item.KeyId) && string.IsNullOrEmpty(requestKid))
		{
			authenticationRequest.RequestContext.Logger.Verbose(() => "Bearer token found");
			return item;
		}
		if (string.Equals(item.KeyId, requestKid, StringComparison.OrdinalIgnoreCase))
		{
			authenticationRequest.RequestContext.Logger.Verbose(() => "Keyed token found");
			return item;
		}
		authenticationRequest.RequestContext.Logger.Info(() => "A token bound to the wrong key was found. Token key id: " + item.KeyId + " Request key id: " + requestKid);
		return null;
	}

	private void FilterTokensByClientId<T>(List<T> tokenCacheItems) where T : MsalCredentialCacheItemBase
	{
		tokenCacheItems.RemoveAll((T x) => !x.ClientId.Equals(ClientId, StringComparison.OrdinalIgnoreCase));
	}

	internal async Task ExpireAllAccessTokensForTestAsync()
	{
		ITokenCacheInternal tokenCacheInternal = this;
		ITokenCacheAccessor accessor = tokenCacheInternal.Accessor;
		foreach (MsalAccessTokenCacheItem allAccessToken in accessor.GetAllAccessTokens())
		{
			accessor.SaveAccessToken(allAccessToken.WithExpiresOn(DateTimeOffset.UtcNow));
		}
		if (tokenCacheInternal.IsAppSubscribedToSerializationEvents())
		{
			TokenCacheNotificationArgs args = new TokenCacheNotificationArgs(this, ClientId, null, hasStateChanged: true, tokenCacheInternal.IsApplicationCache, null, tokenCacheInternal.HasTokensNoLocks(), null, default(CancellationToken), default(Guid), null, null, null, piiLoggingEnabled: false);
			await tokenCacheInternal.OnAfterAccessAsync(args).ConfigureAwait(continueOnCapturedContext: false);
		}
	}

	async Task<MsalRefreshTokenCacheItem> ITokenCacheInternal.FindRefreshTokenAsync(AuthenticationRequestParameters requestParams, string familyId)
	{
		if (requestParams.Authority == null)
		{
			return null;
		}
		string keyFromRequest = CacheKeyFactory.GetKeyFromRequest(requestParams);
		List<MsalRefreshTokenCacheItem> refreshTokens = Accessor.GetAllRefreshTokens(keyFromRequest);
		requestParams.RequestContext.Logger.Always($"[FindRefreshTokenAsync] Discovered {refreshTokens.Count} refresh tokens in cache using key: {keyFromRequest}");
		if (refreshTokens.Count != 0)
		{
			FilterRefreshTokensByHomeAccountIdOrAssertion(refreshTokens, requestParams, familyId);
			if (!requestParams.AppConfig.MultiCloudSupportEnabled)
			{
				string[] aliases = (await ServiceBundle.InstanceDiscoveryManager.GetMetadataEntryTryAvoidNetworkAsync(requestParams.AuthorityInfo, refreshTokens.Select((MsalRefreshTokenCacheItem rt) => rt.Environment), requestParams.RequestContext).ConfigureAwait(continueOnCapturedContext: false)).Aliases;
				refreshTokens.RemoveAll((MsalRefreshTokenCacheItem item) => !aliases.ContainsOrdinalIgnoreCase(item.Environment));
			}
			requestParams.RequestContext.Logger.Info(() => "[FindRefreshTokenAsync] Refresh token found in the cache? - " + (refreshTokens.Count != 0));
			if (refreshTokens.Count > 0)
			{
				return refreshTokens.FirstOrDefault();
			}
		}
		else
		{
			requestParams.RequestContext.Logger.Verbose(() => "[FindRefreshTokenAsync] No RTs found in the MSAL cache ");
		}
		requestParams.RequestContext.Logger.Verbose(() => "[FindRefreshTokenAsync] Checking ADAL cache for matching RT. ");
		if (IsLegacyAdalCacheEnabled(requestParams) && requestParams.Account != null && string.IsNullOrEmpty(familyId))
		{
			string[] aliases2 = (await ServiceBundle.InstanceDiscoveryManager.GetMetadataEntryTryAvoidNetworkAsync(requestParams.AuthorityInfo, refreshTokens.Select((MsalRefreshTokenCacheItem rt) => rt.Environment), requestParams.RequestContext).ConfigureAwait(continueOnCapturedContext: false)).Aliases;
			return CacheFallbackOperations.GetRefreshToken(requestParams.RequestContext.Logger, LegacyCachePersistence, aliases2, requestParams.AppConfig.ClientId, requestParams.Account);
		}
		return null;
	}

	private static void FilterRefreshTokensByHomeAccountIdOrAssertion(List<MsalRefreshTokenCacheItem> cacheItems, AuthenticationRequestParameters requestParams, string familyId)
	{
		if (ApiEvent.IsOnBehalfOfRequest(requestParams.ApiId))
		{
			cacheItems.FilterWithLogging((MsalRefreshTokenCacheItem item) => !string.IsNullOrEmpty(item.OboCacheKey) && item.OboCacheKey.Equals((!string.IsNullOrEmpty(requestParams.LongRunningOboCacheKey)) ? requestParams.LongRunningOboCacheKey : requestParams.UserAssertion.AssertionHash, StringComparison.OrdinalIgnoreCase), requestParams.RequestContext.Logger, (!string.IsNullOrEmpty(requestParams.LongRunningOboCacheKey)) ? ("Filtering RT by user-provided cache key: " + requestParams.LongRunningOboCacheKey) : ("Filtering RT by user assertion: " + requestParams.UserAssertion.AssertionHash));
		}
		else
		{
			cacheItems.FilterWithLogging((MsalRefreshTokenCacheItem item) => item.HomeAccountId.Equals(requestParams.Account.HomeAccountId?.Identifier, StringComparison.OrdinalIgnoreCase), requestParams.RequestContext.Logger, "Filtering RT by home account id");
		}
		cacheItems.FilterWithLogging((MsalRefreshTokenCacheItem item) => string.Equals(item.FamilyId ?? string.Empty, familyId ?? string.Empty, StringComparison.OrdinalIgnoreCase), requestParams.RequestContext.Logger, "Filtering RT by family id");
		if (string.IsNullOrEmpty(familyId))
		{
			cacheItems.FilterWithLogging((MsalRefreshTokenCacheItem item) => item.ClientId.Equals(requestParams.AppConfig.ClientId, StringComparison.OrdinalIgnoreCase), requestParams.RequestContext.Logger, "Filtering RT by client id");
		}
	}

	async Task<bool?> ITokenCacheInternal.IsFociMemberAsync(AuthenticationRequestParameters requestParams, string familyId)
	{
		ILoggerAdapter logger = requestParams.RequestContext.Logger;
		if (requestParams?.AuthorityInfo?.CanonicalAuthority == null)
		{
			logger.Warning("No authority details, can't check app metadata. Returning unknown. ");
			return null;
		}
		List<MsalAppMetadataCacheItem> allAppMetadata = Accessor.GetAllAppMetadata();
		MsalAppMetadataCacheItem msalAppMetadataCacheItem = (await ServiceBundle.InstanceDiscoveryManager.GetMetadataEntryTryAvoidNetworkAsync(requestParams.AuthorityInfo, allAppMetadata.Select((MsalAppMetadataCacheItem m) => m.Environment), requestParams.RequestContext).ConfigureAwait(continueOnCapturedContext: false)).Aliases.Select((string env) => Accessor.GetAppMetadata(new MsalAppMetadataCacheItem(ClientId, env, null))).FirstOrDefault((MsalAppMetadataCacheItem item) => item != null);
		if (msalAppMetadataCacheItem == null)
		{
			logger.Warning("No app metadata found. Returning unknown. ");
			return null;
		}
		return msalAppMetadataCacheItem.FamilyId == familyId;
	}

	async Task<IEnumerable<IAccount>> ITokenCacheInternal.GetAccountsAsync(AuthenticationRequestParameters requestParameters)
	{
		ILoggerAdapter logger = requestParameters.RequestContext.Logger;
		string environment = requestParameters.AuthorityInfo.Host;
		bool num = !_featureFlags.IsFociEnabled;
		string keyFromRequest = CacheKeyFactory.GetKeyFromRequest(requestParameters);
		List<MsalRefreshTokenCacheItem> refreshTokenCacheItems = Accessor.GetAllRefreshTokens(keyFromRequest);
		List<MsalAccountCacheItem> accountCacheItems = Accessor.GetAllAccounts(keyFromRequest);
		if (num)
		{
			FilterTokensByClientId(refreshTokenCacheItems);
		}
		if (logger.IsLoggingEnabled(LogLevel.Verbose))
		{
			logger.Verbose(() => $"[GetAccounts] Found {refreshTokenCacheItems.Count} RTs and {accountCacheItems.Count} accounts in MSAL cache. ");
		}
		ISet<string> set = new HashSet<string>(accountCacheItems.Select((MsalAccountCacheItem aci) => aci.Environment), StringComparer.OrdinalIgnoreCase);
		set.UnionWith(refreshTokenCacheItems.Select((MsalRefreshTokenCacheItem rt) => rt.Environment));
		AdalUsersForMsal adalUsersResult = null;
		if (IsLegacyAdalCacheEnabled(requestParameters))
		{
			adalUsersResult = CacheFallbackOperations.GetAllAdalUsersForMsal(logger, LegacyCachePersistence, ClientId);
			set.UnionWith(adalUsersResult.GetAdalUserEnvironments());
		}
		InstanceDiscoveryMetadataEntry instanceMetadata = await ServiceBundle.InstanceDiscoveryManager.GetMetadataEntryTryAvoidNetworkAsync(requestParameters.AuthorityInfo, set, requestParameters.RequestContext).ConfigureAwait(continueOnCapturedContext: false);
		if (!requestParameters.AppConfig.MultiCloudSupportEnabled)
		{
			refreshTokenCacheItems.RemoveAll((MsalRefreshTokenCacheItem rt) => !instanceMetadata.Aliases.ContainsOrdinalIgnoreCase(rt.Environment));
			accountCacheItems.RemoveAll((MsalAccountCacheItem acc) => !instanceMetadata.Aliases.ContainsOrdinalIgnoreCase(acc.Environment));
		}
		if (logger.IsLoggingEnabled(LogLevel.Verbose))
		{
			logger.Verbose(() => $"[GetAccounts] Found {refreshTokenCacheItems.Count} RTs and {accountCacheItems.Count} accounts in MSAL cache after environment filtering. ");
		}
		IDictionary<string, Account> clientInfoToAccountMap = new Dictionary<string, Account>();
		foreach (MsalRefreshTokenCacheItem rtItem in refreshTokenCacheItems)
		{
			foreach (MsalAccountCacheItem account in accountCacheItems)
			{
				if (RtMatchesAccount(rtItem, account))
				{
					IDictionary<string, TenantProfile> dictionary = await GetTenantProfilesAsync(requestParameters, account.HomeAccountId).ConfigureAwait(continueOnCapturedContext: false);
					clientInfoToAccountMap[rtItem.HomeAccountId] = new Account(account.HomeAccountId, account.PreferredUsername, requestParameters.AppConfig.MultiCloudSupportEnabled ? account.Environment : environment, account.WamAccountIds, dictionary?.Values);
					break;
				}
			}
		}
		if (IsLegacyAdalCacheEnabled(requestParameters))
		{
			UpdateMapWithAdalAccountsWithClientInfo(environment, instanceMetadata.Aliases, adalUsersResult, clientInfoToAccountMap);
		}
		if (requestParameters.AppConfig.IsBrokerEnabled && ServiceBundle.PlatformProxy.BrokerSupportsWamAccounts)
		{
			foreach (MsalAccountCacheItem account in accountCacheItems)
			{
				if (!clientInfoToAccountMap.ContainsKey(account.HomeAccountId) && account.WamAccountIds != null && account.WamAccountIds.ContainsKey(requestParameters.AppConfig.ClientId))
				{
					IDictionary<string, TenantProfile> dictionary2 = await GetTenantProfilesAsync(requestParameters, account.HomeAccountId).ConfigureAwait(continueOnCapturedContext: false);
					Account value = new Account(account.HomeAccountId, account.PreferredUsername, account.Environment, account.WamAccountIds, dictionary2?.Values);
					clientInfoToAccountMap[account.HomeAccountId] = value;
				}
			}
		}
		List<IAccount> accounts = new List<IAccount>(clientInfoToAccountMap.Values);
		if (IsLegacyAdalCacheEnabled(requestParameters))
		{
			UpdateWithAdalAccountsWithoutClientInfo(environment, instanceMetadata.Aliases, adalUsersResult, accounts);
		}
		if (!string.IsNullOrEmpty(requestParameters.HomeAccountId))
		{
			accounts = accounts.Where((IAccount acc) => acc.HomeAccountId.Identifier.Equals(requestParameters.HomeAccountId, StringComparison.OrdinalIgnoreCase)).ToList();
			if (logger.IsLoggingEnabled(LogLevel.Verbose))
			{
				logger.Verbose(() => $"Filtered by home account id. Remaining accounts {accounts.Count} ");
			}
		}
		return accounts;
	}

	private static void UpdateMapWithAdalAccountsWithClientInfo(string envFromRequest, IEnumerable<string> envAliases, AdalUsersForMsal adalUsers, IDictionary<string, Account> clientInfoToAccountMap)
	{
		foreach (KeyValuePair<string, AdalUserInfo> item in adalUsers?.GetUsersWithClientInfo(envAliases))
		{
			string text = ClientInfo.CreateFromJson(item.Key).ToAccountIdentifier();
			if (!clientInfoToAccountMap.ContainsKey(text))
			{
				clientInfoToAccountMap[text] = new Account(text, item.Value.DisplayableId, envFromRequest);
			}
		}
	}

	private static void UpdateWithAdalAccountsWithoutClientInfo(string envFromRequest, IEnumerable<string> envAliases, AdalUsersForMsal adalUsers, List<IAccount> accounts)
	{
		List<string> list = accounts.Select((IAccount a) => a.Username).Distinct().ToList();
		foreach (AdalUserInfo item in adalUsers?.GetUsersWithoutClientInfo(envAliases))
		{
			if (!string.IsNullOrEmpty(item.DisplayableId) && !list.Contains(item.DisplayableId))
			{
				accounts.Add(new Account(null, item.DisplayableId, envFromRequest));
				list.Add(item.DisplayableId);
			}
		}
	}

	MsalIdTokenCacheItem ITokenCacheInternal.GetIdTokenCacheItem(MsalAccessTokenCacheItem msalAccessTokenCacheItem)
	{
		return Accessor.GetIdToken(msalAccessTokenCacheItem);
	}

	private async Task<IDictionary<string, TenantProfile>> GetTenantProfilesAsync(AuthenticationRequestParameters requestParameters, string homeAccountId)
	{
		if (!requestParameters.AuthorityInfo.CanBeTenanted)
		{
			return null;
		}
		if (homeAccountId == null)
		{
			requestParameters.RequestContext.Logger.Warning("No homeAccountId, skipping tenant profiles");
			return null;
		}
		List<MsalIdTokenCacheItem> idTokenCacheItems = Accessor.GetAllIdTokens(homeAccountId);
		FilterTokensByClientId(idTokenCacheItems);
		if (!requestParameters.AppConfig.MultiCloudSupportEnabled)
		{
			ISet<string> existingEnvironmentsInCache = new HashSet<string>(idTokenCacheItems.Select((MsalIdTokenCacheItem aci) => aci.Environment), StringComparer.OrdinalIgnoreCase);
			InstanceDiscoveryMetadataEntry instanceMetadata = await ServiceBundle.InstanceDiscoveryManager.GetMetadataEntryTryAvoidNetworkAsync(requestParameters.AuthorityInfo, existingEnvironmentsInCache, requestParameters.RequestContext).ConfigureAwait(continueOnCapturedContext: false);
			idTokenCacheItems.RemoveAll((MsalIdTokenCacheItem idToken) => !instanceMetadata.Aliases.ContainsOrdinalIgnoreCase(idToken.Environment));
		}
		idTokenCacheItems.RemoveAll((MsalIdTokenCacheItem idToken) => !homeAccountId.Equals(idToken.HomeAccountId));
		Dictionary<string, TenantProfile> dictionary = new Dictionary<string, TenantProfile>();
		foreach (MsalIdTokenCacheItem item in idTokenCacheItems)
		{
			dictionary[item.TenantId] = new TenantProfile(item);
		}
		return dictionary;
	}

	async Task<Account> ITokenCacheInternal.GetAccountAssociatedWithAccessTokenAsync(AuthenticationRequestParameters requestParameters, MsalAccessTokenCacheItem msalAccessTokenCacheItem)
	{
		IDictionary<string, TenantProfile> dictionary = await GetTenantProfilesAsync(requestParameters, msalAccessTokenCacheItem.HomeAccountId).ConfigureAwait(continueOnCapturedContext: false);
		MsalAccountCacheItem account = Accessor.GetAccount(new MsalAccountCacheItem(msalAccessTokenCacheItem.Environment, msalAccessTokenCacheItem.TenantId, msalAccessTokenCacheItem.HomeAccountId, requestParameters.Account?.Username));
		return new Account(msalAccessTokenCacheItem.HomeAccountId, account?.PreferredUsername, account?.Environment, account?.WamAccountIds, dictionary?.Values);
	}

	async Task<bool> ITokenCacheInternal.StopLongRunningOboProcessAsync(string longRunningOboCacheKey, AuthenticationRequestParameters requestParameters)
	{
		requestParameters.RequestContext.Logger.Verbose(() => "[StopLongRunningOboProcessAsync] Entering token cache semaphore. Count " + _semaphoreSlim.GetCurrentCountLogMessage());
		await _semaphoreSlim.WaitAsync(requestParameters.RequestContext.UserCancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		requestParameters.RequestContext.Logger.Verbose(() => "[StopLongRunningOboProcessAsync] Entered token cache semaphore");
		try
		{
			requestParameters.RequestContext.Logger.Info(() => "[StopLongRunningOboProcessAsync] Stopping long running OBO process by removing tokens from cache.");
			ITokenCacheInternal tokenCacheInternal = this;
			bool tokensRemoved;
			try
			{
				if (tokenCacheInternal.IsAppSubscribedToSerializationEvents())
				{
					TokenCacheNotificationArgs args = new TokenCacheNotificationArgs(this, ClientId, null, hasStateChanged: false, tokenCacheInternal.IsApplicationCache, longRunningOboCacheKey, tokenCacheInternal.HasTokensNoLocks(), null, requestParameters.RequestContext.UserCancellationToken, requestParameters.RequestContext.CorrelationId, requestParameters.Scope, requestParameters.AuthorityManager.OriginalAuthority.TenantId, requestParameters.RequestContext.Logger.IdentityLogger, requestParameters.RequestContext.Logger.PiiLoggingEnabled);
					await tokenCacheInternal.OnBeforeAccessAsync(args).ConfigureAwait(continueOnCapturedContext: false);
					await tokenCacheInternal.OnBeforeWriteAsync(args).ConfigureAwait(continueOnCapturedContext: false);
				}
				tokensRemoved = RemoveOboTokensInternal(longRunningOboCacheKey, requestParameters.RequestContext);
			}
			finally
			{
				if (tokenCacheInternal.IsAppSubscribedToSerializationEvents())
				{
					TokenCacheNotificationArgs args2 = new TokenCacheNotificationArgs(this, ClientId, null, hasStateChanged: true, tokenCacheInternal.IsApplicationCache, longRunningOboCacheKey, tokenCacheInternal.HasTokensNoLocks(), null, requestParameters.RequestContext.UserCancellationToken, requestParameters.RequestContext.CorrelationId, requestParameters.Scope, requestParameters.AuthorityManager.OriginalAuthority.TenantId, requestParameters.RequestContext.Logger.IdentityLogger, requestParameters.RequestContext.Logger.PiiLoggingEnabled);
					await tokenCacheInternal.OnAfterAccessAsync(args2).ConfigureAwait(continueOnCapturedContext: false);
				}
			}
			return tokensRemoved;
		}
		finally
		{
			HasStateChanged = false;
			_semaphoreSlim.Release();
		}
	}

	async Task ITokenCacheInternal.RemoveAccountAsync(IAccount account, AuthenticationRequestParameters requestParameters)
	{
		requestParameters.RequestContext.Logger.Verbose(() => "[RemoveAccountAsync] Entering token cache semaphore. Count " + _semaphoreSlim.GetCurrentCountLogMessage());
		await _semaphoreSlim.WaitAsync(requestParameters.RequestContext.UserCancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		requestParameters.RequestContext.Logger.Verbose(() => "[RemoveAccountAsync] Entered token cache semaphore");
		string cacheKey = account.HomeAccountId?.Identifier;
		try
		{
			requestParameters.RequestContext.Logger.Info("[RemoveAccountAsync] Removing account from cache.");
			ITokenCacheInternal tokenCacheInternal = this;
			try
			{
				if (tokenCacheInternal.IsAppSubscribedToSerializationEvents())
				{
					TokenCacheNotificationArgs args = new TokenCacheNotificationArgs(this, ClientId, account, hasStateChanged: true, tokenCacheInternal.IsApplicationCache, cacheKey, tokenCacheInternal.HasTokensNoLocks(), null, requestParameters.RequestContext.UserCancellationToken, requestParameters.RequestContext.CorrelationId, requestParameters.Scope, requestParameters.AuthorityManager.OriginalAuthority.TenantId, requestParameters.RequestContext.Logger.IdentityLogger, requestParameters.RequestContext.Logger.PiiLoggingEnabled);
					await tokenCacheInternal.OnBeforeAccessAsync(args).ConfigureAwait(continueOnCapturedContext: false);
					await tokenCacheInternal.OnBeforeWriteAsync(args).ConfigureAwait(continueOnCapturedContext: false);
				}
				RemoveAccountInternal(account, requestParameters.RequestContext);
				if (IsLegacyAdalCacheEnabled(requestParameters))
				{
					CacheFallbackOperations.RemoveAdalUser(requestParameters.RequestContext.Logger, LegacyCachePersistence, ClientId, account?.Username, cacheKey);
				}
			}
			finally
			{
				if (tokenCacheInternal.IsAppSubscribedToSerializationEvents())
				{
					TokenCacheNotificationArgs args2 = new TokenCacheNotificationArgs(this, ClientId, account, hasStateChanged: true, tokenCacheInternal.IsApplicationCache, cacheKey, tokenCacheInternal.HasTokensNoLocks(), null, requestParameters.RequestContext.UserCancellationToken, requestParameters.RequestContext.CorrelationId, requestParameters.Scope, requestParameters.AuthorityManager.OriginalAuthority.TenantId, requestParameters.RequestContext.Logger.IdentityLogger, requestParameters.RequestContext.Logger.PiiLoggingEnabled);
					await tokenCacheInternal.OnAfterAccessAsync(args2).ConfigureAwait(continueOnCapturedContext: false);
				}
			}
		}
		finally
		{
			HasStateChanged = false;
			_semaphoreSlim.Release();
		}
	}

	bool ITokenCacheInternal.HasTokensNoLocks()
	{
		return Accessor.HasAccessOrRefreshTokens();
	}

	private bool RemoveOboTokensInternal(string oboPartitionKey, RequestContext requestContext)
	{
		ILoggerAdapter logger = requestContext.Logger;
		List<MsalRefreshTokenCacheItem> allRefreshTokens = Accessor.GetAllRefreshTokens(oboPartitionKey, logger);
		allRefreshTokens.RemoveAll((MsalRefreshTokenCacheItem item) => !(item?.OboCacheKey.Equals(oboPartitionKey, StringComparison.OrdinalIgnoreCase)).Value);
		bool filterByClientId;
		int num = RemoveRefreshTokens(allRefreshTokens, logger, out filterByClientId);
		List<MsalAccessTokenCacheItem> allAccessTokens = Accessor.GetAllAccessTokens(oboPartitionKey, logger);
		allAccessTokens.RemoveAll((MsalAccessTokenCacheItem item) => !(item?.OboCacheKey.Equals(oboPartitionKey, StringComparison.OrdinalIgnoreCase)).Value);
		int num2 = RemoveAccessTokens(allAccessTokens, logger, filterByClientId);
		if (num <= 0)
		{
			return num2 > 0;
		}
		return true;
	}

	internal void RemoveAccountInternal(IAccount account, RequestContext requestContext)
	{
		if (account.HomeAccountId != null)
		{
			string partitionKey = account.HomeAccountId.Identifier;
			ILoggerAdapter logger = requestContext.Logger;
			List<MsalRefreshTokenCacheItem> allRefreshTokens = Accessor.GetAllRefreshTokens(partitionKey, logger);
			allRefreshTokens.RemoveAll((MsalRefreshTokenCacheItem item) => !item.HomeAccountId.Equals(partitionKey, StringComparison.OrdinalIgnoreCase));
			RemoveRefreshTokens(allRefreshTokens, logger, out var filterByClientId);
			List<MsalAccessTokenCacheItem> allAccessTokens = Accessor.GetAllAccessTokens(partitionKey, logger);
			allAccessTokens.RemoveAll((MsalAccessTokenCacheItem item) => !item.HomeAccountId.Equals(partitionKey, StringComparison.OrdinalIgnoreCase));
			RemoveAccessTokens(allAccessTokens, logger, filterByClientId);
			RemoveIdTokens(partitionKey, logger, filterByClientId);
			RemoveAccounts(account);
		}
	}

	private int RemoveRefreshTokens(List<MsalRefreshTokenCacheItem> refreshTokens, ILoggerAdapter logger, out bool filterByClientId)
	{
		filterByClientId = !_featureFlags.IsFociEnabled || !FrtExists(refreshTokens);
		if (filterByClientId)
		{
			FilterTokensByClientId(refreshTokens);
		}
		foreach (MsalRefreshTokenCacheItem refreshToken in refreshTokens)
		{
			Accessor.DeleteRefreshToken(refreshToken);
		}
		logger.Info(() => $"[RemoveRefreshTokens] Deleted {refreshTokens.Count} refresh tokens.");
		return refreshTokens.Count;
	}

	private int RemoveAccessTokens(List<MsalAccessTokenCacheItem> accessTokens, ILoggerAdapter logger, bool filterByClientId)
	{
		if (filterByClientId)
		{
			FilterTokensByClientId(accessTokens);
		}
		foreach (MsalAccessTokenCacheItem accessToken in accessTokens)
		{
			Accessor.DeleteAccessToken(accessToken);
		}
		logger.Info(() => $"[RemoveAccessTokens] Deleted {accessTokens.Count} access tokens.");
		return accessTokens.Count;
	}

	private int RemoveIdTokens(string partitionKey, ILoggerAdapter logger, bool filterByClientId)
	{
		List<MsalIdTokenCacheItem> idTokens = Accessor.GetAllIdTokens(partitionKey);
		idTokens.RemoveAll((MsalIdTokenCacheItem item) => !item.HomeAccountId.Equals(partitionKey, StringComparison.OrdinalIgnoreCase));
		if (filterByClientId)
		{
			FilterTokensByClientId(idTokens);
		}
		foreach (MsalIdTokenCacheItem item in idTokens)
		{
			Accessor.DeleteIdToken(item);
		}
		logger.Info(() => $"[RemoveIdTokens] Deleted {idTokens.Count} ID tokens.");
		return idTokens.Count;
	}

	private void RemoveAccounts(IAccount account)
	{
		if (account == null)
		{
			return;
		}
		List<MsalAccountCacheItem> allAccounts = Accessor.GetAllAccounts(account.HomeAccountId.Identifier);
		allAccounts.RemoveAll((MsalAccountCacheItem item) => !item.HomeAccountId.Equals(account.HomeAccountId.Identifier, StringComparison.OrdinalIgnoreCase) || !item.PreferredUsername.Equals(account.Username, StringComparison.OrdinalIgnoreCase));
		foreach (MsalAccountCacheItem item in allAccounts)
		{
			Accessor.DeleteAccount(item);
		}
	}

	bool ITokenCacheInternal.IsAppSubscribedToSerializationEvents()
	{
		if (BeforeAccess == null && AfterAccess == null && BeforeWrite == null && AsyncBeforeAccess == null && AsyncAfterAccess == null)
		{
			return AsyncBeforeWrite != null;
		}
		return true;
	}

	async Task ITokenCacheInternal.OnAfterAccessAsync(TokenCacheNotificationArgs args)
	{
		AfterAccess?.Invoke(args);
		if (AsyncAfterAccess != null)
		{
			await AsyncAfterAccess(args).ConfigureAwait(continueOnCapturedContext: false);
		}
	}

	async Task ITokenCacheInternal.OnBeforeAccessAsync(TokenCacheNotificationArgs args)
	{
		BeforeAccess?.Invoke(args);
		if (AsyncBeforeAccess != null)
		{
			await AsyncBeforeAccess(args).ConfigureAwait(continueOnCapturedContext: false);
		}
	}

	async Task ITokenCacheInternal.OnBeforeWriteAsync(TokenCacheNotificationArgs args)
	{
		HasStateChanged = true;
		args.HasStateChanged = true;
		BeforeWrite?.Invoke(args);
		if (AsyncBeforeWrite != null)
		{
			await AsyncBeforeWrite(args).ConfigureAwait(continueOnCapturedContext: false);
		}
	}

	public void SetBeforeAccess(TokenCacheCallback beforeAccess)
	{
		Validate();
		BeforeAccess = beforeAccess;
	}

	public void SetAfterAccess(TokenCacheCallback afterAccess)
	{
		Validate();
		AfterAccess = afterAccess;
	}

	public void SetBeforeWrite(TokenCacheCallback beforeWrite)
	{
		Validate();
		BeforeWrite = beforeWrite;
	}

	public void SetBeforeAccessAsync(Func<TokenCacheNotificationArgs, Task> beforeAccess)
	{
		Validate();
		AsyncBeforeAccess = beforeAccess;
	}

	public void SetAfterAccessAsync(Func<TokenCacheNotificationArgs, Task> afterAccess)
	{
		Validate();
		AsyncAfterAccess = afterAccess;
	}

	public void SetBeforeWriteAsync(Func<TokenCacheNotificationArgs, Task> beforeWrite)
	{
		Validate();
		AsyncBeforeWrite = beforeWrite;
	}

	private void Validate()
	{
		if (ServiceBundle.Config.AccessorOptions != null)
		{
			throw new MsalClientException("static_cache_with_external_serialization", "You configured MSAL cache serialization at the same time with internal caching options. These are mutually exclusive. Use only one option. Web site and web api scenarios should rely on external cache serialization, as internal cache serialization cannot scale. See https://aka.ms/msal-net-token-cache-serialization .");
		}
	}

	byte[] ITokenCacheSerializer.SerializeAdalV3()
	{
		return LegacyCachePersistence.LoadCache();
	}

	void ITokenCacheSerializer.DeserializeAdalV3(byte[] adalV3State)
	{
		LegacyCachePersistence.WriteCache(adalV3State);
	}

	byte[] ITokenCacheSerializer.SerializeMsalV2()
	{
		return new TokenCacheDictionarySerializer(Accessor).Serialize(_unknownNodes);
	}

	void ITokenCacheSerializer.DeserializeMsalV2(byte[] msalV2State)
	{
		_unknownNodes = new TokenCacheDictionarySerializer(Accessor).Deserialize(msalV2State, clearExistingCacheData: false);
	}

	byte[] ITokenCacheSerializer.SerializeMsalV3()
	{
		return new TokenCacheJsonSerializer(Accessor).Serialize(_unknownNodes);
	}

	void ITokenCacheSerializer.DeserializeMsalV3(byte[] msalV3State, bool shouldClearExistingCache)
	{
		if (msalV3State == null || msalV3State.Length == 0)
		{
			if (shouldClearExistingCache)
			{
				Accessor.Clear();
			}
		}
		else
		{
			_unknownNodes = new TokenCacheJsonSerializer(Accessor).Deserialize(msalV3State, shouldClearExistingCache);
		}
	}
}
