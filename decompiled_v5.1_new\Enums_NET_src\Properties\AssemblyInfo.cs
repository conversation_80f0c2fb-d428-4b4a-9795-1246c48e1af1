using System;
using System.Diagnostics;
using System.Reflection;
using System.Resources;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;
using System.Security;

[assembly: AllowPartiallyTrustedCallers]
[assembly: ComVisible(false)]
[assembly: Guid("f2059370-3a4e-4885-8234-9aedade06108")]
[assembly: CLSCompliant(true)]
[assembly: AssemblyCompany("Tyler Brinkley")]
[assembly: AssemblyConfiguration("Release")]
[assembly: AssemblyCopyright("Copyright © Tyler Brinkley 2016")]
[assembly: AssemblyDescription("Enums.NET is a high-performance type-safe .NET enum utility library")]
[assembly: AssemblyFileVersion("4.0.1")]
[assembly: AssemblyInformationalVersion("4.0.1+a35664d000fb3e7c4f75ec303b8dc419837ab0d4")]
[assembly: AssemblyProduct("Enums.NET")]
[assembly: AssemblyTitle("Enums.NET .NET Core 3.0")]
[assembly: AssemblyMetadata("RepositoryUrl", "https://github.com/TylerBrinkley/Enums.NET")]
[assembly: NeutralResourcesLanguage("en-US")]
[assembly: AssemblyVersion("*******")]
