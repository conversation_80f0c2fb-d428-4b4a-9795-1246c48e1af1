using System;
using System.Globalization;

namespace Microsoft.Identity.Client.Instance;

internal class CiamAuthority : AadAuthority
{
	internal CiamAuthority(AuthorityInfo authorityInfo)
		: base(authorityInfo)
	{
	}

	internal override string GetTenantedAuthority(string tenantId, bool forceSpecifiedTenant = false)
	{
		if (!string.IsNullOrEmpty(tenantId) && (forceSpecifiedTenant || IsCommonOrganizationsOrConsumersTenant()))
		{
			Uri canonicalAuthority = base.AuthorityInfo.CanonicalAuthority;
			return string.Format(CultureInfo.InvariantCulture, "https://{0}/{1}/", canonicalAuthority.Authority, tenantId);
		}
		return base.AuthorityInfo.CanonicalAuthority.AbsoluteUri;
	}

	internal static Uri TransformAuthority(Uri ciamAuthority)
	{
		string text = ciamAuthority.Host + ciamAuthority.AbsolutePath;
		if (string.Equals(ciamAuthority.AbsolutePath, "/"))
		{
			string text2 = text.Substring(0, text.IndexOf(".ciamlogin.com", StringComparison.OrdinalIgnoreCase));
			string text3 = "https://" + text2 + ".ciamlogin.com/";
			string text4 = text2 + ".onmicrosoft.com";
			return new Uri(text3 + text4);
		}
		return ciamAuthority;
	}
}
