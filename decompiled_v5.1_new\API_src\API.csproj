<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <AssemblyName>API</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <PropertyGroup>
    <LangVersion>12.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup />
  <ItemGroup />
  <ItemGroup>
    <Reference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson">
      <HintPath>..\..\Relesez5.1\Microsoft.AspNetCore.Mvc.NewtonsoftJson.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>..\..\Relesez5.1\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Quartz">
      <HintPath>..\..\Relesez5.1\Quartz.dll</HintPath>
    </Reference>
    <Reference Include="BiliveDanmakuAgent">
      <HintPath>..\..\Relesez5.1\BiliveDanmakuAgent.dll</HintPath>
    </Reference>
    <Reference Include="Jint">
      <HintPath>..\..\Relesez5.1\Jint.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Data.SqlClient">
      <HintPath>..\..\Relesez5.1\Microsoft.Data.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.Core">
      <HintPath>..\..\Relesez5.1\NPOI.Core.dll</HintPath>
    </Reference>
    <Reference Include="NPOI.OOXML">
      <HintPath>..\..\Relesez5.1\NPOI.OOXML.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.ImageSharp">
      <HintPath>..\..\Relesez5.1\SixLabors.ImageSharp.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.Fonts">
      <HintPath>..\..\Relesez5.1\SixLabors.Fonts.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.ImageSharp.Drawing">
      <HintPath>..\..\Relesez5.1\SixLabors.ImageSharp.Drawing.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>