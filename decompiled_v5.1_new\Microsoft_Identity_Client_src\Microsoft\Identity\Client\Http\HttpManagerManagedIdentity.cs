namespace Microsoft.Identity.Client.Http;

internal class HttpManagerManagedIdentity : HttpManagerWithRetry
{
	public HttpManagerManagedIdentity(IMsalHttpClientFactory httpClientFactory)
		: base(httpClientFactory)
	{
	}

	protected override bool IsRetryableStatusCode(int statusCode)
	{
		switch (statusCode)
		{
		case 404:
		case 408:
		case 429:
		case 500:
		case 503:
		case 504:
			return true;
		default:
			return false;
		}
	}
}
