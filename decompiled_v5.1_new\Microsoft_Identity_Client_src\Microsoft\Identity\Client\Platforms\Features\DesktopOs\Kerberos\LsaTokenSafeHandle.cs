using System;
using System.ComponentModel;
using System.Runtime.InteropServices;

namespace Microsoft.Identity.Client.Platforms.Features.DesktopOs.Kerberos;

internal class LsaTokenSafeHandle : SafeHandle
{
	public bool Impersonating { get; private set; }

	public override bool IsInvalid => handle == IntPtr.Zero;

	public LsaTokenSafeHandle()
		: base(IntPtr.Zero, ownsHandle: true)
	{
	}

	protected override bool ReleaseHandle()
	{
		Revert();
		if (!NativeMethods.CloseHandle(handle))
		{
			throw new Win32Exception(Marshal.GetLastWin32Error());
		}
		return true;
	}

	private void Revert()
	{
		if (Impersonating)
		{
			if (!NativeMethods.RevertToSelf())
			{
				throw new Win32Exception(Marshal.GetLastWin32Error());
			}
			Impersonating = false;
		}
	}
}
