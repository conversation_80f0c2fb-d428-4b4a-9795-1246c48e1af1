﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
<resheader name="resmimetype"><value>text/microsoft-resx</value></resheader><resheader name="version"><value>1.3</value></resheader><resheader name="reader"><value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></resheader><resheader name="writer"><value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value></resheader><data name="Arithmetic_Trig_Undefined_Tan_PiOver2" xml:space="preserve"><value>The tangent of π/2 is undefined.</value></data>
  <data name="Overflow_Double" xml:space="preserve"><value>BigDecimal is too large for a Double.</value></data>
  <data name="Arg_MustBeOfType" xml:space="preserve"><value>Argument must be of type {0}</value></data>
  <data name="Arg_MustBeAPositiveInteger" xml:space="preserve"><value>Must be a positive integer.</value></data>
  <data name="Arithmetic_Trig_Undefined_Csch_Zero" xml:space="preserve"><value>The hyperbolic cosecant of zero is undefined.</value></data>
  <data name="Overflow_Single" xml:space="preserve"><value>BigDecimal is too large for a Single.</value></data>
  <data name="Overflow_UInt32" xml:space="preserve"><value>BigDecimal is too large for a UInt32.</value></data>
  <data name="Arithmetic_Trig_Undefined_Cot_Zero" xml:space="preserve"><value>The cotangent of zero is undefined.</value></data>
  <data name="Arithmetic_Trig_Undefined_Sec_OddPiOver2" xml:space="preserve"><value>The secant of (2*n + 1)*π/2 (an odd multiple of π/2) is undefined.</value></data>
  <data name="Arithmetic_Trig_Undefined_Csc_Pi" xml:space="preserve"><value>The cosecant of π is undefined.</value></data>
  <data name="Arithmetic_Trig_Undefined_Cot_Pi" xml:space="preserve"><value>The cotangent of π is undefined.</value></data>
  <data name="NotFinite_NaN" xml:space="preserve"><value>value is not a number (NaN).</value></data>
  <data name="Overflow_Fraction" xml:space="preserve"><value>Couldn't parse numerator or denominator.</value></data>
  <data name="NotSupported_NegativePower" xml:space="preserve"><value>Cannot raise zero to a negative power.</value></data>
  <data name="Arithmetic_Trig_Undefined_Tan_3PiOver2" xml:space="preserve"><value>The tangent of 3π/2 is undefined.</value></data>
  <data name="Arg_NegativePrecision" xml:space="preserve"><value>Negative precision cannot round left of the decimal point more place values than there are whole number digits.</value></data>
  <data name="Arg_MustNotEqualZero" xml:space="preserve"><value>{0} must not equal zero.</value></data>
  <data name="Overflow_Int32" xml:space="preserve"><value>BigDecimal is too large for a Int32.</value></data>
  <data name="Arithmetic_Trig_Undefined_Csc_Zero" xml:space="preserve"><value>The cosecant of zero is undefined.</value></data>
  <data name="Overflow_BigDecimal_Infinity" xml:space="preserve"><value>BigDecimal cannot represent infinity.</value></data>
  <data name="Overflow_Decimal" xml:space="preserve"><value>BigDecimal is too large for a Decimal.</value></data>
  <data name="Arg_MustBeGreaterThanOrEqualToOne" xml:space="preserve"><value>Must be greater than or equal to 1</value></data>
  </root>