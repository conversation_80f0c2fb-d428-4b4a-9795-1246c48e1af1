using System;
using System.Runtime.CompilerServices;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Instance;
using Microsoft.IdentityModel.Abstractions;

namespace Microsoft.Identity.Client;

public abstract class BaseAbstractApplicationBuilder<T> where T : BaseAbstractApplicationBuilder<T>
{
	internal ApplicationConfiguration Config { get; }

	internal BaseAbstractApplicationBuilder(ApplicationConfiguration configuration)
	{
		Config = configuration;
	}

	public T WithHttpClientFactory(IMsalHttpClientFactory httpClientFactory)
	{
		Config.HttpClientFactory = httpClientFactory;
		return (T)this;
	}

	public T WithHttpClientFactory(IMsalHttpClientFactory httpClientFactory, bool retryOnceOn5xx)
	{
		Config.HttpClientFactory = httpClientFactory;
		Config.RetryOnServerErrors = retryOnceOn5xx;
		return (T)this;
	}

	internal T WithHttpManager(IHttpManager httpManager)
	{
		Config.HttpManager = httpManager;
		return (T)this;
	}

	public T WithLogging(LogCallback loggingCallback, LogLevel? logLevel = null, bool? enablePiiLogging = null, bool? enableDefaultPlatformLogging = null)
	{
		if (Config.LoggingCallback != null)
		{
			throw new InvalidOperationException("LoggingCallback has already been set. ");
		}
		Config.LoggingCallback = loggingCallback;
		Config.LogLevel = logLevel ?? Config.LogLevel;
		Config.EnablePiiLogging = enablePiiLogging ?? Config.EnablePiiLogging;
		Config.IsDefaultPlatformLoggingEnabled = enableDefaultPlatformLogging ?? Config.IsDefaultPlatformLoggingEnabled;
		return (T)this;
	}

	public T WithLogging(IIdentityLogger identityLogger, bool enablePiiLogging = false)
	{
		Config.IdentityLogger = identityLogger;
		Config.EnablePiiLogging = enablePiiLogging;
		return (T)this;
	}

	public T WithDebugLoggingCallback(LogLevel logLevel = LogLevel.Info, bool enablePiiLogging = false, bool withDefaultPlatformLoggingEnabled = false)
	{
		WithLogging(delegate
		{
		}, logLevel, enablePiiLogging, withDefaultPlatformLoggingEnabled);
		return (T)this;
	}

	protected T WithOptions(BaseApplicationOptions applicationOptions)
	{
		WithLogging(null, applicationOptions.LogLevel, applicationOptions.EnablePiiLogging, applicationOptions.IsDefaultPlatformLoggingEnabled);
		return (T)this;
	}

	public T WithExperimentalFeatures(bool enableExperimentalFeatures = true)
	{
		Config.ExperimentalFeaturesEnabled = enableExperimentalFeatures;
		return (T)this;
	}

	internal virtual ApplicationConfiguration BuildConfiguration()
	{
		ResolveAuthority();
		return Config;
	}

	internal void ResolveAuthority()
	{
		if (Config.Authority?.AuthorityInfo != null)
		{
			if (!string.IsNullOrEmpty(Config.TenantId))
			{
				string tenantedAuthority = Config.Authority.GetTenantedAuthority(Config.TenantId, forceSpecifiedTenant: true);
				Config.Authority = Authority.CreateAuthority(tenantedAuthority, Config.Authority.AuthorityInfo.ValidateAuthority);
			}
		}
		else
		{
			string authorityInstance = GetAuthorityInstance();
			string authorityAudience = GetAuthorityAudience();
			AuthorityInfo authorityInfo = new AuthorityInfo(AuthorityType.Aad, new Uri(authorityInstance + "/" + authorityAudience).ToString(), Config.ValidateAuthority);
			Config.Authority = new AadAuthority(authorityInfo);
		}
	}

	private string GetAuthorityAudience()
	{
		if (!string.IsNullOrWhiteSpace(Config.TenantId) && Config.AadAuthorityAudience != AadAuthorityAudience.None && Config.AadAuthorityAudience != AadAuthorityAudience.AzureAdMyOrg)
		{
			throw new InvalidOperationException("TenantId and AadAuthorityAudience are both set, but they're mutually exclusive. ");
		}
		if (Config.AadAuthorityAudience != AadAuthorityAudience.None)
		{
			return AuthorityInfo.GetAadAuthorityAudienceValue(Config.AadAuthorityAudience, Config.TenantId);
		}
		if (!string.IsNullOrWhiteSpace(Config.TenantId))
		{
			return Config.TenantId;
		}
		return AuthorityInfo.GetAadAuthorityAudienceValue(AadAuthorityAudience.AzureAdAndPersonalMicrosoftAccount, string.Empty);
	}

	private string GetAuthorityInstance()
	{
		if (!string.IsNullOrWhiteSpace(Config.Instance) && Config.AzureCloudInstance != AzureCloudInstance.None)
		{
			throw new InvalidOperationException("Instance and AzureCloudInstance are both set but they're mutually exclusive. ");
		}
		if (!string.IsNullOrWhiteSpace(Config.Instance))
		{
			Config.Instance = Config.Instance.TrimEnd(' ', '/');
			return Config.Instance;
		}
		if (Config.AzureCloudInstance != AzureCloudInstance.None)
		{
			return AuthorityInfo.GetCloudUrl(Config.AzureCloudInstance);
		}
		return AuthorityInfo.GetCloudUrl(AzureCloudInstance.AzurePublic);
	}

	internal void ValidateUseOfExperimentalFeature([CallerMemberName] string memberName = "")
	{
		if (!Config.ExperimentalFeaturesEnabled)
		{
			throw new MsalClientException("experimental_feature", MsalErrorMessage.ExperimentalFeature(memberName));
		}
	}
}
