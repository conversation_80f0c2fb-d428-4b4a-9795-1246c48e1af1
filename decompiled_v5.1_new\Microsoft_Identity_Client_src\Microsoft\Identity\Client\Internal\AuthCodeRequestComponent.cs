using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Internal.Requests;
using Microsoft.Identity.Client.UI;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Internal;

internal class AuthCodeRequestComponent : IAuthCodeRequestComponent
{
	private readonly AuthenticationRequestParameters _requestParams;

	private readonly AcquireTokenInteractiveParameters _interactiveParameters;

	private readonly IServiceBundle _serviceBundle;

	public AuthCodeRequestComponent(AuthenticationRequestParameters requestParams, AcquireTokenInteractiveParameters interactiveParameters)
	{
		_requestParams = requestParams ?? throw new ArgumentNullException("requestParams");
		_interactiveParameters = interactiveParameters ?? throw new ArgumentNullException("requestParams");
		_serviceBundle = _requestParams.RequestContext.ServiceBundle;
	}

	public async Task<Tuple<AuthorizationResult, string>> FetchAuthCodeAndPkceVerifierAsync(CancellationToken cancellationToken)
	{
		IWebUI webUi = CreateWebAuthenticationDialog();
		return await FetchAuthCodeAndPkceInternalAsync(webUi, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}

	public async Task<Uri> GetAuthorizationUriWithoutPkceAsync(CancellationToken cancellationToken)
	{
		return CreateAuthorizationUri(await _requestParams.Authority.GetAuthorizationEndpointAsync(_requestParams.RequestContext).ConfigureAwait(continueOnCapturedContext: false)).Item1;
	}

	public async Task<Uri> GetAuthorizationUriWithPkceAsync(string codeVerifier, CancellationToken cancellationToken)
	{
		return CreateAuthorizationUriWithCodeChallenge(await _requestParams.Authority.GetAuthorizationEndpointAsync(_requestParams.RequestContext).ConfigureAwait(continueOnCapturedContext: false), codeVerifier).Item1;
	}

	private async Task<Tuple<AuthorizationResult, string>> FetchAuthCodeAndPkceInternalAsync(IWebUI webUi, CancellationToken cancellationToken)
	{
		RedirectUriHelper.Validate(_requestParams.RedirectUri);
		_requestParams.RedirectUri = webUi.UpdateRedirectUri(_requestParams.RedirectUri);
		Tuple<Uri, string, string> tuple = CreateAuthorizationUri(await _requestParams.Authority.GetAuthorizationEndpointAsync(_requestParams.RequestContext).ConfigureAwait(continueOnCapturedContext: false), addPkceAndState: true);
		Uri item = tuple.Item1;
		string state = tuple.Item2;
		string codeVerifier = tuple.Item3;
		AuthorizationResult authorizationResult = await webUi.AcquireAuthorizationAsync(item, _requestParams.RedirectUri, _requestParams.RequestContext, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		VerifyAuthorizationResult(authorizationResult, state);
		return new Tuple<AuthorizationResult, string>(authorizationResult, codeVerifier);
	}

	private Tuple<Uri, string> CreateAuthorizationUriWithCodeChallenge(string authEndpoint, string codeVerifier)
	{
		IDictionary<string, string> dictionary = CreateAuthorizationRequestParameters();
		string value = _serviceBundle.PlatformProxy.CryptographyManager.CreateBase64UrlEncodedSha256Hash(codeVerifier);
		dictionary["code_challenge"] = value;
		dictionary["code_challenge_method"] = "S256";
		return new Tuple<Uri, string>(CreateInteractiveRequestParameters(authEndpoint, dictionary).Uri, codeVerifier);
	}

	private Tuple<Uri, string, string> CreateAuthorizationUri(string authEndpoint, bool addPkceAndState = false)
	{
		IDictionary<string, string> dictionary = CreateAuthorizationRequestParameters();
		string text = null;
		string item = null;
		if (addPkceAndState)
		{
			text = _serviceBundle.PlatformProxy.CryptographyManager.GenerateCodeVerifier();
			string value = _serviceBundle.PlatformProxy.CryptographyManager.CreateBase64UrlEncodedSha256Hash(text);
			dictionary["code_challenge"] = value;
			dictionary["code_challenge_method"] = "S256";
			item = (dictionary["state"] = Guid.NewGuid().ToString() + Guid.NewGuid());
		}
		dictionary["client_info"] = "1";
		return new Tuple<Uri, string, string>(CreateInteractiveRequestParameters(authEndpoint, dictionary).Uri, item, text);
	}

	private UriBuilder CreateInteractiveRequestParameters(string authEndpoint, IDictionary<string, string> requestParameters)
	{
		if (_interactiveParameters.Account != null)
		{
			if (!string.IsNullOrEmpty(_interactiveParameters.Account.Username))
			{
				requestParameters["login_hint"] = _interactiveParameters.Account.Username;
			}
			if (_interactiveParameters.Account.HomeAccountId?.ObjectId != null)
			{
				requestParameters["login_req"] = _interactiveParameters.Account.HomeAccountId.ObjectId;
			}
			if (!string.IsNullOrEmpty(_interactiveParameters.Account.HomeAccountId?.TenantId))
			{
				requestParameters["domain_req"] = _interactiveParameters.Account.HomeAccountId.TenantId;
			}
		}
		CheckForDuplicateQueryParameters(_requestParams.ExtraQueryParameters, requestParameters);
		string queryParams = requestParameters.ToQueryParameter();
		UriBuilder uriBuilder = new UriBuilder(authEndpoint);
		uriBuilder.AppendQueryParameters(queryParams);
		return uriBuilder;
	}

	private Dictionary<string, string> CreateAuthorizationRequestParameters(Uri redirectUriOverride = null)
	{
		HashSet<string> hashSet = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
		if (!_interactiveParameters.ExtraScopesToConsent.IsNullOrEmpty())
		{
			hashSet = ScopeHelper.CreateScopeSet(_interactiveParameters.ExtraScopesToConsent);
		}
		if (hashSet.Contains(_requestParams.AppConfig.ClientId))
		{
			throw new ArgumentException("API does not accept client id as a user-provided scope");
		}
		HashSet<string> msalScopes = ScopeHelper.GetMsalScopes(new HashSet<string>(_requestParams.Scope.Concat(hashSet)));
		Dictionary<string, string> dictionary = new Dictionary<string, string>
		{
			["scope"] = msalScopes.AsSingleString(),
			["response_type"] = "code",
			["client_id"] = _requestParams.AppConfig.ClientId,
			["redirect_uri"] = redirectUriOverride?.OriginalString ?? _requestParams.RedirectUri.OriginalString
		};
		if (!string.IsNullOrWhiteSpace(_requestParams.ClaimsAndClientCapabilities))
		{
			dictionary["claims"] = _requestParams.ClaimsAndClientCapabilities;
		}
		if (!string.IsNullOrWhiteSpace(_interactiveParameters.LoginHint) || _requestParams.CcsRoutingHint.HasValue)
		{
			string value;
			if (!_requestParams.CcsRoutingHint.HasValue)
			{
				dictionary["login_hint"] = _interactiveParameters.LoginHint;
				value = CoreHelpers.GetCcsUpnHint(_interactiveParameters.LoginHint);
			}
			else
			{
				dictionary["login_hint"] = _interactiveParameters.LoginHint;
				value = CoreHelpers.GetCcsClientInfoHint(_requestParams.CcsRoutingHint.Value.Key, _requestParams.CcsRoutingHint.Value.Value);
			}
			dictionary["x-anchormailbox"] = value;
		}
		if (_requestParams.RequestContext.CorrelationId != Guid.Empty)
		{
			dictionary["client-request-id"] = _requestParams.RequestContext.CorrelationId.ToString();
		}
		foreach (KeyValuePair<string, string> msalIdParameter in MsalIdHelper.GetMsalIdParameters(_requestParams.RequestContext.Logger))
		{
			dictionary[msalIdParameter.Key] = msalIdParameter.Value;
		}
		if (_interactiveParameters.Prompt == Prompt.NotSpecified)
		{
			dictionary["prompt"] = Prompt.SelectAccount.PromptValue;
		}
		else if (_interactiveParameters.Prompt.PromptValue != Prompt.NoPrompt.PromptValue)
		{
			dictionary["prompt"] = _interactiveParameters.Prompt.PromptValue;
		}
		return dictionary;
	}

	private static void CheckForDuplicateQueryParameters(IDictionary<string, string> queryParamsDictionary, IDictionary<string, string> requestParameters)
	{
		foreach (KeyValuePair<string, string> item in queryParamsDictionary)
		{
			if (requestParameters.ContainsKey(item.Key))
			{
				throw new MsalClientException("duplicate_query_parameter", string.Format(CultureInfo.InvariantCulture, "Duplicate query parameter '{0}' in extraQueryParameters. ", item.Key));
			}
			requestParameters[item.Key] = item.Value;
		}
	}

	private void VerifyAuthorizationResult(AuthorizationResult authorizationResult, string originalState)
	{
		if (authorizationResult.Status == AuthorizationStatus.Success && originalState != null && !originalState.Equals(authorizationResult.State, StringComparison.OrdinalIgnoreCase))
		{
			throw new MsalClientException("state_mismatch", string.Format(CultureInfo.InvariantCulture, "Returned state({0}) from authorize endpoint is not the same as the one sent({1}). See https://aka.ms/msal-statemismatcherror for more details. ", authorizationResult.State, originalState));
		}
		if (authorizationResult.Error == "login_required")
		{
			throw new MsalUiRequiredException("no_prompt_failed", "One of two conditions was encountered: 1. The Prompt.Never flag was passed, but the constraint could not be honored, because user interaction was required. 2. An error occurred during a silent web authentication that prevented the HTTP authentication flow from completing in a short enough time frame. ", null, UiRequiredExceptionClassification.PromptNeverFailed);
		}
		if (authorizationResult.Status == AuthorizationStatus.UserCancel)
		{
			_requestParams.RequestContext.Logger.Info("Authorization result status returned user cancelled authentication. ");
			throw new MsalClientException(authorizationResult.Error, authorizationResult.ErrorDescription ?? "User canceled authentication.");
		}
		if (authorizationResult.Status != AuthorizationStatus.Success)
		{
			_requestParams.RequestContext.Logger.ErrorPii("Authorization result was not successful. See error message for more details. " + authorizationResult.ErrorDescription, "Authorization result was not successful. See error message for more details. ");
			throw new MsalServiceException(authorizationResult.Error, (!string.IsNullOrEmpty(authorizationResult.ErrorDescription)) ? authorizationResult.ErrorDescription : "Unknown error");
		}
	}

	private IWebUI CreateWebAuthenticationDialog()
	{
		if (_interactiveParameters.CustomWebUi != null)
		{
			return new CustomWebUiHandler(_interactiveParameters.CustomWebUi);
		}
		CoreUIParent uiParent = _interactiveParameters.UiParent;
		return _serviceBundle.PlatformProxy.GetWebUiFactory(_requestParams.AppConfig).CreateAuthenticationDialog(uiParent, _interactiveParameters.UseEmbeddedWebView, _requestParams.RequestContext);
	}
}
