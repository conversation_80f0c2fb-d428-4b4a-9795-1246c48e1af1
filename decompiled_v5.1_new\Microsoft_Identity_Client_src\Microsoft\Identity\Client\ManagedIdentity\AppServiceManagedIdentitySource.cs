using System;
using System.Globalization;
using System.Net.Http;
using Microsoft.Identity.Client.AppConfig;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.ManagedIdentity;

internal class AppServiceManagedIdentitySource : AbstractManagedIdentity
{
	private const string AppServiceMsiApiVersion = "2019-08-01";

	private const string SecretHeaderName = "X-IDENTITY-HEADER";

	private readonly Uri _endpoint;

	private readonly string _secret;

	public static AbstractManagedIdentity Create(RequestContext requestContext)
	{
		requestContext.Logger.Info(() => "[Managed Identity] App service managed identity is available.");
		if (!TryValidateEnvVars(EnvironmentVariables.IdentityEndpoint, requestContext.Logger, out var endpointUri))
		{
			return null;
		}
		return new AppServiceManagedIdentitySource(requestContext, endpointUri, EnvironmentVariables.IdentityHeader);
	}

	private AppServiceManagedIdentitySource(RequestContext requestContext, Uri endpoint, string secret)
		: base(requestContext, ManagedIdentitySource.AppService)
	{
		_endpoint = endpoint;
		_secret = secret;
	}

	private static bool TryValidateEnvVars(string msiEndpoint, ILoggerAdapter logger, out Uri endpointUri)
	{
		endpointUri = null;
		try
		{
			endpointUri = new Uri(msiEndpoint);
		}
		catch (FormatException innerException)
		{
			string errorMessage = string.Format(CultureInfo.InvariantCulture, "[Managed Identity] The environment variable {0} contains an invalid Uri {1} in {2} managed identity source.", "IDENTITY_ENDPOINT", msiEndpoint, "App Service");
			throw MsalServiceExceptionFactory.CreateManagedIdentityException("invalid_managed_identity_endpoint", errorMessage, innerException, ManagedIdentitySource.AppService, null);
		}
		logger.Info($"[Managed Identity] Environment variables validation passed for app service managed identity. Endpoint URI: {endpointUri}. Creating App Service managed identity.");
		return true;
	}

	protected override ManagedIdentityRequest CreateRequest(string resource)
	{
		ManagedIdentityRequest managedIdentityRequest = new ManagedIdentityRequest(HttpMethod.Get, _endpoint);
		managedIdentityRequest.Headers.Add("X-IDENTITY-HEADER", _secret);
		managedIdentityRequest.QueryParameters["api-version"] = "2019-08-01";
		managedIdentityRequest.QueryParameters["resource"] = resource;
		switch (_requestContext.ServiceBundle.Config.ManagedIdentityId.IdType)
		{
		case ManagedIdentityIdType.ClientId:
			_requestContext.Logger.Info("[Managed Identity] Adding user assigned client id to the request.");
			managedIdentityRequest.QueryParameters["client_id"] = _requestContext.ServiceBundle.Config.ManagedIdentityId.UserAssignedId;
			break;
		case ManagedIdentityIdType.ResourceId:
			_requestContext.Logger.Info("[Managed Identity] Adding user assigned resource id to the request.");
			managedIdentityRequest.QueryParameters["mi_res_id"] = _requestContext.ServiceBundle.Config.ManagedIdentityId.UserAssignedId;
			break;
		case ManagedIdentityIdType.ObjectId:
			_requestContext.Logger.Info("[Managed Identity] Adding user assigned object id to the request.");
			managedIdentityRequest.QueryParameters["object_id"] = _requestContext.ServiceBundle.Config.ManagedIdentityId.UserAssignedId;
			break;
		}
		return managedIdentityRequest;
	}
}
