using System;
using System.Collections.Generic;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Text.Json.Nodes;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.PlatformsCommon.Factories;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

public class MsalException : Exception
{
	private class ExceptionSerializationKey
	{
		internal const string ExceptionTypeKey = "type";

		internal const string ErrorCodeKey = "error_code";

		internal const string ErrorDescriptionKey = "error_description";

		internal const string AdditionalExceptionData = "additional_exception_data";

		internal const string BrokerErrorContext = "broker_error_context";

		internal const string BrokerErrorTag = "broker_error_tag";

		internal const string BrokerErrorStatus = "broker_error_status";

		internal const string BrokerErrorCode = "broker_error_code";

		internal const string BrokerTelemetry = "broker_telemetry";

		internal const string ManagedIdentitySource = "managed_identity_source";
	}

	public const string BrokerErrorContext = "BrokerErrorContext";

	public const string BrokerErrorTag = "BrokerErrorTag";

	public const string BrokerErrorStatus = "BrokerErrorStatus";

	public const string BrokerErrorCode = "BrokerErrorCode";

	public const string BrokerTelemetry = "BrokerTelemetry";

	public const string ManagedIdentitySource = "ManagedIdentitySource";

	private string _errorCode;

	public bool IsRetryable { get; set; }

	public string ErrorCode
	{
		get
		{
			return _errorCode;
		}
		private set
		{
			if (string.IsNullOrWhiteSpace(value))
			{
				throw new ArgumentNullException("ErrorCode");
			}
			_errorCode = value;
		}
	}

	public string CorrelationId { get; set; }

	public IReadOnlyDictionary<string, string> AdditionalExceptionData { get; set; } = CollectionHelpers.GetEmptyDictionary<string, string>();

	public MsalException()
		: base("Unknown error")
	{
		ErrorCode = "unknown_error";
	}

	public MsalException(string errorCode)
	{
		ErrorCode = errorCode;
	}

	public MsalException(string errorCode, string errorMessage)
		: base(errorMessage)
	{
		if (string.IsNullOrWhiteSpace(Message))
		{
			throw new ArgumentNullException("errorMessage");
		}
		ErrorCode = errorCode;
	}

	public MsalException(string errorCode, string errorMessage, Exception innerException)
		: base(errorMessage, innerException)
	{
		if (string.IsNullOrWhiteSpace(Message))
		{
			throw new ArgumentNullException("errorMessage");
		}
		ErrorCode = errorCode;
	}

	public override string ToString()
	{
		string productName = PlatformProxyFactory.CreatePlatformProxy(null).GetProductName();
		string msalVersion = MsalIdHelper.GetMsalVersion();
		string value = ((base.InnerException == null) ? string.Empty : $"\nInner Exception: {base.InnerException}");
		return $"{productName}.{msalVersion}.{GetType().Name}:\r\n\tErrorCode: {ErrorCode}\r\n{base.ToString()}{value}";
	}

	internal virtual void PopulateJson(JsonObject jObject)
	{
		jObject["type"] = GetType().Name;
		jObject["error_code"] = ErrorCode;
		jObject["error_description"] = Message;
		JsonObject jsonObject = new JsonObject();
		if (AdditionalExceptionData.TryGetValue("BrokerErrorContext", out var value))
		{
			jsonObject["broker_error_context"] = value;
		}
		if (AdditionalExceptionData.TryGetValue("BrokerErrorTag", out var value2))
		{
			jsonObject["broker_error_tag"] = value2;
		}
		if (AdditionalExceptionData.TryGetValue("BrokerErrorStatus", out var value3))
		{
			jsonObject["broker_error_status"] = value3;
		}
		if (AdditionalExceptionData.TryGetValue("BrokerErrorCode", out var value4))
		{
			jsonObject["broker_error_code"] = value4;
		}
		if (AdditionalExceptionData.TryGetValue("BrokerTelemetry", out var value5))
		{
			jsonObject["broker_telemetry"] = value5;
		}
		if (AdditionalExceptionData.TryGetValue("ManagedIdentitySource", out var value6))
		{
			jsonObject["managed_identity_source"] = value6;
		}
		jObject["additional_exception_data"] = jsonObject;
	}

	internal virtual void PopulateObjectFromJson(JsonObject jObject)
	{
		IDictionary<string, string> dictionary = JsonHelper.ExtractInnerJsonAsDictionary(jObject, "additional_exception_data");
		if (dictionary.TryGetValue("broker_error_context", out var value))
		{
			dictionary["BrokerErrorContext"] = value;
			dictionary.Remove("broker_error_context");
		}
		if (dictionary.TryGetValue("broker_error_tag", out var value2))
		{
			dictionary["BrokerErrorTag"] = value2;
			dictionary.Remove("broker_error_tag");
		}
		if (dictionary.TryGetValue("broker_error_status", out var value3))
		{
			dictionary["BrokerErrorStatus"] = value3;
			dictionary.Remove("broker_error_status");
		}
		if (dictionary.TryGetValue("broker_error_code", out var value4))
		{
			dictionary["BrokerErrorCode"] = value4;
			dictionary.Remove("broker_error_code");
		}
		if (dictionary.TryGetValue("broker_telemetry", out var value5))
		{
			dictionary["BrokerTelemetry"] = value5;
			dictionary.Remove("broker_telemetry");
		}
		if (dictionary.TryGetValue("managed_identity_source", out var value6))
		{
			dictionary["ManagedIdentitySource"] = value6;
			dictionary.Remove("managed_identity_source");
		}
		AdditionalExceptionData = (IReadOnlyDictionary<string, string>)dictionary;
	}

	public string ToJsonString()
	{
		JsonObject jsonObject = new JsonObject();
		PopulateJson(jsonObject);
		return jsonObject.ToJsonString(new JsonSerializerOptions
		{
			WriteIndented = true,
			Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping
		});
	}

	public static MsalException FromJsonString(string json)
	{
		JsonObject jsonObject = JsonHelper.ParseIntoJsonObject(json);
		string value = JsonHelper.GetValue<string>(jsonObject["type"]);
		string existingOrEmptyString = JsonHelper.GetExistingOrEmptyString(jsonObject, "error_code");
		string existingOrEmptyString2 = JsonHelper.GetExistingOrEmptyString(jsonObject, "error_description");
		object obj = value switch
		{
			"MsalException" => new MsalException(existingOrEmptyString, existingOrEmptyString2), 
			"MsalClientException" => new MsalClientException(existingOrEmptyString, existingOrEmptyString2), 
			"MsalServiceException" => new MsalServiceException(existingOrEmptyString, existingOrEmptyString2), 
			"MsalUiRequiredException" => new MsalUiRequiredException(existingOrEmptyString, existingOrEmptyString2), 
			_ => throw new MsalClientException("json_parse_failed", "Attempted to deserialize an MsalException but the type was unknown. "), 
		};
		((MsalException)obj).PopulateObjectFromJson(jsonObject);
		return (MsalException)obj;
	}
}
