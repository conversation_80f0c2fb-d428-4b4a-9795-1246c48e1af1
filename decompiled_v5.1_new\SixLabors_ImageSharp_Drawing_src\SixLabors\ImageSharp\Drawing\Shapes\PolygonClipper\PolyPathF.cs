using System.Collections;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

namespace SixLabors.ImageSharp.Drawing.Shapes.PolygonClipper;

internal class PolyPathF : IEnumerable<PolyPathF>, IEnumerable
{
	private readonly PolyPathF parent;

	private readonly List<PolyPathF> items = new List<PolyPathF>();

	public PathF Polygon { get; private set; }

	public int Level => GetLevel();

	public bool IsHole => GetIsHole();

	public int Count => items.Count;

	public PolyPathF this[int index] => items[index];

	public PolyPathF(PolyPathF parent = null)
	{
		this.parent = parent;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public PolyPathF AddChild(PathF p)
	{
		PolyPathF polyPathF = new PolyPathF(this)
		{
			Polygon = p
		};
		items.Add(polyPathF);
		return polyPathF;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public float Area()
	{
		float num = ((Polygon == null) ? 0f : ClipperUtils.Area(Polygon));
		for (int i = 0; i < items.Count; i++)
		{
			PolyPathF polyPathF = items[i];
			num += polyPathF.Area();
		}
		return num;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public void Clear()
	{
		items.Clear();
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private bool GetIsHole()
	{
		int level = Level;
		if (level != 0)
		{
			return (level & 1) == 0;
		}
		return false;
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	private int GetLevel()
	{
		int num = 0;
		for (PolyPathF polyPathF = parent; polyPathF != null; polyPathF = polyPathF.parent)
		{
			num++;
		}
		return num;
	}

	public IEnumerator<PolyPathF> GetEnumerator()
	{
		return items.GetEnumerator();
	}

	IEnumerator IEnumerable.GetEnumerator()
	{
		return items.GetEnumerator();
	}
}
