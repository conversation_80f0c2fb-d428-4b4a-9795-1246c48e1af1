using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal.Broker;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.Utils;
using Microsoft.Identity.Client.WsTrust;

namespace Microsoft.Identity.Client.Internal.Requests;

internal class UsernamePasswordRequest : RequestBase
{
	private readonly CommonNonInteractiveHandler _commonNonInteractiveHandler;

	private readonly AcquireTokenByUsernamePasswordParameters _usernamePasswordParameters;

	private readonly AuthenticationRequestParameters _requestParameters;

	private readonly ILoggerAdapter _logger;

	public UsernamePasswordRequest(IServiceBundle serviceBundle, AuthenticationRequestParameters authenticationRequestParameters, AcquireTokenByUsernamePasswordParameters usernamePasswordParameters)
		: base(serviceBundle, authenticationRequestParameters, usernamePasswordParameters)
	{
		_usernamePasswordParameters = usernamePasswordParameters;
		_requestParameters = authenticationRequestParameters;
		_commonNonInteractiveHandler = new CommonNonInteractiveHandler(authenticationRequestParameters.RequestContext, serviceBundle);
		_logger = _requestParameters.RequestContext.Logger;
	}

	protected override async Task<AuthenticationResult> ExecuteAsync(CancellationToken cancellationToken)
	{
		await ResolveAuthorityAsync().ConfigureAwait(continueOnCapturedContext: false);
		await UpdateUsernameAsync().ConfigureAwait(continueOnCapturedContext: false);
		MsalTokenResponse msalTokenResponse;
		try
		{
			msalTokenResponse = await GetTokenResponseAsync(cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		}
		catch (JsonException innerException)
		{
			throw new MsalServiceException("json_parse_failed", "There was an error parsing the response from the token endpoint, see inner exception for details. Verify that your app is configured correctly. If this is a B2C app, one possible cause is acquiring a token for Microsoft Graph, which is not supported. See https://aka.ms/msal-net-up", innerException);
		}
		return await CacheTokenResponseAndCreateAuthenticationResultAsync(msalTokenResponse).ConfigureAwait(continueOnCapturedContext: false);
	}

	private async Task<MsalTokenResponse> GetTokenResponseAsync(CancellationToken cancellationToken)
	{
		if (_requestParameters.AppConfig.IsBrokerEnabled)
		{
			_logger.Info("Broker is configured. Starting broker flow. ");
			IBroker broker = _requestParameters.RequestContext.ServiceBundle.PlatformProxy.CreateBroker(_requestParameters.RequestContext.ServiceBundle.Config, null);
			if (broker.IsBrokerInstalledAndInvokable(_requestParameters.AuthorityInfo.AuthorityType))
			{
				_logger.Info("Can invoke broker. Will attempt to acquire token with broker. ");
				MsalTokenResponse msalTokenResponse = await broker.AcquireTokenByUsernamePasswordAsync(_requestParameters, _usernamePasswordParameters).ConfigureAwait(continueOnCapturedContext: false);
				if (msalTokenResponse != null)
				{
					_logger.Info("Broker attempt completed successfully. ");
					Metrics.IncrementTotalAccessTokensFromBroker();
					return msalTokenResponse;
				}
				if (string.Equals(_requestParameters.AuthenticationScheme.AccessTokenType, "pop"))
				{
					_logger.Error("A broker application is required for Proof-of-Possesion, but one could not be found or communicated with. See https://aka.ms/msal-net-pop");
					throw new MsalClientException("broker_application_required", "MSAL cannot invoke the broker and it is required for Proof-of-Possession. WAM (Broker) may not be installed on the user's device or there was an error invoking the broker. Use IPublicClientApplication.IsProofOfPossessionSupportedByClient to ensure Proof-of-Possession can be performed before using WithProofOfPossession.Check logs for more details and see https://aka.ms/msal-net-pop. ");
				}
			}
			_logger.Info("Broker request not attempted because the broker is not available.");
			cancellationToken.ThrowIfCancellationRequested();
		}
		return await SendTokenRequestAsync(GetAdditionalBodyParameters(await FetchAssertionFromWsTrustAsync().ConfigureAwait(continueOnCapturedContext: false)), cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}

	private async Task<UserAssertion> FetchAssertionFromWsTrustAsync()
	{
		if (!base.AuthenticationRequestParameters.AuthorityInfo.IsWsTrustFlowSupported)
		{
			return null;
		}
		UserRealmDiscoveryResponse userRealmDiscoveryResponse = await _commonNonInteractiveHandler.QueryUserRealmDataAsync(base.AuthenticationRequestParameters.AuthorityInfo.UserRealmUriPrefix, _usernamePasswordParameters.Username).ConfigureAwait(continueOnCapturedContext: false);
		if (userRealmDiscoveryResponse.IsFederated)
		{
			WsTrustResponse wsTrustResponse = await _commonNonInteractiveHandler.PerformWsTrustMexExchangeAsync(userRealmDiscoveryResponse.FederationMetadataUrl, userRealmDiscoveryResponse.CloudAudienceUrn, UserAuthType.UsernamePassword, _usernamePasswordParameters.Username, _usernamePasswordParameters.Password, _usernamePasswordParameters.FederationMetadata).ConfigureAwait(continueOnCapturedContext: false);
			return new UserAssertion(wsTrustResponse.Token, (wsTrustResponse.TokenType == "urn:oasis:names:tc:SAML:1.0:assertion") ? "urn:ietf:params:oauth:grant-type:saml1_1-bearer" : "urn:ietf:params:oauth:grant-type:saml2-bearer");
		}
		if (userRealmDiscoveryResponse.IsManaged)
		{
			if (_usernamePasswordParameters.Password == null)
			{
				throw new MsalClientException("password_required_for_managed_user");
			}
			return null;
		}
		throw new MsalClientException("unknown_user_type", string.Format(CultureInfo.CurrentCulture, "Unsupported User Type '{0}'. Please see https://aka.ms/msal-net-up. ", userRealmDiscoveryResponse.AccountType));
	}

	private async Task UpdateUsernameAsync()
	{
		if (string.IsNullOrWhiteSpace(_usernamePasswordParameters.Username))
		{
			string username = await _commonNonInteractiveHandler.GetPlatformUserAsync().ConfigureAwait(continueOnCapturedContext: false);
			_usernamePasswordParameters.Username = username;
		}
	}

	private Dictionary<string, string> GetAdditionalBodyParameters(UserAssertion userAssertion)
	{
		Dictionary<string, string> dictionary = new Dictionary<string, string>();
		if (userAssertion != null)
		{
			dictionary["grant_type"] = userAssertion.AssertionType;
			dictionary["assertion"] = Convert.ToBase64String(Encoding.UTF8.GetBytes(userAssertion.Assertion));
		}
		else
		{
			dictionary["grant_type"] = "password";
			dictionary["username"] = _usernamePasswordParameters.Username;
			dictionary["password"] = _usernamePasswordParameters.Password;
		}
		ISet<string> set = new HashSet<string> { "openid", "offline_access", "profile" };
		set.UnionWith(base.AuthenticationRequestParameters.Scope);
		dictionary["scope"] = set.AsSingleString();
		dictionary["client_info"] = "1";
		return dictionary;
	}

	protected override KeyValuePair<string, string>? GetCcsHeader(IDictionary<string, string> additionalBodyParameters)
	{
		return GetCcsUpnHeader(_usernamePasswordParameters.Username);
	}
}
