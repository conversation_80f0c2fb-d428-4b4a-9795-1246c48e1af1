namespace Newtonsoft.Json.Bson;

internal class BsonBoolean : Newtonsoft.Json.Bson.BsonValue
{
	public static readonly Newtonsoft.Json.Bson.BsonBoolean False = new Newtonsoft.Json.Bson.BsonBoolean(value: false);

	public static readonly Newtonsoft.Json.Bson.BsonBoolean True = new Newtonsoft.Json.Bson.BsonBoolean(value: true);

	private BsonBoolean(bool value)
		: base(value, Newtonsoft.Json.Bson.BsonType.Boolean)
	{
	}
}
