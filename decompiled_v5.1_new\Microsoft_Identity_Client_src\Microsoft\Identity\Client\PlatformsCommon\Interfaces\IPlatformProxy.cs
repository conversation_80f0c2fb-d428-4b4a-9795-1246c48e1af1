using System.Threading.Tasks;
using Microsoft.Identity.Client.AuthScheme.PoP;
using Microsoft.Identity.Client.Cache;
using Microsoft.Identity.Client.Internal.Broker;
using Microsoft.Identity.Client.TelemetryCore.OpenTelemetry;
using Microsoft.Identity.Client.UI;

namespace Microsoft.Identity.Client.PlatformsCommon.Interfaces;

internal interface IPlatformProxy
{
	bool LegacyCacheRequiresSerialization { get; }

	ICryptographyManager CryptographyManager { get; }

	IPlatformLogger PlatformLogger { get; }

	IOtelInstrumentation OtelInstrumentation { get; }

	bool BrokerSupportsWamAccounts { get; }

	string GetDeviceModel();

	string GetOperatingSystem();

	string GetProcessorArchitecture();

	Task<string> GetUserPrincipalNameAsync();

	string GetCallingApplicationName();

	string GetCallingApplicationVersion();

	string GetDeviceId();

	string GetDefaultRedirectUri(string clientId, bool useRecommendedRedirectUri = false);

	string GetProductName();

	string GetRuntimeVersion();

	ILegacyCachePersistence CreateLegacyCachePersistence();

	ITokenCacheAccessor CreateTokenCacheAccessor(CacheOptions accessorOptions, bool isApplicationTokenCache = false);

	IWebUIFactory GetWebUiFactory(ApplicationConfiguration appConfig);

	IPoPCryptoProvider GetDefaultPoPCryptoProvider();

	IFeatureFlags GetFeatureFlags();

	void SetFeatureFlags(IFeatureFlags featureFlags);

	Task StartDefaultOsBrowserAsync(string url, bool isBrokerConfigured);

	IBroker CreateBroker(ApplicationConfiguration appConfig, CoreUIParent uiParent);

	IDeviceAuthManager CreateDeviceAuthManager();

	bool CanBrokerSupportSilentAuth();

	IMsalHttpClientFactory CreateDefaultHttpClientFactory();
}
