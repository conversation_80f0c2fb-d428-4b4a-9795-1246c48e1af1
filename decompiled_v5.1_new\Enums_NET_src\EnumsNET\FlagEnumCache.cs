using System;
using System.Runtime.CompilerServices;
using EnumsNET.Numerics;

namespace EnumsNET;

internal sealed class FlagEnumCache<TUnderlying, TUnderlyingOperations> : EnumCache<TUnderlying, TUnderlyingOperations> where TUnderlying : struct, IComparable<TUnderlying>, IEquatable<TUnderlying>, IConvertible where TUnderlyingOperations : struct, IUnderlyingOperations<TUnderlying>
{
	public FlagEnumCache(Type enumType, IEnumBridge<TUnderlying, TUnderlyingOperations> enumBridge, EnumMemberInternal<TUnderlying, TUnderlyingOperations>[] members, EnumMemberInternal<TUnderlying, TUnderlyingOperations>?[] buckets, TUnderlying allFlags, int distinctCount, bool isContiguous, object? customValidator)
		: base(enumType, enumBridge, isFlagEnum: true, members, buckets, allFlags, distinctCount, isContiguous, customValidator)
	{
	}

	public override string AsString(ref byte value)
	{
		return AsString(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	public override string AsString(object value)
	{
		return AsString(ToObject(value));
	}

	[MethodImpl(MethodImplOptions.AggressiveInlining)]
	public new string AsString(TUnderlying value)
	{
		return FormatFlagsInternal(value, GetMember(value), null, Enums.DefaultFormats);
	}

	public override bool TryFormat(ref byte value, Span<char> destination, out int charsWritten)
	{
		return TryFormat(UnsafeUtility.As<byte, TUnderlying>(ref value), destination, out charsWritten);
	}

	public override bool TryFormat(object value, Span<char> destination, out int charsWritten)
	{
		return TryFormat(ToObject(value), destination, out charsWritten);
	}

	public bool TryFormat(TUnderlying value, Span<char> destination, out int charsWritten)
	{
		return TryFormatFlagsInternal(value, GetMember(value), destination, out charsWritten, default(ReadOnlySpan<char>), Enums.DefaultFormats);
	}

	public override bool IsDefined(ref byte value)
	{
		return IsDefined(UnsafeUtility.As<byte, TUnderlying>(ref value));
	}

	public override bool IsDefined(object value)
	{
		return IsDefined(ToObject(value));
	}

	public override void Parse(ReadOnlySpan<char> value, bool ignoreCase, ValueCollection<EnumFormat> formats, ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = ParseFlagsInternal(value, ignoreCase, null, formats);
	}

	public override object Parse(ReadOnlySpan<char> value, bool ignoreCase, ValueCollection<EnumFormat> formats)
	{
		return EnumBridge.ToObjectUnchecked(ParseFlagsInternal(value, ignoreCase, null, formats));
	}

	public override void Parse(ReadOnlySpan<char> value, bool ignoreCase, ref byte result)
	{
		UnsafeUtility.As<byte, TUnderlying>(ref result) = ParseFlagsInternal(value, ignoreCase, null, Enums.DefaultFormats);
	}

	public override object Parse(ReadOnlySpan<char> value, bool ignoreCase)
	{
		return EnumBridge.ToObjectUnchecked(ParseFlagsInternal(value, ignoreCase, null, Enums.DefaultFormats));
	}

	public override bool TryParse(ReadOnlySpan<char> value, bool ignoreCase, ref byte result, ValueCollection<EnumFormat> formats)
	{
		if (TryParseFlags(value, ignoreCase, null, out var result2, formats))
		{
			UnsafeUtility.As<byte, TUnderlying>(ref result) = result2;
			return true;
		}
		return false;
	}

	public override bool TryParse(ReadOnlySpan<char> value, bool ignoreCase, out object? result, ValueCollection<EnumFormat> formats)
	{
		if (TryParseFlags(value, ignoreCase, null, out var result2, formats))
		{
			result = EnumBridge.ToObjectUnchecked(result2);
			return true;
		}
		result = null;
		return false;
	}

	public override bool TryParse(ReadOnlySpan<char> value, bool ignoreCase, ref byte result)
	{
		if (TryParseFlags(value, ignoreCase, null, out var result2, Enums.DefaultFormats))
		{
			UnsafeUtility.As<byte, TUnderlying>(ref result) = result2;
			return true;
		}
		return false;
	}

	public override bool TryParse(ReadOnlySpan<char> value, bool ignoreCase, out object? result)
	{
		if (TryParseFlags(value, ignoreCase, null, out var result2, Enums.DefaultFormats))
		{
			result = EnumBridge.ToObjectUnchecked(result2);
			return true;
		}
		result = null;
		return false;
	}
}
