using System;
using System.Runtime.InteropServices;
using Microsoft.Identity.Client.Platforms.Features.DesktopOs;

namespace Microsoft.Identity.Client.PlatformsCommon.Shared;

internal static class DesktopOsHelper
{
	private static Lazy<bool> s_wamSupportedOSLazy = new Lazy<bool>(IsWamSupportedOSInternal);

	private static Lazy<string> s_winVersionLazy = new Lazy<string>(GetWindowsVersionStringInternal);

	public static bool IsWindows()
	{
		return RuntimeInformation.IsOSPlatform(OSPlatform.Windows);
	}

	public static bool IsLinux()
	{
		return RuntimeInformation.IsOSPlatform(OSPlatform.Linux);
	}

	public static bool IsMac()
	{
		return RuntimeInformation.IsOSPlatform(OSPlatform.OSX);
	}

	private static bool IsWamSupportedOSInternal()
	{
		if (IsWindows() && Win32VersionApi.IsWamSupportedOs())
		{
			return true;
		}
		return false;
	}

	private static string GetWindowsVersionStringInternal()
	{
		return RuntimeInformation.OSDescription;
	}

	public static string GetWindowsVersionString()
	{
		return s_winVersionLazy.Value;
	}

	public static bool IsWin10OrServerEquivalent()
	{
		return s_wamSupportedOSLazy.Value;
	}

	public static bool IsUserInteractive()
	{
		if (IsWindows())
		{
			return IsInteractiveSessionWindows();
		}
		if (IsMac())
		{
			return IsInteractiveSessionMac();
		}
		if (IsLinux())
		{
			return IsInteractiveSessionLinux();
		}
		throw new PlatformNotSupportedException();
	}

	private unsafe static bool IsInteractiveSessionWindows()
	{
		IntPtr processWindowStation = User32.GetProcessWindowStation();
		if (processWindowStation != IntPtr.Zero)
		{
			USEROBJECTFLAGS uSEROBJECTFLAGS = default(USEROBJECTFLAGS);
			uint lpnLengthNeeded = 0u;
			if (User32.GetUserObjectInformation(processWindowStation, 1, &uSEROBJECTFLAGS, (uint)sizeof(USEROBJECTFLAGS), ref lpnLengthNeeded))
			{
				return (uSEROBJECTFLAGS.dwFlags & 1) != 0;
			}
		}
		return true;
	}

	private static bool IsInteractiveSessionMac()
	{
		if (SecurityFramework.SessionGetInfo(-1, out var _, out var attributes) == 0 && (attributes & SessionAttributeBits.SessionHasGraphicAccess) != 0)
		{
			return true;
		}
		return IsInteractiveSessionLinux();
	}

	private static bool IsInteractiveSessionLinux()
	{
		return !string.IsNullOrWhiteSpace(Environment.GetEnvironmentVariable("DISPLAY"));
	}
}
