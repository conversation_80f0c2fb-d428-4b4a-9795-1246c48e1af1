# 批量修复项目文件目标框架脚本
# 将所有项目统一为 net8.0 目标框架

Write-Host "开始批量修复项目文件目标框架..." -ForegroundColor Green

# 获取所有 .csproj 文件
$projectFiles = Get-ChildItem -Path "." -Recurse -Filter "*.csproj" | Where-Object { $_.Name -notlike "*_Fixed*" -and $_.Name -notlike "*_Updated*" }

$totalFiles = $projectFiles.Count
$processedFiles = 0
$modifiedFiles = 0

Write-Host "找到 $totalFiles 个项目文件" -ForegroundColor Yellow

foreach ($file in $projectFiles) {
    $processedFiles++
    Write-Host "[$processedFiles/$totalFiles] 处理: $($file.Name)" -ForegroundColor Cyan
    
    try {
        # 读取文件内容
        $content = Get-Content $file.FullName -Encoding UTF8
        $originalContent = $content -join "`n"
        $modified = $false
        
        # 替换目标框架
        $newContent = $content | ForEach-Object {
            $line = $_
            
            # 修复 netcoreapp8.0 -> net8.0
            if ($line -match '<TargetFramework>netcoreapp8\.0</TargetFramework>') {
                $line = $line -replace 'netcoreapp8\.0', 'net8.0'
                $modified = $true
                Write-Host "  修复: netcoreapp8.0 -> net8.0" -ForegroundColor Green
            }
            
            # 修复 netcoreapp6.0 -> net8.0
            if ($line -match '<TargetFramework>netcoreapp6\.0</TargetFramework>') {
                $line = $line -replace 'netcoreapp6\.0', 'net8.0'
                $modified = $true
                Write-Host "  修复: netcoreapp6.0 -> net8.0" -ForegroundColor Green
            }
            
            # 修复 net6.0 -> net8.0
            if ($line -match '<TargetFramework>net6\.0</TargetFramework>') {
                $line = $line -replace 'net6\.0', 'net8.0'
                $modified = $true
                Write-Host "  修复: net6.0 -> net8.0" -ForegroundColor Green
            }
            
            return $line
        }
        
        # 如果有修改，保存文件
        if ($modified) {
            $newContent | Set-Content $file.FullName -Encoding UTF8
            $modifiedFiles++
            Write-Host "  ✓ 已保存修改" -ForegroundColor Green
        } else {
            Write-Host "  - 无需修改" -ForegroundColor Gray
        }
        
    } catch {
        Write-Host "  ✗ 处理失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n修复完成!" -ForegroundColor Green
Write-Host "总计处理: $totalFiles 个文件" -ForegroundColor Yellow
Write-Host "成功修改: $modifiedFiles 个文件" -ForegroundColor Green

# 验证修复结果
Write-Host "`n验证修复结果..." -ForegroundColor Yellow
$verificationFiles = Get-ChildItem -Path "." -Recurse -Filter "*.csproj" | Where-Object { $_.Name -notlike "*_Fixed*" -and $_.Name -notlike "*_Updated*" }

$net8Count = 0
$otherCount = 0

foreach ($file in $verificationFiles) {
    $content = Get-Content $file.FullName -Raw
    if ($content -match '<TargetFramework>net8\.0</TargetFramework>') {
        $net8Count++
    } else {
        $otherCount++
        Write-Host "警告: $($file.Name) 仍使用其他目标框架" -ForegroundColor Yellow
    }
}

Write-Host "验证结果:" -ForegroundColor Cyan
Write-Host "  net8.0: $net8Count 个项目" -ForegroundColor Green
Write-Host "  其他框架: $otherCount 个项目" -ForegroundColor $(if ($otherCount -eq 0) { "Green" } else { "Yellow" })

if ($otherCount -eq 0) {
    Write-Host "`n🎉 所有项目已成功统一为 net8.0 目标框架!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  仍有项目需要手动检查" -ForegroundColor Yellow
}