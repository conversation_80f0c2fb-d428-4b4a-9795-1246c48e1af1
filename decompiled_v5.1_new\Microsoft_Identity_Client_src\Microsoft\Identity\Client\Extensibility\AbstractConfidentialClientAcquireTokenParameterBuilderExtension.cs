using System;
using System.Threading.Tasks;

namespace Microsoft.Identity.Client.Extensibility;

public static class AbstractConfidentialClientAcquireTokenParameterBuilderExtension
{
	public static AbstractAcquireTokenParameterBuilder<T> OnBeforeTokenRequest<T>(this AbstractAcquireTokenParameterBuilder<T> builder, Func<OnBeforeTokenRequestData, Task> onBeforeTokenRequestHandler) where T : AbstractAcquireTokenParameterBuilder<T>
	{
		builder.CommonParameters.OnBeforeTokenRequestHandler = onBeforeTokenRequestHandler;
		return builder;
	}

	public static AbstractAcquireTokenParameterBuilder<T> WithProofOfPosessionKeyId<T>(this AbstractAcquireTokenParameterBuilder<T> builder, string keyId, string expectedTokenTypeFromAad = "Bearer") where T : AbstractAcquireTokenParameterBuilder<T>
	{
		if (string.IsNullOrEmpty(keyId))
		{
			throw new ArgumentNullException("keyId");
		}
		builder.ValidateUseOfExperimentalFeature("WithProofOfPosessionKeyId");
		builder.CommonParameters.AuthenticationScheme = new ExternalBoundTokenScheme(keyId, expectedTokenTypeFromAad);
		return builder;
	}
}
