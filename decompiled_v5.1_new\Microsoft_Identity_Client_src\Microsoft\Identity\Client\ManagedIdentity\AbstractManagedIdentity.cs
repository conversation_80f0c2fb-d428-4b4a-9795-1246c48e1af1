using System;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.ApiConfig.Parameters;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.ManagedIdentity;

internal abstract class AbstractManagedIdentity
{
	protected readonly RequestContext _requestContext;

	internal const string TimeoutError = "[Managed Identity] Authentication unavailable. The request to the managed identity endpoint timed out.";

	internal readonly ManagedIdentitySource _sourceType;

	private const string ManagedIdentityPrefix = "[Managed Identity] ";

	protected AbstractManagedIdentity(RequestContext requestContext, ManagedIdentitySource sourceType)
	{
		_requestContext = requestContext;
		_sourceType = sourceType;
	}

	public virtual async Task<ManagedIdentityResponse> AuthenticateAsync(AcquireTokenForManagedIdentityParameters parameters, CancellationToken cancellationToken)
	{
		if (cancellationToken.IsCancellationRequested)
		{
			_requestContext.Logger.Error("[Managed Identity] Authentication unavailable. The request to the managed identity endpoint timed out.");
			cancellationToken.ThrowIfCancellationRequested();
		}
		string resource = parameters.Resource;
		ManagedIdentityRequest managedIdentityRequest = CreateRequest(resource);
		try
		{
			HttpResponse httpResponse = ((!(managedIdentityRequest.Method == HttpMethod.Get)) ? (await _requestContext.ServiceBundle.HttpManager.SendPostForceResponseAsync(managedIdentityRequest.ComputeUri(), managedIdentityRequest.Headers, managedIdentityRequest.BodyParameters, _requestContext.Logger, cancellationToken).ConfigureAwait(continueOnCapturedContext: false)) : (await _requestContext.ServiceBundle.HttpManager.SendGetForceResponseAsync(managedIdentityRequest.ComputeUri(), managedIdentityRequest.Headers, _requestContext.Logger, retry: true, cancellationToken).ConfigureAwait(continueOnCapturedContext: false)));
			HttpResponse response = httpResponse;
			return await HandleResponseAsync(parameters, response, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		}
		catch (Exception ex)
		{
			HandleException(ex);
			throw;
		}
	}

	protected virtual Task<ManagedIdentityResponse> HandleResponseAsync(AcquireTokenForManagedIdentityParameters parameters, HttpResponse response, CancellationToken cancellationToken)
	{
		if (response.StatusCode == HttpStatusCode.OK)
		{
			_requestContext.Logger.Info("[Managed Identity] Successful response received.");
			return Task.FromResult(GetSuccessfulResponse(response));
		}
		string messageFromErrorResponse = GetMessageFromErrorResponse(response);
		_requestContext.Logger.Error($"[Managed Identity] request failed, HttpStatusCode: {response.StatusCode} Error message: {messageFromErrorResponse}");
		throw MsalServiceExceptionFactory.CreateManagedIdentityException("managed_identity_request_failed", messageFromErrorResponse, null, _sourceType, (int)response.StatusCode);
	}

	protected abstract ManagedIdentityRequest CreateRequest(string resource);

	protected ManagedIdentityResponse GetSuccessfulResponse(HttpResponse response)
	{
		ManagedIdentityResponse managedIdentityResponse = JsonHelper.DeserializeFromJson<ManagedIdentityResponse>(response.Body);
		if (managedIdentityResponse == null || managedIdentityResponse.AccessToken.IsNullOrEmpty() || managedIdentityResponse.ExpiresOn.IsNullOrEmpty())
		{
			_requestContext.Logger.Error("[Managed Identity] Response is either null or insufficient for authentication.");
			throw MsalServiceExceptionFactory.CreateManagedIdentityException("managed_identity_request_failed", "[Managed Identity] Invalid response, the authentication response received did not contain the expected fields.", null, _sourceType, null);
		}
		return managedIdentityResponse;
	}

	internal string GetMessageFromErrorResponse(HttpResponse response)
	{
		if (string.IsNullOrEmpty(response?.Body))
		{
			return "[Managed Identity] Authentication unavailable. No response received from the managed identity endpoint.";
		}
		try
		{
			ManagedIdentityErrorResponse managedIdentityErrorResponse = JsonHelper.DeserializeFromJson<ManagedIdentityErrorResponse>(response?.Body);
			return ExtractErrorMessageFromManagedIdentityErrorResponse(managedIdentityErrorResponse);
		}
		catch
		{
			return TryGetMessageFromNestedErrorResponse(response.Body);
		}
	}

	private string ExtractErrorMessageFromManagedIdentityErrorResponse(ManagedIdentityErrorResponse managedIdentityErrorResponse)
	{
		StringBuilder stringBuilder = new StringBuilder("[Managed Identity] ");
		if (!string.IsNullOrEmpty(managedIdentityErrorResponse.Error))
		{
			StringBuilder stringBuilder2 = stringBuilder;
			StringBuilder stringBuilder3 = stringBuilder2;
			StringBuilder.AppendInterpolatedStringHandler handler = new StringBuilder.AppendInterpolatedStringHandler(13, 1, stringBuilder2);
			handler.AppendLiteral("Error Code: ");
			handler.AppendFormatted(managedIdentityErrorResponse.Error);
			handler.AppendLiteral(" ");
			stringBuilder3.Append(ref handler);
		}
		if (!string.IsNullOrEmpty(managedIdentityErrorResponse.Message))
		{
			StringBuilder stringBuilder2 = stringBuilder;
			StringBuilder stringBuilder4 = stringBuilder2;
			StringBuilder.AppendInterpolatedStringHandler handler = new StringBuilder.AppendInterpolatedStringHandler(16, 1, stringBuilder2);
			handler.AppendLiteral("Error Message: ");
			handler.AppendFormatted(managedIdentityErrorResponse.Message);
			handler.AppendLiteral(" ");
			stringBuilder4.Append(ref handler);
		}
		if (!string.IsNullOrEmpty(managedIdentityErrorResponse.ErrorDescription))
		{
			StringBuilder stringBuilder2 = stringBuilder;
			StringBuilder stringBuilder5 = stringBuilder2;
			StringBuilder.AppendInterpolatedStringHandler handler = new StringBuilder.AppendInterpolatedStringHandler(20, 1, stringBuilder2);
			handler.AppendLiteral("Error Description: ");
			handler.AppendFormatted(managedIdentityErrorResponse.ErrorDescription);
			handler.AppendLiteral(" ");
			stringBuilder5.Append(ref handler);
		}
		if (!string.IsNullOrEmpty(managedIdentityErrorResponse.CorrelationId))
		{
			StringBuilder stringBuilder2 = stringBuilder;
			StringBuilder stringBuilder6 = stringBuilder2;
			StringBuilder.AppendInterpolatedStringHandler handler = new StringBuilder.AppendInterpolatedStringHandler(84, 1, stringBuilder2);
			handler.AppendLiteral("Managed Identity Correlation ID: ");
			handler.AppendFormatted(managedIdentityErrorResponse.CorrelationId);
			handler.AppendLiteral(" Use this Correlation ID for further investigation.");
			stringBuilder6.Append(ref handler);
		}
		if (stringBuilder.Length == "[Managed Identity] ".Length)
		{
			return "[Managed Identity] The error response was either empty or could not be parsed..";
		}
		return stringBuilder.ToString();
	}

	private string TryGetMessageFromNestedErrorResponse(string response)
	{
		try
		{
			JsonHelper.TryGetValue(JsonHelper.ParseIntoJsonObject(response), "error", out var value);
			StringBuilder stringBuilder = new StringBuilder("[Managed Identity] ");
			if (JsonHelper.TryGetValue(JsonHelper.ToJsonObject(value), "code", out var value2))
			{
				StringBuilder stringBuilder2 = stringBuilder;
				StringBuilder stringBuilder3 = stringBuilder2;
				StringBuilder.AppendInterpolatedStringHandler handler = new StringBuilder.AppendInterpolatedStringHandler(13, 1, stringBuilder2);
				handler.AppendLiteral("Error Code: ");
				handler.AppendFormatted(value2);
				handler.AppendLiteral(" ");
				stringBuilder3.Append(ref handler);
			}
			if (JsonHelper.TryGetValue(JsonHelper.ToJsonObject(value), "message", out var value3))
			{
				StringBuilder stringBuilder2 = stringBuilder;
				StringBuilder stringBuilder4 = stringBuilder2;
				StringBuilder.AppendInterpolatedStringHandler handler = new StringBuilder.AppendInterpolatedStringHandler(15, 1, stringBuilder2);
				handler.AppendLiteral("Error Message: ");
				handler.AppendFormatted(value3);
				stringBuilder4.Append(ref handler);
			}
			if (value3 != null || value2 != null)
			{
				return stringBuilder.ToString();
			}
		}
		catch
		{
		}
		_requestContext.Logger.Error("[Managed Identity] The error response was either empty or could not be parsed.. Error response received from the server: " + response + ".");
		return "[Managed Identity] The error response was either empty or could not be parsed.. Error response received from the server: " + response + ".";
	}

	private void HandleException(Exception ex, ManagedIdentitySource managedIdentitySource = ManagedIdentitySource.None, string additionalInfo = null)
	{
		ManagedIdentitySource source = ((managedIdentitySource != ManagedIdentitySource.None) ? managedIdentitySource : _sourceType);
		if (ex is HttpRequestException ex2)
		{
			CreateAndThrowException("managed_identity_unreachable_network", ex2.Message, ex2, source);
		}
		else if (ex is TaskCanceledException)
		{
			_requestContext.Logger.Error("[Managed Identity] Authentication unavailable. The request to the managed identity endpoint timed out.");
		}
		else if (ex is FormatException ex3)
		{
			string text = additionalInfo ?? ex3.Message;
			_requestContext.Logger.Error("[Managed Identity] Format Exception: " + text);
			CreateAndThrowException("invalid_managed_identity_endpoint", text, ex3, source);
		}
		else if (!(ex is MsalServiceException))
		{
			_requestContext.Logger.Error("[Managed Identity] Exception: " + ex.Message);
			CreateAndThrowException("managed_identity_request_failed", ex.Message, ex, source);
		}
	}

	private static void CreateAndThrowException(string errorCode, string errorMessage, Exception innerException, ManagedIdentitySource source)
	{
		throw MsalServiceExceptionFactory.CreateManagedIdentityException(errorCode, errorMessage, innerException, source, null);
	}
}
