using System;
using System.Collections.Generic;
using System.Globalization;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;

namespace Microsoft.Identity.Client.Http;

internal class HttpManagerWithRetry : HttpManager
{
	public HttpManagerWithRetry(IMsalHttpClientFactory httpClientFactory)
		: base(httpClientFactory)
	{
	}

	public override Task<HttpResponse> SendPostAsync(Uri endpoint, IDictionary<string, string> headers, HttpContent body, ILoggerAdapter logger, CancellationToken cancellationToken = default(CancellationToken))
	{
		return SendRequestAsync(endpoint, headers, body, HttpMethod.Post, logger, doNotThrow: false, retry: true, cancellationToken);
	}

	public override Task<HttpResponse> SendGetAsync(Uri endpoint, IDictionary<string, string> headers, ILoggerAdapter logger, bool retry = true, CancellationToken cancellationToken = default(CancellationToken))
	{
		return SendRequestAsync(endpoint, headers, null, HttpMethod.Get, logger, doNotThrow: false, retry: true, cancellationToken);
	}

	public override Task<HttpResponse> SendGetForceResponseAsync(Uri endpoint, IDictionary<string, string> headers, ILoggerAdapter logger, bool retry = true, CancellationToken cancellationToken = default(CancellationToken))
	{
		return SendRequestAsync(endpoint, headers, null, HttpMethod.Get, logger, doNotThrow: true, retry: true, cancellationToken);
	}

	public override Task<HttpResponse> SendPostForceResponseAsync(Uri uri, IDictionary<string, string> headers, IDictionary<string, string> bodyParameters, ILoggerAdapter logger, CancellationToken cancellationToken = default(CancellationToken))
	{
		HttpContent body = ((bodyParameters == null) ? null : new FormUrlEncodedContent(bodyParameters));
		return SendRequestAsync(uri, headers, body, HttpMethod.Post, logger, doNotThrow: true, retry: true, cancellationToken);
	}

	public override Task<HttpResponse> SendPostForceResponseAsync(Uri uri, IDictionary<string, string> headers, StringContent body, ILoggerAdapter logger, CancellationToken cancellationToken = default(CancellationToken))
	{
		return SendRequestAsync(uri, headers, body, HttpMethod.Post, logger, doNotThrow: true, retry: true, cancellationToken);
	}

	protected override HttpClient GetHttpClient()
	{
		return _httpClientFactory.GetHttpClient();
	}

	protected override async Task<HttpResponse> SendRequestAsync(Uri endpoint, IDictionary<string, string> headers, HttpContent body, HttpMethod method, ILoggerAdapter logger, bool doNotThrow = false, bool retry = true, CancellationToken cancellationToken = default(CancellationToken))
	{
		Exception timeoutException = null;
		bool isRetriableStatusCode = false;
		HttpResponse response = null;
		bool flag;
		try
		{
			HttpContent body2 = body;
			if (body != null)
			{
				body2 = await HttpManager.CloneHttpContentAsync(body).ConfigureAwait(continueOnCapturedContext: false);
			}
			using (logger.LogBlockDuration("[HttpManager] ExecuteAsync"))
			{
				response = await ExecuteAsync(endpoint, headers, body2, method, logger, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			}
			if (response.StatusCode == HttpStatusCode.OK)
			{
				return response;
			}
			logger.Info(() => string.Format(CultureInfo.InvariantCulture, "Response status code does not indicate success: {0} ({1}). ", (int)response.StatusCode, response.StatusCode));
			isRetriableStatusCode = IsRetryableStatusCode((int)response.StatusCode);
			flag = isRetriableStatusCode && !HasRetryAfterHeader(response);
		}
		catch (TaskCanceledException ex)
		{
			if (cancellationToken.IsCancellationRequested)
			{
				logger.Info("The HTTP request was cancelled. ");
				throw;
			}
			logger.Error("The HTTP request failed. " + ex.Message);
			flag = true;
			timeoutException = ex;
		}
		if (flag && retry)
		{
			logger.Info("Retrying one more time..");
			await Task.Delay(TimeSpan.FromSeconds(1.0), cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
			return await SendRequestAsync(endpoint, headers, body, method, logger, doNotThrow, retry: false, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		}
		logger.Warning("Request retry failed.");
		if (timeoutException != null)
		{
			throw new MsalServiceException("request_timeout", "Request to the endpoint timed out.", timeoutException);
		}
		if (doNotThrow)
		{
			return response;
		}
		if (isRetriableStatusCode)
		{
			throw MsalServiceExceptionFactory.FromHttpResponse("service_not_available", "Service is unavailable to process the request", response);
		}
		return response;
	}

	private static bool HasRetryAfterHeader(HttpResponse response)
	{
		RetryConditionHeaderValue retryConditionHeaderValue = response?.Headers?.RetryAfter;
		if (retryConditionHeaderValue != null)
		{
			if (!retryConditionHeaderValue.Delta.HasValue)
			{
				return retryConditionHeaderValue.Date.HasValue;
			}
			return true;
		}
		return false;
	}
}
