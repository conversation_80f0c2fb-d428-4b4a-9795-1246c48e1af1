namespace Microsoft.IdentityModel.Protocols.OpenIdConnect;

public static class OpenIdConnectScope
{
	public const string Address = "address";

	public const string Email = "email";

	public const string OfflineAccess = "offline_access";

	public const string OpenId = "openid";

	public const string OpenIdProfile = "openid profile";

	public const string Phone = "phone";

	public const string UserImpersonation = "user_impersonation";
}
