using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Instance;
using Microsoft.Identity.Client.Instance.Validation;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client;

internal class AuthorityInfo
{
	internal class AuthorityInfoHelper
	{
		public static IAuthorityValidator CreateAuthorityValidator(AuthorityInfo authorityInfo, RequestContext requestContext)
		{
			switch (authorityInfo.AuthorityType)
			{
			case AuthorityType.Adfs:
				return new AdfsAuthorityValidator(requestContext);
			case AuthorityType.Aad:
				return new AadAuthorityValidator(requestContext);
			case AuthorityType.B2C:
			case AuthorityType.Dsts:
			case AuthorityType.Generic:
			case AuthorityType.Ciam:
				return new NullAuthorityValidator();
			default:
				throw new InvalidOperationException("Invalid AuthorityType");
			}
		}

		public static async Task<Authority> CreateAuthorityForRequestAsync(RequestContext requestContext, AuthorityInfo requestAuthorityInfo, IAccount account = null)
		{
			Authority configAuthority = requestContext.ServiceBundle.Config.Authority;
			AuthorityInfo configAuthorityInfo = configAuthority.AuthorityInfo;
			if (configAuthorityInfo == null)
			{
				throw new ArgumentNullException("AuthorityInfo");
			}
			ValidateTypeMismatch(configAuthorityInfo, requestAuthorityInfo);
			await ValidateSameHostAsync(requestAuthorityInfo, requestContext).ConfigureAwait(continueOnCapturedContext: false);
			AuthorityInfo authorityInfo = requestAuthorityInfo ?? configAuthorityInfo;
			switch (configAuthorityInfo.AuthorityType)
			{
			case AuthorityType.Adfs:
				return new AdfsAuthority(authorityInfo);
			case AuthorityType.Dsts:
				return new DstsAuthority(authorityInfo);
			case AuthorityType.B2C:
				return new B2CAuthority(authorityInfo);
			case AuthorityType.Ciam:
				return new CiamAuthority(authorityInfo);
			case AuthorityType.Generic:
				return new GenericAuthority(authorityInfo);
			case AuthorityType.Aad:
			{
				bool flag = requestContext.ServiceBundle.Config.MultiCloudSupportEnabled && account != null && !PublicClientApplication.IsOperatingSystemAccount(account);
				if (requestAuthorityInfo == null)
				{
					return flag ? CreateAuthorityWithTenant(CreateAuthorityWithEnvironment(configAuthorityInfo, account.Environment), account?.HomeAccountId?.TenantId, forceSpecifiedTenant: false) : CreateAuthorityWithTenant(configAuthority, account?.HomeAccountId?.TenantId, forceSpecifiedTenant: false);
				}
				if (configAuthorityInfo.IsDefaultAuthority && requestAuthorityInfo.AuthorityType != AuthorityType.Aad)
				{
					return requestAuthorityInfo.CreateAuthority();
				}
				AadAuthority aadAuthority = (flag ? new AadAuthority(CreateAuthorityWithEnvironment(requestAuthorityInfo, account?.Environment).AuthorityInfo) : new AadAuthority(requestAuthorityInfo));
				if (!aadAuthority.IsCommonOrganizationsOrConsumersTenant() || aadAuthority.IsOrganizationsTenantWithMsaPassthroughEnabled(requestContext.ServiceBundle.Config.IsBrokerEnabled && requestContext.ServiceBundle.Config.BrokerOptions != null && requestContext.ServiceBundle.Config.BrokerOptions.MsaPassthrough, account?.HomeAccountId?.TenantId))
				{
					return aadAuthority;
				}
				return flag ? CreateAuthorityWithTenant(CreateAuthorityWithEnvironment(configAuthorityInfo, account.Environment), account?.HomeAccountId?.TenantId, forceSpecifiedTenant: false) : CreateAuthorityWithTenant(configAuthority, account?.HomeAccountId?.TenantId, forceSpecifiedTenant: false);
			}
			default:
				throw new MsalClientException("invalid_authority_type", "Unsupported authority type");
			}
		}

		internal static Authority CreateAuthorityWithTenant(Authority authority, string tenantId, bool forceSpecifiedTenant)
		{
			return Authority.CreateAuthority(authority.GetTenantedAuthority(tenantId, forceSpecifiedTenant), authority.AuthorityInfo.ValidateAuthority);
		}

		internal static Authority CreateAuthorityWithEnvironment(AuthorityInfo authorityInfo, string environment)
		{
			return Authority.CreateAuthority(new UriBuilder(authorityInfo.CanonicalAuthority)
			{
				Host = environment
			}.Uri.AbsoluteUri, authorityInfo.ValidateAuthority);
		}

		private static void ValidateTypeMismatch(AuthorityInfo configAuthorityInfo, AuthorityInfo requestAuthorityInfo)
		{
			if (!configAuthorityInfo.IsDefaultAuthority && requestAuthorityInfo != null && configAuthorityInfo.AuthorityType != requestAuthorityInfo.AuthorityType)
			{
				throw new MsalClientException("authority_type_mismatch", MsalErrorMessage.AuthorityTypeMismatch(configAuthorityInfo.AuthorityType, requestAuthorityInfo.AuthorityType));
			}
		}

		private static async Task ValidateSameHostAsync(AuthorityInfo requestAuthorityInfo, RequestContext requestContext)
		{
			AuthorityInfo configAuthorityInfo = requestContext.ServiceBundle.Config.Authority.AuthorityInfo;
			if (requestContext.ServiceBundle.Config.MultiCloudSupportEnabled || requestAuthorityInfo == null || string.Equals(requestAuthorityInfo.Host, configAuthorityInfo.Host, StringComparison.OrdinalIgnoreCase))
			{
				return;
			}
			if (requestAuthorityInfo.AuthorityType == AuthorityType.B2C)
			{
				throw new MsalClientException("B2C_authority_host_mismatch", "The B2C authority host that was used when creating the client application is not the same authority host used in the AcquireToken call. See https://aka.ms/msal-net-b2c for details. ");
			}
			if (requestAuthorityInfo.AuthorityType == AuthorityType.Ciam || requestAuthorityInfo.AuthorityType == AuthorityType.Generic)
			{
				return;
			}
			if (!string.IsNullOrEmpty(requestContext.ServiceBundle.Config.AzureRegion))
			{
				throw new MsalClientException("authority_override_regional", "You configured WithAuthority at the request level, and also WithAzureRegion. This is not supported when the environment changes from application to request. Use WithTenantId at the request level instead.");
			}
			if (await IsAuthorityAliasedAsync(requestContext, requestAuthorityInfo).ConfigureAwait(continueOnCapturedContext: false))
			{
				return;
			}
			if (configAuthorityInfo.IsDefaultAuthority)
			{
				throw new MsalClientException("authority_host_mismatch", $"You did not define an authority at the application level, so it defaults to the https://login.microsoftonline.com/common. \n\rHowever, the request is for a different cloud {requestAuthorityInfo.Host}. This is not supported - the app and the request must target the same cloud. \n\r\n\r Add .WithAuthority(\"https://{requestAuthorityInfo.Host}/common\") in the app builder. \n\rSee https://aka.ms/msal-net-authority-override for details");
			}
			throw new MsalClientException("authority_host_mismatch", $"\n\r The application is configured for cloud {configAuthorityInfo.Host} and the request for a different cloud - {requestAuthorityInfo.Host}. This is not supported - the app and the request must target the same cloud. \n\rSee https://aka.ms/msal-net-authority-override for details");
		}

		private static async Task<bool> IsAuthorityAliasedAsync(RequestContext requestContext, AuthorityInfo requestAuthorityInfo)
		{
			return (await requestContext.ServiceBundle.InstanceDiscoveryManager.GetMetadataEntryAsync(requestContext.ServiceBundle.Config.Authority.AuthorityInfo, requestContext).ConfigureAwait(continueOnCapturedContext: false)).Aliases.Any((string alias) => alias.Equals(requestAuthorityInfo.Host));
		}
	}

	public string Host => CanonicalAuthority.Host;

	public Uri CanonicalAuthority { get; }

	internal AuthorityType AuthorityType { get; }

	public string UserRealmUriPrefix { get; }

	public bool ValidateAuthority { get; }

	internal bool IsInstanceDiscoverySupported => AuthorityType == AuthorityType.Aad;

	internal bool IsWsTrustFlowSupported
	{
		get
		{
			if (AuthorityType != AuthorityType.Aad)
			{
				return AuthorityType == AuthorityType.Dsts;
			}
			return true;
		}
	}

	internal bool CanBeTenanted
	{
		get
		{
			if (AuthorityType != AuthorityType.Aad && AuthorityType != AuthorityType.Dsts && AuthorityType != AuthorityType.B2C)
			{
				return AuthorityType == AuthorityType.Ciam;
			}
			return true;
		}
	}

	internal bool IsClientInfoSupported
	{
		get
		{
			if (AuthorityType != AuthorityType.Aad && AuthorityType != AuthorityType.Dsts && AuthorityType != AuthorityType.B2C)
			{
				return AuthorityType == AuthorityType.Ciam;
			}
			return true;
		}
	}

	internal bool IsSha2CredentialSupported
	{
		get
		{
			if (AuthorityType != AuthorityType.Dsts && AuthorityType != AuthorityType.Generic)
			{
				return AuthorityType != AuthorityType.Adfs;
			}
			return false;
		}
	}

	internal bool IsDefaultAuthority => string.Equals(CanonicalAuthority.ToString(), "https://login.microsoftonline.com/common/", StringComparison.OrdinalIgnoreCase);

	public AuthorityInfo(AuthorityType authorityType, string authority, bool validateAuthority)
		: this(authorityType, ValidateAndCreateAuthorityUri(authority, authorityType), validateAuthority)
	{
	}

	public AuthorityInfo(AuthorityType authorityType, Uri authorityUri, bool validateAuthority)
	{
		AuthorityType = authorityType;
		ValidateAuthority = validateAuthority;
		switch (AuthorityType)
		{
		case AuthorityType.Generic:
			CanonicalAuthority = authorityUri;
			break;
		case AuthorityType.B2C:
		{
			string[] pathSegments = GetPathSegments(authorityUri.AbsolutePath);
			if (pathSegments.Length < 3)
			{
				throw new ArgumentException("The B2C authority URI should have at least 3 segments in the path (i.e. https://<host>/tfp/<tenant>/<policy>/...). ");
			}
			CanonicalAuthority = new Uri($"https://{authorityUri.Authority}/{pathSegments[0]}/{pathSegments[1]}/{pathSegments[2]}/");
			break;
		}
		case AuthorityType.Dsts:
		{
			string[] pathSegments = GetPathSegments(authorityUri.AbsolutePath);
			if (pathSegments.Length < 2)
			{
				throw new ArgumentException("The DSTS authority URI should have at least 2 segments in the path (i.e. https://<host>/dstsv2/<tenant>/...). ");
			}
			CanonicalAuthority = new Uri($"https://{authorityUri.Authority}/{pathSegments[0]}/{pathSegments[1]}/");
			UserRealmUriPrefix = UriBuilderExtensions.GetHttpsUriWithOptionalPort($"https://{authorityUri.Authority}/{pathSegments[0]}/common/userrealm/", authorityUri.Port);
			break;
		}
		default:
			CanonicalAuthority = new Uri(UriBuilderExtensions.GetHttpsUriWithOptionalPort($"https://{authorityUri.Authority}/{GetFirstPathSegment(authorityUri)}/", authorityUri.Port));
			UserRealmUriPrefix = UriBuilderExtensions.GetHttpsUriWithOptionalPort("https://" + Host + "/common/userrealm/", authorityUri.Port);
			break;
		}
	}

	public AuthorityInfo(AuthorityInfo other)
		: this(other.CanonicalAuthority, other.AuthorityType, other.UserRealmUriPrefix, other.ValidateAuthority)
	{
	}

	private AuthorityInfo(Uri canonicalAuthority, AuthorityType authorityType, string userRealmUriPrefix, bool validateAuthority)
	{
		CanonicalAuthority = canonicalAuthority;
		AuthorityType = authorityType;
		UserRealmUriPrefix = userRealmUriPrefix;
		ValidateAuthority = validateAuthority;
	}

	internal static AuthorityInfo FromAuthorityUri(string authorityUri, bool validateAuthority)
	{
		Uri authorityUri2 = ValidateAndCreateAuthorityUri(CanonicalizeAuthorityUri(authorityUri));
		authorityUri2 = TransformIfCiamAuthority(authorityUri2);
		AuthorityType authorityType = GetAuthorityType(authorityUri2);
		if (authorityType == AuthorityType.B2C || authorityType == AuthorityType.Generic)
		{
			validateAuthority = false;
		}
		return new AuthorityInfo(authorityType, authorityUri2, validateAuthority);
	}

	private static Uri TransformIfCiamAuthority(Uri authorityUri)
	{
		if (IsCiamAuthority(authorityUri))
		{
			return CiamAuthority.TransformAuthority(authorityUri);
		}
		return authorityUri;
	}

	internal static AuthorityInfo FromAadAuthority(string cloudInstanceUri, Guid tenantId, bool validateAuthority)
	{
		return FromAuthorityUri(cloudInstanceUri.Trim().TrimEnd('/') + "/" + tenantId.ToString("D"), validateAuthority);
	}

	internal static AuthorityInfo FromAadAuthority(string cloudInstanceUri, string tenant, bool validateAuthority)
	{
		if (Guid.TryParse(tenant, out var result))
		{
			return FromAadAuthority(cloudInstanceUri, result, validateAuthority);
		}
		return FromAuthorityUri(cloudInstanceUri.Trim().TrimEnd('/') + "/" + tenant, validateAuthority);
	}

	internal static AuthorityInfo FromAadAuthority(AzureCloudInstance azureCloudInstance, Guid tenantId, bool validateAuthority)
	{
		string authorityUri = GetAuthorityUri(azureCloudInstance, AadAuthorityAudience.AzureAdMyOrg, tenantId.ToString("D"));
		return new AuthorityInfo(AuthorityType.Aad, authorityUri, validateAuthority);
	}

	internal static AuthorityInfo FromAadAuthority(AzureCloudInstance azureCloudInstance, string tenant, bool validateAuthority)
	{
		if (Guid.TryParse(tenant, out var result))
		{
			return FromAadAuthority(azureCloudInstance, result, validateAuthority);
		}
		string authorityUri = GetAuthorityUri(azureCloudInstance, AadAuthorityAudience.AzureAdMyOrg, tenant);
		return new AuthorityInfo(AuthorityType.Aad, authorityUri, validateAuthority);
	}

	internal static AuthorityInfo FromAadAuthority(AzureCloudInstance azureCloudInstance, AadAuthorityAudience authorityAudience, bool validateAuthority)
	{
		string authorityUri = GetAuthorityUri(azureCloudInstance, authorityAudience);
		return new AuthorityInfo(AuthorityType.Aad, authorityUri, validateAuthority);
	}

	internal static AuthorityInfo FromAadAuthority(AadAuthorityAudience authorityAudience, bool validateAuthority)
	{
		string authorityUri = GetAuthorityUri(AzureCloudInstance.AzurePublic, authorityAudience);
		return new AuthorityInfo(AuthorityType.Aad, authorityUri, validateAuthority);
	}

	internal static AuthorityInfo FromAdfsAuthority(string authorityUri, bool validateAuthority)
	{
		return new AuthorityInfo(AuthorityType.Adfs, authorityUri, validateAuthority);
	}

	internal static AuthorityInfo FromB2CAuthority(string authorityUri)
	{
		return new AuthorityInfo(AuthorityType.B2C, authorityUri, validateAuthority: false);
	}

	internal static AuthorityInfo FromGenericAuthority(string authorityUri)
	{
		return new AuthorityInfo(AuthorityType.Generic, authorityUri, validateAuthority: false);
	}

	internal static string GetCloudUrl(AzureCloudInstance azureCloudInstance)
	{
		return azureCloudInstance switch
		{
			AzureCloudInstance.AzurePublic => "https://login.microsoftonline.com", 
			AzureCloudInstance.AzureChina => "https://login.chinacloudapi.cn", 
			AzureCloudInstance.AzureGermany => "https://login.microsoftonline.de", 
			AzureCloudInstance.AzureUsGovernment => "https://login.microsoftonline.us", 
			_ => throw new ArgumentException("azureCloudInstance"), 
		};
	}

	internal static string GetAadAuthorityAudienceValue(AadAuthorityAudience authorityAudience, string tenantId)
	{
		switch (authorityAudience)
		{
		case AadAuthorityAudience.AzureAdAndPersonalMicrosoftAccount:
			return "common";
		case AadAuthorityAudience.AzureAdMultipleOrgs:
			return "organizations";
		case AadAuthorityAudience.PersonalMicrosoftAccount:
			return "consumers";
		case AadAuthorityAudience.AzureAdMyOrg:
			if (string.IsNullOrWhiteSpace(tenantId))
			{
				throw new InvalidOperationException("When specifying AadAuthorityAudience.AzureAdMyOrg, you must also specify a tenant domain or tenant GUID. ");
			}
			return tenantId;
		default:
			throw new ArgumentException("authorityAudience");
		}
	}

	internal static string CanonicalizeAuthorityUri(string uri)
	{
		if (!string.IsNullOrWhiteSpace(uri) && !uri.EndsWith("/", StringComparison.OrdinalIgnoreCase))
		{
			uri += "/";
		}
		return uri?.ToLowerInvariant() ?? string.Empty;
	}

	internal Authority CreateAuthority()
	{
		return AuthorityType switch
		{
			AuthorityType.Adfs => new AdfsAuthority(this), 
			AuthorityType.B2C => new B2CAuthority(this), 
			AuthorityType.Aad => new AadAuthority(this), 
			AuthorityType.Dsts => new DstsAuthority(this), 
			AuthorityType.Ciam => new CiamAuthority(this), 
			AuthorityType.Generic => new GenericAuthority(this), 
			_ => throw new MsalClientException("invalid_authority_type", $"Unsupported authority type {AuthorityType}"), 
		};
	}

	private static Uri ValidateAndCreateAuthorityUri(string authority, AuthorityType? authorityType = null)
	{
		if (string.IsNullOrWhiteSpace(authority))
		{
			throw new ArgumentNullException("authority");
		}
		if (!Uri.IsWellFormedUriString(authority, UriKind.Absolute))
		{
			throw new ArgumentException("The authority (including the tenant ID) must be in a well-formed URI format. ", "authority");
		}
		Uri uri = new Uri(authority);
		if (uri.Scheme != "https")
		{
			throw new ArgumentException("The authority must use HTTPS scheme. ", "authority");
		}
		if (!authorityType.HasValue || authorityType != AuthorityType.Generic)
		{
			string text = uri.AbsolutePath.Substring(1);
			if (string.IsNullOrWhiteSpace(text) && !IsCiamAuthority(uri))
			{
				throw new ArgumentException("The authority URI should have at least one segment in the path (i.e. https://<host>/<path>/...). ", "authority");
			}
			string[] array = text.Split('/');
			if (array == null || array.Length == 0)
			{
				throw new ArgumentException("The authority URI should have at least one segment in the path (i.e. https://<host>/<path>/...). ");
			}
		}
		return uri;
	}

	private static string GetAuthorityUri(AzureCloudInstance azureCloudInstance, AadAuthorityAudience authorityAudience, string tenantId = null)
	{
		string cloudUrl = GetCloudUrl(azureCloudInstance);
		string aadAuthorityAudienceValue = GetAadAuthorityAudienceValue(authorityAudience, tenantId);
		return cloudUrl + "/" + aadAuthorityAudienceValue;
	}

	internal static string GetFirstPathSegment(Uri authority)
	{
		if (authority.Segments.Length >= 2)
		{
			return authority.Segments[1].TrimEnd('/');
		}
		throw new InvalidOperationException("Authority should be in the form <host>/<audience>, for example https://login.microsoftonline.com/common. ");
	}

	internal static string GetSecondPathSegment(Uri authority)
	{
		if (authority.Segments.Length >= 3)
		{
			return authority.Segments[2].TrimEnd('/');
		}
		throw new InvalidOperationException("Authority should be in the form <host>/<audience>/<tenantID>, for example https://login.microsoftonline.com/dsts/<tenantid>. ");
	}

	private static AuthorityType GetAuthorityType(Uri authorityUri)
	{
		if (IsCiamAuthority(authorityUri))
		{
			return AuthorityType.Ciam;
		}
		string firstPathSegment = GetFirstPathSegment(authorityUri);
		if (string.Equals(firstPathSegment, "adfs", StringComparison.OrdinalIgnoreCase))
		{
			return AuthorityType.Adfs;
		}
		if (string.Equals(firstPathSegment, "dstsv2", StringComparison.OrdinalIgnoreCase))
		{
			return AuthorityType.Dsts;
		}
		if (string.Equals(firstPathSegment, "tfp", StringComparison.OrdinalIgnoreCase))
		{
			return AuthorityType.B2C;
		}
		return AuthorityType.Aad;
	}

	private static bool IsCiamAuthority(Uri authorityUri)
	{
		return authorityUri.Host.EndsWith(".ciamlogin.com");
	}

	private static string[] GetPathSegments(string absolutePath)
	{
		return absolutePath.Substring(1).Split(new char[1] { '/' }, StringSplitOptions.RemoveEmptyEntries);
	}
}
