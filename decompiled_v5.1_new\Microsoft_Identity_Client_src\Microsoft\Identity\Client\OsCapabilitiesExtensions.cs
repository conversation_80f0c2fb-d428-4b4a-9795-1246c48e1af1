using System;
using System.Security.Cryptography.X509Certificates;

namespace Microsoft.Identity.Client;

public static class OsCapabilitiesExtensions
{
	public static bool IsSystemWebViewAvailable(this IPublicClientApplication publicClientApplication)
	{
		if (publicClientApplication is PublicClientApplication publicClientApplication2)
		{
			return publicClientApplication2.IsSystemWebViewAvailable;
		}
		throw new ArgumentException("This extension method is only available for the PublicClientApplication implementation of the IPublicClientApplication interface.");
	}

	public static bool IsEmbeddedWebViewAvailable(this IPublicClientApplication publicClientApplication)
	{
		if (publicClientApplication is PublicClientApplication publicClientApplication2)
		{
			return publicClientApplication2.IsEmbeddedWebViewAvailable();
		}
		throw new ArgumentException("This extension method is only available for the PublicClientApplication implementation of the IPublicClientApplication interface.");
	}

	public static bool IsUserInteractive(this IPublicClientApplication publicClientApplication)
	{
		if (publicClientApplication is PublicClientApplication publicClientApplication2)
		{
			return publicClientApplication2.IsUserInteractive();
		}
		throw new ArgumentException("This extension method is only available for the PublicClientApplication implementation of the IPublicClientApplication interface.");
	}

	public static X509Certificate2 GetCertificate(this IConfidentialClientApplication confidentialClientApplication)
	{
		if (confidentialClientApplication is ConfidentialClientApplication confidentialClientApplication2)
		{
			return confidentialClientApplication2.Certificate;
		}
		throw new ArgumentException("This extension method is only available for the ConfidentialClientApplication implementation of the IConfidentialClientApplication interface.");
	}
}
