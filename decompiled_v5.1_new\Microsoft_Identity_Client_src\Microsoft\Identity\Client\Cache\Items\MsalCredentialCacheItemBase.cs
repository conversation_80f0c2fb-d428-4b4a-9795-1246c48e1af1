using System.Text.Json.Nodes;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.Cache.Items;

internal class MsalCredentialCacheItemBase : MsalCacheItemBase
{
	internal string CredentialType { get; set; }

	public string ClientId { get; set; }

	public string Secret { get; set; }

	internal override void PopulateFieldsFromJObject(JsonObject j)
	{
		CredentialType = JsonHelper.ExtractExistingOrEmptyString(j, "credential_type");
		ClientId = JsonHelper.ExtractExistingOrEmptyString(j, "client_id");
		Secret = JsonHelper.ExtractExistingOrEmptyString(j, "secret");
		base.PopulateFieldsFromJObject(j);
	}

	internal override JsonObject ToJObject()
	{
		JsonObject jsonObject = base.ToJObject();
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "client_id", ClientId);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "secret", Secret);
		MsalItemWithAdditionalFields.SetItemIfValueNotNull(jsonObject, "credential_type", CredentialType);
		return jsonObject;
	}
}
