namespace Microsoft.Identity.Client.ApiConfig.Executors;

internal static class ClientExecutorFactory
{
	public static IPublicClientApplicationExecutor CreatePublicClientExecutor(PublicClientApplication publicClientApplication)
	{
		return new PublicClientExecutor(publicClientApplication.ServiceBundle, publicClientApplication);
	}

	public static IConfidentialClientApplicationExecutor CreateConfidentialClientExecutor(ConfidentialClientApplication confidentialClientApplication)
	{
		ApplicationBase.GuardMobileFrameworks();
		return new ConfidentialClientExecutor(confidentialClientApplication.ServiceBundle, confidentialClientApplication);
	}

	public static IManagedIdentityApplicationExecutor CreateManagedIdentityExecutor(ManagedIdentityApplication managedIdentityApplication)
	{
		ApplicationBase.GuardMobileFrameworks();
		return new ManagedIdentityExecutor(managedIdentityApplication.ServiceBundle, managedIdentityApplication);
	}

	public static IClientApplicationBaseExecutor CreateClientApplicationBaseExecutor(ClientApplicationBase clientApplicationBase)
	{
		return new ClientApplicationBaseExecutor(clientApplicationBase.ServiceBundle, clientApplicationBase);
	}
}
