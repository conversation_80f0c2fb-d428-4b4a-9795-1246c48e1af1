<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AssemblyName>NPOI.OpenXml4Net</AssemblyName>
    <GenerateAssemblyInfo>False</GenerateAssemblyInfo>
    <TargetFramework>netcoreapp6.0</TargetFramework>
  </PropertyGroup>
  <PropertyGroup>
    <LangVersion>12.0</LangVersion>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup />
  <ItemGroup />
  <ItemGroup>
    <Reference Include="NPOI.Core">
      <HintPath>..\..\Relesez5.1\NPOI.Core.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>..\..\Relesez5.1\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>