using System;
using System.Collections.Generic;
using System.Globalization;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Http;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Utils;

namespace Microsoft.Identity.Client.WsTrust;

internal class WsTrustWebRequestManager : IWsTrustWebRequestManager
{
	private readonly IHttpManager _httpManager;

	public WsTrustWebRequestManager(IHttpManager httpManager)
	{
		_httpManager = httpManager;
	}

	public async Task<MexDocument> GetMexDocumentAsync(string federationMetadataUrl, RequestContext requestContext, string federationMetadata = null)
	{
		if (!string.IsNullOrEmpty(federationMetadata))
		{
			MexDocument result = new MexDocument(federationMetadata);
			requestContext.Logger.Info(() => "MEX document fetched and parsed from provided federation metadata");
			return result;
		}
		IDictionary<string, string> msalIdParameters = MsalIdHelper.GetMsalIdParameters(requestContext.Logger);
		UriBuilder uriBuilder = new UriBuilder(federationMetadataUrl);
		HttpResponse httpResponse = await _httpManager.SendGetAsync(uriBuilder.Uri, msalIdParameters, requestContext.Logger, retry: true, requestContext.UserCancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		if (httpResponse.StatusCode != HttpStatusCode.OK)
		{
			string errorMessage = string.Format(CultureInfo.CurrentCulture, "Response status code does not indicate success: {0} ({1}). See https://aka.ms/msal-net-ropc for more information. ", (int)httpResponse.StatusCode, httpResponse.StatusCode);
			requestContext.Logger.ErrorPii($"=== Token Acquisition ({requestContext.ApiEvent?.ApiIdString}) failed:\n\tAuthority: {requestContext.ServiceBundle.Config.Authority.AuthorityInfo.CanonicalAuthority}\n\tClientId: {requestContext.ServiceBundle.Config.ClientId}.", $"=== Token Acquisition ({requestContext.ApiEvent?.ApiIdString}) failed.\n\tHost: {requestContext.ServiceBundle.Config.Authority.AuthorityInfo.Host}.");
			throw MsalServiceExceptionFactory.FromHttpResponse("accessing_ws_metadata_exchange_failed", errorMessage, httpResponse);
		}
		MexDocument result2 = new MexDocument(httpResponse.Body);
		requestContext.Logger.InfoPii(() => "MEX document fetched and parsed from '" + federationMetadataUrl + "'", () => "Fetched and parsed MEX");
		return result2;
	}

	public async Task<WsTrustResponse> GetWsTrustResponseAsync(WsTrustEndpoint wsTrustEndpoint, string wsTrustRequest, RequestContext requestContext)
	{
		Dictionary<string, string> headers = new Dictionary<string, string> { 
		{
			"SOAPAction",
			(wsTrustEndpoint.Version == WsTrustVersion.WsTrust2005) ? XmlNamespace.Issue2005.ToString() : XmlNamespace.Issue.ToString()
		} };
		StringContent body = new StringContent(wsTrustRequest, Encoding.UTF8, "application/soap+xml");
		HttpResponse httpResponse = await _httpManager.SendPostForceResponseAsync(wsTrustEndpoint.Uri, headers, body, requestContext.Logger, requestContext.UserCancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		if (httpResponse.StatusCode != HttpStatusCode.OK)
		{
			string text;
			try
			{
				text = WsTrustResponse.ReadErrorResponse(XDocument.Parse(httpResponse.Body, LoadOptions.None));
			}
			catch (XmlException)
			{
				text = httpResponse.Body;
			}
			requestContext.Logger.ErrorPii("Ws-Trust request failed. See error message for more details." + $"Status code: {httpResponse.StatusCode} \nError message: {text}", "Ws-Trust request failed. See error message for more details." + $"Status code: {httpResponse.StatusCode}");
			string errorMessage = string.Format(CultureInfo.CurrentCulture, "Federated service at {0} returned error: {1} ", wsTrustEndpoint.Uri, text);
			throw MsalServiceExceptionFactory.FromHttpResponse("federated_service_returned_error", errorMessage, httpResponse);
		}
		try
		{
			WsTrustResponse wsTrustResponse = WsTrustResponse.CreateFromResponse(httpResponse.Body, wsTrustEndpoint.Version);
			if (wsTrustResponse == null)
			{
				requestContext.Logger.ErrorPii("Token not found in the ws trust response. See response for more details: \n" + httpResponse.Body, "Token not found in WS-Trust response.");
				throw new MsalClientException("parsing_wstrust_response_failed", "There was an error parsing the WS-Trust response from the endpoint. \nThis may occur if there are issues with your ADFS configuration. See https://aka.ms/msal-net-iwa-troubleshooting for more details.\nEnable logging to see more details. See https://aka.ms/msal-net-logging.");
			}
			return wsTrustResponse;
		}
		catch (XmlException innerException)
		{
			string errorMessage2 = string.Format(CultureInfo.CurrentCulture, "Federated service at {0} parse error: Body {1} ", wsTrustEndpoint.Uri, httpResponse.Body);
			throw new MsalClientException("parsing_wstrust_response_failed", errorMessage2, innerException);
		}
	}

	public async Task<UserRealmDiscoveryResponse> GetUserRealmAsync(string userRealmUriPrefix, string userName, RequestContext requestContext)
	{
		requestContext.Logger.Info("Sending request to userrealm endpoint. ");
		IDictionary<string, string> msalIdParameters = MsalIdHelper.GetMsalIdParameters(requestContext.Logger);
		Uri uri = new UriBuilder(userRealmUriPrefix + userName + "?api-version=1.0").Uri;
		HttpResponse httpResponse = await _httpManager.SendGetAsync(uri, msalIdParameters, requestContext.Logger, retry: true, requestContext.UserCancellationToken).ConfigureAwait(continueOnCapturedContext: false);
		if (httpResponse.StatusCode == HttpStatusCode.OK)
		{
			return JsonHelper.DeserializeFromJson<UserRealmDiscoveryResponse>(httpResponse.Body);
		}
		string errorMessage = string.Format(CultureInfo.CurrentCulture, "Response status code does not indicate success: {0} ({1}). ", (int)httpResponse.StatusCode, httpResponse.StatusCode);
		requestContext.Logger.ErrorPii($"=== Token Acquisition ({requestContext.ApiEvent?.ApiIdString}) failed:\n\tAuthority: {requestContext.ServiceBundle.Config.Authority.AuthorityInfo.CanonicalAuthority}\n\tClientId: {requestContext.ServiceBundle.Config.ClientId}.", $"=== Token Acquisition ({requestContext.ApiEvent?.ApiIdString}) failed.\n\tHost: {requestContext.ServiceBundle.Config.Authority.AuthorityInfo.Host}.");
		throw MsalServiceExceptionFactory.FromHttpResponse("user_realm_discovery_failed", errorMessage, httpResponse);
	}
}
