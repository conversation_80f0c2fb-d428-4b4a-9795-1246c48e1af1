using System;

namespace Newtonsoft.Json.Bson.Utilities;

internal static class StringUtils
{
	public const string CarriageReturnLineFeed = "\r\n";

	public const string Empty = "";

	public const char CarriageReturn = '\r';

	public const char LineFeed = '\n';

	public const char Tab = '\t';

	public static string FormatWith(this string format, IFormatProvider provider, object arg0)
	{
		return format.FormatWith(provider, new object[1] { arg0 });
	}

	public static string FormatWith(this string format, IFormatProvider provider, object arg0, object arg1)
	{
		return format.FormatWith(provider, new object[2] { arg0, arg1 });
	}

	public static string FormatWith(this string format, IFormatProvider provider, object arg0, object arg1, object arg2)
	{
		return format.FormatWith(provider, new object[3] { arg0, arg1, arg2 });
	}

	public static string FormatWith(this string format, IFormatProvider provider, object arg0, object arg1, object arg2, object arg3)
	{
		return format.FormatWith(provider, new object[4] { arg0, arg1, arg2, arg3 });
	}

	private static string FormatWith(this string format, IFormatProvider provider, params object[] args)
	{
		ValidationUtils.ArgumentNotNull(format, "format");
		return string.Format(provider, format, args);
	}

	public static bool StartsWith(this string source, char value)
	{
		if (source.Length > 0)
		{
			return source[0] == value;
		}
		return false;
	}

	public static bool EndsWith(this string source, char value)
	{
		if (source.Length > 0)
		{
			return source[source.Length - 1] == value;
		}
		return false;
	}
}
