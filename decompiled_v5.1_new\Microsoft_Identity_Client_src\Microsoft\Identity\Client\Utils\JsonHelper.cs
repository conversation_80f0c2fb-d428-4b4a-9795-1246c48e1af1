using System.Buffers;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Platforms.net6;

namespace Microsoft.Identity.Client.Utils;

internal static class JsonHelper
{
	internal static string SerializeToJson<T>(T toEncode)
	{
		return JsonSerializer.Serialize(toEncode, typeof(T), MsalJsonSerializerContext.Custom);
	}

	internal static T DeserializeFromJson<T>(string json)
	{
		if (string.IsNullOrEmpty(json))
		{
			return default(T);
		}
		return (T)JsonSerializer.Deserialize(json, typeof(T), MsalJsonSerializerContext.Custom);
	}

	internal static T TryToDeserializeFromJson<T>(string json, RequestContext requestContext = null)
	{
		if (string.IsNullOrEmpty(json))
		{
			return default(T);
		}
		T result = default(T);
		try
		{
			result = DeserializeFromJson<T>(json.ToByteArray());
			return result;
		}
		catch (JsonException exWithPii)
		{
			requestContext?.Logger?.WarningPii(exWithPii);
		}
		return result;
	}

	internal static T DeserializeFromJson<T>(byte[] jsonByteArray)
	{
		if (jsonByteArray == null || jsonByteArray.Length == 0)
		{
			return default(T);
		}
		using MemoryStream utf8Json = new MemoryStream(jsonByteArray);
		return (T)JsonSerializer.Deserialize(utf8Json, typeof(T), MsalJsonSerializerContext.Custom);
	}

	internal static string GetExistingOrEmptyString(JsonObject json, string key)
	{
		if (TryGetValue(json, key, out var value))
		{
			return GetValue<string>(value);
		}
		return string.Empty;
	}

	internal static string ExtractExistingOrEmptyString(JsonObject json, string key)
	{
		if (TryGetValue(json, key, out var value))
		{
			string value2 = GetValue<string>(value);
			json.Remove(key);
			return value2;
		}
		return string.Empty;
	}

	internal static IDictionary<string, string> ExtractInnerJsonAsDictionary(JsonObject json, string key)
	{
		if (TryGetValue(json, key, out var value))
		{
			Dictionary<string, string> result = ToJsonObject(value).ToDictionary<KeyValuePair<string, JsonNode>, string, string>((KeyValuePair<string, JsonNode> pair) => pair.Key, (KeyValuePair<string, JsonNode> pair) => (string?)pair.Value);
			json.Remove(key);
			return result;
		}
		return null;
	}

	internal static T ExtractExistingOrDefault<T>(JsonObject json, string key)
	{
		if (TryGetValue(json, key, out var value))
		{
			T value2 = GetValue<T>(value);
			json.Remove(key);
			return value2;
		}
		return default(T);
	}

	internal static long ExtractParsedIntOrZero(JsonObject json, string key)
	{
		string text = ExtractExistingOrEmptyString(json, key);
		if (!string.IsNullOrWhiteSpace(text) && long.TryParse(text, out var result))
		{
			return result;
		}
		return 0L;
	}

	internal static string JsonObjectToString(JsonObject jsonObject)
	{
		return jsonObject.ToJsonString();
	}

	internal static JsonObject ParseIntoJsonObject(string json)
	{
		return JsonNode.Parse(json).AsObject();
	}

	internal static JsonObject ToJsonObject(JsonNode jsonNode)
	{
		return jsonNode.AsObject();
	}

	internal static bool TryGetValue(JsonObject json, string propertyName, out JsonNode value)
	{
		return json.TryGetPropertyValue(propertyName, out value);
	}

	internal static T GetValue<T>(JsonNode json)
	{
		if (json == null)
		{
			return default(T);
		}
		return json.GetValue<T>();
	}

	internal static JsonObject Merge(JsonObject originalJson, JsonObject newContent)
	{
		ArrayBufferWriter<byte> arrayBufferWriter = new ArrayBufferWriter<byte>();
		using (JsonDocument jsonDocument = JsonDocument.Parse(originalJson.ToJsonString()))
		{
			using JsonDocument jsonDocument2 = JsonDocument.Parse(newContent.ToJsonString());
			using Utf8JsonWriter jsonWriter = new Utf8JsonWriter(arrayBufferWriter, new JsonWriterOptions
			{
				Indented = true
			});
			MergeJsonElements(jsonWriter, jsonDocument.RootElement, jsonDocument2.RootElement);
		}
		return ParseIntoJsonObject(Encoding.UTF8.GetString(arrayBufferWriter.WrittenSpan));
	}

	private static void MergeJsonElements(Utf8JsonWriter jsonWriter, JsonElement root1, JsonElement root2)
	{
		switch (root1.ValueKind)
		{
		case JsonValueKind.Object:
			MergeObjects(jsonWriter, root1, root2);
			break;
		case JsonValueKind.Array:
			MergeArrays(jsonWriter, root1, root2);
			break;
		default:
			root1.WriteTo(jsonWriter);
			break;
		}
	}

	private static void MergeObjects(Utf8JsonWriter jsonWriter, JsonElement root1, JsonElement root2)
	{
		jsonWriter.WriteStartObject();
		HashSet<string> hashSet = new HashSet<string>();
		foreach (JsonProperty item in root1.EnumerateObject())
		{
			string name = item.Name;
			JsonValueKind valueKind;
			if (root2.TryGetProperty(name, out var value) && (valueKind = value.ValueKind) != JsonValueKind.Null)
			{
				jsonWriter.WritePropertyName(name);
				hashSet.Add(name);
				JsonElement value2 = item.Value;
				JsonValueKind valueKind2 = value2.ValueKind;
				if ((valueKind == JsonValueKind.Object && valueKind2 == JsonValueKind.Object) || (valueKind == JsonValueKind.Array && valueKind2 == JsonValueKind.Array))
				{
					MergeJsonElements(jsonWriter, value2, value);
				}
				else
				{
					value.WriteTo(jsonWriter);
				}
			}
			else
			{
				item.WriteTo(jsonWriter);
			}
		}
		foreach (JsonProperty item2 in root2.EnumerateObject())
		{
			if (!hashSet.Contains(item2.Name))
			{
				item2.WriteTo(jsonWriter);
			}
		}
		jsonWriter.WriteEndObject();
	}

	private static void MergeArrays(Utf8JsonWriter jsonWriter, JsonElement root1, JsonElement root2)
	{
		jsonWriter.WriteStartArray();
		for (int i = 0; i < root1.GetArrayLength(); i++)
		{
			root1[i].WriteTo(jsonWriter);
		}
		for (int j = 0; j < root2.GetArrayLength(); j++)
		{
			root2[j].WriteTo(jsonWriter);
		}
		jsonWriter.WriteEndArray();
	}
}
