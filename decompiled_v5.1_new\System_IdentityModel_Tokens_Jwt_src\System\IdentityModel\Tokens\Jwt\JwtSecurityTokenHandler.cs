using System.Collections.Generic;
using System.Linq;
using System.Runtime.ExceptionServices;
using System.Security.Claims;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Xml;
using Microsoft.IdentityModel.JsonWebTokens;
using Microsoft.IdentityModel.Logging;
using Microsoft.IdentityModel.Tokens;

namespace System.IdentityModel.Tokens.Jwt;

public class JwtSecurityTokenHandler : SecurityTokenHandler
{
	private delegate bool CertMatcher(X509Certificate2 cert);

	private ISet<string> _inboundClaimFilter;

	private IDictionary<string, string> _inboundClaimTypeMap;

	private static string _jsonClaimType;

	private const string _namespace = "http://schemas.xmlsoap.org/ws/2005/05/identity/claimproperties";

	private const string _className = "System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler";

	private IDictionary<string, string> _outboundClaimTypeMap;

	private IDictionary<string, string> _outboundAlgorithmMap;

	private static string _shortClaimType;

	private bool _mapInboundClaims = DefaultMapInboundClaims;

	public static IDictionary<string, string> DefaultInboundClaimTypeMap;

	public static bool DefaultMapInboundClaims;

	public static IDictionary<string, string> DefaultOutboundClaimTypeMap;

	public static ISet<string> DefaultInboundClaimFilter;

	public static IDictionary<string, string> DefaultOutboundAlgorithmMap;

	public bool MapInboundClaims
	{
		get
		{
			return _mapInboundClaims;
		}
		set
		{
			if (!_mapInboundClaims && value && _inboundClaimTypeMap.Count == 0)
			{
				_inboundClaimTypeMap = new Dictionary<string, string>(DefaultInboundClaimTypeMap);
			}
			_mapInboundClaims = value;
		}
	}

	public IDictionary<string, string> InboundClaimTypeMap
	{
		get
		{
			return _inboundClaimTypeMap;
		}
		set
		{
			_inboundClaimTypeMap = value ?? throw LogHelper.LogArgumentNullException("value");
		}
	}

	public IDictionary<string, string> OutboundClaimTypeMap
	{
		get
		{
			return _outboundClaimTypeMap;
		}
		set
		{
			if (value == null)
			{
				throw LogHelper.LogArgumentNullException("value");
			}
			_outboundClaimTypeMap = value;
		}
	}

	public IDictionary<string, string> OutboundAlgorithmMap => _outboundAlgorithmMap;

	public ISet<string> InboundClaimFilter
	{
		get
		{
			return _inboundClaimFilter;
		}
		set
		{
			if (value == null)
			{
				throw LogHelper.LogArgumentNullException("value");
			}
			_inboundClaimFilter = value;
		}
	}

	public static string ShortClaimTypeProperty
	{
		get
		{
			return _shortClaimType;
		}
		set
		{
			if (string.IsNullOrWhiteSpace(value))
			{
				throw LogHelper.LogArgumentNullException("value");
			}
			_shortClaimType = value;
		}
	}

	public static string JsonClaimTypeProperty
	{
		get
		{
			return _jsonClaimType;
		}
		set
		{
			if (string.IsNullOrWhiteSpace(value))
			{
				throw LogHelper.LogArgumentNullException("value");
			}
			_jsonClaimType = value;
		}
	}

	public override bool CanValidateToken => true;

	public override bool CanWriteToken => true;

	public override Type TokenType => typeof(JwtSecurityToken);

	static JwtSecurityTokenHandler()
	{
		_jsonClaimType = "http://schemas.xmlsoap.org/ws/2005/05/identity/claimproperties/json_type";
		_shortClaimType = "http://schemas.xmlsoap.org/ws/2005/05/identity/claimproperties/ShortTypeName";
		DefaultInboundClaimTypeMap = new Dictionary<string, string>(ClaimTypeMapping.InboundClaimTypeMap);
		DefaultMapInboundClaims = true;
		DefaultOutboundClaimTypeMap = new Dictionary<string, string>(ClaimTypeMapping.OutboundClaimTypeMap);
		DefaultInboundClaimFilter = ClaimTypeMapping.InboundClaimFilter;
		DefaultOutboundAlgorithmMap = new Dictionary<string, string>
		{
			{ "http://www.w3.org/2001/04/xmldsig-more#ecdsa-sha256", "ES256" },
			{ "http://www.w3.org/2001/04/xmldsig-more#ecdsa-sha384", "ES384" },
			{ "http://www.w3.org/2001/04/xmldsig-more#ecdsa-sha512", "ES512" },
			{ "http://www.w3.org/2001/04/xmldsig-more#hmac-sha256", "HS256" },
			{ "http://www.w3.org/2001/04/xmldsig-more#hmac-sha384", "HS384" },
			{ "http://www.w3.org/2001/04/xmldsig-more#hmac-sha512", "HS512" },
			{ "http://www.w3.org/2001/04/xmldsig-more#rsa-sha256", "RS256" },
			{ "http://www.w3.org/2001/04/xmldsig-more#rsa-sha384", "RS384" },
			{ "http://www.w3.org/2001/04/xmldsig-more#rsa-sha512", "RS512" }
		};
	}

	public JwtSecurityTokenHandler()
	{
		if (_mapInboundClaims)
		{
			_inboundClaimTypeMap = new Dictionary<string, string>(DefaultInboundClaimTypeMap);
		}
		else
		{
			_inboundClaimTypeMap = new Dictionary<string, string>();
		}
		_outboundClaimTypeMap = new Dictionary<string, string>(DefaultOutboundClaimTypeMap);
		_inboundClaimFilter = new HashSet<string>(DefaultInboundClaimFilter);
		_outboundAlgorithmMap = new Dictionary<string, string>(DefaultOutboundAlgorithmMap);
	}

	public override bool CanReadToken(string token)
	{
		if (string.IsNullOrWhiteSpace(token))
		{
			return false;
		}
		if (token.Length > MaximumTokenSizeInBytes)
		{
			LogHelper.LogInformation("IDX10209: Token has length: '{0}' which is larger than the MaximumTokenSizeInBytes: '{1}'.", LogHelper.MarkAsNonPII(token.Length), LogHelper.MarkAsNonPII(MaximumTokenSizeInBytes));
			return false;
		}
		string[] array = token.Split(new char[1] { '.' }, 6);
		if (array.Length == 3)
		{
			return JwtTokenUtilities.RegexJws.IsMatch(token);
		}
		if (array.Length == 5)
		{
			return JwtTokenUtilities.RegexJwe.IsMatch(token);
		}
		LogHelper.LogInformation("IDX12720: Token string does not match the token formats: JWE (header.encryptedKey.iv.ciphertext.tag) or JWS (header.payload.signature)");
		return false;
	}

	public virtual string CreateEncodedJwt(SecurityTokenDescriptor tokenDescriptor)
	{
		if (tokenDescriptor == null)
		{
			throw LogHelper.LogArgumentNullException("tokenDescriptor");
		}
		return CreateJwtSecurityToken(tokenDescriptor).RawData;
	}

	public virtual string CreateEncodedJwt(string issuer, string audience, ClaimsIdentity subject, DateTime? notBefore, DateTime? expires, DateTime? issuedAt, SigningCredentials signingCredentials)
	{
		return CreateJwtSecurityTokenPrivate(issuer, audience, subject, notBefore, expires, issuedAt, signingCredentials, null, null, null, null, null).RawData;
	}

	public virtual string CreateEncodedJwt(string issuer, string audience, ClaimsIdentity subject, DateTime? notBefore, DateTime? expires, DateTime? issuedAt, SigningCredentials signingCredentials, EncryptingCredentials encryptingCredentials)
	{
		return CreateJwtSecurityTokenPrivate(issuer, audience, subject, notBefore, expires, issuedAt, signingCredentials, encryptingCredentials, null, null, null, null).RawData;
	}

	public virtual string CreateEncodedJwt(string issuer, string audience, ClaimsIdentity subject, DateTime? notBefore, DateTime? expires, DateTime? issuedAt, SigningCredentials signingCredentials, EncryptingCredentials encryptingCredentials, IDictionary<string, object> claimCollection)
	{
		return CreateJwtSecurityTokenPrivate(issuer, audience, subject, notBefore, expires, issuedAt, signingCredentials, encryptingCredentials, claimCollection, null, null, null).RawData;
	}

	public virtual JwtSecurityToken CreateJwtSecurityToken(SecurityTokenDescriptor tokenDescriptor)
	{
		if (tokenDescriptor == null)
		{
			throw LogHelper.LogArgumentNullException("tokenDescriptor");
		}
		return CreateJwtSecurityTokenPrivate(tokenDescriptor.Issuer, tokenDescriptor.Audience, tokenDescriptor.Subject, tokenDescriptor.NotBefore, tokenDescriptor.Expires, tokenDescriptor.IssuedAt, tokenDescriptor.SigningCredentials, tokenDescriptor.EncryptingCredentials, tokenDescriptor.Claims, tokenDescriptor.TokenType, tokenDescriptor.AdditionalHeaderClaims, tokenDescriptor.AdditionalInnerHeaderClaims);
	}

	public virtual JwtSecurityToken CreateJwtSecurityToken(string issuer, string audience, ClaimsIdentity subject, DateTime? notBefore, DateTime? expires, DateTime? issuedAt, SigningCredentials signingCredentials, EncryptingCredentials encryptingCredentials)
	{
		return CreateJwtSecurityTokenPrivate(issuer, audience, subject, notBefore, expires, issuedAt, signingCredentials, encryptingCredentials, null, null, null, null);
	}

	public virtual JwtSecurityToken CreateJwtSecurityToken(string issuer, string audience, ClaimsIdentity subject, DateTime? notBefore, DateTime? expires, DateTime? issuedAt, SigningCredentials signingCredentials, EncryptingCredentials encryptingCredentials, IDictionary<string, object> claimCollection)
	{
		return CreateJwtSecurityTokenPrivate(issuer, audience, subject, notBefore, expires, issuedAt, signingCredentials, encryptingCredentials, claimCollection, null, null, null);
	}

	public virtual JwtSecurityToken CreateJwtSecurityToken(string issuer = null, string audience = null, ClaimsIdentity subject = null, DateTime? notBefore = null, DateTime? expires = null, DateTime? issuedAt = null, SigningCredentials signingCredentials = null)
	{
		return CreateJwtSecurityTokenPrivate(issuer, audience, subject, notBefore, expires, issuedAt, signingCredentials, null, null, null, null, null);
	}

	public override SecurityToken CreateToken(SecurityTokenDescriptor tokenDescriptor)
	{
		if (tokenDescriptor == null)
		{
			throw LogHelper.LogArgumentNullException("tokenDescriptor");
		}
		return CreateJwtSecurityTokenPrivate(tokenDescriptor.Issuer, tokenDescriptor.Audience, tokenDescriptor.Subject, tokenDescriptor.NotBefore, tokenDescriptor.Expires, tokenDescriptor.IssuedAt, tokenDescriptor.SigningCredentials, tokenDescriptor.EncryptingCredentials, tokenDescriptor.Claims, tokenDescriptor.TokenType, tokenDescriptor.AdditionalHeaderClaims, tokenDescriptor.AdditionalInnerHeaderClaims);
	}

	private JwtSecurityToken CreateJwtSecurityTokenPrivate(string issuer, string audience, ClaimsIdentity subject, DateTime? notBefore, DateTime? expires, DateTime? issuedAt, SigningCredentials signingCredentials, EncryptingCredentials encryptingCredentials, IDictionary<string, object> claimCollection, string tokenType, IDictionary<string, object> additionalHeaderClaims, IDictionary<string, object> additionalInnerHeaderClaims)
	{
		if (base.SetDefaultTimesOnTokenCreation && (!expires.HasValue || !issuedAt.HasValue || !notBefore.HasValue))
		{
			DateTime utcNow = DateTime.UtcNow;
			if (!expires.HasValue)
			{
				expires = utcNow + TimeSpan.FromMinutes(base.TokenLifetimeInMinutes);
			}
			if (!issuedAt.HasValue)
			{
				issuedAt = utcNow;
			}
			if (!notBefore.HasValue)
			{
				notBefore = utcNow;
			}
		}
		LogHelper.LogVerbose("IDX12721: Creating JwtSecurityToken: Issuer: '{0}', Audience: '{1}'", LogHelper.MarkAsNonPII(issuer ?? "null"), LogHelper.MarkAsNonPII(audience ?? "null"));
		JwtPayload jwtPayload = new JwtPayload(issuer, audience, (subject == null) ? null : OutboundClaimTypeTransform(subject.Claims), (claimCollection == null) ? null : OutboundClaimTypeTransform(claimCollection), notBefore, expires, issuedAt);
		JwtHeader jwtHeader = new JwtHeader(signingCredentials, OutboundAlgorithmMap, tokenType, additionalInnerHeaderClaims);
		if (subject?.Actor != null)
		{
			jwtPayload.AddClaim(new Claim("actort", CreateActorValue(subject.Actor)));
		}
		string text = jwtHeader.Base64UrlEncode();
		string text2 = jwtPayload.Base64UrlEncode();
		string input = jwtHeader.Base64UrlEncode() + "." + jwtPayload.Base64UrlEncode();
		string rawSignature = ((signingCredentials == null) ? string.Empty : JwtTokenUtilities.CreateEncodedSignature(input, signingCredentials));
		LogHelper.LogInformation("IDX12722: Creating security token from the header: '{0}', payload: '{1}'.", text, text2);
		if (encryptingCredentials != null)
		{
			return EncryptToken(new JwtSecurityToken(jwtHeader, jwtPayload, text, text2, rawSignature), encryptingCredentials, tokenType, additionalHeaderClaims);
		}
		return new JwtSecurityToken(jwtHeader, jwtPayload, text, text2, rawSignature);
	}

	private JwtSecurityToken EncryptToken(JwtSecurityToken innerJwt, EncryptingCredentials encryptingCredentials, string tokenType, IDictionary<string, object> additionalHeaderClaims)
	{
		if (encryptingCredentials == null)
		{
			throw LogHelper.LogArgumentNullException("encryptingCredentials");
		}
		CryptoProviderFactory cryptoProviderFactory = encryptingCredentials.CryptoProviderFactory ?? encryptingCredentials.Key.CryptoProviderFactory;
		if (cryptoProviderFactory == null)
		{
			throw LogHelper.LogExceptionMessage(new ArgumentException("IDX10620: Unable to obtain a CryptoProviderFactory, both EncryptingCredentials.CryptoProviderFactory and EncryptingCredentials.Key.CrypoProviderFactory are null."));
		}
		byte[] wrappedKey;
		SecurityKey securityKey = JwtTokenUtilities.GetSecurityKey(encryptingCredentials, cryptoProviderFactory, additionalHeaderClaims, out wrappedKey);
		using AuthenticatedEncryptionProvider authenticatedEncryptionProvider = cryptoProviderFactory.CreateAuthenticatedEncryptionProvider(securityKey, encryptingCredentials.Enc);
		if (authenticatedEncryptionProvider == null)
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenEncryptionFailedException("IDX12730: Failed to create the token encryption provider."));
		}
		try
		{
			JwtHeader jwtHeader = new JwtHeader(encryptingCredentials, OutboundAlgorithmMap, tokenType, additionalHeaderClaims);
			AuthenticatedEncryptionResult authenticatedEncryptionResult = authenticatedEncryptionProvider.Encrypt(Encoding.UTF8.GetBytes(innerJwt.RawData), Encoding.ASCII.GetBytes(jwtHeader.Base64UrlEncode()));
			return "dir".Equals(encryptingCredentials.Alg) ? new JwtSecurityToken(jwtHeader, innerJwt, jwtHeader.Base64UrlEncode(), string.Empty, Base64UrlEncoder.Encode(authenticatedEncryptionResult.IV), Base64UrlEncoder.Encode(authenticatedEncryptionResult.Ciphertext), Base64UrlEncoder.Encode(authenticatedEncryptionResult.AuthenticationTag)) : new JwtSecurityToken(jwtHeader, innerJwt, jwtHeader.Base64UrlEncode(), Base64UrlEncoder.Encode(wrappedKey), Base64UrlEncoder.Encode(authenticatedEncryptionResult.IV), Base64UrlEncoder.Encode(authenticatedEncryptionResult.Ciphertext), Base64UrlEncoder.Encode(authenticatedEncryptionResult.AuthenticationTag));
		}
		catch (Exception innerException)
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenEncryptionFailedException(LogHelper.FormatInvariant("IDX10616: Encryption failed. EncryptionProvider failed for: Algorithm: '{0}', SecurityKey: '{1}'. See inner exception.", LogHelper.MarkAsNonPII(encryptingCredentials.Enc), encryptingCredentials.Key), innerException));
		}
	}

	private IEnumerable<Claim> OutboundClaimTypeTransform(IEnumerable<Claim> claims)
	{
		foreach (Claim claim in claims)
		{
			string value = null;
			if (_outboundClaimTypeMap.TryGetValue(claim.Type, out value))
			{
				yield return new Claim(value, claim.Value, claim.ValueType, claim.Issuer, claim.OriginalIssuer, claim.Subject);
			}
			else
			{
				yield return claim;
			}
		}
	}

	private IDictionary<string, object> OutboundClaimTypeTransform(IDictionary<string, object> claimCollection)
	{
		Dictionary<string, object> dictionary = new Dictionary<string, object>();
		foreach (string key in claimCollection.Keys)
		{
			if (_outboundClaimTypeMap.TryGetValue(key, out var value))
			{
				dictionary[value] = claimCollection[key];
			}
			else
			{
				dictionary[key] = claimCollection[key];
			}
		}
		return dictionary;
	}

	public JwtSecurityToken ReadJwtToken(string token)
	{
		if (string.IsNullOrEmpty(token))
		{
			throw LogHelper.LogArgumentNullException("token");
		}
		if (token.Length > MaximumTokenSizeInBytes)
		{
			throw LogHelper.LogExceptionMessage(new ArgumentException(LogHelper.FormatInvariant("IDX10209: Token has length: '{0}' which is larger than the MaximumTokenSizeInBytes: '{1}'.", LogHelper.MarkAsNonPII(token.Length), LogHelper.MarkAsNonPII(MaximumTokenSizeInBytes))));
		}
		if (!CanReadToken(token))
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenMalformedException("IDX12709: CanReadToken() returned false. JWT is not well formed.\nThe token needs to be in JWS or JWE Compact Serialization Format. (JWS): 'EncodedHeader.EndcodedPayload.EncodedSignature'. (JWE): 'EncodedProtectedHeader.EncodedEncryptedKey.EncodedInitializationVector.EncodedCiphertext.EncodedAuthenticationTag'."));
		}
		JwtSecurityToken jwtSecurityToken = new JwtSecurityToken();
		jwtSecurityToken.Decode(token.Split('.'), token);
		return jwtSecurityToken;
	}

	public override SecurityToken ReadToken(string token)
	{
		return ReadJwtToken(token);
	}

	public override SecurityToken ReadToken(XmlReader reader, TokenValidationParameters validationParameters)
	{
		throw new NotImplementedException();
	}

	public override ClaimsPrincipal ValidateToken(string token, TokenValidationParameters validationParameters, out SecurityToken validatedToken)
	{
		if (string.IsNullOrWhiteSpace(token))
		{
			throw LogHelper.LogArgumentNullException("token");
		}
		if (validationParameters == null)
		{
			throw LogHelper.LogArgumentNullException("validationParameters");
		}
		if (token.Length > MaximumTokenSizeInBytes)
		{
			throw LogHelper.LogExceptionMessage(new ArgumentException(LogHelper.FormatInvariant("IDX10209: Token has length: '{0}' which is larger than the MaximumTokenSizeInBytes: '{1}'.", LogHelper.MarkAsNonPII(token.Length), LogHelper.MarkAsNonPII(MaximumTokenSizeInBytes))));
		}
		string[] array = token.Split(new char[1] { '.' }, 6);
		if (array.Length != 3 && array.Length != 5)
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenMalformedException("IDX12741: JWT must have three segments (JWS) or five segments (JWE)."));
		}
		if (array.Length == 5)
		{
			JwtSecurityToken jwtSecurityToken = ReadJwtToken(token);
			string token2 = DecryptToken(jwtSecurityToken, validationParameters);
			return ValidateToken(token2, jwtSecurityToken, validationParameters, out validatedToken);
		}
		return ValidateToken(token, null, validationParameters, out validatedToken);
	}

	private ClaimsPrincipal ValidateToken(string token, JwtSecurityToken outerToken, TokenValidationParameters validationParameters, out SecurityToken signatureValidatedToken)
	{
		BaseConfiguration baseConfiguration = null;
		if (validationParameters.ConfigurationManager != null)
		{
			try
			{
				baseConfiguration = validationParameters.ConfigurationManager.GetBaseConfigurationAsync(CancellationToken.None).ConfigureAwait(continueOnCapturedContext: false).GetAwaiter()
					.GetResult();
			}
			catch (Exception ex)
			{
				LogHelper.LogWarning(LogHelper.FormatInvariant("IDX10261: Unable to retrieve configuration from authority: '{0}'. \nProceeding with token validation in case the relevant properties have been set manually on the TokenValidationParameters. Exception caught: \n {1}. See https://aka.ms/validate-using-configuration-manager for additional information.", LogHelper.MarkAsNonPII(validationParameters.ConfigurationManager.MetadataAddress), ex.ToString()));
			}
		}
		ExceptionDispatchInfo exceptionThrown;
		ClaimsPrincipal claimsPrincipal = ((outerToken != null) ? ValidateJWE(token, outerToken, validationParameters, baseConfiguration, out signatureValidatedToken, out exceptionThrown) : ValidateJWS(token, validationParameters, baseConfiguration, out signatureValidatedToken, out exceptionThrown));
		if (validationParameters.ConfigurationManager != null)
		{
			if (claimsPrincipal != null)
			{
				if (baseConfiguration != null)
				{
					validationParameters.ConfigurationManager.LastKnownGoodConfiguration = baseConfiguration;
				}
				return claimsPrincipal;
			}
			if (TokenUtilities.IsRecoverableException(exceptionThrown.SourceException))
			{
				if (baseConfiguration != null)
				{
					validationParameters.ConfigurationManager.RequestRefresh();
					validationParameters.RefreshBeforeValidation = true;
					BaseConfiguration baseConfiguration2 = baseConfiguration;
					baseConfiguration = validationParameters.ConfigurationManager.GetBaseConfigurationAsync(CancellationToken.None).GetAwaiter().GetResult();
					if (baseConfiguration2 != baseConfiguration)
					{
						claimsPrincipal = ((outerToken != null) ? ValidateJWE(token, outerToken, validationParameters, baseConfiguration, out signatureValidatedToken, out exceptionThrown) : ValidateJWS(token, validationParameters, baseConfiguration, out signatureValidatedToken, out exceptionThrown));
						if (claimsPrincipal != null)
						{
							validationParameters.ConfigurationManager.LastKnownGoodConfiguration = baseConfiguration;
							return claimsPrincipal;
						}
					}
				}
				if (validationParameters.ConfigurationManager.UseLastKnownGoodConfiguration)
				{
					validationParameters.RefreshBeforeValidation = false;
					validationParameters.ValidateWithLKG = true;
					Exception sourceException = exceptionThrown.SourceException;
					string kid = ((outerToken != null) ? outerToken.Header.Kid : (ValidateSignatureUsingDelegates(token, validationParameters, null) ?? GetJwtSecurityTokenFromToken(token, validationParameters)).Header.Kid);
					foreach (BaseConfiguration validLkgConfiguration in validationParameters.ConfigurationManager.GetValidLkgConfigurations())
					{
						if (!validLkgConfiguration.Equals(baseConfiguration) && TokenUtilities.IsRecoverableConfiguration(kid, baseConfiguration, validLkgConfiguration, sourceException))
						{
							claimsPrincipal = ((outerToken != null) ? ValidateJWE(token, outerToken, validationParameters, validLkgConfiguration, out signatureValidatedToken, out exceptionThrown) : ValidateJWS(token, validationParameters, validLkgConfiguration, out signatureValidatedToken, out exceptionThrown));
							if (claimsPrincipal != null)
							{
								return claimsPrincipal;
							}
						}
					}
				}
			}
		}
		if (claimsPrincipal != null)
		{
			return claimsPrincipal;
		}
		exceptionThrown.Throw();
		return null;
	}

	private ClaimsPrincipal ValidateJWE(string decryptedJwt, JwtSecurityToken outerToken, TokenValidationParameters validationParameters, BaseConfiguration currentConfiguration, out SecurityToken signatureValidatedToken, out ExceptionDispatchInfo exceptionThrown)
	{
		exceptionThrown = null;
		try
		{
			SecurityToken signatureValidatedToken2;
			ClaimsPrincipal result = ValidateJWS(decryptedJwt, validationParameters, currentConfiguration, out signatureValidatedToken2, out exceptionThrown);
			outerToken.InnerToken = signatureValidatedToken2 as JwtSecurityToken;
			signatureValidatedToken = ((exceptionThrown == null) ? outerToken : null);
			return result;
		}
		catch (Exception source)
		{
			exceptionThrown = ExceptionDispatchInfo.Capture(source);
			signatureValidatedToken = null;
			return null;
		}
	}

	private ClaimsPrincipal ValidateJWS(string token, TokenValidationParameters validationParameters, BaseConfiguration currentConfiguration, out SecurityToken signatureValidatedToken, out ExceptionDispatchInfo exceptionThrown)
	{
		exceptionThrown = null;
		try
		{
			ClaimsPrincipal result;
			if (validationParameters.SignatureValidator != null || validationParameters.SignatureValidatorUsingConfiguration != null)
			{
				signatureValidatedToken = ValidateSignatureUsingDelegates(token, validationParameters, currentConfiguration);
				result = ValidateTokenPayload(signatureValidatedToken as JwtSecurityToken, validationParameters, currentConfiguration);
				if (currentConfiguration == null)
				{
					ValidateIssuerSecurityKey(signatureValidatedToken.SigningKey, signatureValidatedToken as JwtSecurityToken, validationParameters);
				}
				else
				{
					Validators.ValidateIssuerSecurityKey(signatureValidatedToken.SigningKey, signatureValidatedToken, validationParameters, currentConfiguration);
				}
			}
			else
			{
				JwtSecurityToken jwtSecurityTokenFromToken = GetJwtSecurityTokenFromToken(token, validationParameters);
				if (validationParameters.ValidateSignatureLast)
				{
					result = ValidateTokenPayload(jwtSecurityTokenFromToken, validationParameters, currentConfiguration);
					jwtSecurityTokenFromToken = ValidateSignatureAndIssuerSecurityKey(token, jwtSecurityTokenFromToken, validationParameters, currentConfiguration);
					signatureValidatedToken = jwtSecurityTokenFromToken;
				}
				else
				{
					signatureValidatedToken = ValidateSignatureAndIssuerSecurityKey(token, jwtSecurityTokenFromToken, validationParameters, currentConfiguration);
					result = ValidateTokenPayload(signatureValidatedToken as JwtSecurityToken, validationParameters, currentConfiguration);
				}
			}
			return result;
		}
		catch (Exception source)
		{
			exceptionThrown = ExceptionDispatchInfo.Capture(source);
			signatureValidatedToken = null;
			return null;
		}
	}

	private static JwtSecurityToken ValidateSignatureUsingDelegates(string token, TokenValidationParameters validationParameters, BaseConfiguration configuration)
	{
		if (validationParameters == null)
		{
			throw LogHelper.LogArgumentNullException("validationParameters");
		}
		if (validationParameters.SignatureValidatorUsingConfiguration != null)
		{
			SecurityToken securityToken = validationParameters.SignatureValidatorUsingConfiguration(token, validationParameters, configuration);
			if (securityToken == null)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10505: Signature validation failed. The user defined 'Delegate' specified on TokenValidationParameters returned null when validating token: '{0}'.", LogHelper.MarkAsSecurityArtifact(token, JwtTokenUtilities.SafeLogJwtToken))));
			}
			if (!(securityToken is JwtSecurityToken result))
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10506: Signature validation failed. The user defined 'Delegate' specified on TokenValidationParameters did not return a '{0}', but returned a '{1}' when validating token: '{2}'.", LogHelper.MarkAsNonPII(typeof(JwtSecurityToken)), LogHelper.MarkAsNonPII(securityToken.GetType()), LogHelper.MarkAsSecurityArtifact(token, JwtTokenUtilities.SafeLogJwtToken))));
			}
			return result;
		}
		if (validationParameters.SignatureValidator != null)
		{
			SecurityToken securityToken2 = validationParameters.SignatureValidator(token, validationParameters);
			if (securityToken2 == null)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10505: Signature validation failed. The user defined 'Delegate' specified on TokenValidationParameters returned null when validating token: '{0}'.", LogHelper.MarkAsSecurityArtifact(token, JwtTokenUtilities.SafeLogJwtToken))));
			}
			if (!(securityToken2 is JwtSecurityToken result2))
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10506: Signature validation failed. The user defined 'Delegate' specified on TokenValidationParameters did not return a '{0}', but returned a '{1}' when validating token: '{2}'.", LogHelper.MarkAsNonPII(typeof(JwtSecurityToken)), LogHelper.MarkAsNonPII(securityToken2.GetType()), LogHelper.MarkAsSecurityArtifact(token, JwtTokenUtilities.SafeLogJwtToken))));
			}
			return result2;
		}
		return null;
	}

	private JwtSecurityToken ValidateSignatureAndIssuerSecurityKey(string token, JwtSecurityToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
	{
		JwtSecurityToken result = ValidateSignature(token, jwtToken, validationParameters, configuration);
		if (configuration == null)
		{
			ValidateIssuerSecurityKey(jwtToken.SigningKey, jwtToken, validationParameters);
			return result;
		}
		Validators.ValidateIssuerSecurityKey(jwtToken.SigningKey, jwtToken, validationParameters, configuration);
		return result;
	}

	private JwtSecurityToken GetJwtSecurityTokenFromToken(string token, TokenValidationParameters validationParameters)
	{
		if (validationParameters.TokenReader != null)
		{
			SecurityToken securityToken = validationParameters.TokenReader(token, validationParameters);
			if (securityToken == null)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10510: Token validation failed. The user defined 'Delegate' set on TokenValidationParameters.TokenReader returned null when reading token: '{0}'.", LogHelper.MarkAsSecurityArtifact(token, JwtTokenUtilities.SafeLogJwtToken))));
			}
			if (!(securityToken is JwtSecurityToken result))
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10509: Token validation failed. The user defined 'Delegate' set on TokenValidationParameters.TokenReader did not return a '{0}', but returned a '{1}' when reading token: '{2}'.", LogHelper.MarkAsNonPII(typeof(JsonWebToken)), LogHelper.MarkAsNonPII(securityToken.GetType()), LogHelper.MarkAsSecurityArtifact(token, JwtTokenUtilities.SafeLogJwtToken))));
			}
			return result;
		}
		return ReadJwtToken(token);
	}

	protected ClaimsPrincipal ValidateTokenPayload(JwtSecurityToken jwtToken, TokenValidationParameters validationParameters)
	{
		return ValidateTokenPayload(jwtToken, validationParameters, null);
	}

	private ClaimsPrincipal ValidateTokenPayload(JwtSecurityToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
	{
		if (jwtToken == null)
		{
			throw LogHelper.LogArgumentNullException("jwtToken");
		}
		if (validationParameters == null)
		{
			throw LogHelper.LogArgumentNullException("validationParameters");
		}
		DateTime? expires = ((!jwtToken.Payload.Exp.HasValue) ? ((DateTime?)null) : new DateTime?(jwtToken.ValidTo));
		DateTime? notBefore = ((!jwtToken.Payload.Nbf.HasValue) ? ((DateTime?)null) : new DateTime?(jwtToken.ValidFrom));
		ValidateLifetime(notBefore, expires, jwtToken, validationParameters);
		ValidateAudience(jwtToken.Audiences, jwtToken, validationParameters);
		string issuer = ((configuration == null) ? ValidateIssuer(jwtToken.Issuer, jwtToken, validationParameters) : Validators.ValidateIssuer(jwtToken.Issuer, jwtToken, validationParameters, configuration));
		ValidateTokenReplay(expires, jwtToken.RawData, validationParameters);
		if (validationParameters.ValidateActor && !string.IsNullOrWhiteSpace(jwtToken.Actor))
		{
			ValidateToken(jwtToken.Actor, validationParameters.ActorValidationParameters ?? validationParameters, out var _);
		}
		Validators.ValidateTokenType(jwtToken.Header.Typ, jwtToken, validationParameters);
		ClaimsIdentity claimsIdentity = CreateClaimsIdentity(jwtToken, issuer, validationParameters);
		if (validationParameters.SaveSigninToken)
		{
			claimsIdentity.BootstrapContext = jwtToken.RawData;
		}
		LogHelper.LogInformation("IDX10241: Security token validated. token: '{0}'.", jwtToken);
		return new ClaimsPrincipal(claimsIdentity);
	}

	private ClaimsPrincipal CreateClaimsPrincipalFromToken(JwtSecurityToken jwtToken, string issuer, TokenValidationParameters validationParameters)
	{
		ClaimsIdentity claimsIdentity = CreateClaimsIdentity(jwtToken, issuer, validationParameters);
		if (validationParameters.SaveSigninToken)
		{
			claimsIdentity.BootstrapContext = jwtToken.RawData;
		}
		LogHelper.LogInformation("IDX10241: Security token validated. token: '{0}'.", jwtToken);
		return new ClaimsPrincipal(claimsIdentity);
	}

	public override string WriteToken(SecurityToken token)
	{
		if (token == null)
		{
			throw LogHelper.LogArgumentNullException("token");
		}
		if (!(token is JwtSecurityToken { EncodedPayload: var encodedPayload } jwtSecurityToken))
		{
			throw LogHelper.LogExceptionMessage(new ArgumentException(LogHelper.FormatInvariant("IDX12706: '{0}' can only write SecurityTokens of type: '{1}', 'token' type is: '{2}'.", LogHelper.MarkAsNonPII("System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler"), LogHelper.MarkAsNonPII(typeof(JwtSecurityToken)), LogHelper.MarkAsNonPII(token.GetType())), "token"));
		}
		string text = string.Empty;
		string empty = string.Empty;
		if (jwtSecurityToken.InnerToken != null)
		{
			if (jwtSecurityToken.SigningCredentials != null)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenEncryptionFailedException("IDX12736: JwtSecurityToken.SigningCredentials is not supported when JwtSecurityToken.InnerToken is set."));
			}
			if (jwtSecurityToken.InnerToken.Header.EncryptingCredentials != null)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenEncryptionFailedException("IDX12737: EncryptingCredentials set on JwtSecurityToken.InnerToken is not supported."));
			}
			if (jwtSecurityToken.Header.EncryptingCredentials == null)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenEncryptionFailedException("IDX12735: If JwtSecurityToken.InnerToken != null, then JwtSecurityToken.Header.EncryptingCredentials must be set."));
			}
			if (jwtSecurityToken.InnerToken.SigningCredentials != null)
			{
				text = JwtTokenUtilities.CreateEncodedSignature(jwtSecurityToken.InnerToken.EncodedHeader + "." + jwtSecurityToken.EncodedPayload, jwtSecurityToken.InnerToken.SigningCredentials);
			}
			return EncryptToken(new JwtSecurityToken(jwtSecurityToken.InnerToken.Header, jwtSecurityToken.InnerToken.Payload, jwtSecurityToken.InnerToken.EncodedHeader, encodedPayload, text), jwtSecurityToken.EncryptingCredentials, jwtSecurityToken.InnerToken.Header.Typ, null).RawData;
		}
		JwtHeader jwtHeader = ((jwtSecurityToken.EncryptingCredentials == null) ? jwtSecurityToken.Header : new JwtHeader(jwtSecurityToken.SigningCredentials));
		empty = jwtHeader.Base64UrlEncode();
		if (jwtSecurityToken.SigningCredentials != null)
		{
			text = JwtTokenUtilities.CreateEncodedSignature(empty + "." + encodedPayload, jwtSecurityToken.SigningCredentials);
		}
		if (jwtSecurityToken.EncryptingCredentials != null)
		{
			return EncryptToken(new JwtSecurityToken(jwtHeader, jwtSecurityToken.Payload, empty, encodedPayload, text), jwtSecurityToken.EncryptingCredentials, jwtSecurityToken.Header.Typ, null).RawData;
		}
		return empty + "." + encodedPayload + "." + text;
	}

	private static bool ValidateSignature(byte[] encodedBytes, byte[] signature, SecurityKey key, string algorithm, SecurityToken securityToken, TokenValidationParameters validationParameters)
	{
		Validators.ValidateAlgorithm(algorithm, key, securityToken, validationParameters);
		CryptoProviderFactory cryptoProviderFactory = validationParameters.CryptoProviderFactory ?? key.CryptoProviderFactory;
		SignatureProvider signatureProvider = cryptoProviderFactory.CreateForVerifying(key, algorithm);
		if (signatureProvider == null)
		{
			throw LogHelper.LogExceptionMessage(new InvalidOperationException(LogHelper.FormatInvariant("IDX10636: CryptoProviderFactory.CreateForVerifying returned null for key: '{0}', signatureAlgorithm: '{1}'.", (key == null) ? "Null" : key.ToString(), LogHelper.MarkAsNonPII(algorithm))));
		}
		try
		{
			return signatureProvider.Verify(encodedBytes, signature);
		}
		finally
		{
			cryptoProviderFactory.ReleaseSignatureProvider(signatureProvider);
		}
	}

	protected virtual JwtSecurityToken ValidateSignature(string token, TokenValidationParameters validationParameters)
	{
		JwtSecurityToken jwtSecurityToken = ValidateSignatureUsingDelegates(token, validationParameters, null);
		JwtSecurityToken jwtSecurityTokenFromToken = GetJwtSecurityTokenFromToken(token, validationParameters);
		return ValidateSignature(token, jwtSecurityToken ?? jwtSecurityTokenFromToken, validationParameters, null);
	}

	private JwtSecurityToken ValidateSignature(string token, JwtSecurityToken jwtToken, TokenValidationParameters validationParameters, BaseConfiguration configuration)
	{
		byte[] bytes = Encoding.UTF8.GetBytes(jwtToken.RawHeader + "." + jwtToken.RawPayload);
		bool flag = false;
		IEnumerable<SecurityKey> enumerable = null;
		if (string.IsNullOrEmpty(jwtToken.RawSignature))
		{
			if (validationParameters.RequireSignedTokens)
			{
				throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10504: Unable to validate signature, token does not have a signature: '{0}'.", jwtToken)));
			}
			return jwtToken;
		}
		if (validationParameters.IssuerSigningKeyResolverUsingConfiguration != null)
		{
			enumerable = validationParameters.IssuerSigningKeyResolverUsingConfiguration(token, jwtToken, jwtToken.Header.Kid, validationParameters, configuration);
		}
		if (validationParameters.IssuerSigningKeyResolver != null)
		{
			enumerable = validationParameters.IssuerSigningKeyResolver(token, jwtToken, jwtToken.Header.Kid, validationParameters);
		}
		else
		{
			SecurityKey securityKey = ((configuration == null) ? ResolveIssuerSigningKey(token, jwtToken, validationParameters) : JwtTokenUtilities.ResolveTokenSigningKey(jwtToken.Header.Kid, jwtToken.Header.X5t, validationParameters, configuration));
			if (securityKey != null)
			{
				flag = true;
				enumerable = new List<SecurityKey> { securityKey };
			}
		}
		if (enumerable == null && validationParameters.TryAllIssuerSigningKeys)
		{
			enumerable = TokenUtilities.GetAllSigningKeys(validationParameters, configuration);
		}
		StringBuilder stringBuilder = new StringBuilder();
		StringBuilder stringBuilder2 = new StringBuilder();
		bool flag2 = !string.IsNullOrEmpty(jwtToken.Header.Kid);
		byte[] signature;
		try
		{
			signature = Base64UrlEncoder.DecodeBytes(jwtToken.RawSignature);
		}
		catch (FormatException innerException)
		{
			throw new SecurityTokenInvalidSignatureException("IDX10508: Signature validation failed. Signature is improperly formatted.", innerException);
		}
		if (enumerable != null)
		{
			foreach (SecurityKey item in enumerable)
			{
				try
				{
					if (ValidateSignature(bytes, signature, item, jwtToken.Header.Alg, jwtToken, validationParameters))
					{
						LogHelper.LogInformation("IDX10242: Security token: '{0}' has a valid signature.", jwtToken);
						jwtToken.SigningKey = item;
						return jwtToken;
					}
				}
				catch (Exception ex)
				{
					stringBuilder.AppendLine(ex.ToString());
				}
				if (item != null)
				{
					stringBuilder2.Append(item.ToString()).Append(" , KeyId: ").AppendLine(item.KeyId);
					if (flag2 && !flag && item.KeyId != null)
					{
						flag = jwtToken.Header.Kid.Equals(item.KeyId, (item is X509SecurityKey) ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal);
					}
				}
			}
		}
		IEnumerable<SecurityKey> allSigningKeys = TokenUtilities.GetAllSigningKeys(validationParameters);
		IEnumerable<SecurityKey> allSigningKeys2 = TokenUtilities.GetAllSigningKeys(configuration);
		int num = allSigningKeys.Count();
		int num2 = allSigningKeys2.Count();
		if (flag2)
		{
			if (flag)
			{
				string arg = (allSigningKeys.Any((SecurityKey x) => x.KeyId.Equals(jwtToken.Header.Kid)) ? "TokenValidationParameters" : "Configuration");
				throw LogHelper.LogExceptionMessage(new SecurityTokenInvalidSignatureException(LogHelper.FormatInvariant("IDX10511: Signature validation failed. Keys tried: '{0}'. \nNumber of keys in TokenValidationParameters: '{1}'. \nNumber of keys in Configuration: '{2}'. \nMatched key was in '{3}'. \nkid: '{4}'. \nExceptions caught:\n '{5}'.\ntoken: '{6}'. See https://aka.ms/IDX10511 for details.", stringBuilder2, LogHelper.MarkAsNonPII(num), LogHelper.MarkAsNonPII(num2), LogHelper.MarkAsNonPII(arg), LogHelper.MarkAsNonPII(jwtToken.Header.Kid), stringBuilder, jwtToken)));
			}
			DateTime? expires = ((!jwtToken.Payload.Exp.HasValue) ? ((DateTime?)null) : new DateTime?(jwtToken.ValidTo));
			DateTime? notBefore = ((!jwtToken.Payload.Nbf.HasValue) ? ((DateTime?)null) : new DateTime?(jwtToken.ValidFrom));
			if (!validationParameters.ValidateSignatureLast)
			{
				InternalValidators.ValidateAfterSignatureFailed(jwtToken, notBefore, expires, jwtToken.Audiences, validationParameters, configuration);
			}
		}
		if (stringBuilder2.Length > 0)
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenSignatureKeyNotFoundException(LogHelper.FormatInvariant("IDX10503: Signature validation failed. Token does not have a kid. Keys tried: '{0}'. Number of keys in TokenValidationParameters: '{1}'. \nNumber of keys in Configuration: '{2}'. \nExceptions caught:\n '{3}'.\ntoken: '{4}'. See https://aka.ms/IDX10503 for details.", stringBuilder2, LogHelper.MarkAsNonPII(num), LogHelper.MarkAsNonPII(num2), stringBuilder, jwtToken)));
		}
		throw LogHelper.LogExceptionMessage(new SecurityTokenSignatureKeyNotFoundException("IDX10500: Signature validation failed. No security keys were provided to validate the signature."));
	}

	private static IEnumerable<SecurityKey> GetAllDecryptionKeys(TokenValidationParameters validationParameters)
	{
		if (validationParameters.TokenDecryptionKey != null)
		{
			yield return validationParameters.TokenDecryptionKey;
		}
		if (validationParameters.TokenDecryptionKeys == null)
		{
			yield break;
		}
		foreach (SecurityKey tokenDecryptionKey in validationParameters.TokenDecryptionKeys)
		{
			yield return tokenDecryptionKey;
		}
	}

	protected virtual ClaimsIdentity CreateClaimsIdentity(JwtSecurityToken jwtToken, string issuer, TokenValidationParameters validationParameters)
	{
		if (jwtToken == null)
		{
			throw LogHelper.LogArgumentNullException("jwtToken");
		}
		if (validationParameters == null)
		{
			throw LogHelper.LogArgumentNullException("validationParameters");
		}
		string actualIssuer = issuer;
		if (string.IsNullOrWhiteSpace(issuer))
		{
			LogHelper.LogVerbose("IDX10244: Issuer is null or empty. Using runtime default for creating claims '{0}'.", LogHelper.MarkAsNonPII("LOCAL AUTHORITY"));
			actualIssuer = "LOCAL AUTHORITY";
		}
		if (!MapInboundClaims)
		{
			return CreateClaimsIdentityWithoutMapping(jwtToken, actualIssuer, validationParameters);
		}
		return CreateClaimsIdentityWithMapping(jwtToken, actualIssuer, validationParameters);
	}

	private ClaimsIdentity CreateClaimsIdentityWithMapping(JwtSecurityToken jwtToken, string actualIssuer, TokenValidationParameters validationParameters)
	{
		ClaimsIdentity claimsIdentity = validationParameters.CreateClaimsIdentity(jwtToken, actualIssuer);
		foreach (Claim claim2 in jwtToken.Claims)
		{
			if (_inboundClaimFilter.Contains(claim2.Type))
			{
				continue;
			}
			bool flag = true;
			if (!_inboundClaimTypeMap.TryGetValue(claim2.Type, out var value))
			{
				value = claim2.Type;
				flag = false;
			}
			if (value == "http://schemas.xmlsoap.org/ws/2009/09/identity/claims/actor")
			{
				if (claimsIdentity.Actor != null)
				{
					throw LogHelper.LogExceptionMessage(new InvalidOperationException(LogHelper.FormatInvariant("IDX12710: Only a single 'Actor' is supported. Found second claim of type: '{0}', value: '{1}'", LogHelper.MarkAsNonPII("actort"), LogHelper.MarkAsSecurityArtifact(claim2.Value, JwtTokenUtilities.SafeLogJwtToken))));
				}
				if (CanReadToken(claim2.Value))
				{
					JwtSecurityToken jwtToken2 = ReadToken(claim2.Value) as JwtSecurityToken;
					claimsIdentity.Actor = CreateClaimsIdentity(jwtToken2, actualIssuer, validationParameters);
				}
			}
			Claim claim = new Claim(value, claim2.Value, claim2.ValueType, actualIssuer, actualIssuer, claimsIdentity);
			if (claim2.Properties.Count > 0)
			{
				foreach (KeyValuePair<string, string> property in claim2.Properties)
				{
					claim.Properties[property.Key] = property.Value;
				}
			}
			if (flag)
			{
				claim.Properties[ShortClaimTypeProperty] = claim2.Type;
			}
			claimsIdentity.AddClaim(claim);
		}
		return claimsIdentity;
	}

	private ClaimsIdentity CreateClaimsIdentityWithoutMapping(JwtSecurityToken jwtToken, string actualIssuer, TokenValidationParameters validationParameters)
	{
		ClaimsIdentity claimsIdentity = validationParameters.CreateClaimsIdentity(jwtToken, actualIssuer);
		foreach (Claim claim2 in jwtToken.Claims)
		{
			if (_inboundClaimFilter.Contains(claim2.Type))
			{
				continue;
			}
			string type = claim2.Type;
			if (type == "http://schemas.xmlsoap.org/ws/2009/09/identity/claims/actor")
			{
				if (claimsIdentity.Actor != null)
				{
					throw LogHelper.LogExceptionMessage(new InvalidOperationException(LogHelper.FormatInvariant("IDX12710: Only a single 'Actor' is supported. Found second claim of type: '{0}', value: '{1}'", LogHelper.MarkAsNonPII("actort"), LogHelper.MarkAsSecurityArtifact(claim2.Value, JwtTokenUtilities.SafeLogJwtToken))));
				}
				if (CanReadToken(claim2.Value))
				{
					JwtSecurityToken jwtToken2 = ReadToken(claim2.Value) as JwtSecurityToken;
					claimsIdentity.Actor = CreateClaimsIdentity(jwtToken2, actualIssuer, validationParameters);
				}
			}
			Claim claim = new Claim(type, claim2.Value, claim2.ValueType, actualIssuer, actualIssuer, claimsIdentity);
			if (claim2.Properties.Count > 0)
			{
				foreach (KeyValuePair<string, string> property in claim2.Properties)
				{
					claim.Properties[property.Key] = property.Value;
				}
			}
			claimsIdentity.AddClaim(claim);
		}
		return claimsIdentity;
	}

	protected virtual string CreateActorValue(ClaimsIdentity actor)
	{
		if (actor == null)
		{
			throw LogHelper.LogArgumentNullException("actor");
		}
		if (actor.BootstrapContext != null)
		{
			if (actor.BootstrapContext is string result)
			{
				LogHelper.LogVerbose("IDX12713: Creating actor value using actor.BootstrapContext(as string)");
				return result;
			}
			if (actor.BootstrapContext is JwtSecurityToken jwtSecurityToken)
			{
				if (jwtSecurityToken.RawData != null)
				{
					LogHelper.LogVerbose("IDX12714: Creating actor value using actor.BootstrapContext.rawData");
					return jwtSecurityToken.RawData;
				}
				LogHelper.LogVerbose("IDX12715: Creating actor value by writing the JwtSecurityToken created from actor.BootstrapContext");
				return WriteToken(jwtSecurityToken);
			}
			LogHelper.LogVerbose("IDX12711: actor.BootstrapContext is not a string AND actor.BootstrapContext is not a JWT");
		}
		LogHelper.LogVerbose("IDX12712: actor.BootstrapContext is null. Creating the token using actor.Claims.");
		return WriteToken(new JwtSecurityToken(null, null, actor.Claims));
	}

	protected virtual void ValidateAudience(IEnumerable<string> audiences, JwtSecurityToken jwtToken, TokenValidationParameters validationParameters)
	{
		Validators.ValidateAudience(audiences, jwtToken, validationParameters);
	}

	protected virtual void ValidateLifetime(DateTime? notBefore, DateTime? expires, JwtSecurityToken jwtToken, TokenValidationParameters validationParameters)
	{
		Validators.ValidateLifetime(notBefore, expires, jwtToken, validationParameters);
	}

	protected virtual string ValidateIssuer(string issuer, JwtSecurityToken jwtToken, TokenValidationParameters validationParameters)
	{
		return Validators.ValidateIssuer(issuer, jwtToken, validationParameters);
	}

	protected virtual void ValidateTokenReplay(DateTime? expires, string securityToken, TokenValidationParameters validationParameters)
	{
		Validators.ValidateTokenReplay(expires, securityToken, validationParameters);
	}

	protected virtual SecurityKey ResolveIssuerSigningKey(string token, JwtSecurityToken jwtToken, TokenValidationParameters validationParameters)
	{
		if (validationParameters == null)
		{
			throw LogHelper.LogArgumentNullException("validationParameters");
		}
		if (jwtToken == null)
		{
			throw LogHelper.LogArgumentNullException("jwtToken");
		}
		return JwtTokenUtilities.ResolveTokenSigningKey(jwtToken.Header.Kid, jwtToken.Header.X5t, validationParameters, null);
	}

	protected virtual SecurityKey ResolveTokenDecryptionKey(string token, JwtSecurityToken jwtToken, TokenValidationParameters validationParameters)
	{
		if (jwtToken == null)
		{
			throw LogHelper.LogArgumentNullException("jwtToken");
		}
		if (validationParameters == null)
		{
			throw LogHelper.LogArgumentNullException("validationParameters");
		}
		if (!string.IsNullOrEmpty(jwtToken.Header.Kid))
		{
			if (validationParameters.TokenDecryptionKey != null && string.Equals(validationParameters.TokenDecryptionKey.KeyId, jwtToken.Header.Kid, (validationParameters.TokenDecryptionKey is X509SecurityKey) ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal))
			{
				return validationParameters.TokenDecryptionKey;
			}
			if (validationParameters.TokenDecryptionKeys != null)
			{
				foreach (SecurityKey tokenDecryptionKey in validationParameters.TokenDecryptionKeys)
				{
					if (tokenDecryptionKey != null && string.Equals(tokenDecryptionKey.KeyId, jwtToken.Header.Kid, (tokenDecryptionKey is X509SecurityKey) ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal))
					{
						return tokenDecryptionKey;
					}
				}
			}
		}
		if (!string.IsNullOrEmpty(jwtToken.Header.X5t))
		{
			if (validationParameters.TokenDecryptionKey != null)
			{
				if (string.Equals(validationParameters.TokenDecryptionKey.KeyId, jwtToken.Header.X5t, (validationParameters.TokenDecryptionKey is X509SecurityKey) ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal))
				{
					return validationParameters.TokenDecryptionKey;
				}
				if (validationParameters.TokenDecryptionKey is X509SecurityKey x509SecurityKey && string.Equals(x509SecurityKey.X5t, jwtToken.Header.X5t, StringComparison.OrdinalIgnoreCase))
				{
					return validationParameters.TokenDecryptionKey;
				}
			}
			if (validationParameters.TokenDecryptionKeys != null)
			{
				foreach (SecurityKey tokenDecryptionKey2 in validationParameters.TokenDecryptionKeys)
				{
					if (tokenDecryptionKey2 != null && string.Equals(tokenDecryptionKey2.KeyId, jwtToken.Header.X5t, (tokenDecryptionKey2 is X509SecurityKey) ? StringComparison.OrdinalIgnoreCase : StringComparison.Ordinal))
					{
						return tokenDecryptionKey2;
					}
					if (tokenDecryptionKey2 is X509SecurityKey x509SecurityKey2 && string.Equals(x509SecurityKey2.X5t, jwtToken.Header.X5t, StringComparison.OrdinalIgnoreCase))
					{
						return tokenDecryptionKey2;
					}
				}
			}
		}
		return null;
	}

	protected string DecryptToken(JwtSecurityToken jwtToken, TokenValidationParameters validationParameters)
	{
		if (jwtToken == null)
		{
			throw LogHelper.LogArgumentNullException("jwtToken");
		}
		if (validationParameters == null)
		{
			throw LogHelper.LogArgumentNullException("validationParameters");
		}
		if (string.IsNullOrEmpty(jwtToken.Header.Enc))
		{
			throw LogHelper.LogExceptionMessage(new SecurityTokenException(LogHelper.FormatInvariant("IDX10612: Decryption failed. Header.Enc is null or empty, it must be specified.")));
		}
		IEnumerable<SecurityKey> contentEncryptionKeys = GetContentEncryptionKeys(jwtToken, validationParameters);
		return JwtTokenUtilities.DecryptJwtToken(jwtToken, validationParameters, new JwtTokenDecryptionParameters
		{
			Alg = jwtToken.Header.Alg,
			AuthenticationTagBytes = Base64UrlEncoder.DecodeBytes(jwtToken.RawAuthenticationTag),
			CipherTextBytes = Base64UrlEncoder.DecodeBytes(jwtToken.RawCiphertext),
			DecompressionFunction = JwtTokenUtilities.DecompressToken,
			Enc = jwtToken.Header.Enc,
			EncodedToken = jwtToken.RawData,
			HeaderAsciiBytes = Encoding.ASCII.GetBytes(jwtToken.EncodedHeader),
			InitializationVectorBytes = Base64UrlEncoder.DecodeBytes(jwtToken.RawInitializationVector),
			MaximumDeflateSize = MaximumTokenSizeInBytes,
			Keys = contentEncryptionKeys,
			Zip = jwtToken.Header.Zip
		});
	}

	internal IEnumerable<SecurityKey> GetContentEncryptionKeys(JwtSecurityToken jwtToken, TokenValidationParameters validationParameters)
	{
		IEnumerable<SecurityKey> enumerable = null;
		if (validationParameters.TokenDecryptionKeyResolver != null)
		{
			enumerable = validationParameters.TokenDecryptionKeyResolver(jwtToken.RawData, jwtToken, jwtToken.Header.Kid, validationParameters);
		}
		else
		{
			SecurityKey securityKey = ResolveTokenDecryptionKey(jwtToken.RawData, jwtToken, validationParameters);
			if (securityKey != null)
			{
				enumerable = new List<SecurityKey> { securityKey };
			}
		}
		if (enumerable == null)
		{
			enumerable = GetAllDecryptionKeys(validationParameters);
		}
		if (jwtToken.Header.Alg.Equals("dir"))
		{
			return enumerable;
		}
		List<SecurityKey> list = new List<SecurityKey>();
		StringBuilder stringBuilder = new StringBuilder();
		StringBuilder stringBuilder2 = new StringBuilder();
		foreach (SecurityKey item in enumerable)
		{
			try
			{
				if (SupportedAlgorithms.EcdsaWrapAlgorithms.Contains(jwtToken.Header.Alg))
				{
					EcdhKeyExchangeProvider ecdhKeyExchangeProvider = new EcdhKeyExchangeProvider(item as ECDsaSecurityKey, validationParameters.TokenDecryptionKey as ECDsaSecurityKey, jwtToken.Header.Alg, jwtToken.Header.Enc);
					string standardClaim = jwtToken.Header.GetStandardClaim("apu");
					string standardClaim2 = jwtToken.Header.GetStandardClaim("apv");
					SecurityKey key = ecdhKeyExchangeProvider.GenerateKdf(standardClaim, standardClaim2);
					byte[] key2 = item.CryptoProviderFactory.CreateKeyWrapProviderForUnwrap(key, ecdhKeyExchangeProvider.GetEncryptionAlgorithm()).UnwrapKey(Base64UrlEncoder.DecodeBytes(jwtToken.RawEncryptedKey));
					list.Add(new SymmetricSecurityKey(key2));
				}
				else if (item.CryptoProviderFactory.IsSupportedAlgorithm(jwtToken.Header.Alg, item))
				{
					byte[] key3 = item.CryptoProviderFactory.CreateKeyWrapProviderForUnwrap(item, jwtToken.Header.Alg).UnwrapKey(Base64UrlEncoder.DecodeBytes(jwtToken.RawEncryptedKey));
					list.Add(new SymmetricSecurityKey(key3));
				}
			}
			catch (Exception ex)
			{
				stringBuilder.AppendLine(ex.ToString());
			}
			stringBuilder2.AppendLine(item.ToString());
		}
		if (list.Count > 0 || stringBuilder.Length == 0)
		{
			return list;
		}
		throw LogHelper.LogExceptionMessage(new SecurityTokenKeyWrapException(LogHelper.FormatInvariant("IDX10618: Key unwrap failed using decryption Keys: '{0}'.\nExceptions caught:\n '{1}'.\ntoken: '{2}'.", stringBuilder2, stringBuilder, jwtToken)));
	}

	private static byte[] GetSymmetricSecurityKey(SecurityKey key)
	{
		if (key == null)
		{
			throw LogHelper.LogArgumentNullException("key");
		}
		if (key is SymmetricSecurityKey symmetricSecurityKey)
		{
			return symmetricSecurityKey.Key;
		}
		if (key is JsonWebKey { K: not null } jsonWebKey)
		{
			return Base64UrlEncoder.DecodeBytes(jsonWebKey.K);
		}
		return null;
	}

	protected virtual void ValidateIssuerSecurityKey(SecurityKey key, JwtSecurityToken securityToken, TokenValidationParameters validationParameters)
	{
		Validators.ValidateIssuerSecurityKey(key, securityToken, validationParameters);
	}

	public override void WriteToken(XmlWriter writer, SecurityToken token)
	{
		throw new NotImplementedException();
	}

	public override Task<TokenValidationResult> ValidateTokenAsync(string token, TokenValidationParameters validationParameters)
	{
		try
		{
			SecurityToken validatedToken;
			ClaimsPrincipal claimsPrincipal = ValidateToken(token, validationParameters, out validatedToken);
			return Task.FromResult(new TokenValidationResult
			{
				SecurityToken = validatedToken,
				ClaimsIdentity = (claimsPrincipal?.Identity as ClaimsIdentity),
				IsValid = true
			});
		}
		catch (Exception exception)
		{
			return Task.FromResult(new TokenValidationResult
			{
				IsValid = false,
				Exception = exception
			});
		}
	}
}
