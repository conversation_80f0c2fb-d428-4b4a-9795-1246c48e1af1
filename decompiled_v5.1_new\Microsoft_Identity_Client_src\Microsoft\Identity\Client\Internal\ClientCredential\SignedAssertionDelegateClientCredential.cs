using System;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.OAuth2;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.TelemetryCore;

namespace Microsoft.Identity.Client.Internal.ClientCredential;

internal class SignedAssertionDelegateClientCredential : IClientCredential
{
	internal Func<CancellationToken, Task<string>> _signedAssertionDelegate { get; }

	internal Func<AssertionRequestOptions, Task<string>> _signedAssertionWithInfoDelegate { get; }

	public AssertionType AssertionType => AssertionType.ClientAssertion;

	[EditorBrowsable(EditorBrowsableState.Never)]
	public SignedAssertionDelegateClientCredential(Func<CancellationToken, Task<string>> signedAssertionDelegate)
	{
		_signedAssertionDelegate = signedAssertionDelegate;
	}

	public SignedAssertionDelegateClientCredential(Func<AssertionRequestOptions, Task<string>> signedAssertionDelegate)
	{
		_signedAssertionWithInfoDelegate = signedAssertionDelegate;
	}

	public async Task AddConfidentialClientParametersAsync(OAuth2Client oAuth2Client, ILoggerAdapter logger, ICryptographyManager cryptographyManager, string clientId, string tokenEndpoint, bool sendX5C, bool useSha2, CancellationToken cancellationToken)
	{
		string value = await ((_signedAssertionDelegate != null) ? _signedAssertionDelegate(cancellationToken).ConfigureAwait(continueOnCapturedContext: false) : _signedAssertionWithInfoDelegate(new AssertionRequestOptions
		{
			CancellationToken = cancellationToken,
			ClientID = clientId,
			TokenEndpoint = tokenEndpoint
		}).ConfigureAwait(continueOnCapturedContext: false));
		oAuth2Client.AddBodyParameter("client_assertion_type", "urn:ietf:params:oauth:client-assertion-type:jwt-bearer");
		oAuth2Client.AddBodyParameter("client_assertion", value);
	}
}
