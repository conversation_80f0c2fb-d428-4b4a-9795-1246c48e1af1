using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Internal;

namespace Microsoft.Identity.Client.Instance;

internal class AadAuthority : Authority
{
	public const string DefaultTrustedHost = "login.microsoftonline.com";

	public const string AADCanonicalAuthorityTemplate = "https://{0}/{1}/";

	private const string TokenEndpointTemplate = "{0}oauth2/v2.0/token";

	private const string DeviceCodeEndpointTemplate = "{0}oauth2/v2.0/devicecode";

	private const string AuthorizationEndpointTemplate = "{0}oauth2/v2.0/authorize";

	private static readonly ISet<string> s_tenantlessTenantNames = new HashSet<string>(new string[3] { "common", "organizations", "consumers" }, StringComparer.OrdinalIgnoreCase);

	internal override string TenantId { get; }

	internal AadAuthority(AuthorityInfo authorityInfo)
		: base(authorityInfo)
	{
		TenantId = AuthorityInfo.GetFirstPathSegment(base.AuthorityInfo.CanonicalAuthority);
	}

	internal bool IsWorkAndSchoolOnly()
	{
		if (!TenantId.Equals("common", StringComparison.OrdinalIgnoreCase))
		{
			return !IsConsumers(TenantId);
		}
		return false;
	}

	internal bool IsConsumers()
	{
		return IsConsumers(TenantId);
	}

	internal static bool IsConsumers(string tenantId)
	{
		if (!tenantId.Equals("consumers", StringComparison.OrdinalIgnoreCase))
		{
			return tenantId.Equals("9188040d-6c67-4c5b-b112-36a304b66dad", StringComparison.OrdinalIgnoreCase);
		}
		return true;
	}

	internal bool IsCommonOrganizationsOrConsumersTenant()
	{
		return IsCommonOrganizationsOrConsumersTenant(TenantId);
	}

	internal static bool IsCommonOrganizationsOrConsumersTenant(string tenantId)
	{
		if (!string.IsNullOrEmpty(tenantId))
		{
			if (!IsCommonOrOrganizationsTenant(tenantId))
			{
				return IsConsumers(tenantId);
			}
			return true;
		}
		return false;
	}

	internal bool IsOrganizationsTenantWithMsaPassthroughEnabled(bool isMsaPassthrough, string accountTenantId)
	{
		if (accountTenantId != null && isMsaPassthrough && TenantId.Equals("organizations", StringComparison.OrdinalIgnoreCase))
		{
			return IsConsumers(accountTenantId);
		}
		return false;
	}

	internal bool IsCommonOrOrganizationsTenant()
	{
		return IsCommonOrOrganizationsTenant(TenantId);
	}

	internal static bool IsCommonOrOrganizationsTenant(string tenantId)
	{
		if (!string.IsNullOrEmpty(tenantId))
		{
			return s_tenantlessTenantNames.Contains(tenantId);
		}
		return false;
	}

	internal override string GetTenantedAuthority(string tenantId, bool forceSpecifiedTenant = false)
	{
		if (!string.IsNullOrEmpty(tenantId) && (forceSpecifiedTenant || IsCommonOrganizationsOrConsumersTenant()))
		{
			Uri canonicalAuthority = base.AuthorityInfo.CanonicalAuthority;
			return string.Format(CultureInfo.InvariantCulture, "https://{0}/{1}/", canonicalAuthority.Authority, tenantId);
		}
		return base.AuthorityInfo.CanonicalAuthority.AbsoluteUri;
	}

	internal override Task<string> GetTokenEndpointAsync(RequestContext requestContext)
	{
		return Task.FromResult(string.Format(CultureInfo.InvariantCulture, "{0}oauth2/v2.0/token", base.AuthorityInfo.CanonicalAuthority));
	}

	internal override Task<string> GetAuthorizationEndpointAsync(RequestContext requestContext)
	{
		return Task.FromResult(string.Format(CultureInfo.InvariantCulture, "{0}oauth2/v2.0/authorize", base.AuthorityInfo.CanonicalAuthority));
	}

	internal override Task<string> GetDeviceCodeEndpointAsync(RequestContext requestContext)
	{
		return Task.FromResult(string.Format(CultureInfo.InvariantCulture, "{0}oauth2/v2.0/devicecode", base.AuthorityInfo.CanonicalAuthority));
	}
}
