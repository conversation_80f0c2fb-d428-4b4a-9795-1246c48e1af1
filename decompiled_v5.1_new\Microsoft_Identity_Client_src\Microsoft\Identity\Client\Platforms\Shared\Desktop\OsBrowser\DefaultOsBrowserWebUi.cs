using System;
using System.Globalization;
using System.Net;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Identity.Client.Core;
using Microsoft.Identity.Client.Internal;
using Microsoft.Identity.Client.Platforms.Shared.DefaultOSBrowser;
using Microsoft.Identity.Client.PlatformsCommon.Interfaces;
using Microsoft.Identity.Client.UI;

namespace Microsoft.Identity.Client.Platforms.Shared.Desktop.OsBrowser;

internal class DefaultOsBrowserWebUi : IWebUI
{
	internal const string DefaultSuccessHtml = "<html>\r\n  <head><title>Authentication Complete</title></head>\r\n  <body>\r\n    Authentication complete. You can return to the application. Feel free to close this browser tab.\r\n  </body>\r\n</html>";

	internal const string DefaultFailureHtml = "<html>\r\n  <head><title>Authentication Failed</title></head>\r\n  <body>\r\n    Authentication failed. You can return to the application. Feel free to close this browser tab.\r\n</br></br></br></br>\r\n    Error details: error {0} error_description: {1}\r\n  </body>\r\n</html>";

	private readonly IUriInterceptor _uriInterceptor;

	private readonly ILoggerAdapter _logger;

	private readonly SystemWebViewOptions _webViewOptions;

	private readonly IPlatformProxy _platformProxy;

	public DefaultOsBrowserWebUi(IPlatformProxy proxy, ILoggerAdapter logger, SystemWebViewOptions webViewOptions, IUriInterceptor uriInterceptor = null)
	{
		_logger = logger ?? throw new ArgumentNullException("logger");
		_webViewOptions = webViewOptions;
		_platformProxy = proxy ?? throw new ArgumentNullException("proxy");
		_uriInterceptor = uriInterceptor ?? new HttpListenerInterceptor(_logger);
	}

	public async Task<AuthorizationResult> AcquireAuthorizationAsync(Uri authorizationUri, Uri redirectUri, RequestContext requestContext, CancellationToken cancellationToken)
	{
		try
		{
			Uri uri = await InterceptAuthorizationUriAsync(authorizationUri, redirectUri, requestContext.ServiceBundle.Config.IsBrokerEnabled, cancellationToken).ConfigureAwait(continueOnCapturedContext: true);
			if (!uri.Authority.Equals(redirectUri.Authority, StringComparison.OrdinalIgnoreCase) || !uri.AbsolutePath.Equals(redirectUri.AbsolutePath))
			{
				throw new MsalClientException("loopback_response_uri_mismatch", MsalErrorMessage.RedirectUriMismatch(uri.AbsolutePath, redirectUri.AbsolutePath));
			}
			return AuthorizationResult.FromUri(uri.OriginalString);
		}
		catch (HttpListenerException)
		{
			cancellationToken.ThrowIfCancellationRequested();
			throw;
		}
	}

	public Uri UpdateRedirectUri(Uri redirectUri)
	{
		if (!redirectUri.IsLoopback)
		{
			throw new MsalClientException("loopback_redirect_uri", "Only loopback redirect uri is supported, but " + redirectUri.AbsoluteUri + " was found. Configure http://localhost or http://localhost:port both during app registration and when you create the PublicClientApplication object. See https://aka.ms/msal-net-os-browser for details");
		}
		if (redirectUri.Scheme != "http")
		{
			throw new MsalClientException("loopback_redirect_uri", "Only http uri scheme is supported, but " + redirectUri.Scheme + " was found. Configure http://localhost or http://localhost:port both during app registration and when you create the PublicClientApplication object. See https://aka.ms/msal-net-os-browser for details");
		}
		return FindFreeLocalhostRedirectUri(redirectUri);
	}

	private static Uri FindFreeLocalhostRedirectUri(Uri redirectUri)
	{
		if (redirectUri.Port > 0 && redirectUri.Port != 80)
		{
			return redirectUri;
		}
		TcpListener tcpListener = new TcpListener(IPAddress.Loopback, 0);
		try
		{
			tcpListener.Start();
			return new Uri("http://localhost:" + ((IPEndPoint)tcpListener.LocalEndpoint).Port);
		}
		finally
		{
			tcpListener?.Stop();
		}
	}

	private async Task<Uri> InterceptAuthorizationUriAsync(Uri authorizationUri, Uri redirectUri, bool isBrokerConfigured, CancellationToken cancellationToken)
	{
		Func<Uri, Task> func = (Uri u) => _platformProxy.StartDefaultOsBrowserAsync(u.AbsoluteUri, isBrokerConfigured);
		Func<Uri, Task> obj = _webViewOptions?.OpenBrowserAsync ?? func;
		cancellationToken.ThrowIfCancellationRequested();
		await obj(authorizationUri).ConfigureAwait(continueOnCapturedContext: false);
		cancellationToken.ThrowIfCancellationRequested();
		return await _uriInterceptor.ListenToSingleRequestAndRespondAsync(redirectUri.Port, redirectUri.AbsolutePath, GetResponseMessage, cancellationToken).ConfigureAwait(continueOnCapturedContext: false);
	}

	internal MessageAndHttpCode GetResponseMessage(Uri authCodeUri)
	{
		AuthorizationResult authorizationResult = AuthorizationResult.FromUri(authCodeUri.OriginalString);
		if (!string.IsNullOrEmpty(authorizationResult.Error))
		{
			_logger.Warning("Default OS Browser intercepted an Uri with an error: " + authorizationResult.Error + " " + authorizationResult.ErrorDescription);
			string message = string.Format(CultureInfo.InvariantCulture, _webViewOptions?.HtmlMessageError ?? "<html>\r\n  <head><title>Authentication Failed</title></head>\r\n  <body>\r\n    Authentication failed. You can return to the application. Feel free to close this browser tab.\r\n</br></br></br></br>\r\n    Error details: error {0} error_description: {1}\r\n  </body>\r\n</html>", authorizationResult.Error, authorizationResult.ErrorDescription);
			return GetMessage(_webViewOptions?.BrowserRedirectError, message);
		}
		return GetMessage(_webViewOptions?.BrowserRedirectSuccess, _webViewOptions?.HtmlMessageSuccess ?? "<html>\r\n  <head><title>Authentication Complete</title></head>\r\n  <body>\r\n    Authentication complete. You can return to the application. Feel free to close this browser tab.\r\n  </body>\r\n</html>");
	}

	private static MessageAndHttpCode GetMessage(Uri redirectUri, string message)
	{
		if (redirectUri != null)
		{
			return new MessageAndHttpCode(HttpStatusCode.Found, redirectUri.ToString());
		}
		return new MessageAndHttpCode(HttpStatusCode.OK, message);
	}
}
