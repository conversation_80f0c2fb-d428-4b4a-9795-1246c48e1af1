using System.Diagnostics;
using System.Reflection;
using System.Resources;
using System.Runtime.CompilerServices;
using System.Runtime.Versioning;

[assembly: AssemblyCompany("Microsoft Corporation")]
[assembly: AssemblyConfiguration("Release")]
[assembly: AssemblyCopyright("© Microsoft Corporation. All rights reserved.")]
[assembly: AssemblyDescription("Contains the BinaryData type, which is useful for converting between strings, streams, JSON, and bytes.")]
[assembly: AssemblyFileVersion("1.0.221.20802")]
[assembly: AssemblyInformationalVersion("1.0.2+7e3cf643977591e9041f4c628fd4d28237398e0b")]
[assembly: AssemblyProduct("Azure .NET SDK")]
[assembly: AssemblyTitle("System.Memory.Data")]
[assembly: AssemblyMetadata("RepositoryUrl", "https://github.com/Azure/azure-sdk-for-net")]
[assembly: NeutralResourcesLanguage("en-US")]
[assembly: AssemblyVersion("*******")]
