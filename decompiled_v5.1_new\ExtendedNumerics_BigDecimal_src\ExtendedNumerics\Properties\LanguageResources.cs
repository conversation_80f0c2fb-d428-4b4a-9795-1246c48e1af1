using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

namespace ExtendedNumerics.Properties;

[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
[DebuggerNonUserCode]
[CompilerGenerated]
public class LanguageResources
{
	private static ResourceManager resourceMan;

	private static CultureInfo resourceCulture;

	[EditorBrowsable(EditorBrowsableState.Advanced)]
	public static ResourceManager ResourceManager
	{
		get
		{
			if (resourceMan == null)
			{
				resourceMan = new ResourceManager("ExtendedNumerics.Properties.LanguageResources", typeof(LanguageResources).Assembly);
			}
			return resourceMan;
		}
	}

	[EditorBrowsable(EditorBrowsableState.Advanced)]
	public static CultureInfo Culture
	{
		get
		{
			return resourceCulture;
		}
		set
		{
			resourceCulture = value;
		}
	}

	public static string Arg_MustBeAPositiveInteger => ResourceManager.GetString("Arg_MustBeAPositiveInteger", resourceCulture);

	public static string Arg_MustBeGreaterThanOrEqualToOne => ResourceManager.GetString("Arg_MustBeGreaterThanOrEqualToOne", resourceCulture);

	public static string Arg_MustBeOfType => ResourceManager.GetString("Arg_MustBeOfType", resourceCulture);

	public static string Arg_MustNotEqualZero => ResourceManager.GetString("Arg_MustNotEqualZero", resourceCulture);

	public static string Arg_NegativePrecision => ResourceManager.GetString("Arg_NegativePrecision", resourceCulture);

	public static string Arithmetic_Trig_Undefined_Cot_Pi => ResourceManager.GetString("Arithmetic_Trig_Undefined_Cot_Pi", resourceCulture);

	public static string Arithmetic_Trig_Undefined_Cot_Zero => ResourceManager.GetString("Arithmetic_Trig_Undefined_Cot_Zero", resourceCulture);

	public static string Arithmetic_Trig_Undefined_Csc_Pi => ResourceManager.GetString("Arithmetic_Trig_Undefined_Csc_Pi", resourceCulture);

	public static string Arithmetic_Trig_Undefined_Csc_Zero => ResourceManager.GetString("Arithmetic_Trig_Undefined_Csc_Zero", resourceCulture);

	public static string Arithmetic_Trig_Undefined_Csch_Zero => ResourceManager.GetString("Arithmetic_Trig_Undefined_Csch_Zero", resourceCulture);

	public static string Arithmetic_Trig_Undefined_Sec_OddPiOver2 => ResourceManager.GetString("Arithmetic_Trig_Undefined_Sec_OddPiOver2", resourceCulture);

	public static string Arithmetic_Trig_Undefined_Tan_3PiOver2 => ResourceManager.GetString("Arithmetic_Trig_Undefined_Tan_3PiOver2", resourceCulture);

	public static string Arithmetic_Trig_Undefined_Tan_PiOver2 => ResourceManager.GetString("Arithmetic_Trig_Undefined_Tan_PiOver2", resourceCulture);

	public static string NotFinite_NaN => ResourceManager.GetString("NotFinite_NaN", resourceCulture);

	public static string NotSupported_NegativePower => ResourceManager.GetString("NotSupported_NegativePower", resourceCulture);

	public static string Overflow_BigDecimal_Infinity => ResourceManager.GetString("Overflow_BigDecimal_Infinity", resourceCulture);

	public static string Overflow_Decimal => ResourceManager.GetString("Overflow_Decimal", resourceCulture);

	public static string Overflow_Double => ResourceManager.GetString("Overflow_Double", resourceCulture);

	public static string Overflow_Fraction => ResourceManager.GetString("Overflow_Fraction", resourceCulture);

	public static string Overflow_Int32 => ResourceManager.GetString("Overflow_Int32", resourceCulture);

	public static string Overflow_Single => ResourceManager.GetString("Overflow_Single", resourceCulture);

	public static string Overflow_UInt32 => ResourceManager.GetString("Overflow_UInt32", resourceCulture);

	internal LanguageResources()
	{
	}
}
